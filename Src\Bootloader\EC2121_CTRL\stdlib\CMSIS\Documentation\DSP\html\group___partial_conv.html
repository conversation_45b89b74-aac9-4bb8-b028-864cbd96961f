<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Partial Convolution</title>
<title>CMSIS-DSP: Partial Convolution</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___partial_conv.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Partial Convolution</div>  </div>
<div class="ingroups"><a class="el" href="group__group_filters.html">Filtering Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga16d10f32072cd79fc5fb6e785df45f5e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga16d10f32072cd79fc5fb6e785df45f5e">arm_conv_partial_f32</a> (<a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints)</td></tr>
<tr class="memdesc:ga16d10f32072cd79fc5fb6e785df45f5e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of floating-point sequences.  <a href="#ga16d10f32072cd79fc5fb6e785df45f5e"></a><br/></td></tr>
<tr class="separator:ga16d10f32072cd79fc5fb6e785df45f5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3de9c4ddcc7886de25b70d875099a8d9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga3de9c4ddcc7886de25b70d875099a8d9">arm_conv_partial_fast_opt_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch1, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch2)</td></tr>
<tr class="memdesc:ga3de9c4ddcc7886de25b70d875099a8d9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of Q15 sequences (fast version) for Cortex-M3 and Cortex-M4.  <a href="#ga3de9c4ddcc7886de25b70d875099a8d9"></a><br/></td></tr>
<tr class="separator:ga3de9c4ddcc7886de25b70d875099a8d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1e4d43385cb62262a78c6752fe1fafb2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga1e4d43385cb62262a78c6752fe1fafb2">arm_conv_partial_fast_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints)</td></tr>
<tr class="memdesc:ga1e4d43385cb62262a78c6752fe1fafb2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of Q15 sequences (fast version) for Cortex-M3 and Cortex-M4.  <a href="#ga1e4d43385cb62262a78c6752fe1fafb2"></a><br/></td></tr>
<tr class="separator:ga1e4d43385cb62262a78c6752fe1fafb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga10c5294cda8c4985386f4e3944be7650"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga10c5294cda8c4985386f4e3944be7650">arm_conv_partial_fast_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints)</td></tr>
<tr class="memdesc:ga10c5294cda8c4985386f4e3944be7650"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of Q31 sequences (fast version) for Cortex-M3 and Cortex-M4.  <a href="#ga10c5294cda8c4985386f4e3944be7650"></a><br/></td></tr>
<tr class="separator:ga10c5294cda8c4985386f4e3944be7650"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga834b23b4ade8682beeb55778399101f8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga834b23b4ade8682beeb55778399101f8">arm_conv_partial_opt_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch1, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch2)</td></tr>
<tr class="memdesc:ga834b23b4ade8682beeb55778399101f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of Q15 sequences.  <a href="#ga834b23b4ade8682beeb55778399101f8"></a><br/></td></tr>
<tr class="separator:ga834b23b4ade8682beeb55778399101f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3707e16af1435b215840006a7ab0c98f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga3707e16af1435b215840006a7ab0c98f">arm_conv_partial_opt_q7</a> (<a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch1, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch2)</td></tr>
<tr class="memdesc:ga3707e16af1435b215840006a7ab0c98f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of Q7 sequences.  <a href="#ga3707e16af1435b215840006a7ab0c98f"></a><br/></td></tr>
<tr class="separator:ga3707e16af1435b215840006a7ab0c98f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga209a2a913a0c5e5679c5988da8f46b03"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga209a2a913a0c5e5679c5988da8f46b03">arm_conv_partial_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints)</td></tr>
<tr class="memdesc:ga209a2a913a0c5e5679c5988da8f46b03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of Q15 sequences.  <a href="#ga209a2a913a0c5e5679c5988da8f46b03"></a><br/></td></tr>
<tr class="separator:ga209a2a913a0c5e5679c5988da8f46b03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78e73a5f02d103168a09821fb461e77a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga78e73a5f02d103168a09821fb461e77a">arm_conv_partial_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints)</td></tr>
<tr class="memdesc:ga78e73a5f02d103168a09821fb461e77a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of Q31 sequences.  <a href="#ga78e73a5f02d103168a09821fb461e77a"></a><br/></td></tr>
<tr class="separator:ga78e73a5f02d103168a09821fb461e77a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8567259fe18396dd972242c41741ebf4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___partial_conv.html#ga8567259fe18396dd972242c41741ebf4">arm_conv_partial_q7</a> (<a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pDst, uint32_t firstIndex, uint32_t numPoints)</td></tr>
<tr class="memdesc:ga8567259fe18396dd972242c41741ebf4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partial convolution of Q7 sequences.  <a href="#ga8567259fe18396dd972242c41741ebf4"></a><br/></td></tr>
<tr class="separator:ga8567259fe18396dd972242c41741ebf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Partial Convolution is equivalent to Convolution except that a subset of the output samples is generated. Each function has two additional arguments. <code>firstIndex</code> specifies the starting index of the subset of output samples. <code>numPoints</code> is the number of output samples to compute. The function computes the output in the range <code>[firstIndex, ..., firstIndex+numPoints-1]</code>. The output array <code>pDst</code> contains <code>numPoints</code> values.</p>
<p>The allowable range of output indices is [0 srcALen+srcBLen-2]. If the requested subset does not fall in this range then the functions return ARM_MATH_ARGUMENT_ERROR. Otherwise the functions return ARM_MATH_SUCCESS. </p>
<dl class="section note"><dt>Note</dt><dd>Refer <a class="el" href="group___conv.html#ga3f860dc98c6fc4cafc421e4a2aed3c89" title="Convolution of floating-point sequences.">arm_conv_f32()</a> for details on fixed point behavior.</dd></dl>
<p><b>Fast Versions</b></p>
<dl class="section user"><dt></dt><dd>Fast versions are supported for Q31 and Q15 of partial convolution. Cycles for Fast versions are less compared to Q31 and Q15 of partial conv and the design requires the input signals should be scaled down to avoid intermediate overflows.</dd></dl>
<p><b>Opt Versions</b></p>
<dl class="section user"><dt></dt><dd>Opt versions are supported for Q15 and Q7. Design uses internal scratch buffer for getting good optimisation. These versions are optimised in cycles and consumes more memory(Scratch memory) compared to Q15 and Q7 versions of partial convolution </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga16d10f32072cd79fc5fb6e785df45f5e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2]. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga3de9c4ddcc7886de25b70d875099a8d9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_fast_opt_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch1</td><td>points to scratch buffer of size max(srcALen, srcBLen) + 2*min(srcALen, srcBLen) - 2. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch2</td><td>points to scratch buffer of size min(srcALen, srcBLen). </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2].</dd></dl>
<p>See <code><a class="el" href="group___partial_conv.html#ga209a2a913a0c5e5679c5988da8f46b03" title="Partial convolution of Q15 sequences.">arm_conv_partial_q15()</a></code> for a slower implementation of this function which uses a 64-bit accumulator to avoid wrap around distortion.</p>
<dl class="section user"><dt>Restrictions </dt><dd>If the silicon does not support unaligned memory access enable the macro UNALIGNED_SUPPORT_DISABLE In this case input, output, scratch1 and scratch2 buffers should be aligned by 32-bit </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0">arm_copy_q15()</a>, <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_fill_q15()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga1e4d43385cb62262a78c6752fe1fafb2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_fast_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2].</dd></dl>
<p>See <code><a class="el" href="group___partial_conv.html#ga209a2a913a0c5e5679c5988da8f46b03" title="Partial convolution of Q15 sequences.">arm_conv_partial_q15()</a></code> for a slower implementation of this function which uses a 64-bit accumulator to avoid wrap around distortion. </p>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga10c5294cda8c4985386f4e3944be7650"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_fast_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2].</dd></dl>
<dl class="section user"><dt></dt><dd>See <code><a class="el" href="group___partial_conv.html#ga78e73a5f02d103168a09821fb461e77a" title="Partial convolution of Q31 sequences.">arm_conv_partial_q31()</a></code> for a slower implementation of this function which uses a 64-bit accumulator to provide higher precision. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga834b23b4ade8682beeb55778399101f8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_opt_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch1</td><td>points to scratch buffer of size max(srcALen, srcBLen) + 2*min(srcALen, srcBLen) - 2. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch2</td><td>points to scratch buffer of size min(srcALen, srcBLen). </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2].</dd></dl>
<dl class="section user"><dt>Restrictions </dt><dd>If the silicon does not support unaligned memory access enable the macro UNALIGNED_SUPPORT_DISABLE In this case input, output, state buffers should be aligned by 32-bit</dd></dl>
<p>Refer to <code><a class="el" href="group___partial_conv.html#ga1e4d43385cb62262a78c6752fe1fafb2" title="Partial convolution of Q15 sequences (fast version) for Cortex-M3 and Cortex-M4.">arm_conv_partial_fast_q15()</a></code> for a faster but less precise version of this function for Cortex-M3 and Cortex-M4. </p>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0">arm_copy_q15()</a>, <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_fill_q15()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga3707e16af1435b215840006a7ab0c98f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_opt_q7 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch1</td><td>points to scratch buffer(of type q15_t) of size max(srcALen, srcBLen) + 2*min(srcALen, srcBLen) - 2. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch2</td><td>points to scratch buffer (of type q15_t) of size min(srcALen, srcBLen). </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2].</dd></dl>
<dl class="section user"><dt>Restrictions </dt><dd>If the silicon does not support unaligned memory access enable the macro UNALIGNED_SUPPORT_DISABLE In this case input, output, scratch1 and scratch2 buffers should be aligned by 32-bit </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a3ebff224ad44c217fde9f530342e2960">__PACKq7</a>, <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_fill_q15()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga209a2a913a0c5e5679c5988da8f46b03"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2].</dd></dl>
<p>Refer to <code><a class="el" href="group___partial_conv.html#ga1e4d43385cb62262a78c6752fe1fafb2" title="Partial convolution of Q15 sequences (fast version) for Cortex-M3 and Cortex-M4.">arm_conv_partial_fast_q15()</a></code> for a faster but less precise version of this function for Cortex-M3 and Cortex-M4.</p>
<dl class="section user"><dt></dt><dd>Refer the function <code><a class="el" href="group___partial_conv.html#ga834b23b4ade8682beeb55778399101f8" title="Partial convolution of Q15 sequences.">arm_conv_partial_opt_q15()</a></code> for a faster implementation of this function using scratch buffers. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga78e73a5f02d103168a09821fb461e77a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2].</dd></dl>
<p>See <code><a class="el" href="group___partial_conv.html#ga10c5294cda8c4985386f4e3944be7650" title="Partial convolution of Q31 sequences (fast version) for Cortex-M3 and Cortex-M4.">arm_conv_partial_fast_q31()</a></code> for a faster but less precise implementation of this function for Cortex-M3 and Cortex-M4. </p>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga8567259fe18396dd972242c41741ebf4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_conv_partial_q7 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>firstIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numPoints</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">firstIndex</td><td>is the first output sample to start with. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numPoints</td><td>is the number of output points to be computed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns either ARM_MATH_SUCCESS if the function completed correctly or ARM_MATH_ARGUMENT_ERROR if the requested subset is not in the range [0 srcALen+srcBLen-2].</dd></dl>
<dl class="section user"><dt></dt><dd>Refer the function <code><a class="el" href="group___partial_conv.html#ga3707e16af1435b215840006a7ab0c98f" title="Partial convolution of Q7 sequences.">arm_conv_partial_opt_q7()</a></code> for a faster implementation of this function. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
