<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Status Error Codes</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__usart__execution__status.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">Status Error Codes<div class="ingroups"><a class="el" href="group__usart__interface__gr.html">USART Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Negative values indicate errors (USART has specific codes in addition to common <a class="el" href="group__execution__status.html">Status Error Codes</a>).  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gaa98f35611ec5bd7034f21cb47199322b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__usart__execution__status.html#gaa98f35611ec5bd7034f21cb47199322b">ARM_USART_ERROR_MODE</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 1)</td></tr>
<tr class="memdesc:gaa98f35611ec5bd7034f21cb47199322b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Mode not supported.  <a href="#gaa98f35611ec5bd7034f21cb47199322b">More...</a><br/></td></tr>
<tr class="separator:gaa98f35611ec5bd7034f21cb47199322b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab57c4e8d4cb3a4b73751a002f5ec4586"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__usart__execution__status.html#gab57c4e8d4cb3a4b73751a002f5ec4586">ARM_USART_ERROR_BAUDRATE</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 2)</td></tr>
<tr class="memdesc:gab57c4e8d4cb3a4b73751a002f5ec4586"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified baudrate not supported.  <a href="#gab57c4e8d4cb3a4b73751a002f5ec4586">More...</a><br/></td></tr>
<tr class="separator:gab57c4e8d4cb3a4b73751a002f5ec4586"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaade95ddec6882e96c086dfe8e0ba9a4c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__usart__execution__status.html#gaade95ddec6882e96c086dfe8e0ba9a4c">ARM_USART_ERROR_DATA_BITS</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 3)</td></tr>
<tr class="memdesc:gaade95ddec6882e96c086dfe8e0ba9a4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified number of Data bits not supported.  <a href="#gaade95ddec6882e96c086dfe8e0ba9a4c">More...</a><br/></td></tr>
<tr class="separator:gaade95ddec6882e96c086dfe8e0ba9a4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaefabd886c586a45f4f7346c1f04392d0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__usart__execution__status.html#gaefabd886c586a45f4f7346c1f04392d0">ARM_USART_ERROR_PARITY</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 4)</td></tr>
<tr class="memdesc:gaefabd886c586a45f4f7346c1f04392d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Parity not supported.  <a href="#gaefabd886c586a45f4f7346c1f04392d0">More...</a><br/></td></tr>
<tr class="separator:gaefabd886c586a45f4f7346c1f04392d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1d699654fbbed3ca41c5ea10aac8f859"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__usart__execution__status.html#ga1d699654fbbed3ca41c5ea10aac8f859">ARM_USART_ERROR_STOP_BITS</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 5)</td></tr>
<tr class="memdesc:ga1d699654fbbed3ca41c5ea10aac8f859"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified number of Stop bits not supported.  <a href="#ga1d699654fbbed3ca41c5ea10aac8f859">More...</a><br/></td></tr>
<tr class="separator:ga1d699654fbbed3ca41c5ea10aac8f859"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf8fea8d43ff72c76434d8b5e9eebd890"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__usart__execution__status.html#gaf8fea8d43ff72c76434d8b5e9eebd890">ARM_USART_ERROR_FLOW_CONTROL</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 6)</td></tr>
<tr class="memdesc:gaf8fea8d43ff72c76434d8b5e9eebd890"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Flow Control not supported.  <a href="#gaf8fea8d43ff72c76434d8b5e9eebd890">More...</a><br/></td></tr>
<tr class="separator:gaf8fea8d43ff72c76434d8b5e9eebd890"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a1cd0a1e1bce9b545b0d7854a6fd6d6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__usart__execution__status.html#ga2a1cd0a1e1bce9b545b0d7854a6fd6d6">ARM_USART_ERROR_CPOL</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 7)</td></tr>
<tr class="memdesc:ga2a1cd0a1e1bce9b545b0d7854a6fd6d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Clock Polarity not supported.  <a href="#ga2a1cd0a1e1bce9b545b0d7854a6fd6d6">More...</a><br/></td></tr>
<tr class="separator:ga2a1cd0a1e1bce9b545b0d7854a6fd6d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade1af23c4ed5409dacd99ab76dc2ff8b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__usart__execution__status.html#gade1af23c4ed5409dacd99ab76dc2ff8b">ARM_USART_ERROR_CPHA</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 8)</td></tr>
<tr class="memdesc:gade1af23c4ed5409dacd99ab76dc2ff8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Clock Phase not supported.  <a href="#gade1af23c4ed5409dacd99ab76dc2ff8b">More...</a><br/></td></tr>
<tr class="separator:gade1af23c4ed5409dacd99ab76dc2ff8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Negative values indicate errors (USART has specific codes in addition to common <a class="el" href="group__execution__status.html">Status Error Codes</a>). </p>
<p>The USART driver has additional status error codes that are listed below. Note that the USART driver also returns the common <a class="el" href="group__execution__status.html">Status Error Codes</a>. </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="gab57c4e8d4cb3a4b73751a002f5ec4586"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_USART_ERROR_BAUDRATE&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 2)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified baudrate not supported. </p>
<p>The <b>baude rate</b> requested with the function <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="gade1af23c4ed5409dacd99ab76dc2ff8b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_USART_ERROR_CPHA&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 8)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Clock Phase not supported. </p>
<p>The <b>clock phase</b> requested with the function <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="ga2a1cd0a1e1bce9b545b0d7854a6fd6d6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_USART_ERROR_CPOL&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 7)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Clock Polarity not supported. </p>
<p>The <b>clock polarity</b> requested with the function <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="gaade95ddec6882e96c086dfe8e0ba9a4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_USART_ERROR_DATA_BITS&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 3)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified number of Data bits not supported. </p>
<p>The number of <b>data bits</b> requested with the function <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="gaf8fea8d43ff72c76434d8b5e9eebd890"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_USART_ERROR_FLOW_CONTROL&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 6)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Flow Control not supported. </p>
<p>The <b>flow control</b> requested with the function <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="gaa98f35611ec5bd7034f21cb47199322b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_USART_ERROR_MODE&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 1)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Mode not supported. </p>
<p>The <b>mode</b> requested with the function <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="gaefabd886c586a45f4f7346c1f04392d0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_USART_ERROR_PARITY&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 4)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Parity not supported. </p>
<p>The <b>parity bit</b> requested with the function <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="ga1d699654fbbed3ca41c5ea10aac8f859"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_USART_ERROR_STOP_BITS&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 5)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified number of Stop bits not supported. </p>
<p>The <b>stop bit</b> requested with the function <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control</a> is not supported by this driver. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:51 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
