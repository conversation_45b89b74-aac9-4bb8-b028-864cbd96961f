<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__"><span>_</span></a></li>
      <li class="current"><a href="globals_0x61.html#index_a"><span>a</span></a></li>
      <li><a href="globals_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="globals_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="globals_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="globals_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_0x61.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index_a"></a>- a -</h3><ul>
<li>A_f32
: <a class="el" href="arm__matrix__example__f32_8c.html#aed27b92d9847194d9dcce40cecf2b48a">arm_matrix_example_f32.c</a>
</li>
<li>Ak
: <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">ARM/arm_convolution_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">GCC/arm_convolution_example_f32.c</a>
</li>
<li>ALIGN4
: <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">arm_math.h</a>
</li>
<li>arm_abs_f32()
: <a class="el" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02">arm_abs_f32.c</a>
, <a class="el" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02">arm_math.h</a>
</li>
<li>arm_abs_q15()
: <a class="el" href="group___basic_abs.html#ga39f92964c9b649ba252e26ebe7b95594">arm_abs_q15.c</a>
, <a class="el" href="group___basic_abs.html#ga39f92964c9b649ba252e26ebe7b95594">arm_math.h</a>
</li>
<li>arm_abs_q31()
: <a class="el" href="group___basic_abs.html#ga59eafcdcdb52da60d37f20aec6ff4577">arm_abs_q31.c</a>
, <a class="el" href="group___basic_abs.html#ga59eafcdcdb52da60d37f20aec6ff4577">arm_math.h</a>
</li>
<li>arm_abs_q7()
: <a class="el" href="group___basic_abs.html#gadc30985e33fbf96802a5a7954dece3b1">arm_abs_q7.c</a>
, <a class="el" href="group___basic_abs.html#gadc30985e33fbf96802a5a7954dece3b1">arm_math.h</a>
</li>
<li>arm_add_f32()
: <a class="el" href="group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd">arm_add_f32.c</a>
, <a class="el" href="group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd">arm_math.h</a>
</li>
<li>arm_add_q15()
: <a class="el" href="group___basic_add.html#gabb51285a41f511670bbff62fc0e1bf62">arm_add_q15.c</a>
, <a class="el" href="group___basic_add.html#gabb51285a41f511670bbff62fc0e1bf62">arm_math.h</a>
</li>
<li>arm_add_q31()
: <a class="el" href="group___basic_add.html#ga24d6c3f7f8b9fae4847c0c3f26a39a3b">arm_add_q31.c</a>
, <a class="el" href="group___basic_add.html#ga24d6c3f7f8b9fae4847c0c3f26a39a3b">arm_math.h</a>
</li>
<li>arm_add_q7()
: <a class="el" href="group___basic_add.html#gaed633f415a7840a66861debca2dfb96b">arm_add_q7.c</a>
, <a class="el" href="group___basic_add.html#gaed633f415a7840a66861debca2dfb96b">arm_math.h</a>
</li>
<li>arm_apply_guard_bits()
: <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_matrix_example/ARM/math_helper.c</a>
</li>
<li>ARM_MATH_ARGUMENT_ERROR
: <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">arm_math.h</a>
</li>
<li>ARM_MATH_LENGTH_ERROR
: <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9ae74d7f8a53aec0512ae8f0a421e0e1">arm_math.h</a>
</li>
<li>ARM_MATH_NANINF
: <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6ac55996aaf19245238a8f57a91aeaefcc">arm_math.h</a>
</li>
<li>ARM_MATH_SINGULAR
: <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a91509ea9c819dbd592ac13a6b05382dc">arm_math.h</a>
</li>
<li>ARM_MATH_SIZE_MISMATCH
: <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">arm_math.h</a>
</li>
<li>ARM_MATH_SUCCESS
: <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">arm_math.h</a>
</li>
<li>ARM_MATH_TEST_FAILURE
: <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">arm_math.h</a>
</li>
<li>armBitRevIndexTable1024
: <a class="el" href="arm__common__tables_8c.html#ae69b72fb0be5dab9a0ea76e9b6995cb6">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#ae69b72fb0be5dab9a0ea76e9b6995cb6">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE1024_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#af3b3659a55efaf414757d15e6c0ea9cc">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable128
: <a class="el" href="arm__common__tables_8c.html#a04711bbb245f2ac7202db666eaaf10f2">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a04711bbb245f2ac7202db666eaaf10f2">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable16
: <a class="el" href="arm__common__tables_8c.html#a5ab065857509fe5780d79fdcdce801cb">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a5ab065857509fe5780d79fdcdce801cb">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable2048
: <a class="el" href="arm__common__tables_8c.html#a68b7fcd07ae5433082e600dc7e7c7430">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a68b7fcd07ae5433082e600dc7e7c7430">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE2048_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#a1137f42be79c5941e942b58e262b5225">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable256
: <a class="el" href="arm__common__tables_8c.html#a77b17c8e7539af315c57de27610d8407">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a77b17c8e7539af315c57de27610d8407">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable32
: <a class="el" href="arm__common__tables_8c.html#afae094ea3df14c134012c4cb7b816637">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#afae094ea3df14c134012c4cb7b816637">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable4096
: <a class="el" href="arm__common__tables_8c.html#ac6bd23609f5bb10182e8eae65400541b">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#ac6bd23609f5bb10182e8eae65400541b">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE4096_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#af08eb635c0e1cf0ab3e29931f9bf1492">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable512
: <a class="el" href="arm__common__tables_8c.html#a297a311183fb6d17d7ee0152ad1e43f3">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a297a311183fb6d17d7ee0152ad1e43f3">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable64
: <a class="el" href="arm__common__tables_8c.html#aafcb5c9203dada88ed6d1bdcf16aaba4">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#aafcb5c9203dada88ed6d1bdcf16aaba4">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_128_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#abb73376f7efda869394aab2acef4291c">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_256_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#aa7dc18c3b4f8d76f5a29f7b182007934">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_512_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#ab21231782baf177ef3edad11aeba5a4f">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE__16_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#a52289ebb691669410fbc40d1a8a1562a">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE__32_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#a6e12fc7073f15899078a1b2d8f4afb4c">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE__64_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#a73e1987baf5282c699168bccf635930e">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable_fixed_1024
: <a class="el" href="arm__common__tables_8c.html#a579beb19201fab01210c37253447fa52">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a579beb19201fab01210c37253447fa52">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED_1024_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#ab78db333c5f36a927cf5f6b492e93dd3">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable_fixed_128
: <a class="el" href="arm__common__tables_8c.html#aa10281deffc0cb708a08d55cfa513507">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#aa10281deffc0cb708a08d55cfa513507">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable_fixed_16
: <a class="el" href="arm__common__tables_8c.html#a6c3b510a7d499dccaaea1ff164397ffb">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a6c3b510a7d499dccaaea1ff164397ffb">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable_fixed_2048
: <a class="el" href="arm__common__tables_8c.html#ad888a207e20f601ed80b2ad43428c8cc">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#ad888a207e20f601ed80b2ad43428c8cc">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED_2048_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#a7dbfc9019953b525d83184a50f9976cc">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable_fixed_256
: <a class="el" href="arm__common__tables_8h.html#a721d01114016584629f03c0af37dd21e">arm_common_tables.h</a>
, <a class="el" href="arm__common__tables_8c.html#a721d01114016584629f03c0af37dd21e">arm_common_tables.c</a>
</li>
<li>armBitRevIndexTable_fixed_32
: <a class="el" href="arm__common__tables_8h.html#a59710415522cc38defa30402021f1f6b">arm_common_tables.h</a>
, <a class="el" href="arm__common__tables_8c.html#a59710415522cc38defa30402021f1f6b">arm_common_tables.c</a>
</li>
<li>armBitRevIndexTable_fixed_4096
: <a class="el" href="arm__common__tables_8h.html#a2db644df1e878209441166cbb8d0db4f">arm_common_tables.h</a>
, <a class="el" href="arm__common__tables_8c.html#a2db644df1e878209441166cbb8d0db4f">arm_common_tables.c</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED_4096_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#acbf48883fbb31d3dc71d232aa8e8f91f">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable_fixed_512
: <a class="el" href="arm__common__tables_8c.html#a03d1de7bfc5f318bc4fcfddd920bcb5a">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a03d1de7bfc5f318bc4fcfddd920bcb5a">arm_common_tables.h</a>
</li>
<li>armBitRevIndexTable_fixed_64
: <a class="el" href="arm__common__tables_8c.html#af9e1bbd7d535806a170786b069863b47">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#af9e1bbd7d535806a170786b069863b47">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED__128_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#aa3b70f6b0a87ecd706fc51bb3551977b">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED__256_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#ac0711126d0e162366ec7d0ebcb2a4420">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED__512_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#a5486cba85dce51ffbfe6c0475882cc82">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED___16_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#a1dfdb9f7a5ad88ba7105c6cbc7e2c76e">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED___32_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#aaa9ecdc043a73fa12c941cbe6613f9fa">arm_common_tables.h</a>
</li>
<li>ARMBITREVINDEXTABLE_FIXED___64_TABLE_LENGTH
: <a class="el" href="arm__common__tables_8h.html#ae53dc7c3198f9cfb5393e3a2644a12ac">arm_common_tables.h</a>
</li>
<li>armBitRevTable
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">arm_common_tables.h</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">arm_common_tables.c</a>
</li>
<li>armRecipTableQ15
: <a class="el" href="arm__common__tables_8c.html#a66ca8ac5f3a63d9962f501ae60aa32be">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a56d3642e4ee33e3ada57ff11ecda1498">arm_common_tables.h</a>
</li>
<li>armRecipTableQ31
: <a class="el" href="arm__common__tables_8c.html#aae6056f6c4e8f7e494445196bf864479">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#aae6056f6c4e8f7e494445196bf864479">arm_common_tables.h</a>
</li>
<li>AT_f32
: <a class="el" href="arm__matrix__example__f32_8c.html#a46dc2aa6dfc692af7b4a1379d7329ccd">arm_matrix_example_f32.c</a>
</li>
<li>ATMA_f32
: <a class="el" href="arm__matrix__example__f32_8c.html#a867497c6bf86014513bf2ad3551aa896">arm_matrix_example_f32.c</a>
</li>
<li>ATMAI_f32
: <a class="el" href="arm__matrix__example__f32_8c.html#a44425c149c52b326a3b7a77676686f00">arm_matrix_example_f32.c</a>
</li>
<li>AxB
: <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098">GCC/arm_convolution_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098">ARM/arm_convolution_example_f32.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
