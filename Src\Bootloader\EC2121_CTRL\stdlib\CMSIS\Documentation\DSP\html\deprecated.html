<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Deprecated List</title>
<title>CMSIS-DSP: Deprecated List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('deprecated.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Deprecated List </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><dl class="reflist">
<dt><a class="anchor" id="_deprecated000001"></a>Global <a class="el" href="group___complex_f_f_t.html#ga9fadd650b802f612ae558ddaab789a6d">arm_cfft_radix2_f32</a>  (const <a class="el" href="structarm__cfft__radix2__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_radix2_instance_f32</a> *S, float32_t *pSrc)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> and will be removed in the future.  </dd>
<dt><a class="anchor" id="_deprecated000002"></a>Global <a class="el" href="group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7">arm_cfft_radix2_init_f32</a>  (<a class="el" href="structarm__cfft__radix2__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_radix2_instance_f32</a> *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> and will be removed in the future.  </dd>
<dt><a class="anchor" id="_deprecated000003"></a>Global <a class="el" href="group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858">arm_cfft_radix2_init_q15</a>  (<a class="el" href="structarm__cfft__radix2__instance__q15.html" title="Instance structure for the Q15 CFFT/CIFFT function.">arm_cfft_radix2_instance_q15</a> *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> and will be removed  </dd>
<dt><a class="anchor" id="_deprecated000004"></a>Global <a class="el" href="group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638">arm_cfft_radix2_init_q31</a>  (<a class="el" href="structarm__cfft__radix2__instance__q31.html" title="Instance structure for the Radix-2 Q31 CFFT/CIFFT function.">arm_cfft_radix2_instance_q31</a> *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> and will be removed  </dd>
<dt><a class="anchor" id="_deprecated000005"></a>Global <a class="el" href="group___complex_f_f_t.html#ga55b424341dc3efd3fa0bcaaff4bdbf40">arm_cfft_radix2_q15</a>  (const <a class="el" href="structarm__cfft__radix2__instance__q15.html" title="Instance structure for the Q15 CFFT/CIFFT function.">arm_cfft_radix2_instance_q15</a> *S, q15_t *pSrc)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> and will be removed  </dd>
<dt><a class="anchor" id="_deprecated000006"></a>Global <a class="el" href="group___complex_f_f_t.html#ga6321f703ec87a274aedaab33d3e766b4">arm_cfft_radix2_q31</a>  (const <a class="el" href="structarm__cfft__radix2__instance__q31.html" title="Instance structure for the Radix-2 Q31 CFFT/CIFFT function.">arm_cfft_radix2_instance_q31</a> *S, q31_t *pSrc)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> and will be removed  </dd>
<dt><a class="anchor" id="_deprecated000007"></a>Global <a class="el" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26">arm_cfft_radix4_f32</a>  (const <a class="el" href="structarm__cfft__radix4__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_radix4_instance_f32</a> *S, float32_t *pSrc)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> and will be removed in the future.  </dd>
<dt><a class="anchor" id="_deprecated000008"></a>Global <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32</a>  (<a class="el" href="structarm__cfft__radix4__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_radix4_instance_f32</a> *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag)</dt>
<dd>Do not use this function. It has been superceded by <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> and will be removed in the future.  </dd>
<dt><a class="anchor" id="_deprecated000009"></a>Global <a class="el" href="group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef">arm_cfft_radix4_init_q15</a>  (<a class="el" href="structarm__cfft__radix4__instance__q15.html" title="Instance structure for the Q15 CFFT/CIFFT function.">arm_cfft_radix4_instance_q15</a> *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> and will be removed  </dd>
<dt><a class="anchor" id="_deprecated000010"></a>Global <a class="el" href="group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c">arm_cfft_radix4_init_q31</a>  (<a class="el" href="structarm__cfft__radix4__instance__q31.html" title="Instance structure for the Q31 CFFT/CIFFT function.">arm_cfft_radix4_instance_q31</a> *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> and will be removed  </dd>
<dt><a class="anchor" id="_deprecated000011"></a>Global <a class="el" href="group___complex_f_f_t.html#ga8d66cdac41b8bf6cefdb895456eee84a">arm_cfft_radix4_q15</a>  (const <a class="el" href="structarm__cfft__radix4__instance__q15.html" title="Instance structure for the Q15 CFFT/CIFFT function.">arm_cfft_radix4_instance_q15</a> *S, q15_t *pSrc)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> and will be removed  </dd>
<dt><a class="anchor" id="_deprecated000012"></a>Global <a class="el" href="group___complex_f_f_t.html#gafde3ee1f58cf393b45a9073174fff548">arm_cfft_radix4_q31</a>  (const <a class="el" href="structarm__cfft__radix4__instance__q31.html" title="Instance structure for the Q31 CFFT/CIFFT function.">arm_cfft_radix4_instance_q31</a> *S, q31_t *pSrc)</dt>
<dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> and will be removed  </dd>
<dt><a class="anchor" id="_deprecated000013"></a>Global <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32</a>  (const <a class="el" href="structarm__rfft__instance__f32.html" title="Instance structure for the floating-point RFFT/RIFFT function.">arm_rfft_instance_f32</a> *S, float32_t *pSrc, float32_t *pDst)</dt>
<dd>Do not use this function. It has been superceded by <a class="el" href="group___real_f_f_t.html#ga180d8b764d59cbb85d37a2d5f7cd9799">arm_rfft_fast_f32</a> and will be removed in the future.  </dd>
<dt><a class="anchor" id="_deprecated000014"></a>Global <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32</a>  (<a class="el" href="structarm__rfft__instance__f32.html" title="Instance structure for the floating-point RFFT/RIFFT function.">arm_rfft_instance_f32</a> *S, <a class="el" href="structarm__cfft__radix4__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_radix4_instance_f32</a> *S_CFFT, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag)</dt>
<dd>Do not use this function. It has been superceded by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32</a> and will be removed in the future. </dd>
</dl>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
