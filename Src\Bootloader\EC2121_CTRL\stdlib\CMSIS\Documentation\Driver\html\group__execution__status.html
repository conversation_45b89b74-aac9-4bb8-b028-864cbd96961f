<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Status Error Codes</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__execution__status.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">Status Error Codes<div class="ingroups"><a class="el" href="group__common__drv__gr.html">Common Driver Definitions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Negative return values of functions indicate errors occurred during execution.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga85752c5de59e8adeb001e35ff5be6be7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__execution__status.html#ga85752c5de59e8adeb001e35ff5be6be7">ARM_DRIVER_OK</a>&#160;&#160;&#160;0</td></tr>
<tr class="memdesc:ga85752c5de59e8adeb001e35ff5be6be7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Operation succeeded.  <a href="#ga85752c5de59e8adeb001e35ff5be6be7">More...</a><br/></td></tr>
<tr class="separator:ga85752c5de59e8adeb001e35ff5be6be7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f627075447749bb368d3b768be107cb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__execution__status.html#ga2f627075447749bb368d3b768be107cb">ARM_DRIVER_ERROR</a>&#160;&#160;&#160;-1</td></tr>
<tr class="memdesc:ga2f627075447749bb368d3b768be107cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unspecified error.  <a href="#ga2f627075447749bb368d3b768be107cb">More...</a><br/></td></tr>
<tr class="separator:ga2f627075447749bb368d3b768be107cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga13c1123319c7b9a4735d63447f35116b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__execution__status.html#ga13c1123319c7b9a4735d63447f35116b">ARM_DRIVER_ERROR_BUSY</a>&#160;&#160;&#160;-2</td></tr>
<tr class="memdesc:ga13c1123319c7b9a4735d63447f35116b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Driver is busy.  <a href="#ga13c1123319c7b9a4735d63447f35116b">More...</a><br/></td></tr>
<tr class="separator:ga13c1123319c7b9a4735d63447f35116b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0bac892205bb2d586b822e8b178ab310"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__execution__status.html#ga0bac892205bb2d586b822e8b178ab310">ARM_DRIVER_ERROR_TIMEOUT</a>&#160;&#160;&#160;-3</td></tr>
<tr class="memdesc:ga0bac892205bb2d586b822e8b178ab310"><td class="mdescLeft">&#160;</td><td class="mdescRight">Timeout occurred.  <a href="#ga0bac892205bb2d586b822e8b178ab310">More...</a><br/></td></tr>
<tr class="separator:ga0bac892205bb2d586b822e8b178ab310"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2efa59e480d82697795439220e6884e4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__execution__status.html#ga2efa59e480d82697795439220e6884e4">ARM_DRIVER_ERROR_UNSUPPORTED</a>&#160;&#160;&#160;-4</td></tr>
<tr class="memdesc:ga2efa59e480d82697795439220e6884e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Operation not supported.  <a href="#ga2efa59e480d82697795439220e6884e4">More...</a><br/></td></tr>
<tr class="separator:ga2efa59e480d82697795439220e6884e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac781d4b70ce17c4c2efe2db045be751c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__execution__status.html#gac781d4b70ce17c4c2efe2db045be751c">ARM_DRIVER_ERROR_PARAMETER</a>&#160;&#160;&#160;-5</td></tr>
<tr class="memdesc:gac781d4b70ce17c4c2efe2db045be751c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Parameter error.  <a href="#gac781d4b70ce17c4c2efe2db045be751c">More...</a><br/></td></tr>
<tr class="separator:gac781d4b70ce17c4c2efe2db045be751c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a2b5d68f6649598d099b88c0eaee3e5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a>&#160;&#160;&#160;-6</td></tr>
<tr class="memdesc:ga5a2b5d68f6649598d099b88c0eaee3e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Start of driver specific errors.  <a href="#ga5a2b5d68f6649598d099b88c0eaee3e5">More...</a><br/></td></tr>
<tr class="separator:ga5a2b5d68f6649598d099b88c0eaee3e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Negative return values of functions indicate errors occurred during execution. </p>
<p>Most functions return a status information using negative return values. The following list provides the status error codes that are common in all drivers. The drivers may return also status error codes that are specific to the peripheral. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__execution__status.html">Status Error Codes</a> for SPI driver; <a class="el" href="group__usart__execution__status.html">Status Error Codes</a> for USART driver; <a class="el" href="group__nand__execution__status.html">Status Error Codes</a> for NAND driver; </dd></dl>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="ga2f627075447749bb368d3b768be107cb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_DRIVER_ERROR&#160;&#160;&#160;-1</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unspecified error. </p>
<p>The function did not execute correct and an unspecified error occurred during execution. </p>

</div>
</div>
<a class="anchor" id="ga13c1123319c7b9a4735d63447f35116b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_DRIVER_ERROR_BUSY&#160;&#160;&#160;-2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Driver is busy. </p>
<p>The function cannot be executed because the driver is busy with the execution of a conflicting operation. </p>

</div>
</div>
<a class="anchor" id="gac781d4b70ce17c4c2efe2db045be751c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_DRIVER_ERROR_PARAMETER&#160;&#160;&#160;-5</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Parameter error. </p>
<p>A function parameter is incorrect. </p>

</div>
</div>
<a class="anchor" id="ga5a2b5d68f6649598d099b88c0eaee3e5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_DRIVER_ERROR_SPECIFIC&#160;&#160;&#160;-6</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Start of driver specific errors. </p>
<p>This value indicates the start of status error codes that are specific to the peripheral driver. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__execution__status.html">Status Error Codes</a> for SPI driver; <a class="el" href="group__usart__execution__status.html">Status Error Codes</a> for USART driver; </dd></dl>

</div>
</div>
<a class="anchor" id="ga0bac892205bb2d586b822e8b178ab310"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_DRIVER_ERROR_TIMEOUT&#160;&#160;&#160;-3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Timeout occurred. </p>
<p>The function execution is terminated because a peripheral did not react within a specific timeout limit. </p>

</div>
</div>
<a class="anchor" id="ga2efa59e480d82697795439220e6884e4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_DRIVER_ERROR_UNSUPPORTED&#160;&#160;&#160;-4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Operation not supported. </p>
<p>The function requested an operation (for example by using an illegal control code) that is not supported. </p>

</div>
</div>
<a class="anchor" id="ga85752c5de59e8adeb001e35ff5be6be7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_DRIVER_OK&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Operation succeeded. </p>
<p>The value 0 or positive values indicate that the function execution is completed without any errors. Note that positive values are used to provide for example the number of data items. </p>

<p>Referenced by <a class="el" href="group__i2c__interface__gr.html#ga828f5fa289d065675ef78a9a73d129dc">ARM_I2C_Control()</a>, <a class="el" href="group__i2c__interface__gr.html#ga79d2f7d01b3a681d1cf0d70ac6692696">ARM_I2C_Initialize()</a>, <a class="el" href="group__i2c__interface__gr.html#gafa22504bcf88a85584dfe6e0dd270ad5">ARM_I2C_MasterReceive()</a>, <a class="el" href="group__i2c__interface__gr.html#ga8bf4214580149d5a5d2360f71f0feb94">ARM_I2C_MasterTransmit()</a>, <a class="el" href="group__i2c__interface__gr.html#ga734a69200e063fdbfb5110062afe9329">ARM_I2C_PowerControl()</a>, <a class="el" href="group__i2c__interface__gr.html#gae3c9abccd1d377385d3d4cfe29035164">ARM_I2C_SlaveReceive()</a>, <a class="el" href="group__i2c__interface__gr.html#gafe164f30eba78f066272373b98a62cd4">ARM_I2C_SlaveTransmit()</a>, <a class="el" href="group__i2c__interface__gr.html#ga30d8bf600b6b3182a1f867407b3d6e75">ARM_I2C_Uninitialize()</a>, <a class="el" href="group__mci__interface__gr.html#ga78fd8cd818542a03df45abb117fa916e">ARM_MCI_AbortTransfer()</a>, <a class="el" href="group__mci__interface__gr.html#gab161f80e0eda2815f3e0ebbba1314ff0">ARM_MCI_CardPower()</a>, <a class="el" href="group__mci__interface__gr.html#gaec0506a2aa4ae75cf6bc02528f36fe30">ARM_MCI_Control()</a>, <a class="el" href="group__mci__interface__gr.html#ga8d61aa42ce78d1864fa928c1f273cbd9">ARM_MCI_GetStatus()</a>, <a class="el" href="group__mci__interface__gr.html#ga6f34d4ab362e596ddaf23aac093268cf">ARM_MCI_Initialize()</a>, <a class="el" href="group__mci__interface__gr.html#ga19752749d04ed22dc91c4294645e0244">ARM_MCI_PowerControl()</a>, <a class="el" href="group__mci__interface__gr.html#ga5a431da89feabc2b4bc0c27943dff6f2">ARM_MCI_SendCommand()</a>, <a class="el" href="group__mci__interface__gr.html#gaaec681bcd8e6811c5743e33ee0f35ed1">ARM_MCI_SetupTransfer()</a>, <a class="el" href="group__mci__interface__gr.html#gaef8183e77797e74997551d03646d42c2">ARM_MCI_Uninitialize()</a>, <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control()</a>, <a class="el" href="group__spi__interface__gr.html#ga1a3c11ed523a4355cd91069527945906">ARM_SPI_Initialize()</a>, <a class="el" href="group__spi__interface__gr.html#ga1a1e7e80ea32ae381b75213c32aa8067">ARM_SPI_PowerControl()</a>, <a class="el" href="group__spi__interface__gr.html#ga726aff54e782ed9b47f7ba1280a3d8f6">ARM_SPI_Receive()</a>, <a class="el" href="group__spi__interface__gr.html#gab2a303d1071e926280d50682f4808479">ARM_SPI_Send()</a>, <a class="el" href="group__spi__interface__gr.html#gaa24026b3822c10272e301f1505136ec2">ARM_SPI_Transfer()</a>, <a class="el" href="group__spi__interface__gr.html#ga0c480ee3eabb82fc746e89741ed2e03e">ARM_SPI_Uninitialize()</a>, <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">ARM_USART_Control()</a>, <a class="el" href="group__usart__interface__gr.html#ga51f06805e9a6197c553fa9513ac7b9d6">ARM_USART_Initialize()</a>, <a class="el" href="group__usart__interface__gr.html#ga9bad012b28d544f3eeeea9c2f71a4086">ARM_USART_PowerControl()</a>, <a class="el" href="group__usart__interface__gr.html#gae9efabdabb5aaa17bce83339f8a58803">ARM_USART_Receive()</a>, <a class="el" href="group__usart__interface__gr.html#ga5cf758b0b9d03dca68846962f73c0b08">ARM_USART_Send()</a>, <a class="el" href="group__usart__interface__gr.html#gad8eb0eb1d1c24fc725584ab93214cfc7">ARM_USART_SetModemControl()</a>, <a class="el" href="group__usart__interface__gr.html#ga878899928d34a818edd3e97d67b65c2a">ARM_USART_Transfer()</a>, <a class="el" href="group__usart__interface__gr.html#ga96f31f07a6721cf75de2a7a0ab723d26">ARM_USART_Uninitialize()</a>, <a class="el" href="group__usbd__interface__gr.html#ga99207f7ff5e97a7f65754eab7f775fca">ARM_USBD_DeviceConnect()</a>, <a class="el" href="group__usbd__interface__gr.html#ga37234abecbb63e4e739f9676e489d2d1">ARM_USBD_DeviceDisconnect()</a>, <a class="el" href="group__usbd__interface__gr.html#ga7624d6b2cbe5e6ab5016206ce641eee2">ARM_USBD_DeviceGetState()</a>, <a class="el" href="group__usbd__interface__gr.html#ga7e149a4c6a0196da24a44f4fada75fb1">ARM_USBD_DeviceRemoteWakeup()</a>, <a class="el" href="group__usbd__interface__gr.html#gae66f696584e25fb2ddabe9070fa38670">ARM_USBD_DeviceSetAddress()</a>, <a class="el" href="group__usbd__interface__gr.html#ga62d7d5bdcf9ca7bf7e6d8434368abad8">ARM_USBD_EndpointConfigure()</a>, <a class="el" href="group__usbd__interface__gr.html#ga9502cd7b8e4c583920fccadc4ccf7975">ARM_USBD_EndpointStall()</a>, <a class="el" href="group__usbd__interface__gr.html#ga6e69ad097553125bb01a22dc719e0d30">ARM_USBD_EndpointTransfer()</a>, <a class="el" href="group__usbd__interface__gr.html#ga7cf3bcc105dbb8cbdc915e8caca8529e">ARM_USBD_EndpointTransferAbort()</a>, <a class="el" href="group__usbd__interface__gr.html#gaca913df5188dc0f0c4f707b57c2a88fc">ARM_USBD_EndpointUnconfigure()</a>, <a class="el" href="group__usbd__interface__gr.html#ga60b95c9c0c6767ff5938464cfd748f81">ARM_USBD_Initialize()</a>, <a class="el" href="group__usbd__interface__gr.html#gaa5bdaac19f6df30c6e569abef17cdb42">ARM_USBD_PowerControl()</a>, <a class="el" href="group__usbd__interface__gr.html#ga6bc0ebf699a0f28330f21cab83f85e9e">ARM_USBD_ReadSetupPacket()</a>, <a class="el" href="group__usbd__interface__gr.html#gafaead6713f141be1734de0110eda966b">ARM_USBD_Uninitialize()</a>, <a class="el" href="group__usbh__hci__gr.html#gabc1392a544cb64491b5ea5ce6590d832">ARM_USBH_HCI_Initialize()</a>, <a class="el" href="group__usbh__hci__gr.html#gade1e83403c6ea965fe3e6c4c21fbbded">ARM_USBH_HCI_PortVbusOnOff()</a>, <a class="el" href="group__usbh__hci__gr.html#ga27fa5ec8854cd9877bbef4abffe9a12b">ARM_USBH_HCI_PowerControl()</a>, <a class="el" href="group__usbh__hci__gr.html#gaacb68fdf201cdb1846b31642a760f041">ARM_USBH_HCI_Uninitialize()</a>, <a class="el" href="group__usbh__host__gr.html#gad1e73f778c95dd46d4396e7741a97f0b">ARM_USBH_Initialize()</a>, <a class="el" href="group__usbh__host__gr.html#gab2135041e6d481f186015f36fa0d0521">ARM_USBH_PipeDelete()</a>, <a class="el" href="group__usbh__host__gr.html#ga2076a7ae55f603859c726e57b061ac73">ARM_USBH_PipeModify()</a>, <a class="el" href="group__usbh__host__gr.html#ga7f5a605dbe98e450e6965d515fde65a7">ARM_USBH_PipeReset()</a>, <a class="el" href="group__usbh__host__gr.html#ga817d503a24ad8927fa362c8f6394920d">ARM_USBH_PipeTransfer()</a>, <a class="el" href="group__usbh__host__gr.html#ga1d4048a076aed71e585cea96a21f0afb">ARM_USBH_PipeTransferAbort()</a>, <a class="el" href="group__usbh__host__gr.html#gab99882e11ee03018da9ebe33797cc5ff">ARM_USBH_PortReset()</a>, <a class="el" href="group__usbh__host__gr.html#gab438b55ada37e2987e77e105f061f2de">ARM_USBH_PortResume()</a>, <a class="el" href="group__usbh__host__gr.html#ga620f8852a70a47a581001ed3050436d6">ARM_USBH_PortSuspend()</a>, <a class="el" href="group__usbh__host__gr.html#gaccca5ddd4a9d04388e7678a3aed3f6e4">ARM_USBH_PortVbusOnOff()</a>, <a class="el" href="group__usbh__host__gr.html#ga290a5e2e491da784e63be94699974d4a">ARM_USBH_PowerControl()</a>, and <a class="el" href="group__usbh__host__gr.html#gafc2f18bc12bb0019f9cd1836dcca408d">ARM_USBH_Uninitialize()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
