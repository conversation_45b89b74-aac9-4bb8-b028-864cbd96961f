<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: SPI Interface</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__spi__interface__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Content</a> &#124;
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">SPI Interface</div>  </div>
</div><!--header-->
<div class="contents">

<p>Driver API for SPI Bus Peripheral (Driver_SPI.h)  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Content</h2></td></tr>
<tr class="memitem:group__spi__execution__status"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__execution__status.html">Status Error Codes</a></td></tr>
<tr class="memdesc:group__spi__execution__status"><td class="mdescLeft">&#160;</td><td class="mdescRight">Negative values indicate errors (SPI has specific codes in addition to common <a class="el" href="group__execution__status.html">Status Error Codes</a>). <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group___s_p_i__events"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___s_p_i__events.html">SPI Events</a></td></tr>
<tr class="memdesc:group___s_p_i__events"><td class="mdescLeft">&#160;</td><td class="mdescRight">The SPI driver generates call back events that are notified via the function <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">ARM_SPI_SignalEvent</a>. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group___s_p_i__control"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___s_p_i__control.html">SPI Control Codes</a></td></tr>
<tr class="memdesc:group___s_p_i__control"><td class="mdescLeft">&#160;</td><td class="mdescRight">Many parameters of the SPI driver are configured using the <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> function. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:struct_a_r_m___d_r_i_v_e_r___s_p_i"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i">ARM_DRIVER_SPI</a></td></tr>
<tr class="memdesc:struct_a_r_m___d_r_i_v_e_r___s_p_i"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access structure of the SPI Driver.  <a href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___d_r_i_v_e_r___s_p_i"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_SPI_CAPABILITIES</a></td></tr>
<tr class="memdesc:struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s"><td class="mdescLeft">&#160;</td><td class="mdescRight">SPI Driver Capabilities.  <a href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___s_p_i___s_t_a_t_u_s"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s">ARM_SPI_STATUS</a></td></tr>
<tr class="memdesc:struct_a_r_m___s_p_i___s_t_a_t_u_s"><td class="mdescLeft">&#160;</td><td class="mdescRight">SPI Status.  <a href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___s_p_i___s_t_a_t_u_s"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gafde9205364241ee81290adc0481c6640"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#gafde9205364241ee81290adc0481c6640">ARM_SPI_SignalEvent_t</a> )(uint32_t event)</td></tr>
<tr class="memdesc:gafde9205364241ee81290adc0481c6640"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">ARM_SPI_SignalEvent</a> : Signal SPI Event.  <a href="#gafde9205364241ee81290adc0481c6640">More...</a><br/></td></tr>
<tr class="separator:gafde9205364241ee81290adc0481c6640"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad5db9209ef1d64a7915a7278d6a402c8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#gad5db9209ef1d64a7915a7278d6a402c8">ARM_SPI_GetVersion</a> (void)</td></tr>
<tr class="memdesc:gad5db9209ef1d64a7915a7278d6a402c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver version.  <a href="#gad5db9209ef1d64a7915a7278d6a402c8">More...</a><br/></td></tr>
<tr class="separator:gad5db9209ef1d64a7915a7278d6a402c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4823a11ab5efcd47c79b13801513ddc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_SPI_CAPABILITIES</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#gaf4823a11ab5efcd47c79b13801513ddc">ARM_SPI_GetCapabilities</a> (void)</td></tr>
<tr class="memdesc:gaf4823a11ab5efcd47c79b13801513ddc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver capabilities.  <a href="#gaf4823a11ab5efcd47c79b13801513ddc">More...</a><br/></td></tr>
<tr class="separator:gaf4823a11ab5efcd47c79b13801513ddc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a3c11ed523a4355cd91069527945906"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#ga1a3c11ed523a4355cd91069527945906">ARM_SPI_Initialize</a> (<a class="el" href="group__spi__interface__gr.html#gafde9205364241ee81290adc0481c6640">ARM_SPI_SignalEvent_t</a> cb_event)</td></tr>
<tr class="memdesc:ga1a3c11ed523a4355cd91069527945906"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize SPI Interface.  <a href="#ga1a3c11ed523a4355cd91069527945906">More...</a><br/></td></tr>
<tr class="separator:ga1a3c11ed523a4355cd91069527945906"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0c480ee3eabb82fc746e89741ed2e03e"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#ga0c480ee3eabb82fc746e89741ed2e03e">ARM_SPI_Uninitialize</a> (void)</td></tr>
<tr class="memdesc:ga0c480ee3eabb82fc746e89741ed2e03e"><td class="mdescLeft">&#160;</td><td class="mdescRight">De-initialize SPI Interface.  <a href="#ga0c480ee3eabb82fc746e89741ed2e03e">More...</a><br/></td></tr>
<tr class="separator:ga0c480ee3eabb82fc746e89741ed2e03e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a1e7e80ea32ae381b75213c32aa8067"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#ga1a1e7e80ea32ae381b75213c32aa8067">ARM_SPI_PowerControl</a> (<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:ga1a1e7e80ea32ae381b75213c32aa8067"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control SPI Interface Power.  <a href="#ga1a1e7e80ea32ae381b75213c32aa8067">More...</a><br/></td></tr>
<tr class="separator:ga1a1e7e80ea32ae381b75213c32aa8067"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2a303d1071e926280d50682f4808479"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#gab2a303d1071e926280d50682f4808479">ARM_SPI_Send</a> (const void *data, uint32_t num)</td></tr>
<tr class="memdesc:gab2a303d1071e926280d50682f4808479"><td class="mdescLeft">&#160;</td><td class="mdescRight">Start sending data to SPI transmitter.  <a href="#gab2a303d1071e926280d50682f4808479">More...</a><br/></td></tr>
<tr class="separator:gab2a303d1071e926280d50682f4808479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga726aff54e782ed9b47f7ba1280a3d8f6"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#ga726aff54e782ed9b47f7ba1280a3d8f6">ARM_SPI_Receive</a> (void *data, uint32_t num)</td></tr>
<tr class="memdesc:ga726aff54e782ed9b47f7ba1280a3d8f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Start receiving data from SPI receiver.  <a href="#ga726aff54e782ed9b47f7ba1280a3d8f6">More...</a><br/></td></tr>
<tr class="separator:ga726aff54e782ed9b47f7ba1280a3d8f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa24026b3822c10272e301f1505136ec2"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#gaa24026b3822c10272e301f1505136ec2">ARM_SPI_Transfer</a> (const void *data_out, void *data_in, uint32_t num)</td></tr>
<tr class="memdesc:gaa24026b3822c10272e301f1505136ec2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Start sending/receiving data to/from SPI transmitter/receiver.  <a href="#gaa24026b3822c10272e301f1505136ec2">More...</a><br/></td></tr>
<tr class="separator:gaa24026b3822c10272e301f1505136ec2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaaecaaf4ec1922f22e7f9de63af5ccdb"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#gaaaecaaf4ec1922f22e7f9de63af5ccdb">ARM_SPI_GetDataCount</a> (void)</td></tr>
<tr class="memdesc:gaaaecaaf4ec1922f22e7f9de63af5ccdb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get transferred data count.  <a href="#gaaaecaaf4ec1922f22e7f9de63af5ccdb">More...</a><br/></td></tr>
<tr class="separator:gaaaecaaf4ec1922f22e7f9de63af5ccdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad18d229992598d6677bec250015e5d1a"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> (uint32_t control, uint32_t arg)</td></tr>
<tr class="memdesc:gad18d229992598d6677bec250015e5d1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control SPI Interface.  <a href="#gad18d229992598d6677bec250015e5d1a">More...</a><br/></td></tr>
<tr class="separator:gad18d229992598d6677bec250015e5d1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga60d33d8788a76c388cc36e066240b817"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s">ARM_SPI_STATUS</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">ARM_SPI_GetStatus</a> (void)</td></tr>
<tr class="memdesc:ga60d33d8788a76c388cc36e066240b817"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get SPI status.  <a href="#ga60d33d8788a76c388cc36e066240b817">More...</a><br/></td></tr>
<tr class="separator:ga60d33d8788a76c388cc36e066240b817"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga505b2d787348d51351d38fee98ccba7e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">ARM_SPI_SignalEvent</a> (uint32_t event)</td></tr>
<tr class="memdesc:ga505b2d787348d51351d38fee98ccba7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signal SPI Events.  <a href="#ga505b2d787348d51351d38fee98ccba7e">More...</a><br/></td></tr>
<tr class="separator:ga505b2d787348d51351d38fee98ccba7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Driver API for SPI Bus Peripheral (Driver_SPI.h) </p>
<p>The <b>Serial Peripheral Interface Bus</b> (SPI) implements a synchronous serial bus for data exchange. In microcontroller (MCU) applications, the interface is often used to connect peripheral components at board (PCB) level. SPI devices can operate as Master (SCLK and SS are outputs) or Slave (SCLK and SS are inputs). Wikipedia offers more information about the <a href="http://en.wikipedia.org/wiki/Serial_Peripheral_Interface_Bus" target="_blank"><b>Serial Peripheral Interface Bus</b></a>.</p>
<p><b>SPI Structure</b></p>
<p>The SPI Driver API defines a <b>SPI</b> interface for middleware components. The SPI Driver supports multiple slaves, but if only one slave is connected, then the Slave Select signal can be omitted.</p>
<div class="image">
<img src="SPI_Master1Slaves.png" alt="SPI_Master1Slaves.png"/>
<div class="caption">
SPI Master connected to a single slave</div></div>
 <p>&#160;</p>
<div class="image">
<img src="SPI_Master3Slaves.png" alt="SPI_Master3Slaves.png"/>
<div class="caption">
SPI Master connected to 3 slaves</div></div>
<p> The SPI Driver functions control the following SPI signal lines.</p>
<table class="doxtable">
<tr>
<th>Signal </th><th>Name </th><th>Description</th></tr>
<tr>
<td>SS </td><td>Slave Select (active low) </td><td>Selects the slave. This signal can be part of the SPI peripheral or implemented using a GPIO pin. </td></tr>
<tr>
<td>MOSI </td><td>Master&#160;Out,&#160;Slave&#160;In </td><td>MOSI output of the Master connects to MOSI input of the Slave. </td></tr>
<tr>
<td>SCLK </td><td>Serial Clock </td><td>Serial clock output from Master. Controls the transfer speed and when data are sent and read. </td></tr>
<tr>
<td>MISO </td><td>Master&#160;In,&#160;Slave&#160;Out </td><td>MISO input of the Master connects to MISO output of the Slave. </td></tr>
</table>
<p><b>SPI API</b></p>
<p>The following header files define the Application Programming Interface (API) for the SPI interface:</p>
<ul>
<li><b>Driver_SPI.h</b> : Driver API for SPI Bus Peripheral</li>
</ul>
<p>The driver implementation is a typical part of the Device Family Pack (DFP) that supports the peripherals of the microcontroller family.</p>
<p><b>Driver Functions</b></p>
<p>The driver functions are published in the access struct as explained in <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a></p>
<ul>
<li><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i">ARM_DRIVER_SPI</a> : access struct for SPI driver functions</li>
</ul>
<p><b>Example Code</b></p>
<p>The following example code shows the usage of the SPI interface.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &quot;<a class="code" href="_driver___s_p_i_8h.html">Driver_SPI.h</a>&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;cmsis_os.h&quot;</span>                   <span class="comment">// ARM::CMSIS:RTOS:Keil RTX</span></div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> mySPI_Thread(<span class="keywordtype">void</span> <span class="keyword">const</span> *argument);</div>
<div class="line">osThreadId tid_mySPI_Thread;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* SPI Driver */</span></div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i" title="Access structure of the SPI Driver.">ARM_DRIVER_SPI</a> Driver_SPI0;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> mySPI_callback(uint32_t event)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">switch</span> (event)</div>
<div class="line">    {</div>
<div class="line">    <span class="keywordflow">case</span> <a class="code" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8" title="Data Transfer completed.">ARM_SPI_EVENT_TRANSFER_COMPLETE</a>:</div>
<div class="line">                          <span class="comment">/* Success: Wakeup Thread */</span></div>
<div class="line">        osSignalSet(tid_mySPI_Thread, 0x01);</div>
<div class="line">        <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> <a class="code" href="group___s_p_i__events.html#ga8e63d99c80ea56de596a8d0a51fd8244" title="Data lost: Receive overflow / Transmit underflow.">ARM_SPI_EVENT_DATA_LOST</a>:</div>
<div class="line">        <span class="comment">/*  Occurs in slave mode when data is requested/sent by master</span></div>
<div class="line"><span class="comment">            but send/receive/transfer operation has not been started</span></div>
<div class="line"><span class="comment">            and indicates that data is lost. */</span></div>
<div class="line">                    __breakpoint(0);  <span class="comment">/* Error: Call debugger or replace with custom error handling */</span></div>
<div class="line">        <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> <a class="code" href="group___s_p_i__events.html#ga7eaa229003689aa18598273490b3e630" title="Master Mode Fault (SS deactivated when Master)">ARM_SPI_EVENT_MODE_FAULT</a>:</div>
<div class="line">        <span class="comment">/*  Occurs in master mode when Slave Select is deactivated and</span></div>
<div class="line"><span class="comment">            indicates Master Mode Fault. */</span></div>
<div class="line">                    __breakpoint(0);  <span class="comment">/* Error: Call debugger or replace with custom error handling */</span></div>
<div class="line">        <span class="keywordflow">break</span>;</div>
<div class="line">    }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Test data buffers */</span></div>
<div class="line"><span class="keyword">const</span> uint8_t testdata_out[8] = { 0, 1, 2, 3, 4, 5, 6, 7 }; </div>
<div class="line">uint8_t       testdata_in [8];</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> mySPI_Thread(<span class="keywordtype">void</span> <span class="keyword">const</span>* arg)</div>
<div class="line">{</div>
<div class="line">    <a class="code" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i" title="Access structure of the SPI Driver.">ARM_DRIVER_SPI</a>* SPIdrv = &amp;Driver_SPI0;</div>
<div class="line">          osEvent evt;</div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#ifdef DEBUG</span></div>
<div class="line"><span class="preprocessor"></span>    <a class="code" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n" title="Driver Version.">ARM_DRIVER_VERSION</a>   version;</div>
<div class="line">    <a class="code" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s" title="SPI Driver Capabilities.">ARM_SPI_CAPABILITIES</a> drv_capabilities;</div>
<div class="line"> </div>
<div class="line">    version = SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#a8834b281da48583845c044a81566c1b3" title="Pointer to ARM_SPI_GetVersion : Get driver version.">GetVersion</a>();</div>
<div class="line">    <span class="keywordflow">if</span> (version.<a class="code" href="group__common__drv__gr.html#ad180da20fbde1d3dafc074af87c19540" title="API version.">api</a> &lt; 0x200) <span class="comment">/* requires at minimum API version 2.00 or higher */</span></div>
<div class="line">    {                        <span class="comment">/* error handling                                 */</span></div>
<div class="line">        <span class="keywordflow">return</span>;</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">    drv_capabilities = SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#a065b5fc24d0204692f0f95a44351ac1e" title="Pointer to ARM_SPI_GetCapabilities : Get driver capabilities.">GetCapabilities</a>();</div>
<div class="line">    <span class="keywordflow">if</span> (drv_capabilities.<a class="code" href="group__spi__interface__gr.html#a309619714f0c4febaa497ebdb9b7e3ca" title="Signal Mode Fault event: ARM_SPI_EVENT_MODE_FAULT.">event_mode_fault</a> == 0)</div>
<div class="line">    {                        <span class="comment">/* error handling */</span></div>
<div class="line">        <span class="keywordflow">return</span>;</div>
<div class="line">    }</div>
<div class="line"><span class="preprocessor">#endif</span></div>
<div class="line"><span class="preprocessor"></span> </div>
<div class="line">    <span class="comment">/* Initialize the SPI driver */</span></div>
<div class="line">    SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#afac50d0b28860f7b569293e6b713f8a4" title="Pointer to ARM_SPI_Initialize : Initialize SPI Interface.">Initialize</a>(mySPI_callback);</div>
<div class="line">    <span class="comment">/* Power up the SPI peripheral */</span></div>
<div class="line">    SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef" title="Pointer to ARM_SPI_PowerControl : Control SPI Interface Power.">PowerControl</a>(<a class="code" href="_driver___common_8h.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5abed52b77a9ce4775570e44a842b1295e" title="Power on: full operation at maximum performance.">ARM_POWER_FULL</a>);</div>
<div class="line">    <span class="comment">/* Configure the SPI to Master, 8-bit mode @10000 kBits/sec */</span></div>
<div class="line">    SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_SPI_Control : Control SPI Interface.">Control</a>(<a class="code" href="group__spi__mode__ctrls.html#ga3143ef07c1607b9bc57e29df35cf2fa8" title="SPI Master (Output on MOSI, Input on MISO); arg = Bus Speed in bps.">ARM_SPI_MODE_MASTER</a> | <a class="code" href="group__spi__frame__format__ctrls.html#ga7fab572b2fec303e979e47eb2d13ca74" title="Clock Polarity 1, Clock Phase 1.">ARM_SPI_CPOL1_CPHA1</a> | <a class="code" href="group__spi__bit__order__ctrls.html#ga98228a708cbab6e214c7ac696f77dab6" title="SPI Bit order from MSB to LSB (default)">ARM_SPI_MSB_LSB</a> | <a class="code" href="group__spi__slave__select__mode__ctrls.html#gab5e319aa3f9d4d8c9ed92f0fe865f624" title="SPI Slave Select when Master: Software controlled.">ARM_SPI_SS_MASTER_SW</a> | <a class="code" href="group__spi__data__bits__ctrls.html#gaf6c099a1d67256a32010120c66c55250" title="Number of Data bits.">ARM_SPI_DATA_BITS</a>(8), 10000000);</div>
<div class="line"> </div>
<div class="line">    <span class="comment">/* Set CS line   */</span></div>
<div class="line">    SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_SPI_Control : Control SPI Interface.">Control</a>(<a class="code" href="group__spi__misc__ctrls.html#ga5776272b82decff92da003568540c92f" title="Control Slave Select; arg: 0=inactive, 1=active.">ARM_SPI_CONTROL_SS</a>, <a class="code" href="_driver___s_p_i_8h.html#a335b448e07422e9c25616a693ec581cc" title="SPI Slave Select Signal Inactive.">ARM_SPI_SS_INACTIVE</a>);</div>
<div class="line">    <span class="comment">/* Clear CS line */</span></div>
<div class="line">    SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_SPI_Control : Control SPI Interface.">Control</a>(<a class="code" href="group__spi__misc__ctrls.html#ga5776272b82decff92da003568540c92f" title="Control Slave Select; arg: 0=inactive, 1=active.">ARM_SPI_CONTROL_SS</a>, <a class="code" href="_driver___s_p_i_8h.html#a3f465cdbd1238ddd74f78e14457076c4" title="SPI Slave Select Signal Active.">ARM_SPI_SS_ACTIVE</a>);</div>
<div class="line"> </div>
<div class="line">    <span class="comment">/* thread loop */</span></div>
<div class="line">    <span class="keywordflow">while</span> (1)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">/* Transmit some data */</span></div>
<div class="line">        SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#a44eedddf4428cf4b98883b6c27d31922" title="Pointer to ARM_SPI_Send : Start sending data to SPI Interface.">Send</a>(testdata_out, <span class="keyword">sizeof</span>(testdata_in));</div>
<div class="line">        <span class="comment">/* Wait for completion */</span></div>
<div class="line">        evt = osSignalWait(0x01, 100);</div>
<div class="line">        <span class="keywordflow">if</span> (evt.status == osEventTimeout)  {</div>
<div class="line">           __breakpoint(0);  <span class="comment">/* Timeout error: Call debugger */</span></div>
<div class="line">        }</div>
<div class="line">        <span class="comment">/* Receive 8 bytes of reply */</span></div>
<div class="line">        SPIdrv-&gt;<a class="code" href="group__spi__interface__gr.html#adb9224a35fe16c92eb0dd103638e4cf3" title="Pointer to ARM_SPI_Receive : Start receiving data from SPI Interface.">Receive</a>(testdata_in, 8);</div>
<div class="line">        osSignalWait(0x01, 100);</div>
<div class="line">                                <span class="keywordflow">if</span> (evt.status == osEventTimeout)  {</div>
<div class="line">           __breakpoint(0);  <span class="comment">/* Timeout error: Call debugger */</span></div>
<div class="line">        }</div>
<div class="line">         </div>
<div class="line">    }</div>
<div class="line">}</div>
</div><!-- fragment --> <hr/><h2 class="groupheader">Data Structure Documentation</h2>
<a name="struct_a_r_m___d_r_i_v_e_r___s_p_i" id="struct_a_r_m___d_r_i_v_e_r___s_p_i"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_DRIVER_SPI</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Access structure of the SPI Driver. </p>
<p>The functions of the SPI driver are accessed by function pointers exposed by this structure. Refer to <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a> for overview information.</p>
<p>Each instance of a SPI interface provides such an access structure. The instance is identified by a postfix number in the symbol name of the access structure, for example:</p>
<ul>
<li><b>Driver_SPI0</b> is the name of the access struct of the first instance (no. 0).</li>
<li><b>Driver_SPI1</b> is the name of the access struct of the second instance (no. 1).</li>
</ul>
<p>A middleware configuration setting allows connecting the middleware to a specific driver instance <b>Driver_SPI<em>n</em></b>. The default is <span class="XML-Token">0</span>, which connects a middleware to the first instance of a driver. </p>
</div><table class="memberdecls">
<tr><td colspan="2"><h3>Data Fields</h3></td></tr>
<tr class="memitem:a8834b281da48583845c044a81566c1b3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#a8834b281da48583845c044a81566c1b3">GetVersion</a> )(void)</td></tr>
<tr class="memdesc:a8834b281da48583845c044a81566c1b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#gad5db9209ef1d64a7915a7278d6a402c8">ARM_SPI_GetVersion</a> : Get driver version.  <a href="#a8834b281da48583845c044a81566c1b3">More...</a><br/></td></tr>
<tr class="separator:a8834b281da48583845c044a81566c1b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a065b5fc24d0204692f0f95a44351ac1e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_SPI_CAPABILITIES</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#a065b5fc24d0204692f0f95a44351ac1e">GetCapabilities</a> )(void)</td></tr>
<tr class="memdesc:a065b5fc24d0204692f0f95a44351ac1e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#gaf4823a11ab5efcd47c79b13801513ddc">ARM_SPI_GetCapabilities</a> : Get driver capabilities.  <a href="#a065b5fc24d0204692f0f95a44351ac1e">More...</a><br/></td></tr>
<tr class="separator:a065b5fc24d0204692f0f95a44351ac1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afac50d0b28860f7b569293e6b713f8a4"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#afac50d0b28860f7b569293e6b713f8a4">Initialize</a> )(<a class="el" href="group__spi__interface__gr.html#gafde9205364241ee81290adc0481c6640">ARM_SPI_SignalEvent_t</a> cb_event)</td></tr>
<tr class="memdesc:afac50d0b28860f7b569293e6b713f8a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#ga1a3c11ed523a4355cd91069527945906">ARM_SPI_Initialize</a> : Initialize SPI Interface.  <a href="#afac50d0b28860f7b569293e6b713f8a4">More...</a><br/></td></tr>
<tr class="separator:afac50d0b28860f7b569293e6b713f8a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcf20681a1402869ecb5c6447fada17b"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#adcf20681a1402869ecb5c6447fada17b">Uninitialize</a> )(void)</td></tr>
<tr class="memdesc:adcf20681a1402869ecb5c6447fada17b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#ga0c480ee3eabb82fc746e89741ed2e03e">ARM_SPI_Uninitialize</a> : De-initialize SPI Interface.  <a href="#adcf20681a1402869ecb5c6447fada17b">More...</a><br/></td></tr>
<tr class="separator:adcf20681a1402869ecb5c6447fada17b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba8f1c8019af95ffe19c32403e3240ef"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">PowerControl</a> )(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:aba8f1c8019af95ffe19c32403e3240ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#ga1a1e7e80ea32ae381b75213c32aa8067">ARM_SPI_PowerControl</a> : Control SPI Interface Power.  <a href="#aba8f1c8019af95ffe19c32403e3240ef">More...</a><br/></td></tr>
<tr class="separator:aba8f1c8019af95ffe19c32403e3240ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44eedddf4428cf4b98883b6c27d31922"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#a44eedddf4428cf4b98883b6c27d31922">Send</a> )(const void *data, uint32_t num)</td></tr>
<tr class="memdesc:a44eedddf4428cf4b98883b6c27d31922"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#gab2a303d1071e926280d50682f4808479">ARM_SPI_Send</a> : Start sending data to SPI Interface.  <a href="#a44eedddf4428cf4b98883b6c27d31922">More...</a><br/></td></tr>
<tr class="separator:a44eedddf4428cf4b98883b6c27d31922"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb9224a35fe16c92eb0dd103638e4cf3"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#adb9224a35fe16c92eb0dd103638e4cf3">Receive</a> )(void *data, uint32_t num)</td></tr>
<tr class="memdesc:adb9224a35fe16c92eb0dd103638e4cf3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#ga726aff54e782ed9b47f7ba1280a3d8f6">ARM_SPI_Receive</a> : Start receiving data from SPI Interface.  <a href="#adb9224a35fe16c92eb0dd103638e4cf3">More...</a><br/></td></tr>
<tr class="separator:adb9224a35fe16c92eb0dd103638e4cf3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad88b63ed74c03ba06b0599ab06ad4cf7"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#ad88b63ed74c03ba06b0599ab06ad4cf7">Transfer</a> )(const void *data_out, void *data_in, uint32_t num)</td></tr>
<tr class="memdesc:ad88b63ed74c03ba06b0599ab06ad4cf7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#gaa24026b3822c10272e301f1505136ec2">ARM_SPI_Transfer</a> : Start sending/receiving data to/from SPI.  <a href="#ad88b63ed74c03ba06b0599ab06ad4cf7">More...</a><br/></td></tr>
<tr class="separator:ad88b63ed74c03ba06b0599ab06ad4cf7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1d892ab3932f65cd7cdf2d0a91ae5da"><td class="memItemLeft" align="right" valign="top">uint32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#ad1d892ab3932f65cd7cdf2d0a91ae5da">GetDataCount</a> )(void)</td></tr>
<tr class="memdesc:ad1d892ab3932f65cd7cdf2d0a91ae5da"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#gaaaecaaf4ec1922f22e7f9de63af5ccdb">ARM_SPI_GetDataCount</a> : Get transferred data count.  <a href="#ad1d892ab3932f65cd7cdf2d0a91ae5da">More...</a><br/></td></tr>
<tr class="separator:ad1d892ab3932f65cd7cdf2d0a91ae5da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e0f47a92f626a971c5197fca6545505"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#a6e0f47a92f626a971c5197fca6545505">Control</a> )(uint32_t control, uint32_t arg)</td></tr>
<tr class="memdesc:a6e0f47a92f626a971c5197fca6545505"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> : Control SPI Interface.  <a href="#a6e0f47a92f626a971c5197fca6545505">More...</a><br/></td></tr>
<tr class="separator:a6e0f47a92f626a971c5197fca6545505"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7305e7248420cdb4b02ceba87672178d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s">ARM_SPI_STATUS</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__interface__gr.html#a7305e7248420cdb4b02ceba87672178d">GetStatus</a> )(void)</td></tr>
<tr class="memdesc:a7305e7248420cdb4b02ceba87672178d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">ARM_SPI_GetStatus</a> : Get SPI status.  <a href="#a7305e7248420cdb4b02ceba87672178d">More...</a><br/></td></tr>
<tr class="separator:a7305e7248420cdb4b02ceba87672178d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h4 class="groupheader">Field Documentation</h4>
<a class="anchor" id="a6e0f47a92f626a971c5197fca6545505"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Control)(uint32_t control, uint32_t arg)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> : Control SPI Interface. </p>

</div>
</div>
<a class="anchor" id="a065b5fc24d0204692f0f95a44351ac1e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_SPI_CAPABILITIES</a>(* GetCapabilities)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#gaf4823a11ab5efcd47c79b13801513ddc">ARM_SPI_GetCapabilities</a> : Get driver capabilities. </p>

</div>
</div>
<a class="anchor" id="ad1d892ab3932f65cd7cdf2d0a91ae5da"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t(* GetDataCount)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#gaaaecaaf4ec1922f22e7f9de63af5ccdb">ARM_SPI_GetDataCount</a> : Get transferred data count. </p>

</div>
</div>
<a class="anchor" id="a7305e7248420cdb4b02ceba87672178d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s">ARM_SPI_STATUS</a>(* GetStatus)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">ARM_SPI_GetStatus</a> : Get SPI status. </p>

</div>
</div>
<a class="anchor" id="a8834b281da48583845c044a81566c1b3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(* GetVersion)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#gad5db9209ef1d64a7915a7278d6a402c8">ARM_SPI_GetVersion</a> : Get driver version. </p>

</div>
</div>
<a class="anchor" id="afac50d0b28860f7b569293e6b713f8a4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Initialize)(<a class="el" href="group__spi__interface__gr.html#gafde9205364241ee81290adc0481c6640">ARM_SPI_SignalEvent_t</a> cb_event)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#ga1a3c11ed523a4355cd91069527945906">ARM_SPI_Initialize</a> : Initialize SPI Interface. </p>

</div>
</div>
<a class="anchor" id="aba8f1c8019af95ffe19c32403e3240ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* PowerControl)(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#ga1a1e7e80ea32ae381b75213c32aa8067">ARM_SPI_PowerControl</a> : Control SPI Interface Power. </p>

</div>
</div>
<a class="anchor" id="adb9224a35fe16c92eb0dd103638e4cf3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Receive)(void *data, uint32_t num)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#ga726aff54e782ed9b47f7ba1280a3d8f6">ARM_SPI_Receive</a> : Start receiving data from SPI Interface. </p>

</div>
</div>
<a class="anchor" id="a44eedddf4428cf4b98883b6c27d31922"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Send)(const void *data, uint32_t num)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#gab2a303d1071e926280d50682f4808479">ARM_SPI_Send</a> : Start sending data to SPI Interface. </p>

</div>
</div>
<a class="anchor" id="ad88b63ed74c03ba06b0599ab06ad4cf7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Transfer)(const void *data_out, void *data_in, uint32_t num)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#gaa24026b3822c10272e301f1505136ec2">ARM_SPI_Transfer</a> : Start sending/receiving data to/from SPI. </p>

</div>
</div>
<a class="anchor" id="adcf20681a1402869ecb5c6447fada17b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Uninitialize)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#ga0c480ee3eabb82fc746e89741ed2e03e">ARM_SPI_Uninitialize</a> : De-initialize SPI Interface. </p>

</div>
</div>

</div>
</div>
<a name="struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s" id="struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_SPI_CAPABILITIES</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>SPI Driver Capabilities. </p>
<p>A SPI driver can be implemented with different capabilities. The bitfield members of this struct encode the capabilities implemented by this driver.</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__spi__interface__gr.html#gaf4823a11ab5efcd47c79b13801513ddc">ARM_SPI_GetCapabilities</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a309619714f0c4febaa497ebdb9b7e3ca"></a>uint32_t</td>
<td class="fieldname">
event_mode_fault: 1</td>
<td class="fielddoc">
Signal Mode Fault event: <a class="el" href="group___s_p_i__events.html#ga7eaa229003689aa18598273490b3e630">ARM_SPI_EVENT_MODE_FAULT</a>. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a9b4e858eb1d414128994742bf121f94c"></a>uint32_t</td>
<td class="fieldname">
microwire: 1</td>
<td class="fielddoc">
supports Microwire Interface </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="af244e2c2facf6414e3886495ee6b40bc"></a>uint32_t</td>
<td class="fieldname">
simplex: 1</td>
<td class="fielddoc">
supports Simplex Mode (Master and Slave) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a8053c540e5d531b692224bdc2463f36a"></a>uint32_t</td>
<td class="fieldname">
ti_ssi: 1</td>
<td class="fielddoc">
supports TI Synchronous Serial Interface </td></tr>
</table>

</div>
</div>
<a name="struct_a_r_m___s_p_i___s_t_a_t_u_s" id="struct_a_r_m___s_p_i___s_t_a_t_u_s"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_SPI_STATUS</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>SPI Status. </p>
<p>Structure with information about the status of the SPI. The bitfields encode busy flag and error flags.</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">ARM_SPI_GetStatus</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a50c88f3c1d787773e2ac1b59533f034a"></a>uint32_t</td>
<td class="fieldname">
busy: 1</td>
<td class="fielddoc">
Transmitter/Receiver busy flag. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a9675630df67587ecd171c7ef12b9d22a"></a>uint32_t</td>
<td class="fieldname">
data_lost: 1</td>
<td class="fielddoc">
Data lost: Receive overflow / Transmit underflow (cleared on start of transfer operation) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="aeaf54ec655b7a64b9e88578c5f39d4e3"></a>uint32_t</td>
<td class="fieldname">
mode_fault: 1</td>
<td class="fielddoc">
Mode fault detected; optional (cleared on start of transfer operation) </td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="gafde9205364241ee81290adc0481c6640"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ARM_SPI_SignalEvent_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">ARM_SPI_SignalEvent</a> : Signal SPI Event. </p>
<p>Provides the typedef for the callback function <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">ARM_SPI_SignalEvent</a>.</p>
<p><b>Parameter for:</b></p>
<ul>
<li><a class="el" href="group__spi__interface__gr.html#ga1a3c11ed523a4355cd91069527945906">ARM_SPI_Initialize</a> </li>
</ul>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gad18d229992598d6677bec250015e5d1a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_SPI_Control </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>control</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>arg</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control SPI Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">control</td><td>Operation </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">arg</td><td>Argument of operation (optional) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>common <a class="el" href="group__execution__status.html">Status Error Codes</a> and driver specific <a class="el" href="group__spi__execution__status.html">Status Error Codes</a></dd></dl>
<p>Controls the SPI interface settings and executes various operations.</p>
<p>The parameter <em>control</em> is a bit mask that specifies various operations (see tables below). The control bits of the various groups can be combined to a control code as shown in the following example. Depending on the control bits, the parameter <em>arg</em> provides additional information, for example the baudrate.</p>
<p><b>Example</b> </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i" title="Access structure of the SPI Driver.">ARM_DRIVER_SPI</a> Driver_SPI0;</div>
<div class="line"></div>
<div class="line"><span class="comment">// configure: SPI master | clock polarity=1, clock phase=1 | bits per frame=16 | bus speed : 1000000 </span></div>
<div class="line">status = Driver_SPI0.<a class="code" href="group__spi__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_SPI_Control : Control SPI Interface.">Control</a>(<a class="code" href="group__spi__mode__ctrls.html#ga3143ef07c1607b9bc57e29df35cf2fa8" title="SPI Master (Output on MOSI, Input on MISO); arg = Bus Speed in bps.">ARM_SPI_MODE_MASTER</a>   | </div>
<div class="line">                             <a class="code" href="group__spi__frame__format__ctrls.html#ga7fab572b2fec303e979e47eb2d13ca74" title="Clock Polarity 1, Clock Phase 1.">ARM_SPI_CPOL1_CPHA1</a>   | </div>
<div class="line">                             <a class="code" href="group__spi__data__bits__ctrls.html#gaf6c099a1d67256a32010120c66c55250" title="Number of Data bits.">ARM_SPI_DATA_BITS</a>(16), 1000000);</div>
</div><!-- fragment --><p><b>ARM_SPI_MODE_xxx</b> specifies the SPI mode.</p>
<table class="doxtable">
<tr>
<th align="left">Mode Control Bits </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__spi__mode__ctrls.html#ga974e3d7c178b76b0540d7644b977bff3">ARM_SPI_MODE_INACTIVE</a> </td><td align="left">Set SPI to inactive. </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__mode__ctrls.html#ga3143ef07c1607b9bc57e29df35cf2fa8">ARM_SPI_MODE_MASTER</a> </td><td align="left">Set the SPI Master (Output on MOSI, and the Input on MISO); <em>arg</em> = Bus Speed in <span class="XML-Token">bps</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__mode__ctrls.html#ga382b394c5e68f7d1206b837843732a3e">ARM_SPI_MODE_SLAVE</a> </td><td align="left">Set the SPI Slave (Output on MISO, and the Input on MOSI) </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__mode__ctrls.html#gaf34d849c7cde1151a768887f154e19bd">ARM_SPI_MODE_MASTER_SIMPLEX</a> </td><td align="left">Set the SPI Master (Output and Input on MOSI); <em>arg</em> = Bus Speed in <span class="XML-Token">bps</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__mode__ctrls.html#ga9b113d8b336047e1c22f73ad44851fdf">ARM_SPI_MODE_SLAVE_SIMPLEX</a> </td><td align="left">Set the SPI Slave (Output and Input on MISO) </td></tr>
</table>
<p><b>ARM_SPI_CPOLx_CPHAy</b> specifies the clock polarity and clock phase. Additional parameters <b>ARM_SPI_TI_SSI</b> and <b>ARM_SPI_MICROWIRE</b> specify special formats.</p>
<table class="doxtable">
<tr>
<th align="left">Mode Parameters: Frame Format </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__spi__frame__format__ctrls.html#gab4ac9a609c078d1e8332cf95da34e50e">ARM_SPI_CPOL0_CPHA0</a> </td><td align="left">CPOL=<span class="XML-Token">0</span> and CPHA=<span class="XML-Token">0</span>: Clock Polarity 0, Clock Phase 0 (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__frame__format__ctrls.html#ga5498eb08c2ba8de2e1c2801428e79d71">ARM_SPI_CPOL0_CPHA1</a> </td><td align="left">CPOL=<span class="XML-Token">0</span> and CPHA=<span class="XML-Token">1</span>: Clock Polarity 0, Clock Phase 1 </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__frame__format__ctrls.html#ga67193d9b5af1ec312a66d007c33b597f">ARM_SPI_CPOL1_CPHA0</a> </td><td align="left">CPOL=<span class="XML-Token">1</span> and CPHA=<span class="XML-Token">0</span>: Clock Polarity 1, Clock Phase 0 </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__frame__format__ctrls.html#ga7fab572b2fec303e979e47eb2d13ca74">ARM_SPI_CPOL1_CPHA1</a> </td><td align="left">CPOL=<span class="XML-Token">1</span> and CPHA=<span class="XML-Token">1</span>: Clock Polarity 1, Clock Phase 1 </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__frame__format__ctrls.html#ga225185710ba38848a489013ba4475915">ARM_SPI_TI_SSI</a> </td><td align="left">Specifies that the frame format corresponds to the Texas Instruments Frame Format </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__frame__format__ctrls.html#ga44f481d32b9a9ea93673f05af82ccf86">ARM_SPI_MICROWIRE</a> </td><td align="left">Specifies that the frame format corresponds to the National Microwire Frame Format </td></tr>
</table>
<p><b><a class="el" href="group__spi__data__bits__ctrls.html#gaf6c099a1d67256a32010120c66c55250" title="Number of Data bits.">ARM_SPI_DATA_BITS(x)</a></b> specifies the number of bits transferred per SPI frame.</p>
<table class="doxtable">
<tr>
<th align="left">Mode Parameters: Data Bits </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__spi__data__bits__ctrls.html#gaf6c099a1d67256a32010120c66c55250">ARM_SPI_DATA_BITS(n)</a> </td><td align="left">Set the number of bits per SPI frame; the possible range for <em>n</em> is 1..32. </td></tr>
</table>
<p><b>ARM_SPI_MSB_LSB</b> and <b>ARM_SPI_LSB_MSB</b> specify the bit order.</p>
<table class="doxtable">
<tr>
<th align="left">Mode Parameters: Bit order </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__spi__bit__order__ctrls.html#ga98228a708cbab6e214c7ac696f77dab6">ARM_SPI_MSB_LSB</a> </td><td align="left">Set the bit order from MSB to LSB (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__bit__order__ctrls.html#ga41c53c3b396a89ce78018467e561aaaf">ARM_SPI_LSB_MSB</a> </td><td align="left">Set the bit order from LSB to MSB </td></tr>
</table>
<p><b>ARM_SPI_SS_MASTER_xxx</b> and <b>ARM_SPI_SS_SLAVE_xxx</b> specify the Slave Select mode.</p>
<table class="doxtable">
<tr>
<th align="left">Mode Parameters: Slave Select Mode </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__spi__slave__select__mode__ctrls.html#gae19343adc7bd71408b51733171f99dc7">ARM_SPI_SS_MASTER_UNUSED</a> </td><td align="left">Set the Slave Select mode for the master to <b>Not used</b> (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__slave__select__mode__ctrls.html#gab5e319aa3f9d4d8c9ed92f0fe865f624">ARM_SPI_SS_MASTER_SW</a> </td><td align="left">Set the Slave Select mode for the master to <b>Software controlled</b> </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__slave__select__mode__ctrls.html#ga07762709a40dc90aca85553f500c8761">ARM_SPI_SS_MASTER_HW_OUTPUT</a> </td><td align="left">Set the Slave Select mode for the master to <b>Hardware controlled Output</b> </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__slave__select__mode__ctrls.html#ga8561bd0cc25ab2bb02b138c1c6a586cd">ARM_SPI_SS_MASTER_HW_INPUT</a> </td><td align="left">Set the Slave Select mode for the master to <b>Hardware monitored Input</b> </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__slave__select__mode__ctrls.html#ga2bd0d1f3ade2dc0cc48cc0593336ad70">ARM_SPI_SS_SLAVE_HW</a> </td><td align="left">Set the Slave Select mode for the slave to <b>Hardware monitored</b> (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__slave__select__mode__ctrls.html#gad371f6ba0d12a57bdcc3217c351abfb0">ARM_SPI_SS_SLAVE_SW</a> </td><td align="left">Set the Slave Select mode for the slave to <b>Software controlled</b> </td></tr>
</table>
<p><b> Miscellaneous Controls</b> execute various operations.</p>
<table class="doxtable">
<tr>
<th align="left">Miscellaneous&#160;Controls </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__spi__misc__ctrls.html#ga5ef3d114979f3fd6010d0df16c2bf5c1">ARM_SPI_SET_BUS_SPEED</a> </td><td align="left">Set the bus speed in <span class="XML-Token">bps</span>; the argument <em>arg</em> specifies the speed value </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__misc__ctrls.html#gafc00fe35bb4c89b076d014b43168b2b3">ARM_SPI_GET_BUS_SPEED</a> </td><td align="left">Get the bus speed in <span class="XML-Token">bps</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__misc__ctrls.html#gae9861221dee78d52bd1522b7846535ce">ARM_SPI_SET_DEFAULT_TX_VALUE</a> </td><td align="left">Set the default transmission value; the argument <em>arg</em> sets the value </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__misc__ctrls.html#ga5776272b82decff92da003568540c92f">ARM_SPI_CONTROL_SS</a> </td><td align="left">Control the Slave Select signal; the argument <em>arg:</em> <span class="XML-Token">0=inactive; 1=active</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__spi__misc__ctrls.html#ga44708b80e48984be099cd6eb11780dc3">ARM_SPI_ABORT_TRANSFER</a> </td><td align="left">Abort the current data transfer </td></tr>
</table>

</div>
</div>
<a class="anchor" id="gaf4823a11ab5efcd47c79b13801513ddc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_SPI_CAPABILITIES</a> ARM_SPI_GetCapabilities </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver capabilities. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_SPI_CAPABILITIES</a></dd></dl>
<p>Retrieves information about capabilities in this driver implementation. The bitfield members of the struct <a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_SPI_CAPABILITIES</a> encode various capabilities, for example supported modes.</p>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i" title="Access structure of the SPI Driver.">ARM_DRIVER_SPI</a> Driver_SPI0;</div>
<div class="line"><a class="code" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i" title="Access structure of the SPI Driver.">ARM_DRIVER_SPI</a> *drv_info;</div>
<div class="line">  </div>
<div class="line"><span class="keywordtype">void</span> read_capabilities (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s" title="SPI Driver Capabilities.">ARM_SPI_CAPABILITIES</a> drv_capabilities;</div>
<div class="line"> </div>
<div class="line">  drv_info = &amp;Driver_SPI0;  </div>
<div class="line">  drv_capabilities = drv_info-&gt;<a class="code" href="group__spi__interface__gr.html#a065b5fc24d0204692f0f95a44351ac1e" title="Pointer to ARM_SPI_GetCapabilities : Get driver capabilities.">GetCapabilities</a> ();</div>
<div class="line">  <span class="comment">// interrogate capabilities</span></div>
<div class="line"> </div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="gaaaecaaf4ec1922f22e7f9de63af5ccdb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t ARM_SPI_GetDataCount </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get transferred data count. </p>
<dl class="section return"><dt>Returns</dt><dd>number of data items transferred</dd></dl>
<p>Returns the number of currently transferred data items during <a class="el" href="group__spi__interface__gr.html#gab2a303d1071e926280d50682f4808479">ARM_SPI_Send</a>, <a class="el" href="group__spi__interface__gr.html#ga726aff54e782ed9b47f7ba1280a3d8f6">ARM_SPI_Receive</a> and <a class="el" href="group__spi__interface__gr.html#gaa24026b3822c10272e301f1505136ec2">ARM_SPI_Transfer</a> operation. </p>

</div>
</div>
<a class="anchor" id="ga60d33d8788a76c388cc36e066240b817"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s">ARM_SPI_STATUS</a> ARM_SPI_GetStatus </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get SPI status. </p>
<dl class="section return"><dt>Returns</dt><dd>SPI status <a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s">ARM_SPI_STATUS</a></dd></dl>
<p>Retrieves the current SPI interface status. </p>

</div>
</div>
<a class="anchor" id="gad5db9209ef1d64a7915a7278d6a402c8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a> ARM_SPI_GetVersion </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver version. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></dd></dl>
<p>Returns version information of the driver implementation in <a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></p>
<ul>
<li>API version is the version of the CMSIS-Driver specification used to implement this driver.</li>
<li>Driver version is source code version of the actual driver implementation.</li>
</ul>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i" title="Access structure of the SPI Driver.">ARM_DRIVER_SPI</a> Driver_SPI0;</div>
<div class="line"><a class="code" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i" title="Access structure of the SPI Driver.">ARM_DRIVER_SPI</a> *drv_info;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> setup_spi (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n" title="Driver Version.">ARM_DRIVER_VERSION</a>  version;</div>
<div class="line"> </div>
<div class="line">  drv_info = &amp;Driver_SPI0;  </div>
<div class="line">  version = drv_info-&gt;<a class="code" href="group__spi__interface__gr.html#a8834b281da48583845c044a81566c1b3" title="Pointer to ARM_SPI_GetVersion : Get driver version.">GetVersion</a> ();</div>
<div class="line">  <span class="keywordflow">if</span> (version.<a class="code" href="group__common__drv__gr.html#ad180da20fbde1d3dafc074af87c19540" title="API version.">api</a> &lt; 0x10A)   {      <span class="comment">// requires at minimum API version 1.10 or higher</span></div>
<div class="line">    <span class="comment">// error handling</span></div>
<div class="line">    <span class="keywordflow">return</span>;</div>
<div class="line">  }</div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="ga1a3c11ed523a4355cd91069527945906"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_SPI_Initialize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__spi__interface__gr.html#gafde9205364241ee81290adc0481c6640">ARM_SPI_SignalEvent_t</a>&#160;</td>
          <td class="paramname"><em>cb_event</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize SPI Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">cb_event</td><td>Pointer to <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">ARM_SPI_SignalEvent</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function initializes the SPI interface. It is called when the middleware component starts operation.</p>
<p>The function performs the following operations:</p>
<ul>
<li>Initializes the resources needed for the SPI interface.</li>
<li>Registers the <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">ARM_SPI_SignalEvent</a> callback function.</li>
</ul>
<p>The parameter <em>cb_event</em> is a pointer to the <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">ARM_SPI_SignalEvent</a> callback function; use a NULL pointer when no callback signals are required.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__spi__interface__gr.html">SPI Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="ga1a1e7e80ea32ae381b75213c32aa8067"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_SPI_PowerControl </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a>&#160;</td>
          <td class="paramname"><em>state</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control SPI Interface Power. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">state</td><td>Power state </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Allows you to control the power modes of the SPI interface. </p>

</div>
</div>
<a class="anchor" id="ga726aff54e782ed9b47f7ba1280a3d8f6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_SPI_Receive </td>
          <td>(</td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>num</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Start receiving data from SPI receiver. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">data</td><td>Pointer to buffer for data to receive from SPI receiver </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">num</td><td>Number of data items to receive </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>This functions is used to only receive data (transmits the default value as specified by <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> with <a class="el" href="group__spi__misc__ctrls.html#gae9861221dee78d52bd1522b7846535ce">ARM_SPI_SET_DEFAULT_TX_VALUE</a> as control parameter).</p>
<p>The function parameters specify the buffer for data and the number of items to receive. The item size is defined by the data type which depends on the configured number of data bits.</p>
<p>Data type is:</p>
<ul>
<li><em>uint8_t</em> when configured for 1..8 data bits</li>
<li><em>uint16_t</em> when configured for 9..16 data bits</li>
<li><em>uint32_t</em> when configured for 17..32 data bits</li>
</ul>
<p>Calling the function <b>ARM_SPI_Receive</b> only starts the receive operation. The function is non-blocking and returns as soon as the driver has started the operation (driver typically configures DMA or the interrupt system for continuous transfer). When in slave mode the operation is only registered and started when the master starts the transfer. During the operation it is not allowed to call this function or any other data transfer function again. Also the data buffer must stay allocated. When receive operation is completed (requested number of items received) the <a class="el" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8">ARM_SPI_EVENT_TRANSFER_COMPLETE</a> event is generated. Progress of receive operation can also be monitored by reading the number of items already received by calling <a class="el" href="group__spi__interface__gr.html#gaaaecaaf4ec1922f22e7f9de63af5ccdb">ARM_SPI_GetDataCount</a>.</p>
<p>Status of the receiver can also be monitored by calling the <a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">ARM_SPI_GetStatus</a> and checking the <em>busy</em> flag which indicates if reception is still in progress or pending.</p>
<p>When in master mode and configured to monitor slave select and the slave select gets deactivated during transfer then the SPI mode changes to inactive and the <a class="el" href="group___s_p_i__events.html#ga7eaa229003689aa18598273490b3e630">ARM_SPI_EVENT_MODE_FAULT</a> event is generated (instead of <a class="el" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8">ARM_SPI_EVENT_TRANSFER_COMPLETE</a>).</p>
<p>When in slave mode but send/receive/transfer operation is not started and data is sent/requested by the master then the <a class="el" href="group___s_p_i__events.html#ga8e63d99c80ea56de596a8d0a51fd8244">ARM_SPI_EVENT_DATA_LOST</a> event is generated.</p>
<p>Receive operation can be aborted by calling <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> with <a class="el" href="group__spi__misc__ctrls.html#ga44708b80e48984be099cd6eb11780dc3">ARM_SPI_ABORT_TRANSFER</a> as the control parameter. </p>

</div>
</div>
<a class="anchor" id="gab2a303d1071e926280d50682f4808479"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_SPI_Send </td>
          <td>(</td>
          <td class="paramtype">const void *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>num</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Start sending data to SPI transmitter. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">data</td><td>Pointer to buffer with data to send to SPI transmitter </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">num</td><td>Number of data items to send </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>This functions is used to only send data to the SPI transmitter (received data is ignored).</p>
<p>The function parameters specify the buffer with data and the number of items to send. The item size is defined by the data type which depends on the configured number of data bits.</p>
<p>Data type is:</p>
<ul>
<li><em>uint8_t</em> when configured for 1..8 data bits</li>
<li><em>uint16_t</em> when configured for 9..16 data bits</li>
<li><em>uint32_t</em> when configured for 17..32 data bits</li>
</ul>
<p>Calling the function <b>ARM_SPI_Send</b> only starts the send operation. When in slave mode the operation is only registered and started when the master starts the transfer. The function is non-blocking and returns as soon as the driver has started the operation (driver typically configures DMA or the interrupt system for continuous transfer). During the operation it is not allowed to call this function or any other data transfer function again. Also the data buffer must stay allocated and the contents of unsent data must not be modified. When send operation is completed (requested number of items sent) the <a class="el" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8">ARM_SPI_EVENT_TRANSFER_COMPLETE</a> event is generated. Progress of send operation can also be monitored by reading the number of items already sent by calling <a class="el" href="group__spi__interface__gr.html#gaaaecaaf4ec1922f22e7f9de63af5ccdb">ARM_SPI_GetDataCount</a>.</p>
<p>Status of the transmitter can also be monitored by calling the <a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">ARM_SPI_GetStatus</a> and checking the <em>busy</em> flag which indicates if transmission is still in progress or pending.</p>
<p>When in master mode and configured to monitor slave select and the slave select gets deactivated during transfer then the SPI mode changes to inactive and the <a class="el" href="group___s_p_i__events.html#ga7eaa229003689aa18598273490b3e630">ARM_SPI_EVENT_MODE_FAULT</a> event is generated (instead of <a class="el" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8">ARM_SPI_EVENT_TRANSFER_COMPLETE</a>).</p>
<p>When in slave mode but send/receive/transfer operation is not started and data is sent/requested by the master then the <a class="el" href="group___s_p_i__events.html#ga8e63d99c80ea56de596a8d0a51fd8244">ARM_SPI_EVENT_DATA_LOST</a> event is generated.</p>
<p>Send operation can be aborted by calling <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> with <a class="el" href="group__spi__misc__ctrls.html#ga44708b80e48984be099cd6eb11780dc3">ARM_SPI_ABORT_TRANSFER</a> as the control parameter. </p>

</div>
</div>
<a class="anchor" id="ga505b2d787348d51351d38fee98ccba7e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void ARM_SPI_SignalEvent </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>event</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signal SPI Events. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">event</td><td><a class="el" href="group___s_p_i__events.html">SPI Events</a> notification mask </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p>The function <b>ARM_SPI_SignalEvent</b> notifies the application of the <a class="el" href="group___s_p_i__events.html">SPI Events</a> and it is registered by the function <a class="el" href="group__spi__interface__gr.html#ga1a3c11ed523a4355cd91069527945906">ARM_SPI_Initialize</a>. The function <a class="el" href="group__spi__interface__gr.html#gaf4823a11ab5efcd47c79b13801513ddc">ARM_SPI_GetCapabilities</a> returns information about the implemented optional events in a driver.</p>
<p>The argument <em>event</em> represents the notification mask of the events. Each event is coded in a separate bit and therefore it is possible to signal multiple events in the event call back function. The following call back notifications are generated:</p>
<table class="doxtable">
<tr>
<th>Bit </th><th align="left">Event </th><th align="left">Description</th></tr>
<tr>
<td>0 </td><td align="left"><a class="el" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8">ARM_SPI_EVENT_TRANSFER_COMPLETE</a> </td><td align="left">Occurs after call to <a class="el" href="group__spi__interface__gr.html#gab2a303d1071e926280d50682f4808479">ARM_SPI_Send</a>, <a class="el" href="group__spi__interface__gr.html#ga726aff54e782ed9b47f7ba1280a3d8f6">ARM_SPI_Receive</a> or <a class="el" href="group__spi__interface__gr.html#gaa24026b3822c10272e301f1505136ec2">ARM_SPI_Transfer</a> to indicate that all the data has been transferred. The driver is ready for the next transfer operation. </td></tr>
<tr>
<td>1 </td><td align="left"><a class="el" href="group___s_p_i__events.html#ga8e63d99c80ea56de596a8d0a51fd8244">ARM_SPI_EVENT_DATA_LOST</a> </td><td align="left">Occurs in slave mode when data is requested/sent by master but send/receive/transfer operation has not been started and indicates that data is lost. </td></tr>
<tr>
<td>2 </td><td align="left"><a class="el" href="group___s_p_i__events.html#ga7eaa229003689aa18598273490b3e630">ARM_SPI_EVENT_MODE_FAULT</a> </td><td align="left">Occurs in master mode when Slave Select is deactivated and indicates Master Mode Fault. </td></tr>
</table>

</div>
</div>
<a class="anchor" id="gaa24026b3822c10272e301f1505136ec2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_SPI_Transfer </td>
          <td>(</td>
          <td class="paramtype">const void *&#160;</td>
          <td class="paramname"><em>data_out</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>data_in</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>num</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Start sending/receiving data to/from SPI transmitter/receiver. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">data_out</td><td>Pointer to buffer with data to send to SPI transmitter </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">data_in</td><td>Pointer to buffer for data to receive from SPI receiver </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">num</td><td>Number of data items to transfer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>This functions is used to transfer data via SPI. It synchronously sends data to the SPI transmitter and receives data from the SPI receiver.</p>
<p>The function parameters specify the buffer with data to send, the buffer for data to receive and the number of items to transfer. The item size is defined by the data type which depends on the configured number of data bits.</p>
<p>Data type is:</p>
<ul>
<li><em>uint8_t</em> when configured for 1..8 data bits</li>
<li><em>uint16_t</em> when configured for 9..16 data bits</li>
<li><em>uint32_t</em> when configured for 17..32 data bits</li>
</ul>
<p>Calling the function <b>ARM_SPI_Transfer</b> only starts the transfer operation. The function is non-blocking and returns as soon as the driver has started the operation (driver typically configures DMA or the interrupt system for continuous transfer). When in slave mode the operation is only registered and started when the master starts the transfer. During the operation it is not allowed to call this function or any other data transfer function again. Also the data buffers must stay allocated and the contents of unsent data must not be modified. When transfer operation is completed (requested number of items transferred) the <a class="el" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8">ARM_SPI_EVENT_TRANSFER_COMPLETE</a> event is generated. Progress of transfer operation can also be monitored by reading the number of items already transferred by calling <a class="el" href="group__spi__interface__gr.html#gaaaecaaf4ec1922f22e7f9de63af5ccdb">ARM_SPI_GetDataCount</a>.</p>
<p>Status of the transmitter and receiver can also be monitored by calling the <a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">ARM_SPI_GetStatus</a> and checking the <em>busy</em> flag.</p>
<p>When in master mode and configured to monitor slave select and the slave select gets deactivated during transfer then the SPI mode changes to inactive and the <a class="el" href="group___s_p_i__events.html#ga7eaa229003689aa18598273490b3e630">ARM_SPI_EVENT_MODE_FAULT</a> event is generated (instead of <a class="el" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8">ARM_SPI_EVENT_TRANSFER_COMPLETE</a>).</p>
<p>When in slave mode but send/receive/transfer operation is not started and data is sent/requested by the master then the <a class="el" href="group___s_p_i__events.html#ga8e63d99c80ea56de596a8d0a51fd8244">ARM_SPI_EVENT_DATA_LOST</a> event is generated.</p>
<p>Transfer operation can also be aborted by calling <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> with <a class="el" href="group__spi__misc__ctrls.html#ga44708b80e48984be099cd6eb11780dc3">ARM_SPI_ABORT_TRANSFER</a> as the control parameter. </p>

</div>
</div>
<a class="anchor" id="ga0c480ee3eabb82fc746e89741ed2e03e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_SPI_Uninitialize </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>De-initialize SPI Interface. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__spi__interface__gr.html#ga0c480ee3eabb82fc746e89741ed2e03e">ARM_SPI_Uninitialize</a> de-initializes the resources of SPI interface.</p>
<p>It is called when the middleware component stops operation and releases the software resources used by the interface. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
