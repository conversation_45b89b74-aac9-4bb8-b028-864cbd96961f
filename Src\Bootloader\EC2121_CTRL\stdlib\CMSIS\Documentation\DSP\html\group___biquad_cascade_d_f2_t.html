<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Biquad Cascade IIR Filters Using a Direct Form II Transposed Structure</title>
<title>CMSIS-DSP: Biquad Cascade IIR Filters Using a Direct Form II Transposed Structure</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___biquad_cascade_d_f2_t.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Biquad Cascade IIR Filters Using a Direct Form II Transposed Structure</div>  </div>
<div class="ingroups"><a class="el" href="group__group_filters.html">Filtering Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga114f373fbc16a314e9f293c7c7649c7f"><td class="memItemLeft" align="right" valign="top">LOW_OPTIMIZATION_ENTER void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f2_t.html#ga114f373fbc16a314e9f293c7c7649c7f">arm_biquad_cascade_df2T_f32</a> (const <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html">arm_biquad_cascade_df2T_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga114f373fbc16a314e9f293c7c7649c7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point transposed direct form II Biquad cascade filter.  <a href="#ga114f373fbc16a314e9f293c7c7649c7f"></a><br/></td></tr>
<tr class="separator:ga114f373fbc16a314e9f293c7c7649c7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa8735dda5f3f36d0936283794c2aa771"><td class="memItemLeft" align="right" valign="top">LOW_OPTIMIZATION_ENTER void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f2_t.html#gaa8735dda5f3f36d0936283794c2aa771">arm_biquad_cascade_df2T_f64</a> (const <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html">arm_biquad_cascade_df2T_instance_f64</a> *S, <a class="el" href="arm__math_8h.html#ac55f3ae81b5bc9053760baacf57e47f4">float64_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ac55f3ae81b5bc9053760baacf57e47f4">float64_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gaa8735dda5f3f36d0936283794c2aa771"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point transposed direct form II Biquad cascade filter.  <a href="#gaa8735dda5f3f36d0936283794c2aa771"></a><br/></td></tr>
<tr class="separator:gaa8735dda5f3f36d0936283794c2aa771"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70eaddf317a4a8bde6bd6a97df67fedd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f2_t.html#ga70eaddf317a4a8bde6bd6a97df67fedd">arm_biquad_cascade_df2T_init_f32</a> (<a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html">arm_biquad_cascade_df2T_instance_f32</a> *S, uint8_t numStages, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pState)</td></tr>
<tr class="memdesc:ga70eaddf317a4a8bde6bd6a97df67fedd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point transposed direct form II Biquad cascade filter.  <a href="#ga70eaddf317a4a8bde6bd6a97df67fedd"></a><br/></td></tr>
<tr class="separator:ga70eaddf317a4a8bde6bd6a97df67fedd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga12dc5d8e8892806ad70e79ca2ff9f86e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f2_t.html#ga12dc5d8e8892806ad70e79ca2ff9f86e">arm_biquad_cascade_df2T_init_f64</a> (<a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html">arm_biquad_cascade_df2T_instance_f64</a> *S, uint8_t numStages, <a class="el" href="arm__math_8h.html#ac55f3ae81b5bc9053760baacf57e47f4">float64_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#ac55f3ae81b5bc9053760baacf57e47f4">float64_t</a> *pState)</td></tr>
<tr class="memdesc:ga12dc5d8e8892806ad70e79ca2ff9f86e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point transposed direct form II Biquad cascade filter.  <a href="#ga12dc5d8e8892806ad70e79ca2ff9f86e"></a><br/></td></tr>
<tr class="separator:ga12dc5d8e8892806ad70e79ca2ff9f86e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac75de449c3e4f733477d81bd0ada5eec"><td class="memItemLeft" align="right" valign="top">LOW_OPTIMIZATION_ENTER void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f2_t.html#gac75de449c3e4f733477d81bd0ada5eec">arm_biquad_cascade_stereo_df2T_f32</a> (const <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html">arm_biquad_cascade_stereo_df2T_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gac75de449c3e4f733477d81bd0ada5eec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point transposed direct form II Biquad cascade filter.  <a href="#gac75de449c3e4f733477d81bd0ada5eec"></a><br/></td></tr>
<tr class="separator:gac75de449c3e4f733477d81bd0ada5eec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga405197c89fe4d34003efd23786296425"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f2_t.html#ga405197c89fe4d34003efd23786296425">arm_biquad_cascade_stereo_df2T_init_f32</a> (<a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html">arm_biquad_cascade_stereo_df2T_instance_f32</a> *S, uint8_t numStages, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pState)</td></tr>
<tr class="memdesc:ga405197c89fe4d34003efd23786296425"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point transposed direct form II Biquad cascade filter.  <a href="#ga405197c89fe4d34003efd23786296425"></a><br/></td></tr>
<tr class="separator:ga405197c89fe4d34003efd23786296425"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This set of functions implements arbitrary order recursive (IIR) filters using a transposed direct form II structure. The filters are implemented as a cascade of second order Biquad sections. These functions provide a slight memory savings as compared to the direct form I Biquad filter functions. Only floating-point data is supported.</p>
<p>This function operate on blocks of input and output data and each call to the function processes <code>blockSize</code> samples through the filter. <code>pSrc</code> points to the array of input data and <code>pDst</code> points to the array of output data. Both arrays contain <code>blockSize</code> values.</p>
<dl class="section user"><dt>Algorithm </dt><dd>Each Biquad stage implements a second order filter using the difference equation: <pre>       
   y[n] = b0 * x[n] + d1       
   d1 = b1 * x[n] + a1 * y[n] + d2       
   d2 = b2 * x[n] + a2 * y[n]       
</pre> where d1 and d2 represent the two state values.</dd></dl>
<dl class="section user"><dt></dt><dd>A Biquad filter using a transposed Direct Form II structure is shown below. <div class="image">
<img src="BiquadDF2Transposed.gif" alt="BiquadDF2Transposed.gif"/>
<div class="caption">
Single transposed Direct Form II Biquad</div></div>
 Coefficients <code>b0, b1, and b2 </code> multiply the input signal <code>x[n]</code> and are referred to as the feedforward coefficients. Coefficients <code>a1</code> and <code>a2</code> multiply the output signal <code>y[n]</code> and are referred to as the feedback coefficients. Pay careful attention to the sign of the feedback coefficients. Some design tools flip the sign of the feedback coefficients: <pre>       
   y[n] = b0 * x[n] + d1;       
   d1 = b1 * x[n] - a1 * y[n] + d2;       
   d2 = b2 * x[n] - a2 * y[n];       
</pre> In this case the feedback coefficients <code>a1</code> and <code>a2</code> must be negated when used with the CMSIS DSP Library.</dd></dl>
<dl class="section user"><dt></dt><dd>Higher order filters are realized as a cascade of second order sections. <code>numStages</code> refers to the number of second order stages used. For example, an 8th order filter would be realized with <code>numStages=4</code> second order stages. A 9th order filter would be realized with <code>numStages=5</code> second order stages with the coefficients for one of the stages configured as a first order filter (<code>b2=0</code> and <code>a2=0</code>).</dd></dl>
<dl class="section user"><dt></dt><dd><code>pState</code> points to the state variable array. Each Biquad stage has 2 state variables <code>d1</code> and <code>d2</code>. The state variables are arranged in the <code>pState</code> array as: <pre>       
    {d11, d12, d21, d22, ...}       
</pre> where <code>d1x</code> refers to the state variables for the first Biquad and <code>d2x</code> refers to the state variables for the second Biquad. The state array has a total length of <code>2*numStages</code> values. The state variables are updated after each block of data is processed; the coefficients are untouched.</dd></dl>
<dl class="section user"><dt></dt><dd>The CMSIS library contains Biquad filters in both Direct Form I and transposed Direct Form II. The advantage of the Direct Form I structure is that it is numerically more robust for fixed-point data types. That is why the Direct Form I structure supports Q15 and Q31 data types. The transposed Direct Form II structure, on the other hand, requires a wide dynamic range for the state variables <code>d1</code> and <code>d2</code>. Because of this, the CMSIS library only has a floating-point version of the Direct Form II Biquad. The advantage of the Direct Form II Biquad is that it requires half the number of state variables, 2 rather than 4, per Biquad stage.</dd></dl>
<dl class="section user"><dt>Instance Structure </dt><dd>The coefficients and state variables for a filter are stored together in an instance data structure. A separate instance structure must be defined for each filter. Coefficient arrays may be shared among several instances while state variable arrays cannot be shared.</dd></dl>
<dl class="section user"><dt>Init Functions </dt><dd>There is also an associated initialization function. The initialization function performs following operations:<ul>
<li>Sets the values of the internal structure fields.</li>
<li>Zeros out the values in the state buffer. To do this manually without calling the init function, assign the follow subfields of the instance structure: numStages, pCoeffs, pState. Also set all of the values in pState to zero.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Use of the initialization function is optional. However, if the initialization function is used, then the instance structure cannot be placed into a const data section. To place an instance structure into a const data section, the instance structure must be manually initialized. Set the values in the state buffer to zeros before static initialization. For example, to statically initialize the instance structure use <pre>       
    <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html" title="Instance structure for the floating-point transposed direct form II Biquad cascade filter...">arm_biquad_cascade_df2T_instance_f32</a> S1 = {numStages, pState, pCoeffs};       
</pre> where <code>numStages</code> is the number of Biquad stages in the filter; <code>pState</code> is the address of the state buffer. <code>pCoeffs</code> is the address of the coefficient buffer;</dd></dl>
<p>This set of functions implements arbitrary order recursive (IIR) filters using a transposed direct form II structure. The filters are implemented as a cascade of second order Biquad sections. These functions provide a slight memory savings as compared to the direct form I Biquad filter functions. Only floating-point data is supported.</p>
<p>This function operate on blocks of input and output data and each call to the function processes <code>blockSize</code> samples through the filter. <code>pSrc</code> points to the array of input data and <code>pDst</code> points to the array of output data. Both arrays contain <code>blockSize</code> values.</p>
<dl class="section user"><dt>Algorithm </dt><dd>Each Biquad stage implements a second order filter using the difference equation: <pre>       
   y[n] = b0 * x[n] + d1       
   d1 = b1 * x[n] + a1 * y[n] + d2       
   d2 = b2 * x[n] + a2 * y[n]       
</pre> where d1 and d2 represent the two state values.</dd></dl>
<dl class="section user"><dt></dt><dd>A Biquad filter using a transposed Direct Form II structure is shown below. <div class="image">
<img src="BiquadDF2Transposed.gif" alt="BiquadDF2Transposed.gif"/>
<div class="caption">
Single transposed Direct Form II Biquad</div></div>
 Coefficients <code>b0, b1, and b2 </code> multiply the input signal <code>x[n]</code> and are referred to as the feedforward coefficients. Coefficients <code>a1</code> and <code>a2</code> multiply the output signal <code>y[n]</code> and are referred to as the feedback coefficients. Pay careful attention to the sign of the feedback coefficients. Some design tools flip the sign of the feedback coefficients: <pre>       
   y[n] = b0 * x[n] + d1;       
   d1 = b1 * x[n] - a1 * y[n] + d2;       
   d2 = b2 * x[n] - a2 * y[n];       
</pre> In this case the feedback coefficients <code>a1</code> and <code>a2</code> must be negated when used with the CMSIS DSP Library.</dd></dl>
<dl class="section user"><dt></dt><dd>Higher order filters are realized as a cascade of second order sections. <code>numStages</code> refers to the number of second order stages used. For example, an 8th order filter would be realized with <code>numStages=4</code> second order stages. A 9th order filter would be realized with <code>numStages=5</code> second order stages with the coefficients for one of the stages configured as a first order filter (<code>b2=0</code> and <code>a2=0</code>).</dd></dl>
<dl class="section user"><dt></dt><dd><code>pState</code> points to the state variable array. Each Biquad stage has 2 state variables <code>d1</code> and <code>d2</code>. The state variables are arranged in the <code>pState</code> array as: <pre>       
    {d11, d12, d21, d22, ...}       
</pre> where <code>d1x</code> refers to the state variables for the first Biquad and <code>d2x</code> refers to the state variables for the second Biquad. The state array has a total length of <code>2*numStages</code> values. The state variables are updated after each block of data is processed; the coefficients are untouched.</dd></dl>
<dl class="section user"><dt></dt><dd>The CMSIS library contains Biquad filters in both Direct Form I and transposed Direct Form II. The advantage of the Direct Form I structure is that it is numerically more robust for fixed-point data types. That is why the Direct Form I structure supports Q15 and Q31 data types. The transposed Direct Form II structure, on the other hand, requires a wide dynamic range for the state variables <code>d1</code> and <code>d2</code>. Because of this, the CMSIS library only has a floating-point version of the Direct Form II Biquad. The advantage of the Direct Form II Biquad is that it requires half the number of state variables, 2 rather than 4, per Biquad stage.</dd></dl>
<dl class="section user"><dt>Instance Structure </dt><dd>The coefficients and state variables for a filter are stored together in an instance data structure. A separate instance structure must be defined for each filter. Coefficient arrays may be shared among several instances while state variable arrays cannot be shared.</dd></dl>
<dl class="section user"><dt>Init Functions </dt><dd>There is also an associated initialization function. The initialization function performs following operations:<ul>
<li>Sets the values of the internal structure fields.</li>
<li>Zeros out the values in the state buffer. To do this manually without calling the init function, assign the follow subfields of the instance structure: numStages, pCoeffs, pState. Also set all of the values in pState to zero.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Use of the initialization function is optional. However, if the initialization function is used, then the instance structure cannot be placed into a const data section. To place an instance structure into a const data section, the instance structure must be manually initialized. Set the values in the state buffer to zeros before static initialization. For example, to statically initialize the instance structure use <pre>       
    <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html" title="Instance structure for the floating-point transposed direct form II Biquad cascade filter...">arm_biquad_cascade_df2T_instance_f64</a> S1 = {numStages, pState, pCoeffs};       
</pre> where <code>numStages</code> is the number of Biquad stages in the filter; <code>pState</code> is the address of the state buffer. <code>pCoeffs</code> is the address of the coefficient buffer; </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga114f373fbc16a314e9f293c7c7649c7f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LOW_OPTIMIZATION_ENTER void arm_biquad_cascade_df2T_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html">arm_biquad_cascade_df2T_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the filter data structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a4d17958c33c3d0a905f974bac50f033f">arm_biquad_cascade_df2T_instance_f32::numStages</a>, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a49a24fe1b6ad3b0b26779c32d8d80b2e">arm_biquad_cascade_df2T_instance_f32::pCoeffs</a>, and <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a24d223addfd926a7177088cf2efe76b1">arm_biquad_cascade_df2T_instance_f32::pState</a>.</p>

</div>
</div>
<a class="anchor" id="gaa8735dda5f3f36d0936283794c2aa771"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LOW_OPTIMIZATION_ENTER void arm_biquad_cascade_df2T_f64 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html">arm_biquad_cascade_df2T_instance_f64</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ac55f3ae81b5bc9053760baacf57e47f4">float64_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ac55f3ae81b5bc9053760baacf57e47f4">float64_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the filter data structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#ad55380ff835b533aa5168f836db8a4de">arm_biquad_cascade_df2T_instance_f64::numStages</a>, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#ae2f0180f9038c0393e1d6921bb3b878b">arm_biquad_cascade_df2T_instance_f64::pCoeffs</a>, and <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#a0bde57b618e3f9059b23b0de64e12ce3">arm_biquad_cascade_df2T_instance_f64::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga70eaddf317a4a8bde6bd6a97df67fedd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df2T_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html">arm_biquad_cascade_df2T_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the filter data structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of 2nd order stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Coefficient and State Ordering:</b> </p>
<dl class="section user"><dt></dt><dd>The coefficients are stored in the array <code>pCoeffs</code> in the following order: <pre>    
    {b10, b11, b12, a11, a12, b20, b21, b22, a21, a22, ...}    
</pre></dd></dl>
<dl class="section user"><dt></dt><dd>where <code>b1x</code> and <code>a1x</code> are the coefficients for the first stage, <code>b2x</code> and <code>a2x</code> are the coefficients for the second stage, and so on. The <code>pCoeffs</code> array contains a total of <code>5*numStages</code> values.</dd></dl>
<dl class="section user"><dt></dt><dd>The <code>pState</code> is a pointer to state array. Each Biquad stage has 2 state variables <code>d1,</code> and <code>d2</code>. The 2 state variables for stage 1 are first, then the 2 state variables for stage 2, and so on. The state array has a total length of <code>2*numStages</code> values. The state variables are updated after each block of data is processed; the coefficients are untouched. </dd></dl>

<p>References <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a4d17958c33c3d0a905f974bac50f033f">arm_biquad_cascade_df2T_instance_f32::numStages</a>, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a49a24fe1b6ad3b0b26779c32d8d80b2e">arm_biquad_cascade_df2T_instance_f32::pCoeffs</a>, and <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a24d223addfd926a7177088cf2efe76b1">arm_biquad_cascade_df2T_instance_f32::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga12dc5d8e8892806ad70e79ca2ff9f86e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df2T_init_f64 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html">arm_biquad_cascade_df2T_instance_f64</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ac55f3ae81b5bc9053760baacf57e47f4">float64_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ac55f3ae81b5bc9053760baacf57e47f4">float64_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the filter data structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of 2nd order stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Coefficient and State Ordering:</b> </p>
<dl class="section user"><dt></dt><dd>The coefficients are stored in the array <code>pCoeffs</code> in the following order: <pre>    
    {b10, b11, b12, a11, a12, b20, b21, b22, a21, a22, ...}    
</pre></dd></dl>
<dl class="section user"><dt></dt><dd>where <code>b1x</code> and <code>a1x</code> are the coefficients for the first stage, <code>b2x</code> and <code>a2x</code> are the coefficients for the second stage, and so on. The <code>pCoeffs</code> array contains a total of <code>5*numStages</code> values.</dd></dl>
<dl class="section user"><dt></dt><dd>The <code>pState</code> is a pointer to state array. Each Biquad stage has 2 state variables <code>d1,</code> and <code>d2</code>. The 2 state variables for stage 1 are first, then the 2 state variables for stage 2, and so on. The state array has a total length of <code>2*numStages</code> values. The state variables are updated after each block of data is processed; the coefficients are untouched. </dd></dl>

<p>References <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#ad55380ff835b533aa5168f836db8a4de">arm_biquad_cascade_df2T_instance_f64::numStages</a>, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#ae2f0180f9038c0393e1d6921bb3b878b">arm_biquad_cascade_df2T_instance_f64::pCoeffs</a>, and <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#a0bde57b618e3f9059b23b0de64e12ce3">arm_biquad_cascade_df2T_instance_f64::pState</a>.</p>

</div>
</div>
<a class="anchor" id="gac75de449c3e4f733477d81bd0ada5eec"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LOW_OPTIMIZATION_ENTER void arm_biquad_cascade_stereo_df2T_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html">arm_biquad_cascade_stereo_df2T_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Processing function for the floating-point transposed direct form II Biquad cascade filter. 2 channels.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the filter data structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a5655328252da5c2c2425ceed253bc4f1">arm_biquad_cascade_stereo_df2T_instance_f32::numStages</a>, <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a58b15644de62a632c5e9d4a563569dc6">arm_biquad_cascade_stereo_df2T_instance_f32::pCoeffs</a>, and <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a2cb00048bb1fe957a03c1ff56dfaf8f0">arm_biquad_cascade_stereo_df2T_instance_f32::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga405197c89fe4d34003efd23786296425"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_stereo_df2T_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html">arm_biquad_cascade_stereo_df2T_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the filter data structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of 2nd order stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Coefficient and State Ordering:</b> </p>
<dl class="section user"><dt></dt><dd>The coefficients are stored in the array <code>pCoeffs</code> in the following order: <pre>    
    {b10, b11, b12, a11, a12, b20, b21, b22, a21, a22, ...}    
</pre></dd></dl>
<dl class="section user"><dt></dt><dd>where <code>b1x</code> and <code>a1x</code> are the coefficients for the first stage, <code>b2x</code> and <code>a2x</code> are the coefficients for the second stage, and so on. The <code>pCoeffs</code> array contains a total of <code>5*numStages</code> values.</dd></dl>
<dl class="section user"><dt></dt><dd>The <code>pState</code> is a pointer to state array. Each Biquad stage has 2 state variables <code>d1,</code> and <code>d2</code> for each channel. The 2 state variables for stage 1 are first, then the 2 state variables for stage 2, and so on. The state array has a total length of <code>2*numStages</code> values. The state variables are updated after each block of data is processed; the coefficients are untouched. </dd></dl>

<p>References <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a5655328252da5c2c2425ceed253bc4f1">arm_biquad_cascade_stereo_df2T_instance_f32::numStages</a>, <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a58b15644de62a632c5e9d4a563569dc6">arm_biquad_cascade_stereo_df2T_instance_f32::pCoeffs</a>, and <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a2cb00048bb1fe957a03c1ff56dfaf8f0">arm_biquad_cascade_stereo_df2T_instance_f32::pState</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
