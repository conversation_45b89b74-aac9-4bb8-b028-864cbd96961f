<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_linear_interp_example_f32.c</title>
<title>CMSIS-DSP: arm_linear_interp_example_f32.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_linear_interp_example_f32_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_linear_interp_example_f32.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* $Date:         17. January 2013</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.0</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment">* Title:         arm_linear_interp_example_f32.c</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Description:   Example code demonstrating usage of sin function</span></div>
<div class="line"><span class="comment">*                and uses linear interpolation to get higher precision</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment"> * -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;math_helper.h&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#define SNR_THRESHOLD           90</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define TEST_LENGTH_SAMPLES     10</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define XSPACING               (0.00005f)</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Test input data for F32 SIN function</span></div>
<div class="line"><span class="comment">* Generated by the MATLAB rand() function</span></div>
<div class="line"><span class="comment">* randn(&#39;state&#39;, 0)</span></div>
<div class="line"><span class="comment">* xi = (((1/4.18318581819710)* randn(blockSize, 1) * 2* pi));</span></div>
<div class="line"><span class="comment">* --------------------------------------------------------------------*/</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="arm__linear__interp__example__f32_8c.html#a4be0c4d25e63ce04b8cc8ad070805287">testInputSin_f32</a>[<a name="a1"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>] =</div>
<div class="line">{</div>
<div class="line">   -0.649716504673081170, -2.501723745497831200,</div>
<div class="line">    0.188250329003310100,  0.432092748487532540,</div>
<div class="line">   -1.722010988459680800,  1.788766476323060600,</div>
<div class="line">    1.786136060975809500, -0.056525543169408797,</div>
<div class="line">    0.491596272728153760,  0.262309671126153390</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">*  Reference out of SIN F32 function for Block Size = 10</span></div>
<div class="line"><span class="comment">*  Calculated from sin(testInputSin_f32)</span></div>
<div class="line"><span class="comment">*------------------------------------------------------------------------------*/</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a2"></a><a class="code" href="arm__linear__interp__example__f32_8c.html#a5a33218d422603f3e5267b6984bdddd1">testRefSinOutput32_f32</a>[<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>] =</div>
<div class="line">{</div>
<div class="line">   -0.604960695383043530, -0.597090287967934840,</div>
<div class="line">    0.187140422442966500,  0.418772124875992690,</div>
<div class="line">   -0.988588831792106880,  0.976338412038794010,</div>
<div class="line">    0.976903856413481100, -0.056495446835214236,</div>
<div class="line">    0.472033731854734240,  0.259311907228582830</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">*  Method 1: Test out Buffer Calculated from Cubic Interpolation</span></div>
<div class="line"><span class="comment">*------------------------------------------------------------------------------*/</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a3"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>[<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>];</div>
<div class="line"></div>
<div class="line"><span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">*  Method 2: Test out buffer Calculated from Linear Interpolation</span></div>
<div class="line"><span class="comment">*------------------------------------------------------------------------------*/</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a4"></a><a class="code" href="arm__linear__interp__example__f32_8c.html#a8ca7d0ad6e04efed464bcaacedacf925">testLinIntOutput</a>[<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>];</div>
<div class="line"></div>
<div class="line"><span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">*  External table used for linear interpolation</span></div>
<div class="line"><span class="comment">*------------------------------------------------------------------------------*/</span></div>
<div class="line"><span class="keyword">extern</span> <span class="keywordtype">float</span> <a name="a5"></a><a class="code" href="arm__linear__interp__data_8c.html#a1a5a6c95f39221fcf8129fe478f54696">arm_linear_interep_table</a>[188495];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Global Variables for caluclating SNR&#39;s for Method1 &amp; Method 2</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a6"></a><a class="code" href="arm__linear__interp__example__f32_8c.html#ad492c21cf2dd4e9199ae46c77f812cbc">snr1</a>;</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a7"></a><a class="code" href="arm__linear__interp__example__f32_8c.html#a269948ab25f230d33e3f22eab85aa1cf">snr2</a>;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Calculation of Sine values from Cubic Interpolation and Linear interpolation</span></div>
<div class="line"><span class="comment">* ---------------------------------------------------------------------------- */</span></div>
<div class="line">int32_t <a name="a8"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">  uint32_t i;</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a9"></a><a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>;</div>
<div class="line"></div>
<div class="line">  <a name="_a10"></a><a class="code" href="structarm__linear__interp__instance__f32.html" title="Instance structure for the floating-point Linear Interpolate function.">arm_linear_interp_instance_f32</a> S = {188495, -3.141592653589793238, <a name="a11"></a><a class="code" href="arm__linear__interp__example__f32_8c.html#a0ecae49cebd837aac53411c8f877503d">XSPACING</a>, &amp;<a class="code" href="arm__linear__interp__data_8c.html#a1a5a6c95f39221fcf8129fe478f54696">arm_linear_interep_table</a>[0]};</div>
<div class="line"></div>
<div class="line">  <span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  *  Method 1: Test out Calculated from Cubic Interpolation</span></div>
<div class="line"><span class="comment">  *------------------------------------------------------------------------------*/</span></div>
<div class="line">  <span class="keywordflow">for</span>(i=0; i&lt; <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>; i++)</div>
<div class="line">  {</div>
<div class="line">    <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>[i] = <a name="a12"></a><a class="code" href="group__sin.html#gae164899c4a3fc0e946dc5d55555fe541" title="Fast approximation to the trigonometric sine function for floating-point data.">arm_sin_f32</a>(<a class="code" href="arm__linear__interp__example__f32_8c.html#a4be0c4d25e63ce04b8cc8ad070805287">testInputSin_f32</a>[i]);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  *  Method 2: Test out Calculated from Cubic Interpolation and Linear interpolation</span></div>
<div class="line"><span class="comment">  *------------------------------------------------------------------------------*/</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">for</span>(i=0; i&lt; <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>; i++)</div>
<div class="line">  {</div>
<div class="line">      <a class="code" href="arm__linear__interp__example__f32_8c.html#a8ca7d0ad6e04efed464bcaacedacf925">testLinIntOutput</a>[i] = <a name="a13"></a><a class="code" href="group___linear_interpolate.html#ga2269263d810cafcd19681957b37d5cf6" title="Process function for the floating-point Linear Interpolation Function.">arm_linear_interp_f32</a>(&amp;S, <a class="code" href="arm__linear__interp__example__f32_8c.html#a4be0c4d25e63ce04b8cc8ad070805287">testInputSin_f32</a>[i]);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  *            SNR calculation for method 1</span></div>
<div class="line"><span class="comment">  *------------------------------------------------------------------------------*/</span></div>
<div class="line">  <a class="code" href="arm__linear__interp__example__f32_8c.html#ad492c21cf2dd4e9199ae46c77f812cbc">snr1</a> = <a name="a14"></a><a class="code" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab" title="Caluclation of SNR.">arm_snr_f32</a>(<a class="code" href="arm__linear__interp__example__f32_8c.html#a5a33218d422603f3e5267b6984bdddd1">testRefSinOutput32_f32</a>, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, 2);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  *            SNR calculation for method 2</span></div>
<div class="line"><span class="comment">  *------------------------------------------------------------------------------*/</span></div>
<div class="line">  <a class="code" href="arm__linear__interp__example__f32_8c.html#a269948ab25f230d33e3f22eab85aa1cf">snr2</a> = <a class="code" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab" title="Caluclation of SNR.">arm_snr_f32</a>(<a class="code" href="arm__linear__interp__example__f32_8c.html#a5a33218d422603f3e5267b6984bdddd1">testRefSinOutput32_f32</a>, <a class="code" href="arm__linear__interp__example__f32_8c.html#a8ca7d0ad6e04efed464bcaacedacf925">testLinIntOutput</a>, 2);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  *            Initialise status depending on SNR calculations</span></div>
<div class="line"><span class="comment">  *------------------------------------------------------------------------------*/</span></div>
<div class="line">  <span class="keywordflow">if</span>( <a class="code" href="arm__linear__interp__example__f32_8c.html#a269948ab25f230d33e3f22eab85aa1cf">snr2</a> &gt; <a class="code" href="arm__linear__interp__example__f32_8c.html#ad492c21cf2dd4e9199ae46c77f812cbc">snr1</a>)</div>
<div class="line">  {</div>
<div class="line">    status = <a name="a15"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>;</div>
<div class="line">  }</div>
<div class="line">  <span class="keywordflow">else</span></div>
<div class="line">  {</div>
<div class="line">    status = <a name="a16"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>;</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Loop here if the signals fail the PASS check.</span></div>
<div class="line"><span class="comment">  ** This denotes a test failure</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line">  <span class="keywordflow">if</span>( status != <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>)</div>
<div class="line">  {</div>
<div class="line">    <span class="keywordflow">while</span>(1);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
<div class="line"></div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
