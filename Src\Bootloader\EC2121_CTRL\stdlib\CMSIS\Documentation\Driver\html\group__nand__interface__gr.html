<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: NAND Interface</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__nand__interface__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Content</a> &#124;
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">NAND Interface</div>  </div>
</div><!--header-->
<div class="contents">

<p>Driver API for NAND Flash Device Interface (Driver_NAND.h).  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Content</h2></td></tr>
<tr class="memitem:group__nand__execution__status"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__execution__status.html">Status Error Codes</a></td></tr>
<tr class="memdesc:group__nand__execution__status"><td class="mdescLeft">&#160;</td><td class="mdescRight">Negative values indicate errors (NAND has specific codes in addition to common <a class="el" href="group__execution__status.html">Status Error Codes</a>). <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group___n_a_n_d__events"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_a_n_d__events.html">NAND Events</a></td></tr>
<tr class="memdesc:group___n_a_n_d__events"><td class="mdescLeft">&#160;</td><td class="mdescRight">The NAND driver generates call back events that are notified via the function <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a>. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__nand__driver__flag__codes"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__driver__flag__codes.html">NAND Flags</a></td></tr>
<tr class="memdesc:group__nand__driver__flag__codes"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specify Flag codes. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__nand__control__gr"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__control__gr.html">NAND Control Codes</a></td></tr>
<tr class="memdesc:group__nand__control__gr"><td class="mdescLeft">&#160;</td><td class="mdescRight">Many parameters of the NAND driver are configured using the <a class="el" href="group__nand__interface__gr.html#ga83061d6d53ffb148853efbc87a864607">ARM_NAND_Control</a> function. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__nand__driver__ecc__codes"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__driver__ecc__codes.html">NAND ECC Codes</a></td></tr>
<tr class="memdesc:group__nand__driver__ecc__codes"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specify ECC codes. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__nand__driver__seq__exec__codes"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__driver__seq__exec__codes.html">NAND Sequence Execution Codes</a></td></tr>
<tr class="memdesc:group__nand__driver__seq__exec__codes"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specify execution codes. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:struct_a_r_m___n_a_n_d___s_t_a_t_u_s"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s">ARM_NAND_STATUS</a></td></tr>
<tr class="memdesc:struct_a_r_m___n_a_n_d___s_t_a_t_u_s"><td class="mdescLeft">&#160;</td><td class="mdescRight">NAND Status.  <a href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___n_a_n_d___s_t_a_t_u_s"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___d_r_i_v_e_r___n_a_n_d"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d">ARM_DRIVER_NAND</a></td></tr>
<tr class="memdesc:struct_a_r_m___d_r_i_v_e_r___n_a_n_d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access structure of the NAND Driver.  <a href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___d_r_i_v_e_r___n_a_n_d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a></td></tr>
<tr class="memdesc:struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s"><td class="mdescLeft">&#160;</td><td class="mdescRight">NAND Driver Capabilities.  <a href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o">ARM_NAND_ECC_INFO</a></td></tr>
<tr class="memdesc:struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o"><td class="mdescLeft">&#160;</td><td class="mdescRight">NAND ECC (Error Correction Code) Information.  <a href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga09f4cf2f2df0bb690bce38b13d77e50f"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga09f4cf2f2df0bb690bce38b13d77e50f">ARM_NAND_SignalEvent_t</a> )(uint32_t dev_num, uint32_t event)</td></tr>
<tr class="memdesc:ga09f4cf2f2df0bb690bce38b13d77e50f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a> : Signal NAND Event.  <a href="#ga09f4cf2f2df0bb690bce38b13d77e50f">More...</a><br/></td></tr>
<tr class="separator:ga09f4cf2f2df0bb690bce38b13d77e50f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga01255fd4f15e7fa4751c7ea59648ef5a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga01255fd4f15e7fa4751c7ea59648ef5a">ARM_NAND_GetVersion</a> (void)</td></tr>
<tr class="memdesc:ga01255fd4f15e7fa4751c7ea59648ef5a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver version.  <a href="#ga01255fd4f15e7fa4751c7ea59648ef5a">More...</a><br/></td></tr>
<tr class="separator:ga01255fd4f15e7fa4751c7ea59648ef5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f2609975c2008d21b9ae28f15daf147"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga9f2609975c2008d21b9ae28f15daf147">ARM_NAND_GetCapabilities</a> (void)</td></tr>
<tr class="memdesc:ga9f2609975c2008d21b9ae28f15daf147"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver capabilities.  <a href="#ga9f2609975c2008d21b9ae28f15daf147">More...</a><br/></td></tr>
<tr class="separator:ga9f2609975c2008d21b9ae28f15daf147"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga74ad34718a595e7a4375b90f33e72750"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga74ad34718a595e7a4375b90f33e72750">ARM_NAND_Initialize</a> (<a class="el" href="group__nand__interface__gr.html#ga09f4cf2f2df0bb690bce38b13d77e50f">ARM_NAND_SignalEvent_t</a> cb_event)</td></tr>
<tr class="memdesc:ga74ad34718a595e7a4375b90f33e72750"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize the NAND Interface.  <a href="#ga74ad34718a595e7a4375b90f33e72750">More...</a><br/></td></tr>
<tr class="separator:ga74ad34718a595e7a4375b90f33e72750"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa788b638ab696b166fee2f4a4bc8d97a"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#gaa788b638ab696b166fee2f4a4bc8d97a">ARM_NAND_Uninitialize</a> (void)</td></tr>
<tr class="memdesc:gaa788b638ab696b166fee2f4a4bc8d97a"><td class="mdescLeft">&#160;</td><td class="mdescRight">De-initialize the NAND Interface.  <a href="#gaa788b638ab696b166fee2f4a4bc8d97a">More...</a><br/></td></tr>
<tr class="separator:gaa788b638ab696b166fee2f4a4bc8d97a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c9975637980b5d42db7baba0191fda1"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga9c9975637980b5d42db7baba0191fda1">ARM_NAND_PowerControl</a> (<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:ga9c9975637980b5d42db7baba0191fda1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control the NAND interface power.  <a href="#ga9c9975637980b5d42db7baba0191fda1">More...</a><br/></td></tr>
<tr class="separator:ga9c9975637980b5d42db7baba0191fda1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga11adcbaaace09746581a36befbd563c9"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga11adcbaaace09746581a36befbd563c9">ARM_NAND_DevicePower</a> (uint32_t voltage)</td></tr>
<tr class="memdesc:ga11adcbaaace09746581a36befbd563c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set device power supply voltage.  <a href="#ga11adcbaaace09746581a36befbd563c9">More...</a><br/></td></tr>
<tr class="separator:ga11adcbaaace09746581a36befbd563c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1987e65a4e756d748db86332c9fb1cec"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga1987e65a4e756d748db86332c9fb1cec">ARM_NAND_WriteProtect</a> (uint32_t dev_num, bool enable)</td></tr>
<tr class="memdesc:ga1987e65a4e756d748db86332c9fb1cec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control WPn (Write Protect).  <a href="#ga1987e65a4e756d748db86332c9fb1cec">More...</a><br/></td></tr>
<tr class="separator:ga1987e65a4e756d748db86332c9fb1cec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1c0cba87cb7b706ad5986dc67c831ad1"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga1c0cba87cb7b706ad5986dc67c831ad1">ARM_NAND_ChipEnable</a> (uint32_t dev_num, bool enable)</td></tr>
<tr class="memdesc:ga1c0cba87cb7b706ad5986dc67c831ad1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control CEn (Chip Enable).  <a href="#ga1c0cba87cb7b706ad5986dc67c831ad1">More...</a><br/></td></tr>
<tr class="separator:ga1c0cba87cb7b706ad5986dc67c831ad1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43011066306bd716b580e6aa9a80cf65"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga43011066306bd716b580e6aa9a80cf65">ARM_NAND_GetDeviceBusy</a> (uint32_t dev_num)</td></tr>
<tr class="memdesc:ga43011066306bd716b580e6aa9a80cf65"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get Device Busy pin state.  <a href="#ga43011066306bd716b580e6aa9a80cf65">More...</a><br/></td></tr>
<tr class="separator:ga43011066306bd716b580e6aa9a80cf65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f70b89ba478eadfe7f5dee7453a4fb7"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga9f70b89ba478eadfe7f5dee7453a4fb7">ARM_NAND_SendCommand</a> (uint32_t dev_num, uint8_t cmd)</td></tr>
<tr class="memdesc:ga9f70b89ba478eadfe7f5dee7453a4fb7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send command to NAND device.  <a href="#ga9f70b89ba478eadfe7f5dee7453a4fb7">More...</a><br/></td></tr>
<tr class="separator:ga9f70b89ba478eadfe7f5dee7453a4fb7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga00e195031e03d364db7595858a7e76f3"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga00e195031e03d364db7595858a7e76f3">ARM_NAND_SendAddress</a> (uint32_t dev_num, uint8_t addr)</td></tr>
<tr class="memdesc:ga00e195031e03d364db7595858a7e76f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send address to NAND device.  <a href="#ga00e195031e03d364db7595858a7e76f3">More...</a><br/></td></tr>
<tr class="separator:ga00e195031e03d364db7595858a7e76f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1899a20ef107400c8bf84fad477a8ce"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#gae1899a20ef107400c8bf84fad477a8ce">ARM_NAND_ReadData</a> (uint32_t dev_num, void *data, uint32_t cnt, uint32_t mode)</td></tr>
<tr class="memdesc:gae1899a20ef107400c8bf84fad477a8ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read data from NAND device.  <a href="#gae1899a20ef107400c8bf84fad477a8ce">More...</a><br/></td></tr>
<tr class="separator:gae1899a20ef107400c8bf84fad477a8ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1fa497dd51a86fc308e946b4419fd006"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga1fa497dd51a86fc308e946b4419fd006">ARM_NAND_WriteData</a> (uint32_t dev_num, const void *data, uint32_t cnt, uint32_t mode)</td></tr>
<tr class="memdesc:ga1fa497dd51a86fc308e946b4419fd006"><td class="mdescLeft">&#160;</td><td class="mdescRight">Write data to NAND device.  <a href="#ga1fa497dd51a86fc308e946b4419fd006">More...</a><br/></td></tr>
<tr class="separator:ga1fa497dd51a86fc308e946b4419fd006"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a0108dba757a4610475151144b52825"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga8a0108dba757a4610475151144b52825">ARM_NAND_ExecuteSequence</a> (uint32_t dev_num, uint32_t code, uint32_t cmd, uint32_t addr_col, uint32_t addr_row, void *data, uint32_t data_cnt, uint8_t *status, uint32_t *count)</td></tr>
<tr class="memdesc:ga8a0108dba757a4610475151144b52825"><td class="mdescLeft">&#160;</td><td class="mdescRight">Execute sequence of operations.  <a href="#ga8a0108dba757a4610475151144b52825">More...</a><br/></td></tr>
<tr class="separator:ga8a0108dba757a4610475151144b52825"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga00832861f018db0d8368900b099ecd30"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga00832861f018db0d8368900b099ecd30">ARM_NAND_AbortSequence</a> (uint32_t dev_num)</td></tr>
<tr class="memdesc:ga00832861f018db0d8368900b099ecd30"><td class="mdescLeft">&#160;</td><td class="mdescRight">Abort sequence execution.  <a href="#ga00832861f018db0d8368900b099ecd30">More...</a><br/></td></tr>
<tr class="separator:ga00832861f018db0d8368900b099ecd30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83061d6d53ffb148853efbc87a864607"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga83061d6d53ffb148853efbc87a864607">ARM_NAND_Control</a> (uint32_t dev_num, uint32_t control, uint32_t arg)</td></tr>
<tr class="memdesc:ga83061d6d53ffb148853efbc87a864607"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control NAND Interface.  <a href="#ga83061d6d53ffb148853efbc87a864607">More...</a><br/></td></tr>
<tr class="separator:ga83061d6d53ffb148853efbc87a864607"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4578642f37a556b58b0bba0ad5d42641"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s">ARM_NAND_STATUS</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ga4578642f37a556b58b0bba0ad5d42641">ARM_NAND_GetStatus</a> (uint32_t dev_num)</td></tr>
<tr class="memdesc:ga4578642f37a556b58b0bba0ad5d42641"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get NAND status.  <a href="#ga4578642f37a556b58b0bba0ad5d42641">More...</a><br/></td></tr>
<tr class="separator:ga4578642f37a556b58b0bba0ad5d42641"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac21425454d586ef48fdfc35e7bd78947"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#gac21425454d586ef48fdfc35e7bd78947">ARM_NAND_InquireECC</a> (int32_t index, <a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o">ARM_NAND_ECC_INFO</a> *info)</td></tr>
<tr class="memdesc:gac21425454d586ef48fdfc35e7bd78947"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inquire about available ECC.  <a href="#gac21425454d586ef48fdfc35e7bd78947">More...</a><br/></td></tr>
<tr class="separator:gac21425454d586ef48fdfc35e7bd78947"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4ce80b0fd6717de7ddfb1cfaf7dd754"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a> (uint32_t dev_num, uint32_t event)</td></tr>
<tr class="memdesc:gaf4ce80b0fd6717de7ddfb1cfaf7dd754"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signal NAND event.  <a href="#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">More...</a><br/></td></tr>
<tr class="separator:gaf4ce80b0fd6717de7ddfb1cfaf7dd754"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Driver API for NAND Flash Device Interface (Driver_NAND.h). </p>
<p><b>NAND</b> devices are a type of non-volatile storage and do not require power to hold data. Wikipedia offers more information about the <a href="http://en.wikipedia.org/wiki/Flash_memory#ARM_NAND_memories" target="_blank"><b>Flash Memories</b></a>, including NAND.</p>
<p><b>NAND Structure</b></p>
<p>&#160;</p>
<div class="image">
<img src="NAND_Schematics.png" alt="NAND_Schematics.png"/>
<div class="caption">
Simplified NAND Flash Schematic</div></div>
<p>&#160;</p>
<p><b>NAND API</b></p>
<p>The following header files define the Application Programming Interface (API) for the NAND interface:</p>
<ul>
<li><b>Driver_NAND.h</b> : Driver API for NAND Flash Device Interface</li>
</ul>
<p>The driver implementation is a typical part of the Device Family Pack (DFP) that supports the peripherals of the microcontroller family.</p>
<p><b>Driver Functions</b></p>
<p>The driver functions are published in the access struct as explained in <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a></p>
<ul>
<li><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d">ARM_DRIVER_NAND</a> : access struct for NAND driver functions </li>
</ul>
<hr/><h2 class="groupheader">Data Structure Documentation</h2>
<a name="struct_a_r_m___n_a_n_d___s_t_a_t_u_s" id="struct_a_r_m___n_a_n_d___s_t_a_t_u_s"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_NAND_STATUS</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>NAND Status. </p>
<p>Structure with information about the status of a NAND. The bitfields encode flags for the driver.</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__nand__interface__gr.html#ga4578642f37a556b58b0bba0ad5d42641">ARM_NAND_GetStatus</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a50c88f3c1d787773e2ac1b59533f034a"></a>uint32_t</td>
<td class="fieldname">
busy: 1</td>
<td class="fielddoc">
Driver busy flag. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a7707d2200a3bf8f49b148ffc8ded7636"></a>uint32_t</td>
<td class="fieldname">
ecc_error: 1</td>
<td class="fielddoc">
ECC error detected (cleared on next Read/WriteData or ExecuteSequence) </td></tr>
</table>

</div>
</div>
<a name="struct_a_r_m___d_r_i_v_e_r___n_a_n_d" id="struct_a_r_m___d_r_i_v_e_r___n_a_n_d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_DRIVER_NAND</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Access structure of the NAND Driver. </p>
<p>The functions of the NAND driver are accessed by function pointers exposed by this structure. Refer to <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a> for overview information.</p>
<p>Each instance of a NAND interface provides such an access structure. The instance is identified by a postfix number in the symbol name of the access structure, for example:</p>
<ul>
<li><b>Driver_NAND0</b> is the name of the access struct of the first instance (no. 0).</li>
<li><b>Driver_NAND1</b> is the name of the access struct of the second instance (no. 1).</li>
</ul>
<p>A middleware configuration setting allows connecting the middleware to a specific driver instance <b>Driver_NAND<em>n</em></b>. The default is <span class="XML-Token">0</span>, which connects a middleware to the first instance of a driver. </p>
</div><table class="memberdecls">
<tr><td colspan="2"><h3>Data Fields</h3></td></tr>
<tr class="memitem:a8834b281da48583845c044a81566c1b3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#a8834b281da48583845c044a81566c1b3">GetVersion</a> )(void)</td></tr>
<tr class="memdesc:a8834b281da48583845c044a81566c1b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga01255fd4f15e7fa4751c7ea59648ef5a">ARM_NAND_GetVersion</a> : Get driver version.  <a href="#a8834b281da48583845c044a81566c1b3">More...</a><br/></td></tr>
<tr class="separator:a8834b281da48583845c044a81566c1b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adab9d081aee3e5d1f83c6911e45ceaa6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#adab9d081aee3e5d1f83c6911e45ceaa6">GetCapabilities</a> )(void)</td></tr>
<tr class="memdesc:adab9d081aee3e5d1f83c6911e45ceaa6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga9f2609975c2008d21b9ae28f15daf147">ARM_NAND_GetCapabilities</a> : Get driver capabilities.  <a href="#adab9d081aee3e5d1f83c6911e45ceaa6">More...</a><br/></td></tr>
<tr class="separator:adab9d081aee3e5d1f83c6911e45ceaa6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28b29ab7b6114bb97175bd40d18854ac"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#a28b29ab7b6114bb97175bd40d18854ac">Initialize</a> )(<a class="el" href="group__nand__interface__gr.html#ga09f4cf2f2df0bb690bce38b13d77e50f">ARM_NAND_SignalEvent_t</a> cb_event)</td></tr>
<tr class="memdesc:a28b29ab7b6114bb97175bd40d18854ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga74ad34718a595e7a4375b90f33e72750">ARM_NAND_Initialize</a> : Initialize NAND Interface.  <a href="#a28b29ab7b6114bb97175bd40d18854ac">More...</a><br/></td></tr>
<tr class="separator:a28b29ab7b6114bb97175bd40d18854ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcf20681a1402869ecb5c6447fada17b"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#adcf20681a1402869ecb5c6447fada17b">Uninitialize</a> )(void)</td></tr>
<tr class="memdesc:adcf20681a1402869ecb5c6447fada17b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#gaa788b638ab696b166fee2f4a4bc8d97a">ARM_NAND_Uninitialize</a> : De-initialize NAND Interface.  <a href="#adcf20681a1402869ecb5c6447fada17b">More...</a><br/></td></tr>
<tr class="separator:adcf20681a1402869ecb5c6447fada17b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba8f1c8019af95ffe19c32403e3240ef"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">PowerControl</a> )(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:aba8f1c8019af95ffe19c32403e3240ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga9c9975637980b5d42db7baba0191fda1">ARM_NAND_PowerControl</a> : Control NAND Interface Power.  <a href="#aba8f1c8019af95ffe19c32403e3240ef">More...</a><br/></td></tr>
<tr class="separator:aba8f1c8019af95ffe19c32403e3240ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ba6f3066cda5c8d781c309a17315a58"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#a9ba6f3066cda5c8d781c309a17315a58">DevicePower</a> )(uint32_t voltage)</td></tr>
<tr class="memdesc:a9ba6f3066cda5c8d781c309a17315a58"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga11adcbaaace09746581a36befbd563c9">ARM_NAND_DevicePower</a> : Set device power supply voltage.  <a href="#a9ba6f3066cda5c8d781c309a17315a58">More...</a><br/></td></tr>
<tr class="separator:a9ba6f3066cda5c8d781c309a17315a58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add6fa19a729c42303581214bc9dec819"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#add6fa19a729c42303581214bc9dec819">WriteProtect</a> )(uint32_t dev_num, bool enable)</td></tr>
<tr class="memdesc:add6fa19a729c42303581214bc9dec819"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga1987e65a4e756d748db86332c9fb1cec">ARM_NAND_WriteProtect</a> : Control WPn (Write Protect).  <a href="#add6fa19a729c42303581214bc9dec819">More...</a><br/></td></tr>
<tr class="separator:add6fa19a729c42303581214bc9dec819"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac090c205fe3d1b3dcb7288b06468bbe5"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ac090c205fe3d1b3dcb7288b06468bbe5">ChipEnable</a> )(uint32_t dev_num, bool enable)</td></tr>
<tr class="memdesc:ac090c205fe3d1b3dcb7288b06468bbe5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga1c0cba87cb7b706ad5986dc67c831ad1">ARM_NAND_ChipEnable</a> : Control CEn (Chip Enable).  <a href="#ac090c205fe3d1b3dcb7288b06468bbe5">More...</a><br/></td></tr>
<tr class="separator:ac090c205fe3d1b3dcb7288b06468bbe5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9bc93fb1a089c6ac71428122f3a072e"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ac9bc93fb1a089c6ac71428122f3a072e">GetDeviceBusy</a> )(uint32_t dev_num)</td></tr>
<tr class="memdesc:ac9bc93fb1a089c6ac71428122f3a072e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga43011066306bd716b580e6aa9a80cf65">ARM_NAND_GetDeviceBusy</a> : Get Device Busy pin state.  <a href="#ac9bc93fb1a089c6ac71428122f3a072e">More...</a><br/></td></tr>
<tr class="separator:ac9bc93fb1a089c6ac71428122f3a072e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3dbd36d86718980665ce2e3a1ba672ca"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#a3dbd36d86718980665ce2e3a1ba672ca">SendCommand</a> )(uint32_t dev_num, uint8_t cmd)</td></tr>
<tr class="memdesc:a3dbd36d86718980665ce2e3a1ba672ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga9f70b89ba478eadfe7f5dee7453a4fb7">ARM_NAND_SendCommand</a> : Send command to NAND device.  <a href="#a3dbd36d86718980665ce2e3a1ba672ca">More...</a><br/></td></tr>
<tr class="separator:a3dbd36d86718980665ce2e3a1ba672ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a43001ef1ca6c6d73f03e366bf41cb5"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#a5a43001ef1ca6c6d73f03e366bf41cb5">SendAddress</a> )(uint32_t dev_num, uint8_t addr)</td></tr>
<tr class="memdesc:a5a43001ef1ca6c6d73f03e366bf41cb5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga00e195031e03d364db7595858a7e76f3">ARM_NAND_SendAddress</a> : Send address to NAND device.  <a href="#a5a43001ef1ca6c6d73f03e366bf41cb5">More...</a><br/></td></tr>
<tr class="separator:a5a43001ef1ca6c6d73f03e366bf41cb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeba263544c0d63ec8c29e919232615cb"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#aeba263544c0d63ec8c29e919232615cb">ReadData</a> )(uint32_t dev_num, void *data, uint32_t cnt, uint32_t mode)</td></tr>
<tr class="memdesc:aeba263544c0d63ec8c29e919232615cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#gae1899a20ef107400c8bf84fad477a8ce">ARM_NAND_ReadData</a> : Read data from NAND device.  <a href="#aeba263544c0d63ec8c29e919232615cb">More...</a><br/></td></tr>
<tr class="separator:aeba263544c0d63ec8c29e919232615cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78393d355e539c6f845b33417da60a7e"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#a78393d355e539c6f845b33417da60a7e">WriteData</a> )(uint32_t dev_num, const void *data, uint32_t cnt, uint32_t mode)</td></tr>
<tr class="memdesc:a78393d355e539c6f845b33417da60a7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga1fa497dd51a86fc308e946b4419fd006">ARM_NAND_WriteData</a> : Write data to NAND device.  <a href="#a78393d355e539c6f845b33417da60a7e">More...</a><br/></td></tr>
<tr class="separator:a78393d355e539c6f845b33417da60a7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af0dd5e96fbcc5c15bb183363f8541af8"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#af0dd5e96fbcc5c15bb183363f8541af8">ExecuteSequence</a> )(uint32_t dev_num, uint32_t code, uint32_t cmd, uint32_t addr_col, uint32_t addr_row, void *data, uint32_t data_cnt, uint8_t *status, uint32_t *count)</td></tr>
<tr class="memdesc:af0dd5e96fbcc5c15bb183363f8541af8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga8a0108dba757a4610475151144b52825">ARM_NAND_ExecuteSequence</a> : Execute sequence of operations.  <a href="#af0dd5e96fbcc5c15bb183363f8541af8">More...</a><br/></td></tr>
<tr class="separator:af0dd5e96fbcc5c15bb183363f8541af8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6e1d53e9028baff856899f795c0d0c8"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#ad6e1d53e9028baff856899f795c0d0c8">AbortSequence</a> )(uint32_t dev_num)</td></tr>
<tr class="memdesc:ad6e1d53e9028baff856899f795c0d0c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga00832861f018db0d8368900b099ecd30">ARM_NAND_AbortSequence</a> : Abort sequence execution.  <a href="#ad6e1d53e9028baff856899f795c0d0c8">More...</a><br/></td></tr>
<tr class="separator:ad6e1d53e9028baff856899f795c0d0c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a706fedbc88921808e210d75b7b5da168"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#a706fedbc88921808e210d75b7b5da168">Control</a> )(uint32_t dev_num, uint32_t control, uint32_t arg)</td></tr>
<tr class="memdesc:a706fedbc88921808e210d75b7b5da168"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga83061d6d53ffb148853efbc87a864607">ARM_NAND_Control</a> : Control NAND Interface.  <a href="#a706fedbc88921808e210d75b7b5da168">More...</a><br/></td></tr>
<tr class="separator:a706fedbc88921808e210d75b7b5da168"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa43ee108ee5bf29e40485ca89b34188b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s">ARM_NAND_STATUS</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#aa43ee108ee5bf29e40485ca89b34188b">GetStatus</a> )(uint32_t dev_num)</td></tr>
<tr class="memdesc:aa43ee108ee5bf29e40485ca89b34188b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#ga4578642f37a556b58b0bba0ad5d42641">ARM_NAND_GetStatus</a> : Get NAND status.  <a href="#aa43ee108ee5bf29e40485ca89b34188b">More...</a><br/></td></tr>
<tr class="separator:aa43ee108ee5bf29e40485ca89b34188b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aecd239806e9f08b77ce0d00f61e78cf8"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__nand__interface__gr.html#aecd239806e9f08b77ce0d00f61e78cf8">InquireECC</a> )(int32_t index, <a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o">ARM_NAND_ECC_INFO</a> *info)</td></tr>
<tr class="memdesc:aecd239806e9f08b77ce0d00f61e78cf8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__nand__interface__gr.html#gac21425454d586ef48fdfc35e7bd78947">ARM_NAND_InquireECC</a> : Inquire about available ECC.  <a href="#aecd239806e9f08b77ce0d00f61e78cf8">More...</a><br/></td></tr>
<tr class="separator:aecd239806e9f08b77ce0d00f61e78cf8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h4 class="groupheader">Field Documentation</h4>
<a class="anchor" id="ad6e1d53e9028baff856899f795c0d0c8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* AbortSequence)(uint32_t dev_num)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga00832861f018db0d8368900b099ecd30">ARM_NAND_AbortSequence</a> : Abort sequence execution. </p>

</div>
</div>
<a class="anchor" id="ac090c205fe3d1b3dcb7288b06468bbe5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* ChipEnable)(uint32_t dev_num, bool enable)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga1c0cba87cb7b706ad5986dc67c831ad1">ARM_NAND_ChipEnable</a> : Control CEn (Chip Enable). </p>

</div>
</div>
<a class="anchor" id="a706fedbc88921808e210d75b7b5da168"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Control)(uint32_t dev_num, uint32_t control, uint32_t arg)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga83061d6d53ffb148853efbc87a864607">ARM_NAND_Control</a> : Control NAND Interface. </p>

</div>
</div>
<a class="anchor" id="a9ba6f3066cda5c8d781c309a17315a58"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* DevicePower)(uint32_t voltage)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga11adcbaaace09746581a36befbd563c9">ARM_NAND_DevicePower</a> : Set device power supply voltage. </p>

</div>
</div>
<a class="anchor" id="af0dd5e96fbcc5c15bb183363f8541af8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* ExecuteSequence)(uint32_t dev_num, uint32_t code, uint32_t cmd, uint32_t addr_col, uint32_t addr_row, void *data, uint32_t data_cnt, uint8_t *status, uint32_t *count)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga8a0108dba757a4610475151144b52825">ARM_NAND_ExecuteSequence</a> : Execute sequence of operations. </p>

</div>
</div>
<a class="anchor" id="adab9d081aee3e5d1f83c6911e45ceaa6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a>(* GetCapabilities)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga9f2609975c2008d21b9ae28f15daf147">ARM_NAND_GetCapabilities</a> : Get driver capabilities. </p>

</div>
</div>
<a class="anchor" id="ac9bc93fb1a089c6ac71428122f3a072e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* GetDeviceBusy)(uint32_t dev_num)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga43011066306bd716b580e6aa9a80cf65">ARM_NAND_GetDeviceBusy</a> : Get Device Busy pin state. </p>

</div>
</div>
<a class="anchor" id="aa43ee108ee5bf29e40485ca89b34188b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s">ARM_NAND_STATUS</a>(* GetStatus)(uint32_t dev_num)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga4578642f37a556b58b0bba0ad5d42641">ARM_NAND_GetStatus</a> : Get NAND status. </p>

</div>
</div>
<a class="anchor" id="a8834b281da48583845c044a81566c1b3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(* GetVersion)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga01255fd4f15e7fa4751c7ea59648ef5a">ARM_NAND_GetVersion</a> : Get driver version. </p>

</div>
</div>
<a class="anchor" id="a28b29ab7b6114bb97175bd40d18854ac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Initialize)(<a class="el" href="group__nand__interface__gr.html#ga09f4cf2f2df0bb690bce38b13d77e50f">ARM_NAND_SignalEvent_t</a> cb_event)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga74ad34718a595e7a4375b90f33e72750">ARM_NAND_Initialize</a> : Initialize NAND Interface. </p>

</div>
</div>
<a class="anchor" id="aecd239806e9f08b77ce0d00f61e78cf8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* InquireECC)(int32_t index, <a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o">ARM_NAND_ECC_INFO</a> *info)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#gac21425454d586ef48fdfc35e7bd78947">ARM_NAND_InquireECC</a> : Inquire about available ECC. </p>

</div>
</div>
<a class="anchor" id="aba8f1c8019af95ffe19c32403e3240ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* PowerControl)(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga9c9975637980b5d42db7baba0191fda1">ARM_NAND_PowerControl</a> : Control NAND Interface Power. </p>

</div>
</div>
<a class="anchor" id="aeba263544c0d63ec8c29e919232615cb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* ReadData)(uint32_t dev_num, void *data, uint32_t cnt, uint32_t mode)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#gae1899a20ef107400c8bf84fad477a8ce">ARM_NAND_ReadData</a> : Read data from NAND device. </p>

</div>
</div>
<a class="anchor" id="a5a43001ef1ca6c6d73f03e366bf41cb5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* SendAddress)(uint32_t dev_num, uint8_t addr)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga00e195031e03d364db7595858a7e76f3">ARM_NAND_SendAddress</a> : Send address to NAND device. </p>

</div>
</div>
<a class="anchor" id="a3dbd36d86718980665ce2e3a1ba672ca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* SendCommand)(uint32_t dev_num, uint8_t cmd)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga9f70b89ba478eadfe7f5dee7453a4fb7">ARM_NAND_SendCommand</a> : Send command to NAND device. </p>

</div>
</div>
<a class="anchor" id="adcf20681a1402869ecb5c6447fada17b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Uninitialize)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#gaa788b638ab696b166fee2f4a4bc8d97a">ARM_NAND_Uninitialize</a> : De-initialize NAND Interface. </p>

</div>
</div>
<a class="anchor" id="a78393d355e539c6f845b33417da60a7e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* WriteData)(uint32_t dev_num, const void *data, uint32_t cnt, uint32_t mode)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga1fa497dd51a86fc308e946b4419fd006">ARM_NAND_WriteData</a> : Write data to NAND device. </p>

</div>
</div>
<a class="anchor" id="add6fa19a729c42303581214bc9dec819"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* WriteProtect)(uint32_t dev_num, bool enable)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#ga1987e65a4e756d748db86332c9fb1cec">ARM_NAND_WriteProtect</a> : Control WPn (Write Protect). </p>

</div>
</div>

</div>
</div>
<a name="struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s" id="struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_NAND_CAPABILITIES</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>NAND Driver Capabilities. </p>
<p>A NAND driver can be implemented with different capabilities. The bitfield members of this struct encode the capabilities implemented by this driver.</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__nand__interface__gr.html#ga9f2609975c2008d21b9ae28f15daf147">ARM_NAND_GetCapabilities</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ad5dd0fcdd7f6d5e5cd739f73323a2b11"></a>uint32_t</td>
<td class="fieldname">
ce_lines: 4</td>
<td class="fielddoc">
Number of CEn (Chip Enable) lines: ce_lines + 1. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a2b8044d986995b183b057217643466bf"></a>uint32_t</td>
<td class="fieldname">
ce_manual: 1</td>
<td class="fielddoc">
Supports manual CEn (Chip Enable) Control. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a0f22baea13daa9101bf6fc1fdfddc747"></a>uint32_t</td>
<td class="fieldname">
data_width_16: 1</td>
<td class="fielddoc">
Supports 16-bit data. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="aa9acfde38637fe749aa9271c0a8dae1a"></a>uint32_t</td>
<td class="fieldname">
ddr: 1</td>
<td class="fielddoc">
Supports NV-DDR Data Interface (ONFI) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ae086693990cbd5d628014c0fcc7c1f2c"></a>uint32_t</td>
<td class="fieldname">
ddr2: 1</td>
<td class="fielddoc">
Supports NV-DDR2 Data Interface (ONFI) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a6d9b66da0e56d04d545e0bb6841891b2"></a>uint32_t</td>
<td class="fieldname">
ddr2_timing_mode: 3</td>
<td class="fielddoc">
Fastest (highest) NV_DDR2 Timing Mode supported (ONFI) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a00c1f5db7d7c4abe7556733c36da7783"></a>uint32_t</td>
<td class="fieldname">
ddr_timing_mode: 3</td>
<td class="fielddoc">
Fastest (highest) NV_DDR Timing Mode supported (ONFI) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ae672b2a65dd3d0b93812c088491c4552"></a>uint32_t</td>
<td class="fieldname">
driver_strength_18: 1</td>
<td class="fielddoc">
Supports Driver Strength 2.0x = 18 Ohms. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ae87c19872b838dac7d3136a3fd466f6a"></a>uint32_t</td>
<td class="fieldname">
driver_strength_25: 1</td>
<td class="fielddoc">
Supports Driver Strength 1.4x = 25 Ohms. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="aef3d6e1522a6cf7fb87fd113dcd43ad5"></a>uint32_t</td>
<td class="fieldname">
driver_strength_50: 1</td>
<td class="fielddoc">
Supports Driver Strength 0.7x = 50 Ohms. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a5f347e9b63764bbb657f52dc20682128"></a>uint32_t</td>
<td class="fieldname">
event_device_ready: 1</td>
<td class="fielddoc">
Signal Device Ready event (R/Bn rising edge) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a69f5e734ee4a9bb501718cf78a740c3e"></a>uint32_t</td>
<td class="fieldname">
rb_monitor: 1</td>
<td class="fielddoc">
Supports R/Bn (Ready/Busy) Monitoring. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ae0514834750c7452431717a881471e2b"></a>uint32_t</td>
<td class="fieldname">
reentrant_operation: 1</td>
<td class="fielddoc">
Supports re-entrant operation (SendCommand/Address, Read/WriteData) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a21036f2047273d90c0af0e97031df5a9"></a>uint32_t</td>
<td class="fieldname">
sdr_timing_mode: 3</td>
<td class="fielddoc">
Fastest (highest) SDR Timing Mode supported (ONFI) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="afa4b798731b1154878c26dda3f090acf"></a>uint32_t</td>
<td class="fieldname">
sequence_operation: 1</td>
<td class="fielddoc">
Supports Sequence operation (ExecuteSequence, AbortSequence) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a35cfa22b2140b109fe24b97c42d5a5ed"></a>uint32_t</td>
<td class="fieldname">
vcc: 1</td>
<td class="fielddoc">
Supports VCC Power Supply Control. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a0e7d3b9258d468492b22de55d855a06e"></a>uint32_t</td>
<td class="fieldname">
vcc_1v8: 1</td>
<td class="fielddoc">
Supports 1.8 VCC Power Supply. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ab1cdfce6eb051bed7b904e0fd1719afa"></a>uint32_t</td>
<td class="fieldname">
vccq: 1</td>
<td class="fielddoc">
Supports VCCQ I/O Power Supply Control. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a1896a7548bb6fab285f23cc0d0b23d7d"></a>uint32_t</td>
<td class="fieldname">
vccq_1v8: 1</td>
<td class="fielddoc">
Supports 1.8 VCCQ I/O Power Supply. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a75b97f7c917bba90b2f5c747d6857d23"></a>uint32_t</td>
<td class="fieldname">
vpp: 1</td>
<td class="fielddoc">
Supports VPP High Voltage Power Supply Control. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="afe7f5b149b8d92859398315b1ad31ddc"></a>uint32_t</td>
<td class="fieldname">
wp: 1</td>
<td class="fielddoc">
Supports WPn (Write Protect) Control. </td></tr>
</table>

</div>
</div>
<a name="struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o" id="struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_NAND_ECC_INFO</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>NAND ECC (Error Correction Code) Information. </p>
<p>Structure with information about the Error Correction Code for a NAND.</p>
<p><b>Parameter for:</b></p>
<ul>
<li><a class="el" href="group__nand__interface__gr.html#gac21425454d586ef48fdfc35e7bd78947">ARM_NAND_InquireECC</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ae8cff208d9efb5067d38ced675916c66"></a>uint16_t</td>
<td class="fieldname">
codeword_size[2]</td>
<td class="fielddoc">
Number of bytes over which ECC is calculated. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ae65f920c4ad99fd0c6bdf5fd8c4d161a"></a>uint32_t</td>
<td class="fieldname">
correctable_bits: 8</td>
<td class="fielddoc">
Number of correctable bits (based on 512 byte codeword size) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a22d6a1813a47a7044f7acb478f8e9eb8"></a>uint16_t</td>
<td class="fieldname">
ecc_offset[2]</td>
<td class="fielddoc">
ECC offset in bytes (where ECC starts in Spare area) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a22365f6a2af1171a1c3629c8ae5fe001"></a>uint16_t</td>
<td class="fieldname">
ecc_size[2]</td>
<td class="fielddoc">
ECC size in bytes (rounded up) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="aa993bc236650aa405b01d00b7ca72904"></a>uint32_t</td>
<td class="fieldname">
page_count: 3</td>
<td class="fielddoc">
Number of virtual pages: N = 2 ^ page_count. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a5952ba4313bda7833fefd358f5aff979"></a>uint32_t</td>
<td class="fieldname">
page_layout: 1</td>
<td class="fielddoc">
Page layout: 0=|Data0|Spare0|...|DataN-1|SpareN-1|, 1=|Data0|...|DataN-1|Spare0|...|SpareN-1|. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a9dd3e47e968a8f6beb5d88c6d1b7ebe9"></a>uint32_t</td>
<td class="fieldname">
page_size: 4</td>
<td class="fielddoc">
Virtual Page size (Data+Spare): 0=512+16, 1=1k+32, 2=2k+64, 3=4k+128, 4=8k+256, 8=512+28, 9=1k+56, 10=2k+112, 11=4k+224, 12=8k+448. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="aa43c4c21b173ada1b6b7568956f0d650"></a>uint32_t</td>
<td class="fieldname">
reserved: 14</td>
<td class="fielddoc">
Reserved (must be zero) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ad44b615021ed3ccb734fcaf583ef4a03"></a>uint32_t</td>
<td class="fieldname">
type: 2</td>
<td class="fielddoc">
Type: 1=ECC0 over Data, 2=ECC0 over Data+Spare, 3=ECC0 over Data and ECC1 over Spare. </td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="ga09f4cf2f2df0bb690bce38b13d77e50f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ARM_NAND_SignalEvent_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a> : Signal NAND Event. </p>
<p>Provides the typedef for the callback function <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a>.</p>
<p><b>Parameter for:</b></p>
<ul>
<li><a class="el" href="group__nand__interface__gr.html#ga74ad34718a595e7a4375b90f33e72750">ARM_NAND_Initialize</a> </li>
</ul>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga00832861f018db0d8368900b099ecd30"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_AbortSequence </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Abort sequence execution. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Aborts execution of the current sequence for a NAND device. The parameter <em>dev_num</em> is the device number. </p>

</div>
</div>
<a class="anchor" id="ga1c0cba87cb7b706ad5986dc67c831ad1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_ChipEnable </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enable</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control CEn (Chip Enable). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">enable</td><td><ul>
<li><b>false</b> Chip Enable off</li>
<li><b>true</b> Chip Enable on </li>
</ul>
</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Controls the Chip Enable (CEn) pin of a NAND device in order to enable or disable the device. The parameter <em>dev_num</em> is the device number. The boolean parameter <em>enable</em> specifies to enables or disable the device.</p>
<p>This function is optional and supported only when reported by <em>ce_manual</em> flag in <a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a>. Otherwise the Chip Enable (CEn) signal is controlled automatically by SendCommand/Address, Read/WriteData and ExecuteSequence (for example when the NAND device is connected to a memory bus). </p>

</div>
</div>
<a class="anchor" id="ga83061d6d53ffb148853efbc87a864607"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_Control </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>control</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>arg</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control NAND Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">control</td><td>Operation </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">arg</td><td>Argument of operation </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Controls the NAND interface settings.</p>
<p>The parameter <em>dev_num</em> is the device number. The parameter <em>control</em> specifies various settings (see tables below). Depending on the control bits, the parameter <em>arg</em> provides additional information.</p>
<p>The table lists values for the parameter <em>control</em>.</p>
<table class="doxtable">
<tr>
<th align="left">Mode Control Bits </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__nand__control__codes.html#ga9b063c3078e86b50d4aa892518b2e2d8">ARM_NAND_BUS_MODE</a> </td><td align="left">Set the bus mode. Specify the value with the parameter <em>arg</em> as defined in the table <b><a class="el" href="group__nand__interface__gr.html#bus_mode_table_a">Bus Mode</a></b>. </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__control__codes.html#ga2d3356f5b47871c465ae7136a2c533f4">ARM_NAND_BUS_DATA_WIDTH</a> </td><td align="left">Set the data bus width. Specify the value with the parameter <em>arg</em> as defined in the table <b><a class="el" href="group__nand__interface__gr.html#data_bus_width_table_a">Data Bus Width</a></b>. </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__control__codes.html#ga5d1d46198404fe115b013bdae7af2a2f">ARM_NAND_DRIVER_STRENGTH</a> </td><td align="left">Set the driver strength. Specify the value with the parameter <em>arg</em> as defined in the table <b><a class="el" href="group__nand__interface__gr.html#driver_strength_table_a">Driver Strength</a></b>. </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__control__codes.html#ga1bffc9f341e704ee0e845d86a2989921">ARM_NAND_DEVICE_READY_EVENT</a> </td><td align="left">Generate <a class="el" href="group___n_a_n_d__events.html#gae0be7e1b41188def905de0a1568d442d">ARM_NAND_EVENT_DEVICE_READY</a>; arg = [<span class="XML-Token">0:disabled (default); 1:enabled</span>]. </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__control__codes.html#gaab6dea1b565aeb53e360876a4e50783c">ARM_NAND_DRIVER_READY_EVENT</a> </td><td align="left">Generate <a class="el" href="group___n_a_n_d__events.html#ga7b390a906db42c5ea4db38e0e85bb9e9">ARM_NAND_EVENT_DRIVER_READY</a>; arg = [<span class="XML-Token">0:disabled (default); 1:enabled</span>]. </td></tr>
</table>
<p><a class="anchor" id="bus_mode_table_a"></a><b>ARM_NAND_BUS_xxx</b> specifies the bus mode (ONFI - Open NAND Flash Interface).</p>
<table class="doxtable">
<tr>
<th align="left">Control Bits: Bus Mode </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#gac7743aeb6411b97f9fc6a24b556f4963">ARM_NAND_BUS_SDR</a> </td><td align="left">Set the Data Interface: SDR (Single Data Rate) - Traditional interface (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga82b8261b3d0d85881535adada318a7df">ARM_NAND_BUS_DDR</a> </td><td align="left">Set the Data Interface: NV-DDR (Double Data Rate) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga13c102201d6021db184a2f068656c518">ARM_NAND_BUS_DDR2</a> </td><td align="left">Set the Data Interface: NV-DDR2 (Double Data Rate) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga971e574ac412bbba445055e9afc384ba">ARM_NAND_BUS_TIMING_MODE_0</a> </td><td align="left">Set the Timing Mode 0 (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga475a339e929eca46e11bc8a7b330aa45">ARM_NAND_BUS_TIMING_MODE_1</a> </td><td align="left">Set the Timing Mode 1 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#gaed6154fb03b5516faf0bfd11d7a46309">ARM_NAND_BUS_TIMING_MODE_2</a> </td><td align="left">Set the Timing Mode 2 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#gacbc4e07e1af6ef0e4c656428e81464a9">ARM_NAND_BUS_TIMING_MODE_3</a> </td><td align="left">Set the Timing Mode 3 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga709d51a5215cd23ce2d85aec57141456">ARM_NAND_BUS_TIMING_MODE_4</a> </td><td align="left">Set the Timing Mode 4 (SDR EDO capable) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#gaee3cad14ce2b8b9af69149bf74597791">ARM_NAND_BUS_TIMING_MODE_5</a> </td><td align="left">Set the Timing Mode 5 (SDR EDO capable) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga4a3524e0eba994b3a66e06cde877f0f6">ARM_NAND_BUS_TIMING_MODE_6</a> </td><td align="left">Set the Timing Mode 6 (NV-DDR2 only) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#gaa63d75f5f2b48a7345a066d58de1bd23">ARM_NAND_BUS_TIMING_MODE_7</a> </td><td align="left">Set the Timing Mode 7 (NV-DDR2 only) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga77348df5f5c2c96bcaeec60b6da02c1b">ARM_NAND_BUS_DDR2_DO_WCYC_0</a> </td><td align="left">Set the DDR2 Data Output Warmup cycles: 0 (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga5839be0b4b2eb930ec039a3403b5e89e">ARM_NAND_BUS_DDR2_DO_WCYC_1</a> </td><td align="left">Set the DDR2 Data Output Warmup cycles: 1 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga10a1ef3be69bfa7e6cc657bee751a077">ARM_NAND_BUS_DDR2_DO_WCYC_2</a> </td><td align="left">Set the DDR2 Data Output Warmup cycles: 2 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga7f9e8416c4a4e20c4a04323e39f2100d">ARM_NAND_BUS_DDR2_DO_WCYC_4</a> </td><td align="left">Set the DDR2 Data Output Warmup cycles: 4 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#gaeee1853dea5e96cb19d2596cc0e70169">ARM_NAND_BUS_DDR2_DI_WCYC_0</a> </td><td align="left">Set the DDR2 Data Input Warmup cycles: 0 (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga42560a1f046e20cc4956276156c4ce25">ARM_NAND_BUS_DDR2_DI_WCYC_1</a> </td><td align="left">Set the DDR2 Data Input Warmup cycles: 1 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#gaad2e7807292d84a5070143626f5c2756">ARM_NAND_BUS_DDR2_DI_WCYC_2</a> </td><td align="left">Set the DDR2 Data Input Warmup cycles: 2 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga3ebb54a1ae971cd34f3c8fc9ff3ab6d5">ARM_NAND_BUS_DDR2_DI_WCYC_4</a> </td><td align="left">Set the DDR2 Data Input Warmup cycles: 4 </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga465ae06a6e097959620346304182e273">ARM_NAND_BUS_DDR2_VEN</a> </td><td align="left">Set the DDR2 Enable external VREFQ as reference </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#gad38354e4a34adbf881afc7f89ff06e89">ARM_NAND_BUS_DDR2_CMPD</a> </td><td align="left">Set the DDR2 Enable complementary DQS (DQS_c) signal </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__bus__mode__codes.html#ga8a2d599082b9fe56cee1c6454bb3c6a1">ARM_NAND_BUS_DDR2_CMPR</a> </td><td align="left">Set the DDR2 Enable complementary RE_n (RE_c) signal </td></tr>
</table>
<p><a class="anchor" id="data_bus_width_table_a"></a><b>ARM_NAND_BUS_DATA_WIDTH_xx</b> specifies the bus data width.</p>
<table class="doxtable">
<tr>
<th align="left">Control Bits: Data Bus Width </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__nand__data__bus__width__codes.html#ga578051cc193ae0b7125aec8007071d21">ARM_NAND_BUS_DATA_WIDTH_8</a> </td><td align="left">Set the Bus Data Width: 8 bit (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__data__bus__width__codes.html#ga49e0e3a946a4d9f26dbd5b32ccc3b2f3">ARM_NAND_BUS_DATA_WIDTH_16</a> </td><td align="left">Set the Bus Data Width: 16 bit </td></tr>
</table>
<p><a class="anchor" id="driver_strength_table_a"></a><b>ARM_NAND_DRIVER_STRENGTH_xx</b> specifies the driver strength (ONFI - Open NAND Flash Interface).</p>
<table class="doxtable">
<tr>
<th align="left">Control Bits: Driver Strength </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__strength__codes.html#ga942e20df12022f3bbd0e9a558ec1c7a0">ARM_NAND_DRIVER_STRENGTH_18</a> </td><td align="left">Set the Driver Strength 2.0x = 18 Ohms </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__strength__codes.html#ga17188e039f5f87c581033327399a057d">ARM_NAND_DRIVER_STRENGTH_25</a> </td><td align="left">Set the Driver Strength 1.4x = 25 Ohms </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__strength__codes.html#ga33562a66a5bf328eea82b2f1893a7874">ARM_NAND_DRIVER_STRENGTH_35</a> </td><td align="left">Set the Driver Strength 1.0x = 35 Ohms (default) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__strength__codes.html#gaa502e2c995447037d266f939faa43223">ARM_NAND_DRIVER_STRENGTH_50</a> </td><td align="left">Set the Driver Strength 0.7x = 50 Ohms </td></tr>
</table>

</div>
</div>
<a class="anchor" id="ga11adcbaaace09746581a36befbd563c9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_DevicePower </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>voltage</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set device power supply voltage. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">voltage</td><td>NAND Device supply voltage </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Controls power supply of the NAND device.</p>
<p>The parameter <em>voltage</em> sets the device supply voltage as defined in the table.</p>
<p><b>AMR_NAND_POWER_xxx_xxx</b> specifies power settings.</p>
<table class="doxtable">
<tr>
<th align="left">Device Power Bits </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="_driver___n_a_n_d_8h.html#a323c320a6195b78c2c79f5c6e85f02e1">ARM_NAND_POWER_VCC_OFF</a> </td><td align="left">Set VCC Power off </td></tr>
<tr>
<td align="left"><a class="el" href="_driver___n_a_n_d_8h.html#ad15355d67bc239ff49cceac69c2024b3">ARM_NAND_POWER_VCC_3V3</a> </td><td align="left">Set VCC = 3.3V </td></tr>
<tr>
<td align="left"><a class="el" href="_driver___n_a_n_d_8h.html#aa7b9d5a71125b745caba5c1d7aff6385">ARM_NAND_POWER_VCC_1V8</a> </td><td align="left">Set VCC = 1.8V </td></tr>
<tr>
<td align="left"><a class="el" href="_driver___n_a_n_d_8h.html#aca7679e8269ee986559f4218816937c3">ARM_NAND_POWER_VCCQ_OFF</a> </td><td align="left">Set VCCQ I/O Power off </td></tr>
<tr>
<td align="left"><a class="el" href="_driver___n_a_n_d_8h.html#a6d5a8a33a0fdaaff2e57e1ac53c984c2">ARM_NAND_POWER_VCCQ_3V3</a> </td><td align="left">Set VCCQ = 3.3V </td></tr>
<tr>
<td align="left"><a class="el" href="_driver___n_a_n_d_8h.html#a653d9b4d7bee173beb49d8fec0469476">ARM_NAND_POWER_VCCQ_1V8</a> </td><td align="left">Set VCCQ = 1.8V </td></tr>
<tr>
<td align="left"><a class="el" href="_driver___n_a_n_d_8h.html#ae2d278901881ffc73d3e0b48717b22f0">ARM_NAND_POWER_VPP_OFF</a> </td><td align="left">Set VPP off </td></tr>
<tr>
<td align="left"><a class="el" href="_driver___n_a_n_d_8h.html#aeb0d50e30bbcd8ab59c3b78db634aad5">ARM_NAND_POWER_VPP_ON</a> </td><td align="left">Set VPP on </td></tr>
</table>

</div>
</div>
<a class="anchor" id="ga8a0108dba757a4610475151144b52825"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_ExecuteSequence </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>code</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>cmd</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>addr_col</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>addr_row</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>data_cnt</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *&#160;</td>
          <td class="paramname"><em>status</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&#160;</td>
          <td class="paramname"><em>count</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Execute sequence of operations. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">code</td><td>Sequence code </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cmd</td><td>Command(s) </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">addr_col</td><td>Column address </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">addr_row</td><td>Row address </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">data</td><td>Pointer to data to be written or read </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">data_cnt</td><td>Number of data items in one iteration </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">status</td><td>Pointer to status read </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">count</td><td>Number of iterations </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Execute a sequence of operations for a NAND device. The parameter <em>dev_num</em> is the device number. The parameter <em>code</em> is the sequence encoding as defined in the table <b>Sequence execution Code</b>. The parameter <em>cmd</em> is the command or a series of commands The parameter <em>addr_col</em> is the column address. The parameter <em>addr_row</em> is the row address. The parameter <em>data</em> is a pointer to the buffer that stores the data to or loads the data from. The parameter <em>data_cnt</em> is the number of data items to read or write in one iteration. The parameter <em>status</em> is a pointer to the buffer that stores the status read. The parameter <em>count</em> is a pointer to the number of iterations.</p>
<p><b>ARM_NAND_CODE_xxx</b> specifies sequence execution codes.</p>
<table class="doxtable">
<tr>
<th align="left">Sequence Execution Code </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#gaef90c96cd4f2309044d7d438c6b0930a">ARM_NAND_CODE_SEND_CMD1</a> </td><td align="left">Send Command 1 (cmd[7..0]) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#ga891bcba60ebb1195ec80c00c9bec748a">ARM_NAND_CODE_SEND_ADDR_COL1</a> </td><td align="left">Send Column Address 1 (addr_col[7..0]) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#ga62a3f6ddcfb9ee317655bbec9e09bc10">ARM_NAND_CODE_SEND_ADDR_COL2</a> </td><td align="left">Send Column Address 2 (addr_col[15..8]) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#gadc001e69d1e81dc28a542237c6fe11ff">ARM_NAND_CODE_SEND_ADDR_ROW1</a> </td><td align="left">Send Row Address 1 (addr_row[7..0]) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#ga5e55628cb59f5d7d35c529f04ebfcd10">ARM_NAND_CODE_SEND_ADDR_ROW2</a> </td><td align="left">Send Row Address 2 (addr_row[15..8]) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#gaeb5d1be9c13b7ad2ad246d5db10cd419">ARM_NAND_CODE_SEND_ADDR_ROW3</a> </td><td align="left">Send Row Address 3 (addr_row[23..16]) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#ga959522c98183036da32984dd5e07979b">ARM_NAND_CODE_INC_ADDR_ROW</a> </td><td align="left">Auto-increment Row Address </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#ga1b40fc5fbf22dc4fa8130f5836e30d12">ARM_NAND_CODE_WRITE_DATA</a> </td><td align="left">Write Data </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#gacffafbbbca74f7ffa4cd3bb6b067c4ef">ARM_NAND_CODE_SEND_CMD2</a> </td><td align="left">Send Command 2 (cmd[15..8]) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#ga0f4a8b1e97656e09f1c383852f290a37">ARM_NAND_CODE_WAIT_BUSY</a> </td><td align="left">Wait while R/Bn busy </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#gab524d840ab57c720ce8560144651dc9d">ARM_NAND_CODE_READ_DATA</a> </td><td align="left">Read Data </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#ga20f96743ab77bda14ba391dc0c3cdba5">ARM_NAND_CODE_SEND_CMD3</a> </td><td align="left">Send Command 3 (cmd[23..16]) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__seq__exec__codes.html#ga2250f6a532d2c0834bfdc618761ddc86">ARM_NAND_CODE_READ_STATUS</a> </td><td align="left">Read Status byte and check FAIL bit (bit 0) </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#gac2eb4475f12a443209165d29fe200030">ARM_NAND_ECC(n)</a> </td><td align="left">Select ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#ga15c79a12200c16f953936635f930df1d">ARM_NAND_ECC0</a> </td><td align="left">Use ECC0 of selected ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#gaee653288a88318ee33d1db81baa69bbc">ARM_NAND_ECC1</a> </td><td align="left">Use ECC1 of selected ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__flag__codes.html#gaf40631ba62411e0ac06c3a945d608581">ARM_NAND_DRIVER_DONE_EVENT</a> </td><td align="left">Generate <a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">ARM_NAND_EVENT_DRIVER_DONE</a> </td></tr>
</table>
<p>The data item size is defined by the data type which depends on the configured data bus width.</p>
<p>Data type is:</p>
<ul>
<li><em>uint8_t</em> for 8-bit data bus</li>
<li><em>uint16_t</em> for 16-bit data bus</li>
</ul>
<p>The function is non-blocking and returns as soon as the driver has started executing the specified sequence. When the operation is completed the <a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">ARM_NAND_EVENT_DRIVER_DONE</a> event is generated (if enabled by <b>ARM_NAND_DRIVER_DONE_EVENT</b>). Progress of the operation can also be monitored by calling the <a class="el" href="group__nand__interface__gr.html#ga4578642f37a556b58b0bba0ad5d42641">ARM_NAND_GetStatus</a> function and checking the <em>busy</em> flag.</p>
<p>Driver executes the number of specified iterations where in each iteration items specified by <b>ARM_NAND_CODE_xxx</b> are executed in the order as listed in the table <b>Sequence execution Code</b>. The parameter <em>count</em> is holding the current number of iterations left.</p>
<p>Execution is automatically aborted and <a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">ARM_NAND_EVENT_DRIVER_DONE</a> event is generated (if enabled by <b>ARM_NAND_DRIVER_DONE_EVENT</b>):</p>
<ul>
<li>if Read Status is enabled and the FAIL bit (bit 0) is set</li>
<li>if ECC is used and ECC fails (also sets <a class="el" href="group___n_a_n_d__events.html#ga7bee0c32528ab991c0c064f895f80664">ARM_NAND_EVENT_ECC_ERROR</a> event)</li>
</ul>
<dl class="section note"><dt>Note</dt><dd><b>ARM_NAND_CODE_WAIT_BUSY</b> can only be specified if Device Ready event can be generated (reported by <em>event_device_ready</em> in <a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a>). The event <a class="el" href="group___n_a_n_d__events.html#gae0be7e1b41188def905de0a1568d442d">ARM_NAND_EVENT_DEVICE_READY</a> is not generated during sequence execution but rather used internally by the driver. </dd></dl>

</div>
</div>
<a class="anchor" id="ga9f2609975c2008d21b9ae28f15daf147"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a> ARM_NAND_GetCapabilities </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver capabilities. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a></dd></dl>
<p>Retrieves information about capabilities in this driver implementation. The bitfield members of the struct <a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a> encode various capabilities, for example if a hardware is able to create signal events using the <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a> callback function.</p>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d" title="Access structure of the NAND Driver.">ARM_DRIVER_NAND</a> Driver_NAND0;</div>
<div class="line"><a class="code" href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d" title="Access structure of the NAND Driver.">ARM_DRIVER_NAND</a> *drv_info;</div>
<div class="line">  </div>
<div class="line"><span class="keywordtype">void</span> read_capabilities (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s" title="NAND Driver Capabilities.">ARM_NAND_CAPABILITIES</a> drv_capabilities;</div>
<div class="line"> </div>
<div class="line">  drv_info = &amp;Driver_NAND0;  </div>
<div class="line">  drv_capabilities = drv_info-&gt;<a class="code" href="group__nand__interface__gr.html#adab9d081aee3e5d1f83c6911e45ceaa6" title="Pointer to ARM_NAND_GetCapabilities : Get driver capabilities.">GetCapabilities</a> ();</div>
<div class="line">  <span class="comment">// interrogate capabilities</span></div>
<div class="line"> </div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="ga43011066306bd716b580e6aa9a80cf65"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_GetDeviceBusy </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get Device Busy pin state. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>1=busy, 0=not busy, or error</dd></dl>
<p>Retrieve the Device Busy pin state of a NAND device. The parameter <em>dev_num</em> is the device number.</p>
<p>The function returns the status of the Device Busy pin: [<span class="XML-Token">1=busy; 0=not busy or error</span>]. </p>

</div>
</div>
<a class="anchor" id="ga4578642f37a556b58b0bba0ad5d42641"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s">ARM_NAND_STATUS</a> ARM_NAND_GetStatus </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get NAND status. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NAND status <a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s">ARM_NAND_STATUS</a></dd></dl>
<p>Retrieve the current NAND interface status. The parameter <em>dev_num</em> is the device number. </p>

</div>
</div>
<a class="anchor" id="ga01255fd4f15e7fa4751c7ea59648ef5a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a> ARM_NAND_GetVersion </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver version. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></dd></dl>
<p>Returns version information of the driver implementation in <a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></p>
<ul>
<li>API version is the version of the CMSIS-Driver specification used to implement this driver.</li>
<li>Driver version is source code version of the actual driver implementation.</li>
</ul>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d" title="Access structure of the NAND Driver.">ARM_DRIVER_NAND</a> Driver_NAND0;</div>
<div class="line"><a class="code" href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d" title="Access structure of the NAND Driver.">ARM_DRIVER_NAND</a> *drv_info;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> setup_nand (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n" title="Driver Version.">ARM_DRIVER_VERSION</a>  version;</div>
<div class="line"> </div>
<div class="line">  drv_info = &amp;Driver_NAND0;  </div>
<div class="line">  version = drv_info-&gt;<a class="code" href="group__nand__interface__gr.html#a8834b281da48583845c044a81566c1b3" title="Pointer to ARM_NAND_GetVersion : Get driver version.">GetVersion</a> ();</div>
<div class="line">  <span class="keywordflow">if</span> (version.<a class="code" href="group__common__drv__gr.html#ad180da20fbde1d3dafc074af87c19540" title="API version.">api</a> &lt; 0x10A)   {      <span class="comment">// requires at minimum API version 1.10 or higher</span></div>
<div class="line">    <span class="comment">// error handling</span></div>
<div class="line">    <span class="keywordflow">return</span>;</div>
<div class="line">  }</div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="ga74ad34718a595e7a4375b90f33e72750"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_Initialize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__nand__interface__gr.html#ga09f4cf2f2df0bb690bce38b13d77e50f">ARM_NAND_SignalEvent_t</a>&#160;</td>
          <td class="paramname"><em>cb_event</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize the NAND Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">cb_event</td><td>Pointer to <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function initializes the NAND interface. It is called when the middleware component starts operation.</p>
<p>The function performs the following operations:</p>
<ul>
<li>Initializes the resources needed for the NAND interface.</li>
<li>Registers the <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a> callback function.</li>
</ul>
<p>The parameter <em>cb_event</em> is a pointer to the <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">ARM_NAND_SignalEvent</a> callback function; use a NULL pointer when no callback signals are required.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__nand__interface__gr.html">NAND Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="gac21425454d586ef48fdfc35e7bd78947"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_InquireECC </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>index</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o">ARM_NAND_ECC_INFO</a> *&#160;</td>
          <td class="paramname"><em>info</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Inquire about available ECC. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">index</td><td>Device number </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">info</td><td>Pointer to ECC information <a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o">ARM_NAND_ECC_INFO</a> retrieved </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Retrieve information about the ECC referenced by index. </p>

</div>
</div>
<a class="anchor" id="ga9c9975637980b5d42db7baba0191fda1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_PowerControl </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a>&#160;</td>
          <td class="paramname"><em>state</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control the NAND interface power. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">state</td><td>Power state </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Allows you to control the power modes of the NAND interface. </p>

</div>
</div>
<a class="anchor" id="gae1899a20ef107400c8bf84fad477a8ce"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_ReadData </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>cnt</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>mode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Read data from NAND device. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">data</td><td>Pointer to buffer for data to read from NAND device </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cnt</td><td>Number of data items to read </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mode</td><td>Operation mode </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>number of data items read or <a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Read data from a NAND device. The parameter <em>dev_num</em> is the device number. The parameter <em>data</em> is a pointer to the buffer that stores the data read from a NAND device. The parameter <em>cnt</em> is the number of data items to read. The parameter <em>mode</em> defines the operation mode as listed in the table below.</p>
<table class="doxtable">
<tr>
<th align="left">Read Data Mode </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#gac2eb4475f12a443209165d29fe200030">ARM_NAND_ECC(n)</a> </td><td align="left">Select ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#ga15c79a12200c16f953936635f930df1d">ARM_NAND_ECC0</a> </td><td align="left">Use ECC0 of selected ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#gaee653288a88318ee33d1db81baa69bbc">ARM_NAND_ECC1</a> </td><td align="left">Use ECC1 of selected ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__flag__codes.html#gaf40631ba62411e0ac06c3a945d608581">ARM_NAND_DRIVER_DONE_EVENT</a> </td><td align="left">Generate <a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">ARM_NAND_EVENT_DRIVER_DONE</a> </td></tr>
</table>
<p>The data item size is defined by the data type which depends on the configured data bus width.</p>
<p>Data type is:</p>
<ul>
<li><em>uint8_t</em> for 8-bit data bus</li>
<li><em>uint16_t</em> for 16-bit data bus</li>
</ul>
<p>The function executes in the following ways:</p>
<ul>
<li>When the operation is blocking (typical for devices connected to memory bus when not using DMA) then the function returns after all data is read and returns the number of data items read.</li>
<li>When the operation is non-blocking (typical for NAND controllers) then the function only starts the operation and returns with zero number of data items read. After the operation is completed the <a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">ARM_NAND_EVENT_DRIVER_DONE</a> event is generated (if enabled by <b>ARM_NAND_DRIVER_DONE_EVENT</b>). Progress of the operation can also be monitored by calling the <a class="el" href="group__nand__interface__gr.html#ga4578642f37a556b58b0bba0ad5d42641">ARM_NAND_GetStatus</a> function and checking the <em>busy</em> flag. Operation is automatically aborted if ECC is used and ECC correction fails which generates the <a class="el" href="group___n_a_n_d__events.html#ga7bee0c32528ab991c0c064f895f80664">ARM_NAND_EVENT_ECC_ERROR</a> event (together with <a class="el" href="group__nand__driver__flag__codes.html#gaf40631ba62411e0ac06c3a945d608581">ARM_NAND_DRIVER_DONE_EVENT</a> if enabled). </li>
</ul>

</div>
</div>
<a class="anchor" id="ga00e195031e03d364db7595858a7e76f3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_SendAddress </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>addr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send address to NAND device. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">addr</td><td>Address </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Send an address to the NAND device. The parameter <em>dev_num</em> is the device number. The parameter <em>addr</em> is the address. </p>

</div>
</div>
<a class="anchor" id="ga9f70b89ba478eadfe7f5dee7453a4fb7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_SendCommand </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>cmd</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send command to NAND device. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cmd</td><td>Command </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Send a command to the NAND device. The parameter <em>dev_num</em> is the device number. The parameter <em>cmd</em> is the command sent to the NAND device. </p>

</div>
</div>
<a class="anchor" id="gaf4ce80b0fd6717de7ddfb1cfaf7dd754"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void ARM_NAND_SignalEvent </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>event</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signal NAND event. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">event</td><td>Event notification mask </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p>The function <b>ARM_NAND_SignalEvent</b> notifies the application of the <a class="el" href="group___n_a_n_d__events.html">NAND Events</a> and it is registered by the function <a class="el" href="group__nand__interface__gr.html#ga74ad34718a595e7a4375b90f33e72750">ARM_NAND_Initialize</a>. The function <a class="el" href="group__nand__interface__gr.html#ga9f2609975c2008d21b9ae28f15daf147">ARM_NAND_GetCapabilities</a> returns information about the implemented optional events in a driver.</p>
<p>The parameter <em>dev_num</em> is the device number. The argument <em>event</em> represents the notification mask of the events. Each event is coded in a separate bit and therefore it is possible to signal multiple events in the event call back function. The following call back notifications are generated:</p>
<table class="doxtable">
<tr>
<th>Bit </th><th align="left">Event </th><th align="left">Description</th></tr>
<tr>
<td>0 </td><td align="left"><a class="el" href="group___n_a_n_d__events.html#gae0be7e1b41188def905de0a1568d442d">ARM_NAND_EVENT_DEVICE_READY</a> </td><td align="left">Occurs when rising edge is detected on R/Bn (Ready/Busy) pin indicating that the device is ready. </td></tr>
<tr>
<td>1 </td><td align="left"><a class="el" href="group___n_a_n_d__events.html#ga7b390a906db42c5ea4db38e0e85bb9e9">ARM_NAND_EVENT_DRIVER_READY</a> </td><td align="left">Occurs when SendCommand/Address, Read/WriteData or ExecuteSequence indicate that they can be executed (after previously being busy and not able to start the requested operation). </td></tr>
<tr>
<td>2 </td><td align="left"><a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">ARM_NAND_EVENT_DRIVER_DONE</a> </td><td align="left">Occurs after Read/WriteData or ExecuteSequence operation has finished (after being successfully started). </td></tr>
<tr>
<td>3 </td><td align="left"><a class="el" href="group___n_a_n_d__events.html#ga7bee0c32528ab991c0c064f895f80664">ARM_NAND_EVENT_ECC_ERROR</a> </td><td align="left">Occurs after Read/WriteData or ExecuteSequence (together with <b>ARM_NAND_EVENT_DRIVER_DONE</b>) when ECC generation or correction failed. </td></tr>
</table>

</div>
</div>
<a class="anchor" id="gaa788b638ab696b166fee2f4a4bc8d97a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_Uninitialize </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>De-initialize the NAND Interface. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__nand__interface__gr.html#gaa788b638ab696b166fee2f4a4bc8d97a">ARM_NAND_Uninitialize</a> de-initializes the resources of NAND interface.</p>
<p>It is called when the middleware component stops operation and releases the software resources used by the interface. </p>

</div>
</div>
<a class="anchor" id="ga1fa497dd51a86fc308e946b4419fd006"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_WriteData </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>cnt</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>mode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Write data to NAND device. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">data</td><td>Pointer to buffer with data to write to NAND device </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cnt</td><td>Number of data items to write </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mode</td><td>Operation mode </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>number of data items written or <a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Write data to a NAND device. The parameter <em>dev_num</em> is the device number. The parameter <em>data</em> is a pointer to the buffer with data to write. The parameter <em>cnt</em> is the number of data items to write. The parameter <em>mode</em> defines the operation mode as listed in the table below.</p>
<table class="doxtable">
<tr>
<th align="left">Write Data Mode </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#gac2eb4475f12a443209165d29fe200030">ARM_NAND_ECC(n)</a> </td><td align="left">Select ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#ga15c79a12200c16f953936635f930df1d">ARM_NAND_ECC0</a> </td><td align="left">Use ECC0 of selected ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__ecc__codes.html#gaee653288a88318ee33d1db81baa69bbc">ARM_NAND_ECC1</a> </td><td align="left">Use ECC1 of selected ECC </td></tr>
<tr>
<td align="left"><a class="el" href="group__nand__driver__flag__codes.html#gaf40631ba62411e0ac06c3a945d608581">ARM_NAND_DRIVER_DONE_EVENT</a> </td><td align="left">Generate <a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">ARM_NAND_EVENT_DRIVER_DONE</a> </td></tr>
</table>
<p>The data item size is defined by the data type which depends on the configured data bus width.</p>
<p>Data type is:</p>
<ul>
<li><em>uint8_t</em> for 8-bit data bus</li>
<li><em>uint16_t</em> for 16-bit data bus</li>
</ul>
<p>The function executes in the following ways:</p>
<ul>
<li>When the operation is blocking (typical for devices connected to memory bus when not using DMA) then the function returns after all data is written and returns the number of data items written.</li>
<li>When the operation is non-blocking (typical for NAND controllers) then the function only starts the operation and returns with zero number of data items written. After the operation is completed the <a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">ARM_NAND_EVENT_DRIVER_DONE</a> event is generated (if enabled by <b>ARM_NAND_DRIVER_DONE_EVENT</b>). Progress of the operation can also be monitored by calling the <a class="el" href="group__nand__interface__gr.html#ga4578642f37a556b58b0bba0ad5d42641">ARM_NAND_GetStatus</a> function and checking the <em>busy</em> flag. Operation is automatically aborted if ECC is used and ECC generation fails which generates the <a class="el" href="group___n_a_n_d__events.html#ga7bee0c32528ab991c0c064f895f80664">ARM_NAND_EVENT_ECC_ERROR</a> event (together with <a class="el" href="group__nand__driver__flag__codes.html#gaf40631ba62411e0ac06c3a945d608581">ARM_NAND_DRIVER_DONE_EVENT</a> if enabled). </li>
</ul>

</div>
</div>
<a class="anchor" id="ga1987e65a4e756d748db86332c9fb1cec"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_NAND_WriteProtect </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>dev_num</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enable</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control WPn (Write Protect). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">dev_num</td><td>Device number </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">enable</td><td><ul>
<li><b>false</b> Write Protect off</li>
<li><b>true</b> Write Protect on </li>
</ul>
</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Control the Write Protect (WPn) pin of a NAND device in order to enable or disable write protection. The parameter <em>dev_num</em> is the device number. The boolean parameter <em>enable</em> specifies to enables or disables write protection. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
