<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_matrix_example_f32.c</title>
<title>CMSIS-DSP: arm_matrix_example_f32.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_matrix_example_f32_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_matrix_example_f32.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* $Date:         17. January 2013</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.0</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment">* Title:         arm_matrix_example_f32.c</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Description:   Example code demonstrating least square fit to data</span></div>
<div class="line"><span class="comment">*                using matrix functions</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment"> * -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;math_helper.h&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#define SNR_THRESHOLD   90</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* --------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Test input data(Cycles) taken from FIR Q15 module for differant cases of blockSize</span></div>
<div class="line"><span class="comment">* and tapSize</span></div>
<div class="line"><span class="comment">* --------------------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="arm__matrix__example__f32_8c.html#a974d5f0aace6a99e29ca767907fb3b9f">B_f32</a>[4] =</div>
<div class="line">{</div>
<div class="line">  782.0, 7577.0, 470.0, 4505.0</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><span class="comment">/* --------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Formula to fit is  C1 + C2 * numTaps + C3 * blockSize + C4 * numTaps * blockSize</span></div>
<div class="line"><span class="comment">* -------------------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a1"></a><a class="code" href="arm__matrix__example__f32_8c.html#aed27b92d9847194d9dcce40cecf2b48a">A_f32</a>[16] =</div>
<div class="line">{</div>
<div class="line">  <span class="comment">/* Const,   numTaps,   blockSize,   numTaps*blockSize */</span></div>
<div class="line">  1.0,     32.0,      4.0,     128.0,</div>
<div class="line">  1.0,     32.0,     64.0,    2048.0,</div>
<div class="line">  1.0,     16.0,      4.0,      64.0,</div>
<div class="line">  1.0,     16.0,     64.0,    1024.0,</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Temporary buffers  for storing intermediate values</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="comment">/* Transpose of A Buffer */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a2"></a><a class="code" href="arm__matrix__example__f32_8c.html#a46dc2aa6dfc692af7b4a1379d7329ccd">AT_f32</a>[16];</div>
<div class="line"><span class="comment">/* (Transpose of A * A) Buffer */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a3"></a><a class="code" href="arm__matrix__example__f32_8c.html#a867497c6bf86014513bf2ad3551aa896">ATMA_f32</a>[16];</div>
<div class="line"><span class="comment">/* Inverse(Transpose of A * A)  Buffer */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a4"></a><a class="code" href="arm__matrix__example__f32_8c.html#a44425c149c52b326a3b7a77676686f00">ATMAI_f32</a>[16];</div>
<div class="line"><span class="comment">/* Test Output Buffer */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a5"></a><a class="code" href="arm__matrix__example__f32_8c.html#a98c67c0fc0cb5f2df51b21482d31d21c">X_f32</a>[4];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Reference ouput buffer C1, C2, C3 and C4 taken from MATLAB</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a6"></a><a class="code" href="arm__matrix__example__f32_8c.html#a6184758419722fa16bb883097c2f596b">xRef_f32</a>[4] = {73.0, 8.0, 21.25, 2.875};</div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a7"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a>;</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Max magnitude FFT Bin test</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">int32_t <a name="a8"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line"></div>
<div class="line">  <a name="_a9"></a><a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> A;      <span class="comment">/* Matrix A Instance */</span></div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> AT;     <span class="comment">/* Matrix AT(A transpose) instance */</span></div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> ATMA;   <span class="comment">/* Matrix ATMA( AT multiply with A) instance */</span></div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> ATMAI;  <span class="comment">/* Matrix ATMAI(Inverse of ATMA) instance */</span></div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> B;      <span class="comment">/* Matrix B instance */</span></div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> X;      <span class="comment">/* Matrix X(Unknown Matrix) instance */</span></div>
<div class="line"></div>
<div class="line">  uint32_t srcRows, srcColumns;  <span class="comment">/* Temporary variables */</span></div>
<div class="line">  <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a10"></a><a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>;</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialise A Matrix Instance with numRows, numCols and data array(A_f32) */</span></div>
<div class="line">  srcRows = 4;</div>
<div class="line">  srcColumns = 4;</div>
<div class="line">  <a name="a11"></a><a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;A, srcRows, srcColumns, (<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="arm__matrix__example__f32_8c.html#aed27b92d9847194d9dcce40cecf2b48a">A_f32</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialise Matrix Instance AT with numRows, numCols and data array(AT_f32) */</span></div>
<div class="line">  srcRows = 4;</div>
<div class="line">  srcColumns = 4;</div>
<div class="line">  <a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;AT, srcRows, srcColumns, <a class="code" href="arm__matrix__example__f32_8c.html#a46dc2aa6dfc692af7b4a1379d7329ccd">AT_f32</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* calculation of A transpose */</span></div>
<div class="line">  status = <a name="a12"></a><a class="code" href="group___matrix_trans.html#gad7dd9f108429da13d3864696ceeec789" title="Floating-point matrix transpose.">arm_mat_trans_f32</a>(&amp;A, &amp;AT);</div>
<div class="line"></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialise ATMA Matrix Instance with numRows, numCols and data array(ATMA_f32) */</span></div>
<div class="line">  srcRows = 4;</div>
<div class="line">  srcColumns = 4;</div>
<div class="line">  <a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;ATMA, srcRows, srcColumns, <a class="code" href="arm__matrix__example__f32_8c.html#a867497c6bf86014513bf2ad3551aa896">ATMA_f32</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* calculation of AT Multiply with A */</span></div>
<div class="line">  status = <a name="a13"></a><a class="code" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0" title="Floating-point matrix multiplication.">arm_mat_mult_f32</a>(&amp;AT, &amp;A, &amp;ATMA);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialise ATMAI Matrix Instance with numRows, numCols and data array(ATMAI_f32) */</span></div>
<div class="line">  srcRows = 4;</div>
<div class="line">  srcColumns = 4;</div>
<div class="line">  <a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;ATMAI, srcRows, srcColumns, <a class="code" href="arm__matrix__example__f32_8c.html#a44425c149c52b326a3b7a77676686f00">ATMAI_f32</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* calculation of Inverse((Transpose(A) * A) */</span></div>
<div class="line">  status = <a name="a14"></a><a class="code" href="group___matrix_inv.html#ga542be7aabbf7a2297a4b62cf212910e3" title="Floating-point matrix inverse.">arm_mat_inverse_f32</a>(&amp;ATMA, &amp;ATMAI);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* calculation of (Inverse((Transpose(A) * A)) *  Transpose(A)) */</span></div>
<div class="line">  status = <a class="code" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0" title="Floating-point matrix multiplication.">arm_mat_mult_f32</a>(&amp;ATMAI, &amp;AT, &amp;ATMA);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialise B Matrix Instance with numRows, numCols and data array(B_f32) */</span></div>
<div class="line">  srcRows = 4;</div>
<div class="line">  srcColumns = 1;</div>
<div class="line">  <a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;B, srcRows, srcColumns, (<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="arm__matrix__example__f32_8c.html#a974d5f0aace6a99e29ca767907fb3b9f">B_f32</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialise X Matrix Instance with numRows, numCols and data array(X_f32) */</span></div>
<div class="line">  srcRows = 4;</div>
<div class="line">  srcColumns = 1;</div>
<div class="line">  <a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;X, srcRows, srcColumns, <a class="code" href="arm__matrix__example__f32_8c.html#a98c67c0fc0cb5f2df51b21482d31d21c">X_f32</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* calculation ((Inverse((Transpose(A) * A)) *  Transpose(A)) * B) */</span></div>
<div class="line">  status = <a class="code" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0" title="Floating-point matrix multiplication.">arm_mat_mult_f32</a>(&amp;ATMA, &amp;B, &amp;X);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Comparison of reference with test output */</span></div>
<div class="line">  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a> = <a name="a15"></a><a class="code" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab" title="Caluclation of SNR.">arm_snr_f32</a>((<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="arm__matrix__example__f32_8c.html#a6184758419722fa16bb883097c2f596b">xRef_f32</a>, <a class="code" href="arm__matrix__example__f32_8c.html#a98c67c0fc0cb5f2df51b21482d31d21c">X_f32</a>, 4);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/*------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  *            Initialise status depending on SNR calculations</span></div>
<div class="line"><span class="comment">  *------------------------------------------------------------------------------*/</span></div>
<div class="line">  <span class="keywordflow">if</span>( <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a> &gt; <a name="a16"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af08ec3fef897d77c6817638bf0e0c5c6">SNR_THRESHOLD</a>)</div>
<div class="line">  {</div>
<div class="line">    status = <a name="a17"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>;</div>
<div class="line">  }</div>
<div class="line">  <span class="keywordflow">else</span></div>
<div class="line">  {</div>
<div class="line">    status = <a name="a18"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>;</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Loop here if the signals fail the PASS check.</span></div>
<div class="line"><span class="comment">  ** This denotes a test failure</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line">  <span class="keywordflow">if</span>( status != <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>)</div>
<div class="line">  {</div>
<div class="line">    <span class="keywordflow">while</span>(1);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
<div class="line"></div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
