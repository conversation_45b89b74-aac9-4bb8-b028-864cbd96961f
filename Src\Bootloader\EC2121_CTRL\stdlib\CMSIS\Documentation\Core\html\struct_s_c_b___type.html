<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>SCB_Type Struct Reference</title>
<title>CMSIS-CORE: SCB_Type Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('struct_s_c_b___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">SCB_Type Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Structure type to access the System Control Block (SCB).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:afa7a9ee34dfa1da0b60b4525da285032"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#afa7a9ee34dfa1da0b60b4525da285032">CPUID</a></td></tr>
<tr class="memdesc:afa7a9ee34dfa1da0b60b4525da285032"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 (R/ ) CPUID Base Register.  <a href="#afa7a9ee34dfa1da0b60b4525da285032"></a><br/></td></tr>
<tr class="separator:afa7a9ee34dfa1da0b60b4525da285032"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e66570ab689d28aebefa7e84e85dc4a"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a3e66570ab689d28aebefa7e84e85dc4a">ICSR</a></td></tr>
<tr class="memdesc:a3e66570ab689d28aebefa7e84e85dc4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x004 (R/W) Interrupt Control and State Register.  <a href="#a3e66570ab689d28aebefa7e84e85dc4a"></a><br/></td></tr>
<tr class="separator:a3e66570ab689d28aebefa7e84e85dc4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0faf96f964931cadfb71cfa54e051f6f"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a0faf96f964931cadfb71cfa54e051f6f">VTOR</a></td></tr>
<tr class="memdesc:a0faf96f964931cadfb71cfa54e051f6f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x008 (R/W) Vector Table Offset Register.  <a href="#a0faf96f964931cadfb71cfa54e051f6f"></a><br/></td></tr>
<tr class="separator:a0faf96f964931cadfb71cfa54e051f6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ed3c9064013343ea9fd0a73a734f29d"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a6ed3c9064013343ea9fd0a73a734f29d">AIRCR</a></td></tr>
<tr class="memdesc:a6ed3c9064013343ea9fd0a73a734f29d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x00C (R/W) Application Interrupt and Reset Control Register.  <a href="#a6ed3c9064013343ea9fd0a73a734f29d"></a><br/></td></tr>
<tr class="separator:a6ed3c9064013343ea9fd0a73a734f29d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfad14e7b4534d73d329819625d77a16"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#abfad14e7b4534d73d329819625d77a16">SCR</a></td></tr>
<tr class="memdesc:abfad14e7b4534d73d329819625d77a16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x010 (R/W) System Control Register.  <a href="#abfad14e7b4534d73d329819625d77a16"></a><br/></td></tr>
<tr class="separator:abfad14e7b4534d73d329819625d77a16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d273c6b90bad15c91dfbbad0f6e92d8"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a6d273c6b90bad15c91dfbbad0f6e92d8">CCR</a></td></tr>
<tr class="memdesc:a6d273c6b90bad15c91dfbbad0f6e92d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x014 (R/W) Configuration Control Register.  <a href="#a6d273c6b90bad15c91dfbbad0f6e92d8"></a><br/></td></tr>
<tr class="separator:a6d273c6b90bad15c91dfbbad0f6e92d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6336103f8be0cab29de51daed5a65f4"><td class="memItemLeft" align="right" valign="top">__IO uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#af6336103f8be0cab29de51daed5a65f4">SHP</a> [12]</td></tr>
<tr class="memdesc:af6336103f8be0cab29de51daed5a65f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x018 (R/W) System Handlers Priority Registers (4-7, 8-11, 12-15)  <a href="#af6336103f8be0cab29de51daed5a65f4"></a><br/></td></tr>
<tr class="separator:af6336103f8be0cab29de51daed5a65f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9891a59abbe51b0b2067ca507ca212f"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#ae9891a59abbe51b0b2067ca507ca212f">SHCSR</a></td></tr>
<tr class="memdesc:ae9891a59abbe51b0b2067ca507ca212f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x024 (R/W) System Handler Control and State Register.  <a href="#ae9891a59abbe51b0b2067ca507ca212f"></a><br/></td></tr>
<tr class="separator:ae9891a59abbe51b0b2067ca507ca212f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2f94bf549b16fdeb172352e22309e3c4"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a2f94bf549b16fdeb172352e22309e3c4">CFSR</a></td></tr>
<tr class="memdesc:a2f94bf549b16fdeb172352e22309e3c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x028 (R/W) Configurable Fault Status Register.  <a href="#a2f94bf549b16fdeb172352e22309e3c4"></a><br/></td></tr>
<tr class="separator:a2f94bf549b16fdeb172352e22309e3c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7bed53391da4f66d8a2a236a839d4c3d"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a7bed53391da4f66d8a2a236a839d4c3d">HFSR</a></td></tr>
<tr class="memdesc:a7bed53391da4f66d8a2a236a839d4c3d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x02C (R/W) HardFault Status Register.  <a href="#a7bed53391da4f66d8a2a236a839d4c3d"></a><br/></td></tr>
<tr class="separator:a7bed53391da4f66d8a2a236a839d4c3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7d61d9525fa9162579c3da0b87bff8d"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#ad7d61d9525fa9162579c3da0b87bff8d">DFSR</a></td></tr>
<tr class="memdesc:ad7d61d9525fa9162579c3da0b87bff8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x030 (R/W) Debug Fault Status Register.  <a href="#ad7d61d9525fa9162579c3da0b87bff8d"></a><br/></td></tr>
<tr class="separator:ad7d61d9525fa9162579c3da0b87bff8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac49b24b3f222508464f111772f2c44dd"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#ac49b24b3f222508464f111772f2c44dd">MMFAR</a></td></tr>
<tr class="memdesc:ac49b24b3f222508464f111772f2c44dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x034 (R/W) MemManage Fault Address Register.  <a href="#ac49b24b3f222508464f111772f2c44dd"></a><br/></td></tr>
<tr class="separator:ac49b24b3f222508464f111772f2c44dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f79afe86c949c9862e7d5fce077c3a"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a31f79afe86c949c9862e7d5fce077c3a">BFAR</a></td></tr>
<tr class="memdesc:a31f79afe86c949c9862e7d5fce077c3a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x038 (R/W) BusFault Address Register.  <a href="#a31f79afe86c949c9862e7d5fce077c3a"></a><br/></td></tr>
<tr class="separator:a31f79afe86c949c9862e7d5fce077c3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb77053c84f49c261ab5b8374e8958ef"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#aeb77053c84f49c261ab5b8374e8958ef">AFSR</a></td></tr>
<tr class="memdesc:aeb77053c84f49c261ab5b8374e8958ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x03C (R/W) Auxiliary Fault Status Register.  <a href="#aeb77053c84f49c261ab5b8374e8958ef"></a><br/></td></tr>
<tr class="separator:aeb77053c84f49c261ab5b8374e8958ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f51c43f952f3799951d0c54e76b0cb7"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a3f51c43f952f3799951d0c54e76b0cb7">PFR</a> [2]</td></tr>
<tr class="memdesc:a3f51c43f952f3799951d0c54e76b0cb7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x040 (R/ ) Processor Feature Register.  <a href="#a3f51c43f952f3799951d0c54e76b0cb7"></a><br/></td></tr>
<tr class="separator:a3f51c43f952f3799951d0c54e76b0cb7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a586a5225467262b378c0f231ccc77f86"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#a586a5225467262b378c0f231ccc77f86">DFR</a></td></tr>
<tr class="memdesc:a586a5225467262b378c0f231ccc77f86"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x048 (R/ ) Debug Feature Register.  <a href="#a586a5225467262b378c0f231ccc77f86"></a><br/></td></tr>
<tr class="separator:a586a5225467262b378c0f231ccc77f86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaedf846e435ed05c68784b40d3db2bf2"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#aaedf846e435ed05c68784b40d3db2bf2">ADR</a></td></tr>
<tr class="memdesc:aaedf846e435ed05c68784b40d3db2bf2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x04C (R/ ) Auxiliary Feature Register.  <a href="#aaedf846e435ed05c68784b40d3db2bf2"></a><br/></td></tr>
<tr class="separator:aaedf846e435ed05c68784b40d3db2bf2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec2f8283d2737c6897188568a4214976"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#aec2f8283d2737c6897188568a4214976">MMFR</a> [4]</td></tr>
<tr class="memdesc:aec2f8283d2737c6897188568a4214976"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x050 (R/ ) Memory Model Feature Register.  <a href="#aec2f8283d2737c6897188568a4214976"></a><br/></td></tr>
<tr class="separator:aec2f8283d2737c6897188568a4214976"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acee8e458f054aac964268f4fe647ea4f"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#acee8e458f054aac964268f4fe647ea4f">ISAR</a> [5]</td></tr>
<tr class="memdesc:acee8e458f054aac964268f4fe647ea4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x060 (R/ ) Instruction Set Attributes Register.  <a href="#acee8e458f054aac964268f4fe647ea4f"></a><br/></td></tr>
<tr class="separator:acee8e458f054aac964268f4fe647ea4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac89a5d9901e3748d22a7090bfca2bee6"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#ac89a5d9901e3748d22a7090bfca2bee6">RESERVED0</a> [5]</td></tr>
<tr class="memdesc:ac89a5d9901e3748d22a7090bfca2bee6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#ac89a5d9901e3748d22a7090bfca2bee6"></a><br/></td></tr>
<tr class="separator:ac89a5d9901e3748d22a7090bfca2bee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af460b56ce524a8e3534173f0aee78e85"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_s_c_b___type.html#af460b56ce524a8e3534173f0aee78e85">CPACR</a></td></tr>
<tr class="memdesc:af460b56ce524a8e3534173f0aee78e85"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x088 (R/W) Coprocessor Access Control Register.  <a href="#af460b56ce524a8e3534173f0aee78e85"></a><br/></td></tr>
<tr class="separator:af460b56ce524a8e3534173f0aee78e85"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="aaedf846e435ed05c68784b40d3db2bf2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t SCB_Type::ADR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aeb77053c84f49c261ab5b8374e8958ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::AFSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6ed3c9064013343ea9fd0a73a734f29d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::AIRCR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a31f79afe86c949c9862e7d5fce077c3a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::BFAR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6d273c6b90bad15c91dfbbad0f6e92d8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::CCR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2f94bf549b16fdeb172352e22309e3c4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::CFSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af460b56ce524a8e3534173f0aee78e85"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::CPACR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afa7a9ee34dfa1da0b60b4525da285032"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t SCB_Type::CPUID</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a586a5225467262b378c0f231ccc77f86"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t SCB_Type::DFR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad7d61d9525fa9162579c3da0b87bff8d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::DFSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7bed53391da4f66d8a2a236a839d4c3d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::HFSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3e66570ab689d28aebefa7e84e85dc4a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::ICSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acee8e458f054aac964268f4fe647ea4f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t SCB_Type::ISAR[5]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac49b24b3f222508464f111772f2c44dd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::MMFAR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aec2f8283d2737c6897188568a4214976"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t SCB_Type::MMFR[4]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3f51c43f952f3799951d0c54e76b0cb7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t SCB_Type::PFR[2]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac89a5d9901e3748d22a7090bfca2bee6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t SCB_Type::RESERVED0[5]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abfad14e7b4534d73d329819625d77a16"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::SCR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae9891a59abbe51b0b2067ca507ca212f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::SHCSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af6336103f8be0cab29de51daed5a65f4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint8_t SCB_Type::SHP[12]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0faf96f964931cadfb71cfa54e051f6f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t SCB_Type::VTOR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_s_c_b___type.html">SCB_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
