<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: SPI Frame Format</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__spi__frame__format__ctrls.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">SPI Frame Format<div class="ingroups"><a class="el" href="group___s_p_i__control.html">SPI Control Codes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Defines the frame format.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gab4ac9a609c078d1e8332cf95da34e50e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__frame__format__ctrls.html#gab4ac9a609c078d1e8332cf95da34e50e">ARM_SPI_CPOL0_CPHA0</a>&#160;&#160;&#160;(0UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td></tr>
<tr class="memdesc:gab4ac9a609c078d1e8332cf95da34e50e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clock Polarity 0, Clock Phase 0 (default)  <a href="#gab4ac9a609c078d1e8332cf95da34e50e">More...</a><br/></td></tr>
<tr class="separator:gab4ac9a609c078d1e8332cf95da34e50e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5498eb08c2ba8de2e1c2801428e79d71"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__frame__format__ctrls.html#ga5498eb08c2ba8de2e1c2801428e79d71">ARM_SPI_CPOL0_CPHA1</a>&#160;&#160;&#160;(1UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td></tr>
<tr class="memdesc:ga5498eb08c2ba8de2e1c2801428e79d71"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clock Polarity 0, Clock Phase 1.  <a href="#ga5498eb08c2ba8de2e1c2801428e79d71">More...</a><br/></td></tr>
<tr class="separator:ga5498eb08c2ba8de2e1c2801428e79d71"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga67193d9b5af1ec312a66d007c33b597f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__frame__format__ctrls.html#ga67193d9b5af1ec312a66d007c33b597f">ARM_SPI_CPOL1_CPHA0</a>&#160;&#160;&#160;(2UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td></tr>
<tr class="memdesc:ga67193d9b5af1ec312a66d007c33b597f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clock Polarity 1, Clock Phase 0.  <a href="#ga67193d9b5af1ec312a66d007c33b597f">More...</a><br/></td></tr>
<tr class="separator:ga67193d9b5af1ec312a66d007c33b597f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7fab572b2fec303e979e47eb2d13ca74"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__frame__format__ctrls.html#ga7fab572b2fec303e979e47eb2d13ca74">ARM_SPI_CPOL1_CPHA1</a>&#160;&#160;&#160;(3UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td></tr>
<tr class="memdesc:ga7fab572b2fec303e979e47eb2d13ca74"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clock Polarity 1, Clock Phase 1.  <a href="#ga7fab572b2fec303e979e47eb2d13ca74">More...</a><br/></td></tr>
<tr class="separator:ga7fab572b2fec303e979e47eb2d13ca74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga225185710ba38848a489013ba4475915"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__frame__format__ctrls.html#ga225185710ba38848a489013ba4475915">ARM_SPI_TI_SSI</a>&#160;&#160;&#160;(4UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td></tr>
<tr class="memdesc:ga225185710ba38848a489013ba4475915"><td class="mdescLeft">&#160;</td><td class="mdescRight">Texas Instruments Frame Format.  <a href="#ga225185710ba38848a489013ba4475915">More...</a><br/></td></tr>
<tr class="separator:ga225185710ba38848a489013ba4475915"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga44f481d32b9a9ea93673f05af82ccf86"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__frame__format__ctrls.html#ga44f481d32b9a9ea93673f05af82ccf86">ARM_SPI_MICROWIRE</a>&#160;&#160;&#160;(5UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td></tr>
<tr class="memdesc:ga44f481d32b9a9ea93673f05af82ccf86"><td class="mdescLeft">&#160;</td><td class="mdescRight">National Microwire Frame Format.  <a href="#ga44f481d32b9a9ea93673f05af82ccf86">More...</a><br/></td></tr>
<tr class="separator:ga44f481d32b9a9ea93673f05af82ccf86"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Defines the frame format. </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="gab4ac9a609c078d1e8332cf95da34e50e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_CPOL0_CPHA0&#160;&#160;&#160;(0UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Clock Polarity 0, Clock Phase 0 (default) </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5498eb08c2ba8de2e1c2801428e79d71"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_CPOL0_CPHA1&#160;&#160;&#160;(1UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Clock Polarity 0, Clock Phase 1. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga67193d9b5af1ec312a66d007c33b597f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_CPOL1_CPHA0&#160;&#160;&#160;(2UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Clock Polarity 1, Clock Phase 0. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7fab572b2fec303e979e47eb2d13ca74"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_CPOL1_CPHA1&#160;&#160;&#160;(3UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Clock Polarity 1, Clock Phase 1. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga44f481d32b9a9ea93673f05af82ccf86"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_MICROWIRE&#160;&#160;&#160;(5UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>National Microwire Frame Format. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga225185710ba38848a489013ba4475915"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_TI_SSI&#160;&#160;&#160;(4UL &lt;&lt; ARM_SPI_FRAME_FORMAT_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Texas Instruments Frame Format. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
