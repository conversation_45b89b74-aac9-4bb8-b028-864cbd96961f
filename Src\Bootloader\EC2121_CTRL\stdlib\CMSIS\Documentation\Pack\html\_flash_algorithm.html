<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Pack: Flash Programming Algorithms</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Pack
   &#160;<span id="projectnumber">Version 1.3.1</span>
   </div>
   <div id="projectbrief">Delivery Mechanism for Software Packs</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li class="current"><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_flash_algorithm.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Flash Programming Algorithms </div>  </div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#CreateFPA">Creating a new Algorithm</a></li>
<li class="level1"><a href="#AddFPA">Adding an Algorithm to a Pack</a></li>
</ul>
</div>
<div class="textblock"><p><b>Flash Programming Algorithms</b> are a piece of software to erase or download applications to Flash devices. A <a class="el" href="_create_pack__d_f_p.html">Pack with Device Support</a> usually contains predefined Flash algorithms for programming the devices that are supported by the DFP. A template for creating new algorithms are available in the <b>ARM:CMSIS</b> Pack. The following section describes the process in more detail.</p>
<h1><a class="anchor" id="CreateFPA"></a>
Creating a new Algorithm</h1>
<p>Flash programming algorithms are defined with functions to erase and program the Flash device. Special compiler and linker settings are required. Follow these steps to create and configure a new Flash programming algorithm:</p>
<ol type="1">
<li>Copy the content from the <b>ARM:CMSIS</b> Pack folder (usually C:\Keil\ARM\Pack\ARM\CMSIS\<em>version</em>\Device\_Template_Flash) to a new folder.</li>
<li>Rename the project file <b>NewDevice.uvproj</b> to represent the new Flash ROM device name, for example <b>MyDevice.uvproj</b>.</li>
<li>Open the project with uVision. From the toolbar, use the drop-down <b>Select Target</b> to define the processor architecture. <b>Cortex-M</b> fits for all Cortex-M0/M0+, M3, and M4 devices. The configuration assumes a little-endian microcontroller. In case of a big-endian microcontroller, select the correct processor core with <b>Project</b> - <b>Options for Target</b> - <b>Device</b>.</li>
<li>Open the dialog <b>Project</b> - <b>Options for Target</b> - <b>Output</b> and change the content of the field <b>Name of Executable</b> to represent the device, for example <b>MyDevice</b>.</li>
<li>Adapt the programming algorithms in the file <a class="el" href="_flash_algorithm.html#FlashPrg">FlashPrg</a>.</li>
<li>Adapt the device parameters in the file <a class="el" href="_flash_algorithm.html#FlashDev">FlashDev</a>.</li>
<li>Use <b>Project</b> - <b>Build Target</b> to generate the new Flash programming algorithm. The output file (for example <b>MyDevice.FLM</b>) has to be <a class="el" href="_flash_algorithm.html#AddFPA">added</a> to the <b>DFP</b>.</li>
</ol>
<dl class="section note"><dt>Note</dt><dd><ul>
<li>Creating a Flash programming algorithm with <a href="http://www.keil.com/arm/selector.asp" class="el" target="_blank">MDK-Lite</a> is not supported.</li>
<li>Flash programming algorithms use <b>Read-Only Position Independent</b> and <b>Read-Write Position Independent</b> program code. These options are set in the dialogs <b>Project</b> - <b>Options for Target</b> - <b>C/C++</b> and <b>Project</b> - <b>Options for Target</b> - <b>Asm</b>.</li>
<li>The dialog <b>Project</b> - <b>Options for Target</b> - <b>Linker</b> defines the linker scatter file <b>Target.lin</b>. The error L6305 is disabled with <code>&ndash;diag_suppress</code> <code>L6305</code>.</li>
<li>The <a class="el" href="_algorithm_func.html">Flash Algorithm Functions</a> section contains reference for all the available functions.</li>
</ul>
</dd></dl>
<p><a class="anchor" id="FlashPrg"></a></p>
<h2>FlashPrg.c</h2>
<p>The file <b>FlashPrg.c</b> contains the mandatory Flash programming functions <a class="el" href="_algorithm_func.html#Init">Init</a>, <a class="el" href="_algorithm_func.html#UnInit">UnInit</a>, <a class="el" href="_algorithm_func.html#EraseSector">EraseSector</a>, and <a class="el" href="_algorithm_func.html#ProgramPage">ProgramPage</a>. Optionally, depending on the device features (or to speed-up execution), the functions <a class="el" href="_algorithm_func.html#EraseChip">EraseChip</a>, <a class="el" href="_algorithm_func.html#BlankCheck">BlankCheck</a>, and <a class="el" href="_algorithm_func.html#Verify">Verify</a> can be implemented.</p>
<p><a class="anchor" id="FlashDev"></a></p>
<h2>FlashDev.c</h2>
<p>The file <b>FlashDev.c</b> contains parameter definitions for:</p>
<ol type="1">
<li>the Flash programming functions.</li>
<li>the <b>FlashDevice</b> structure: <div class="fragment"><div class="line"><span class="keyword">struct </span>FlashDevice const FlashDevice  =  {</div>
<div class="line">FLASH_DRV_VERS,                   <span class="comment">// Driver Version, do not modify!</span></div>
<div class="line"><span class="stringliteral">&quot;STM32Fxxx High-density Flash&quot;</span>,   <span class="comment">// Device Name (512kB/384kB/256kB)</span></div>
<div class="line">ONCHIP,                           <span class="comment">// Device Type</span></div>
<div class="line">0x08000000,                       <span class="comment">// Device Start Address</span></div>
<div class="line">0x00080000,                       <span class="comment">// Device Size in Bytes (512kB)</span></div>
<div class="line">1024,                             <span class="comment">// Programming Page Size</span></div>
<div class="line">0,                                <span class="comment">// Reserved, must be 0</span></div>
<div class="line">0xFF,                             <span class="comment">// Initial Content of Erased Memory</span></div>
<div class="line">100,                              <span class="comment">// Program Page Timeout 100 mSec</span></div>
<div class="line">500,                              <span class="comment">// Erase Sector Timeout 500 mSec</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">// Specify Size and Address of Sectors</span></div>
<div class="line">0x0800, 0x000000,                 <span class="comment">// Sector Size 2kB (256 Sectors)</span></div>
<div class="line">SECTOR_END</div>
<div class="line">};</div>
</div><!-- fragment --></li>
</ol>
<dl class="section note"><dt>Note</dt><dd>The <b>Device Name</b> will usually be shown in tools to identify the Flash algorithm. Make sure that this is changed to the correct device name from the DFP.</dd></dl>
<h2>Testing Algorithms</h2>
<p>The _Template_Flash\Test-folder contains a project that shows how to test a new Flash programming algorithm on behalf of a STM32F10x device. Adapt the code to your needs.</p>
<h1><a class="anchor" id="AddFPA"></a>
Adding an Algorithm to a Pack</h1>
<p>The generated <b>*.FLM</b> file needs to be added to the <a class="el" href="_create_pack__d_f_p.html">Pack with Device Support</a>, so that it is available to the tool user for programming his device. Usually, a directory <b>Flash</b> is created and the algorithm is saved in this directory.</p>
<p>The algorithm is specified within the the <a class="el" href="pdsc_family_pg.html#element_family">/package/devices/family</a> level: </p>
<div class="fragment"><div class="line">&lt;family Dfamily=<span class="stringliteral">&quot;STM32F4&quot;</span> Dvendor=<span class="stringliteral">&quot;STMicroelectronics:13&quot;</span>&gt;</div>
<div class="line">  ...</div>
<div class="line">  &lt;algorithm name=<span class="stringliteral">&quot;Flash\STM32F2xx_512.flm&quot;</span> start=0x08000000 size=0x10000 <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;         &lt;!-- valid <span class="keywordflow">for</span> all devices of the family  --&gt;</div>
<div class="line"> </div>
<div class="line">  &lt;subFamily DsubFamily=<span class="stringliteral">&quot;STM32F405&quot;</span>&gt;</div>
<div class="line">    &lt;algorithm name=<span class="stringliteral">&quot;Flash\STM32F2xx_1024.flm&quot;</span> start=0x08000000 size=0x20000 <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;       &lt;!-- valid <span class="keywordflow">for</span> all devices of a subFamily --&gt;</div>
<div class="line"></div>
<div class="line">    &lt;device Dname=<span class="stringliteral">&quot;STM32F405OE&quot;</span>&gt;</div>
<div class="line">      &lt;algorithm name=<span class="stringliteral">&quot;Flash\STM32F2xx_2048.flm&quot;</span> start=0x08000000 size=0x40000 <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;     &lt;!-- <span class="keywordflow">finally</span>, <span class="keyword">this</span> is the <span class="keywordflow">default</span> <span class="keywordflow">for</span> the device --&gt;</div>
<div class="line">    &lt;/device&gt;</div>
<div class="line">    ...</div>
<div class="line">&lt;/family&gt;</div>
</div><!-- fragment --><p>The argument <em>start</em> specifies the base address for the Flash programming algorithm.</p>
<p>The argument <em>size</em> specifies the size covered by the Flash programming algorithm. End address = start + size - 1.</p>
<p>The argument <em>default</em> specifies whether a Flash programming algorithm is set as the default algorithm in a project (when <span class="XML-Token">true</span>). If <em>default</em> is not set or false, the Flash programming algorithm can be configured on a lower level. However, the Flash programming algorithm of a project can be changed manually at any time during development. </p>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:18:10 for CMSIS-Pack by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
