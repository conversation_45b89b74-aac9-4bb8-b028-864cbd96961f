<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Introduction</title>
<title>CMSIS: Introduction</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS
   &#160;<span id="projectnumber">Version 4.3.0</span>
   </div>
   <div id="projectbrief">Cortex Microcontroller Software Interface Standard</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li class="current"><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('index.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">Introduction </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>The <b>Cortex Microcontroller Software Interface Standard</b> (CMSIS) is a vendor-independent hardware abstraction layer for the Cortex-M processor series and defines generic tool interfaces. The CMSIS enables consistent device support and simple software interfaces to the processor and the peripherals, simplifying software re-use, reducing the learning curve for microcontroller developers, and reducing the time to market for new devices.</p>
<p>The CMSIS is defined in close cooperation with various silicon and software vendors and provides a common approach to interface to peripherals, real-time operating systems, and middleware components. The CMSIS is intended to enable the combination of software components from multiple middleware vendors.</p>
<p><a class="anchor" id="CM_Components"></a>The CMSIS components are:</p>
<ul>
<li><a href="../../Core/html/index.html"><b>CMSIS-CORE</b></a>: API for the Cortex-M processor core and peripherals. It provides at standardized interface for Cortex-M0, Cortex-M3, Cortex-M4, SC000, and SC300. Included are also SIMD intrinsic functions for Cortex-M4 SIMD instructions.</li>
</ul>
<ul>
<li><a href="../../Driver/html/index.html"><b>CMSIS-Driver</b></a>: defines generic peripheral driver interfaces for middleware making it reusable across supported devices. The API is RTOS independent and connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces.</li>
</ul>
<ul>
<li><a href="../../DSP/html/index.html"><b>CMSIS-DSP</b></a>: DSP Library Collection with over 60 Functions for various data types: fix-point (fractional q7, q15, q31) and single precision floating-point (32-bit). The library is available for Cortex-M0, Cortex-M3, and Cortex-M4. The Cortex-M4 implementation is optimized for the SIMD instruction set.</li>
</ul>
<ul>
<li><a href="../../RTOS/html/index.html"><b>CMSIS-RTOS API</b></a>: Common API for Real-Time operating systems. It provides a standardized programming interface that is portable to many RTOS and enables therefore software templates, middleware, libraries, and other components that can work across supported the RTOS systems.</li>
</ul>
<ul>
<li><a href="../../Pack/html/index.html"><b>CMSIS-Pack</b></a>: describes with a XML based package description (PDSC) file the user and device relevant parts of a file collection (called software pack) that includes source, header, and library files, documentation, Flash programming algorithms, source code templates, and example projects. Development tools and web infrastructures use the PDSC file to extract device parameters, software components, and evaluation board configurations.</li>
</ul>
<ul>
<li><a href="../../SVD/html/index.html"><b>CMSIS-SVD</b></a>: System View Description for Peripherals. Describes the peripherals of a device in an XML file and can be used to create peripheral awareness in debuggers or header files with peripheral register and interrupt definitions.</li>
</ul>
<ul>
<li><a href="http://www.arm.com/cmsis" target="_blank"><b>CMSIS-DAP</b></a>: Debug Access Port. Standardized firmware for a Debug Unit that connects to the CoreSight Debug Access Port. CMSIS-DAP is distributed as separate package and well suited for integration on evaluation boards. This component is provided as separate download.</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>Refer to <a class="el" href="index.html#CM_Pack_Content">ARM::CMSIS Pack</a> for more information on the content of the Software Pack.</dd></dl>
<div class="image">
<img src="CMSISv4_small.png" alt="CMSISv4_small.png"/>
<div class="caption">
CMSIS Structure</div></div>
 <h1><a class="anchor" id="Motivation"></a>
Motivation</h1>
<p>CMSIS has been created to help the industry in standardization. It enables consistent software layers and device support across a wide range of development tools and microcontrollers. CMSIS is not a huge software layer that introduces overhead and does not define standard peripherals. The silicon industry can therefore support the wide variations of Cortex-M processor-based devices with this common standard.</p>
<p>In detail the benefits of the CMSIS are:</p>
<ul>
<li>Overall CMSIS reduces the learning curve, development costs, and time-to-market. Developers can write software quicker through a variety of easy-to-use, standardized software interfaces.</li>
<li>Consistent software interfaces improve the software portability and re-usability. Generic software libraries and interfaces provide consistent software framework.</li>
<li>Provides interfaces for debug connectivity, debug peripheral views, software delivery, and device support to reduce time-to-market for new microcontroller deployment.</li>
<li>Provides a compiler independent layer that allows using different compilers. CMSIS is supported by all mainstream compilers (ARMCC, IAR, and GNU).</li>
<li>Enhances program debugging with peripheral information for debuggers and ITM channels for printf-style output and RTOS kernel awareness.</li>
<li>CMSIS is delivered in CMSIS-Pack format which enables fast software delivery, simplifies updates, and enables consistent integration into development tools.</li>
</ul>
<h1><a class="anchor" id="CodingRules"></a>
Coding Rules</h1>
<p>The CMSIS uses the following essential coding rules and conventions:</p>
<ul>
<li>Compliant with ANSI C and C++.</li>
<li>Uses ANSI C standard data types defined in <b>&lt;stdint.h&gt;</b>.</li>
<li>Variables and parameters have a complete data type.</li>
<li>Expressions for <em>#define</em> constants are enclosed in parenthesis.</li>
<li>Conforms to MISRA 2004. MISRA rule violations are documented.</li>
</ul>
<p>In addition, the CMSIS recommends the following conventions for identifiers:</p>
<ul>
<li><b>CAPITAL</b> names to identify Core Registers, Peripheral Registers, and CPU Instructions.</li>
<li><b>CamelCase</b> names to identify function names and interrupt functions.</li>
<li><b>Namespace_</b> prefixes avoid clashes with user identifiers and provide functional groups (i.e. for peripherals, RTOS, or DSP Library).</li>
</ul>
<p>The CMSIS is documented within the source files with: </p>
<ul>
<li>Comments that use the C or C++ style. </li>
<li>Doxygen compliant <b>function comments</b> that provide:<ul>
<li>brief function overview.</li>
<li>detailed description of the function.</li>
<li>detailed parameter explanation.</li>
<li>detailed information about return values.</li>
</ul>
</li>
</ul>
<p>Doxygen comment example: </p>
<pre class="fragment">/** 
 * @brief  Enable Interrupt in NVIC Interrupt Controller
 * @param  IRQn  interrupt number that specifies the interrupt
 * @return none.
 * Enable the specified interrupt in the NVIC Interrupt Controller.
 * Other settings of the interrupt such as priority are not affected.
 */
</pre><h1><a class="anchor" id="License"></a>
Licence</h1>
<p>The CMSIS is provided free of charge by ARM and can be used for all Cortex-M based devices.</p>
<p>The software portions that are deployed in the application program are under a BSD license which allows usage of CMSIS in any commercial or open source projects.</p>
<p>View the <a href="CMSIS_END_USER_LICENCE_AGREEMENT.pdf">LICENCE AGREEMENT for CMSIS</a> in detail.</p>
<h1><a class="anchor" id="CM_Pack_Content"></a>
ARM::CMSIS Pack</h1>
<p>The <b>ARM::CMSIS</b> Pack contains the following: </p>
<table class="doxtable">
<tr>
<th>File/Folder </th><th>Content </th></tr>
<tr>
<td><b>ARM.CMSIS.pdsc</b> </td><td>Package description file </td></tr>
<tr>
<td><b>CMSIS</b> </td><td><a class="el" href="index.html#CM_Components">CMSIS components</a> (see below) </td></tr>
<tr>
<td><b>CMSIS_RTX</b> </td><td><a href="http://www.keil.com/pack/doc/cmsis_rtx/index.html" class="el" target="_blank">Keil RTX</a> implementation of <a href="../../RTOS/html/index.html"><b>CMSIS-RTOS</b></a> </td></tr>
<tr>
<td><b>Device</b> </td><td>ARM reference implementations of Cortex-M devices </td></tr>
</table>
<h2>CMSIS Directory</h2>
<p>The directory <b>CMSIS</b> contains the "CMSIS End User License Agreement" as PDF and RTF, a README text file, the index.html file for this documentation, and the following sub-directories:</p>
<table class="doxtable">
<tr>
<th>Directory </th><th>Content </th></tr>
<tr>
<td><b>Documentation</b> </td><td>This documentation </td></tr>
<tr>
<td><b>Driver</b> </td><td>Header files for the <a href="../../Driver/html/index.html"><b>CMSIS-Driver</b></a> peripheral interface API </td></tr>
<tr>
<td><b>DSP_Lib</b> </td><td><a href="../../DSP/html/index.html"><b>CMSIS-DSP</b></a> software library files </td></tr>
<tr>
<td><b>Include</b> </td><td>Include files for <a href="../../Core/html/index.html"><b>CMSIS-CORE</b></a> and <a href="../../DSP/html/index.html"><b>CMSIS-DSP</b></a> </td></tr>
<tr>
<td><b>Lib</b> </td><td><a href="../../DSP/html/index.html"><b>CMSIS-DSP</b></a> libraries for ARMCC and GCC </td></tr>
<tr>
<td><b>Pack</b> </td><td><a href="../../Pack/html/_pack__example.html"><b>CMSIS-Pack</b></a> example </td></tr>
<tr>
<td><b>RTOS</b> </td><td><a href="../../RTOS/html/index.html"><b>CMSIS-RTOS</b></a> API header file </td></tr>
<tr>
<td><b>SVD</b> </td><td><a href="../../SVD/html/index.html"><b>CMSIS-SVD</b></a> example </td></tr>
<tr>
<td><b>UserCodeTemplates\ARM</b> </td><td>ITM_Retarget.c, CMSIS <a href="../../Core/html/group___i_t_m___debug__gr.html"><b>retarget output</b></a> to ITM Channel 0 template file </td></tr>
<tr>
<td><b>Utilities</b> </td><td>PACK.xsd (<a href="../../Pack/html/_pack__example.html"><b>CMSIS-Pack</b></a> schema file), PackChk.exe (checking tool for software packs), <br/>
 CMSIS-SVD.xsd (<a href="../../SVD/html/index.html"><b>CMSIS-SVD</b></a> schema file), SVDConv.exe (conversion tool for SVD files) </td></tr>
</table>
<hr/>
 </div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:52 for CMSIS by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
