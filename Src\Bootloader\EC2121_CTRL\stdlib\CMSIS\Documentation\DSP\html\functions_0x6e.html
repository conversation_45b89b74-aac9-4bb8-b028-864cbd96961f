<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Data Fields</title>
<title>CMSIS-DSP: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li class="current"><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions_0x6e.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a class="anchor" id="index_n"></a>- n -</h3><ul>
<li>N
: <a class="el" href="structarm__dct4__instance__f32.html#a262b29a51c371b46efc89120e31ccf37">arm_dct4_instance_f32</a>
, <a class="el" href="structarm__dct4__instance__q31.html#a46a9f136457350676e2bfd3768ff9d6d">arm_dct4_instance_q31</a>
, <a class="el" href="structarm__dct4__instance__q15.html#a53d24009bb9b2e93d0aa07db7f1a6c25">arm_dct4_instance_q15</a>
</li>
<li>Nby2
: <a class="el" href="structarm__dct4__instance__f32.html#adb1ef2739ddbe62e5cdadc47455a4147">arm_dct4_instance_f32</a>
, <a class="el" href="structarm__dct4__instance__q31.html#a32d3268ba4629908dba056599f0a904d">arm_dct4_instance_q31</a>
, <a class="el" href="structarm__dct4__instance__q15.html#af43dcbbc2fc661ffbc525afe3dcbd7da">arm_dct4_instance_q15</a>
</li>
<li>normalize
: <a class="el" href="structarm__dct4__instance__q31.html#ac80ff7b28fca36aeef74dea12e8312dd">arm_dct4_instance_q31</a>
, <a class="el" href="structarm__dct4__instance__q15.html#a197098140d68e89a08f7a249003a0b86">arm_dct4_instance_q15</a>
, <a class="el" href="structarm__dct4__instance__f32.html#a61ce8c967b2e998a9c0041cca73cdef8">arm_dct4_instance_f32</a>
</li>
<li>numCols
: <a class="el" href="structarm__bilinear__interp__instance__q15.html#a7fa8772d01583374ff8ac18205a26a37">arm_bilinear_interp_instance_q15</a>
, <a class="el" href="structarm__bilinear__interp__instance__q7.html#a860dd0d24380ea06cfbb348fb3b12c9a">arm_bilinear_interp_instance_q7</a>
, <a class="el" href="structarm__matrix__instance__f32.html#acdd1fb73734df68b89565c54f1dd8ae2">arm_matrix_instance_f32</a>
, <a class="el" href="structarm__matrix__instance__f64.html#ab0f0399aff3201880e2d8a447de9a7ee">arm_matrix_instance_f64</a>
, <a class="el" href="structarm__matrix__instance__q15.html#acbbce67ba058d8e1c867c71d57288c97">arm_matrix_instance_q15</a>
, <a class="el" href="structarm__matrix__instance__q31.html#abd161da7614eda927157f18b698074b1">arm_matrix_instance_q31</a>
, <a class="el" href="structarm__bilinear__interp__instance__f32.html#aede17bebfb1f835b61d71dd813eab3f8">arm_bilinear_interp_instance_f32</a>
, <a class="el" href="structarm__bilinear__interp__instance__q31.html#a6c3eff4eb17ff1d43f170efb84713a2d">arm_bilinear_interp_instance_q31</a>
</li>
<li>numRows
: <a class="el" href="structarm__matrix__instance__f32.html#a23f4e34d70a82c9cad7612add5640b7b">arm_matrix_instance_f32</a>
, <a class="el" href="structarm__matrix__instance__f64.html#a8b44d1e5003345047c4ead9e1593bf22">arm_matrix_instance_f64</a>
, <a class="el" href="structarm__matrix__instance__q15.html#a9bac6ed54be287c4d4f01a1a28be65f5">arm_matrix_instance_q15</a>
, <a class="el" href="structarm__matrix__instance__q31.html#a63bacac158a821c8cfc06088d251598c">arm_matrix_instance_q31</a>
, <a class="el" href="structarm__bilinear__interp__instance__f32.html#a34f2b17cc57b95011960df9718af6ed6">arm_bilinear_interp_instance_f32</a>
, <a class="el" href="structarm__bilinear__interp__instance__q31.html#a2082e3eac56354d75291f03e96ce4aa5">arm_bilinear_interp_instance_q31</a>
, <a class="el" href="structarm__bilinear__interp__instance__q15.html#a2130ae30a804995a9f5d0e2189e08565">arm_bilinear_interp_instance_q15</a>
, <a class="el" href="structarm__bilinear__interp__instance__q7.html#ad5a8067cab5f9ea4688b11a623e16607">arm_bilinear_interp_instance_q7</a>
</li>
<li>numStages
: <a class="el" href="structarm__fir__lattice__instance__f32.html#ad369bd9997a250f195254df37408a38f">arm_fir_lattice_instance_f32</a>
, <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a5655328252da5c2c2425ceed253bc4f1">arm_biquad_cascade_stereo_df2T_instance_f32</a>
, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#ad55380ff835b533aa5168f836db8a4de">arm_biquad_cascade_df2T_instance_f64</a>
, <a class="el" href="structarm__fir__lattice__instance__q15.html#a38b179138d6a6c9cac4f8f79b6fd5357">arm_fir_lattice_instance_q15</a>
, <a class="el" href="structarm__fir__lattice__instance__q31.html#a9f3773bbb76bc5a8a5ee9d37786bf478">arm_fir_lattice_instance_q31</a>
, <a class="el" href="structarm__iir__lattice__instance__q15.html#a96fbed313bef01070409fa182d26ba3f">arm_iir_lattice_instance_q15</a>
, <a class="el" href="structarm__iir__lattice__instance__q31.html#a9df4570ed28c50fd9193ab654ff236ad">arm_iir_lattice_instance_q31</a>
, <a class="el" href="structarm__iir__lattice__instance__f32.html#af8de449af5efe1f30be82f9ba35587ee">arm_iir_lattice_instance_f32</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#ad6d95e70abcf4ff1300181415ad92153">arm_biquad_casd_df1_inst_q15</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a2c2b579f1df1d8273a5d9d945c27e1b2">arm_biquad_casd_df1_inst_q31</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#af69820c37a87252c46453e4cfe120585">arm_biquad_casd_df1_inst_f32</a>
, <a class="el" href="structarm__biquad__cas__df1__32x64__ins__q31.html#ad7cb9a9f5df8f4fcfc7a0b633672e574">arm_biquad_cas_df1_32x64_ins_q31</a>
, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a4d17958c33c3d0a905f974bac50f033f">arm_biquad_cascade_df2T_instance_f32</a>
</li>
<li>numTaps
: <a class="el" href="structarm__fir__sparse__instance__f32.html#a5e19e7f234ac30a3db843352bf2a8515">arm_fir_sparse_instance_f32</a>
, <a class="el" href="structarm__fir__instance__f32.html#a20cf98c92b5323799b7881c9ff4d2f7c">arm_fir_instance_f32</a>
, <a class="el" href="structarm__fir__decimate__instance__q31.html#a37915d42b0dc5e3057ebe83110798482">arm_fir_decimate_instance_q31</a>
, <a class="el" href="structarm__lms__norm__instance__f32.html#ac95f8ca3d816524c2070643852fac5e8">arm_lms_norm_instance_f32</a>
, <a class="el" href="structarm__lms__instance__q15.html#a0078e894f805af1b360369e619fb57b3">arm_lms_instance_q15</a>
, <a class="el" href="structarm__lms__instance__q31.html#ac0d84f7d054555931ef8a62511fbcb8a">arm_lms_instance_q31</a>
, <a class="el" href="structarm__lms__norm__instance__q31.html#a28e4c085af69c9c3e2e95dacf8004c3e">arm_lms_norm_instance_q31</a>
, <a class="el" href="structarm__fir__instance__q31.html#a918fadd775b7a0482b21bf34dae2f094">arm_fir_instance_q31</a>
, <a class="el" href="structarm__fir__decimate__instance__f32.html#a2aa2986129db8affef03ede88dd45a03">arm_fir_decimate_instance_f32</a>
, <a class="el" href="structarm__fir__instance__q7.html#a9b50840e2c5ef5b17e1a584fb4cf0d06">arm_fir_instance_q7</a>
, <a class="el" href="structarm__lms__norm__instance__q15.html#a9ee7a45f4f315d7996a969e25fdc7146">arm_lms_norm_instance_q15</a>
, <a class="el" href="structarm__fir__decimate__instance__q15.html#ac1e9844488ec717da334fbd4c4f41990">arm_fir_decimate_instance_q15</a>
, <a class="el" href="structarm__lms__instance__f32.html#af73880d9009982f5d14529869494ec3d">arm_lms_instance_f32</a>
, <a class="el" href="structarm__fir__sparse__instance__q7.html#a54cdd27ca1c672b126c38763ce678b1c">arm_fir_sparse_instance_q7</a>
, <a class="el" href="structarm__fir__sparse__instance__q31.html#a07b6c01e58ec6dde384719130d36b0dc">arm_fir_sparse_instance_q31</a>
, <a class="el" href="structarm__fir__instance__q15.html#a0e46f93cf51bfb18b1be808be9c5bfc9">arm_fir_instance_q15</a>
, <a class="el" href="structarm__fir__sparse__instance__q15.html#a0f66b126dd8b85f7467cfb01b7bc4d77">arm_fir_sparse_instance_q15</a>
</li>
<li>nValues
: <a class="el" href="structarm__linear__interp__instance__f32.html#a95f02a926b16d35359aca5b31e813b11">arm_linear_interp_instance_f32</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
