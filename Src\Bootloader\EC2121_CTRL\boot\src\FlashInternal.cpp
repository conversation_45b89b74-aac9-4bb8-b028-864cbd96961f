#include "FlashInternal.h"
//#include "Board.h"
#include "Version.h"
#include "ErrorCode.h"
#include <cstdio>
#include  "stm32f4xx_flash.h"


bool g_CurOpenJ2534Version = 1;  // 0->j2534-1  1->j2534-2


//#define FirwarVersion (__DATE__" "__TIME__" V0.0.6")
__no_init extern uint32_t g_fHotStart;


const InternalFlash FlashInfo[12] = 
{
    {ADDR_FLASH_SECTOR_0,   FLASH_Sector_0,     SIZE_FLASH_SECTOR_0},
    {ADDR_FLASH_SECTOR_1,   FLASH_Sector_1,     SIZE_FLASH_SECTOR_1},
    {ADDR_FLASH_SECTOR_2,   FLASH_Sector_2,     SIZE_FLASH_SECTOR_2},
    {ADDR_FLASH_SECTOR_3,   FLASH_Sector_3,     SIZE_FLASH_SECTOR_3},
    {ADDR_FLASH_SECTOR_4,   FLASH_Sector_4,     SIZE_FLASH_SECTOR_4},
    {ADDR_FLASH_SECTOR_5,   FLASH_Sector_5,     SIZE_FLASH_SECTOR_5},
    {ADDR_FLASH_SECTOR_6,   FLASH_Sector_6,     SIZE_FLASH_SECTOR_6},
    {ADDR_FLASH_SECTOR_7,   FLASH_Sector_7,     SIZE_FLASH_SECTOR_7},
    {ADDR_FLASH_SECTOR_8,   FLASH_Sector_8,     SIZE_FLASH_SECTOR_8},
    {ADDR_FLASH_SECTOR_9,   FLASH_Sector_9,     SIZE_FLASH_SECTOR_9},
    {ADDR_FLASH_SECTOR_10,  FLASH_Sector_10,    SIZE_FLASH_SECTOR_10},
    {ADDR_FLASH_SECTOR_11,  FLASH_Sector_11,    SIZE_FLASH_SECTOR_11},
};

const unsigned char g_ucEncryptedCode[] =
{
    0x53, 0x41, 0x45, 0x20, 0x4A, 0x32, 0x35, 0x33, 0x34, 0x2D, 0x31, 0x20, 0x64, 0x65, 0x66, 0x69,
    0x6E, 0x65, 0x73, 0x20, 0x61, 0x20, 0x73, 0x74, 0x61, 0x6E, 0x64, 0x61, 0x72, 0x64, 0x20, 0x76,
    0x65, 0x68, 0x69, 0x63, 0x6C, 0x65, 0x20, 0x6E, 0x65, 0x74, 0x77, 0x6F, 0x72, 0x6B, 0x20, 0x69,
    0x6E, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x63, 0x61,
    0x6E, 0x20, 0x62, 0x65, 0x20, 0x75, 0x73, 0x65, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x72, 0x65, 0x70,
    0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x20, 0x65, 0x6D, 0x69, 0x73, 0x73, 0x69, 0x6F, 0x6E, 0x2D,
    0x72, 0x65, 0x6C, 0x61, 0x74, 0x65, 0x64, 0x20, 0x63, 0x6F, 0x6E, 0x74, 0x72, 0x6F, 0x6C, 0x0D,
    0x0A, 0x6D, 0x6F, 0x64, 0x75, 0x6C, 0x65, 0x73, 0x2E, 0x20, 0x48, 0x6F, 0x77, 0x65, 0x76, 0x65,
    0x72, 0x2C, 0x20, 0x74, 0x68, 0x65, 0x72, 0x65, 0x20, 0x69, 0x73, 0x20, 0x61, 0x20, 0x6E, 0x65,
    0x65, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x73, 0x75, 0x70, 0x70, 0x6F, 0x72, 0x74, 0x20, 0x76, 0x65,
    0x68, 0x69, 0x63, 0x6C, 0x65, 0x73, 0x20, 0x70, 0x72, 0x69, 0x6F, 0x72, 0x20, 0x74, 0x6F, 0x20,
    0x74, 0x68, 0x65, 0x20, 0x32, 0x30, 0x30, 0x34, 0x20, 0x6D, 0x6F, 0x64, 0x65, 0x6C, 0x20, 0x79,
    0x65, 0x61, 0x72, 0x20, 0x61, 0x73, 0x20, 0x77, 0x65, 0x6C, 0x6C, 0x20, 0x61, 0x73, 0x20, 0x6E,
    0x6F, 0x6E, 0x2D, 0x65, 0x6D, 0x69, 0x73, 0x73, 0x69, 0x6F, 0x6E, 0x20, 0x72, 0x65, 0x6C, 0x61,
    0x74, 0x65, 0x64, 0x0D, 0x0A, 0x63, 0x6F, 0x6E, 0x74, 0x72, 0x6F, 0x6C, 0x20, 0x6D, 0x6F, 0x64,
    0x75, 0x6C, 0x65, 0x73, 0x2E, 0x0D, 0x0A, 0x54, 0x68, 0x65, 0x20, 0x53, 0x41, 0x45, 0x20, 0x4A,
    0x32, 0x35, 0x33, 0x34, 0x2D, 0x32, 0x20, 0x64, 0x6F, 0x63, 0x75, 0x6D, 0x65, 0x6E, 0x74, 0x20,
    0x6D, 0x65, 0x65, 0x74, 0x73, 0x20, 0x74, 0x68, 0x65, 0x73, 0x65, 0x20, 0x6E, 0x65, 0x65, 0x64,
    0x73, 0x20, 0x62, 0x79, 0x20, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6C, 0x69, 0x6E, 0x67, 0x20, 0x65,
    0x78, 0x74, 0x65, 0x6E, 0x73, 0x69, 0x6F, 0x6E, 0x73, 0x20, 0x74, 0x6F, 0x20, 0x61, 0x6E, 0x20,
    0x53, 0x41, 0x45, 0x20, 0x4A, 0x32, 0x35, 0x33, 0x34, 0x2D, 0x31, 0x20, 0x73, 0x70, 0x65, 0x63,
    0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6F, 0x6E, 0x2E, 0x20, 0x49, 0x74, 0x20, 0x69, 0x73,
    0x20, 0x6E, 0x6F, 0x74, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x0D, 0x0A, 0x66,
    0x6F, 0x72, 0x20, 0x61, 0x6E, 0x20, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x20,
    0x74, 0x6F, 0x20, 0x62, 0x65, 0x20, 0x66, 0x75, 0x6C, 0x6C, 0x79, 0x20, 0x63, 0x6F, 0x6D, 0x70,
    0x6C, 0x69, 0x61, 0x6E, 0x74, 0x20, 0x77, 0x69, 0x74, 0x68, 0x20, 0x53, 0x41, 0x45, 0x20, 0x4A,
    0x32, 0x35, 0x33, 0x34, 0x2D, 0x31, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
    0x74, 0x69, 0x6F, 0x6E, 0x20, 0x74, 0x6F, 0x20, 0x69, 0x6D, 0x70, 0x6C, 0x65, 0x6D, 0x65, 0x6E,
    0x74, 0x20, 0x73, 0x6F, 0x6D, 0x65, 0x20, 0x6F, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x66, 0x65,
    0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
    0x20, 0x69, 0x6E, 0x20, 0x74, 0x68, 0x69, 0x73, 0x0D, 0x0A, 0x64, 0x6F, 0x63, 0x75, 0x6D, 0x65,
    0x6E, 0x74, 0x2E, 0x20, 0x54, 0x6F, 0x67, 0x65, 0x74, 0x68, 0x65, 0x72, 0x2C, 0x20, 0x74, 0x68
};

#define ENCRYPTED_MASKCODE  0x04c11db7


extern void LedBlink(uint8_t color);


void DecryptFileData(unsigned char* pData, unsigned long iStart, unsigned long iEnd)
{
    unsigned char* ptemp = pData;
    for(; iStart < iEnd; iStart++)
    {
    	LedBlink(COLOR_YELLOW);
        unsigned char byte_mask = (ENCRYPTED_MASKCODE >> ((iStart & 0x03) << 3));
        unsigned char temp = *ptemp - g_ucEncryptedCode[~iStart & 0xff];
        *ptemp++ = (byte_mask & g_ucEncryptedCode[iStart & 0xff]) ^ temp;
    }
}

uint32_t CFlashInternal::GetSector(uint32_t addr)
{
    if(addr < ADDR_FLASH_SECTOR_1)
    {
        return FLASH_Sector_0;
    }
    else if(addr < ADDR_FLASH_SECTOR_2)
    {
        return FLASH_Sector_1;
    }
    else if(addr < ADDR_FLASH_SECTOR_3)
    {
        return FLASH_Sector_2;
    }
    else if(addr < ADDR_FLASH_SECTOR_4)
    {
        return FLASH_Sector_3;
    }
    else if(addr < ADDR_FLASH_SECTOR_5)
    {
        return FLASH_Sector_4;
    }
    else if(addr < ADDR_FLASH_SECTOR_6)
    {
        return FLASH_Sector_5;
    }
    else if(addr < ADDR_FLASH_SECTOR_7)
    {
        return FLASH_Sector_6;
    }
    else if(addr < ADDR_FLASH_SECTOR_8)
    {
        return FLASH_Sector_7;
    }
    else if(addr < ADDR_FLASH_SECTOR_9)
    {
        return FLASH_Sector_8;
    }
    else if(addr < ADDR_FLASH_SECTOR_10)
    {
        return FLASH_Sector_9;
    }
    else if(addr < ADDR_FLASH_SECTOR_11)
    {
        return FLASH_Sector_10;
    }
    return FLASH_Sector_11;
}

uint32_t CFlashInternal::GetSectorNumber(uint32_t addr)
{
    uint32_t sector = GetSector(addr);
    uint32_t i = 0;
    for(i = 0;i < 12;i++)
    {
        if(sector == FlashInfo[i].ulSector)
        {
            break;
        }
    }
    return i;
}

uint32_t CFlashInternal::EraseFlash()
{
    int iRetValue = 0;
    FLASH_Unlock();	 //解锁 --- wuyuanfu增加
    FLASH_DataCacheCmd(DISABLE);//FLASH擦除期间,必须禁止数据缓存
    
    //for(int i = GetSectorNumber(FLASH_PROG_START_ADD);i < GetSectorNumber(FLASH_PROG_END_ADD);i++)
    for(int i = GetSectorNumber(FLASH_SHARE_DATA_ADD);i <= GetSectorNumber(FLASH_PROG_END_ADD);i++)
    {
        iRetValue = FLASH_EraseSector(FlashInfo[i].ulSector,VoltageRange_3);
        if(iRetValue != FLASH_COMPLETE)
        {
            FLASH_DataCacheCmd(ENABLE);	//FLASH擦除结束,开启数据缓存
            return iRetValue + CErrorCode::ERR_FLASH_BUSY - 1;
        }
    }
    FLASH_DataCacheCmd(ENABLE);	//FLASH擦除结束,开启数据缓存
    return CErrorCode::STATUS_NOERROR;
}

/*20170703 Kevin modified 加入回读校验和闪存满校验*/
uint32_t CFlashInternal::WriteFlash(uint32_t &uFlashAddr, int iLength, uint32_t &iWritedLenth,  void *pData)
{
    u32 i = 0;
    u32 WriteStart = uFlashAddr;
    u16 *pTempData = (u16 *)pData;
    u16 *pFlash = (u16 *)uFlashAddr;
    u32 iRetValue = CErrorCode::STATUS_NOERROR;
    u32 iRet = CErrorCode::STATUS_NOERROR;
    iWritedLenth = 0X00;
    /*如果写满返回错误*/
    if((iLength  + uFlashAddr - 1) > FLASH_PROG_END_ADD)
    {
        iLength = FLASH_PROG_END_ADD - uFlashAddr + 1;
        return CErrorCode::ERR_FAILED;
    }
    //解决固件长度不是偶数问题
    if((iLength % 2) != 0)
    {
        *(((uint8_t *)pData) + iLength) = 0xFF;
        iLength =  iLength / 2 + 1;
    }
    else
    {
        iLength =  iLength / 2;
    }
    
    FLASH_Unlock();	 //解锁 
    FLASH_DataCacheCmd(DISABLE);//FLASH擦除期间,必须禁止数据缓存
    /*开始写FLASH*/
    for(i = 0; i < iLength; i++)
    {
    	LedBlink(COLOR_YELLOW);
        iRetValue = FLASH_ProgramHalfWord(uFlashAddr, *pTempData);
        pTempData += 1;
        if(iRetValue != FLASH_COMPLETE)
        {
            iRet = iRetValue + CErrorCode::ERR_FLASH_BUSY - 1;
            goto EXIT;
        }
        uFlashAddr += 2;
        iWritedLenth += 2;
    }
    /*开始回读*/
    uFlashAddr = WriteStart;/*写完之后，增加了回读校验，还原uFlashAddr和pTempData指针初值*/
    pTempData = (u16 *)pData;
    for(i = 0; i < iLength; i++)
    {
        if(pFlash[i] != *pTempData)/*每一包读闪存判断跟不跟想写入一样，如果不是，重写，再不成功，返回错误*/
        {
            /*f407页数据较大，会误擦其它数据，造成boot升级错误，wuyuanfu-2021-09-23
            FLASH_EraseSector(GetSector(uFlashAddr),VoltageRange_3);
            uFlashAddr = WriteStart;
            pTempData = (u16 *)pData;
            for(i = 0; i < iLength; i++)
            {
                iRetValue = FLASH_ProgramHalfWord(uFlashAddr, *pTempData);
                pTempData += 1;
                if(iRetValue != FLASH_COMPLETE)
                {
                    iRet = iRetValue + CErrorCode::ERR_FLASH_BUSY - 1;
                    goto EXIT;
                }
                uFlashAddr += 2;
            }
            uFlashAddr = WriteStart;
            pTempData = (u16 *)pData;
            for(i = 0; i < iLength; i++)
            {
                if(pFlash[i] != *pTempData)
                {
                    iRet = CErrorCode::ERR_FAILED ;
                    goto EXIT;
                }
                pTempData += 1;
                uFlashAddr += 2;
            }
            iRet = CErrorCode::STATUS_NOERROR;*/
          
            iRet = CErrorCode::ERR_FAILED;
            //kprintf("\n\n\n boot is update error ERR_FAILED  = %x\n\n\n",i);
            goto EXIT;
        }
        pTempData += 1;
        uFlashAddr += 2;
    }
EXIT:
    FLASH_DataCacheCmd(ENABLE);	//FLASH擦除结束,开启数据缓存
    FLASH_Lock();//上锁
    return iRet;
}
