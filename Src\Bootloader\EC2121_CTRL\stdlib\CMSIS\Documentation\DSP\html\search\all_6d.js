var searchData=
[
  ['matrix_20functions',['Matrix Functions',['../group__group_matrix.html',1,'']]],
  ['m',['M',['../structarm__fir__decimate__instance__q15.html#aad9320284218b3aa378527ea518cf093',1,'arm_fir_decimate_instance_q15::M()'],['../structarm__fir__decimate__instance__q31.html#ad3d6936c36303b30dd38f1eddf248ae5',1,'arm_fir_decimate_instance_q31::M()'],['../structarm__fir__decimate__instance__f32.html#a76a8b2161731638eb3d67f277919f95d',1,'arm_fir_decimate_instance_f32::M()']]],
  ['m0',['M0',['../arm__class__marks__example_2_a_r_m_2_abstract_8txt.html#a59a24f1db2c97fc0ad7948b4a74267ee',1,'M0():&#160;Abstract.txt'],['../arm__class__marks__example_2_g_c_c_2_abstract_8txt.html#a59a24f1db2c97fc0ad7948b4a74267ee',1,'M0():&#160;Abstract.txt'],['../arm__convolution__example_2_a_r_m_2_abstract_8txt.html#adc5db9f8d5aa735dbce2dc35f184d85e',1,'M0():&#160;Abstract.txt'],['../arm__convolution__example_2_g_c_c_2_abstract_8txt.html#adc5db9f8d5aa735dbce2dc35f184d85e',1,'M0():&#160;Abstract.txt'],['../arm__dotproduct__example_2_a_r_m_2_abstract_8txt.html#aafffda4c4e9b93a580e5a8cc96c11e37',1,'M0():&#160;Abstract.txt'],['../arm__dotproduct__example_2_g_c_c_2_abstract_8txt.html#aafffda4c4e9b93a580e5a8cc96c11e37',1,'M0():&#160;Abstract.txt'],['../arm__fft__bin__example_2_a_r_m_2_abstract_8txt.html#ad0415ef995ebc3fc2ad584da9907dcb5',1,'M0():&#160;Abstract.txt'],['../arm__fft__bin__example_2_g_c_c_2_abstract_8txt.html#ad0415ef995ebc3fc2ad584da9907dcb5',1,'M0():&#160;Abstract.txt'],['../arm__fir__example_2_a_r_m_2_abstract_8txt.html#abf726d1cea9345acb0021535d4fdd5af',1,'M0():&#160;Abstract.txt'],['../arm__graphic__equalizer__example_2_a_r_m_2_abstract_8txt.html#ac378b27aa1fc4fe4ac9a4dfa2d185553',1,'M0():&#160;Abstract.txt'],['../arm__linear__interp__example_2_a_r_m_2_abstract_8txt.html#a80f8916aca7a5a98fcfc39d1ef743b37',1,'M0():&#160;Abstract.txt'],['../arm__matrix__example_2_a_r_m_2_abstract_8txt.html#a267aee43e600168b057c1aa126029002',1,'M0():&#160;Abstract.txt'],['../arm__signal__converge__example_2_a_r_m_2_abstract_8txt.html#a20d2ac954144a7f2d4aced0816eecef6',1,'M0():&#160;Abstract.txt'],['../arm__sin__cos__example_2_a_r_m_2_abstract_8txt.html#ae39c147711857790fd5c3cc7a75ef0c3',1,'M0():&#160;Abstract.txt'],['../arm__variance__example_2_a_r_m_2_abstract_8txt.html#a8c8cf1e0f2ebd6135c79d5338a60899c',1,'M0():&#160;Abstract.txt']]],
  ['main',['main',['../_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc',1,'main():&#160;arm_class_marks_example_f32.c'],['../_g_c_c_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc',1,'main():&#160;arm_class_marks_example_f32.c'],['../_a_r_m_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_convolution_example_f32.c'],['../_g_c_c_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_convolution_example_f32.c'],['../_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_dotproduct_example_f32.c'],['../_g_c_c_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_dotproduct_example_f32.c'],['../_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_fft_bin_example_f32.c'],['../_g_c_c_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_fft_bin_example_f32.c'],['../arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_fir_example_f32.c'],['../arm__graphic__equalizer__example__q31_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_graphic_equalizer_example_q31.c'],['../arm__linear__interp__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_linear_interp_example_f32.c'],['../arm__matrix__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_matrix_example_f32.c'],['../arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_signal_converge_example_f32.c'],['../arm__sin__cos__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_sin_cos_example_f32.c'],['../arm__variance__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65',1,'main(void):&#160;arm_variance_example_f32.c']]],
  ['matrix_20addition',['Matrix Addition',['../group___matrix_add.html',1,'']]],
  ['matrix_20example',['Matrix Example',['../group___matrix_example.html',1,'']]],
  ['matrix_20initialization',['Matrix Initialization',['../group___matrix_init.html',1,'']]],
  ['matrix_20inverse',['Matrix Inverse',['../group___matrix_inv.html',1,'']]],
  ['matrix_20multiplication',['Matrix Multiplication',['../group___matrix_mult.html',1,'']]],
  ['matrix_20scale',['Matrix Scale',['../group___matrix_scale.html',1,'']]],
  ['matrix_20subtraction',['Matrix Subtraction',['../group___matrix_sub.html',1,'']]],
  ['matrix_20transpose',['Matrix Transpose',['../group___matrix_trans.html',1,'']]],
  ['maximum',['Maximum',['../group___max.html',1,'']]],
  ['max_5fblocksize',['MAX_BLOCKSIZE',['../_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b',1,'MAX_BLOCKSIZE():&#160;arm_convolution_example_f32.c'],['../_g_c_c_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b',1,'MAX_BLOCKSIZE():&#160;arm_convolution_example_f32.c'],['../_a_r_m_2arm__dotproduct__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b',1,'MAX_BLOCKSIZE():&#160;arm_dotproduct_example_f32.c'],['../_g_c_c_2arm__dotproduct__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b',1,'MAX_BLOCKSIZE():&#160;arm_dotproduct_example_f32.c'],['../arm__sin__cos__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b',1,'MAX_BLOCKSIZE():&#160;arm_sin_cos_example_f32.c'],['../arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b',1,'MAX_BLOCKSIZE():&#160;arm_variance_example_f32.c']]],
  ['max_5fmarks',['max_marks',['../_a_r_m_2arm__class__marks__example__f32_8c.html#aad32888fa966b3d9db9c31bcbba9d9ef',1,'max_marks():&#160;arm_class_marks_example_f32.c'],['../_g_c_c_2arm__class__marks__example__f32_8c.html#aad32888fa966b3d9db9c31bcbba9d9ef',1,'max_marks():&#160;arm_class_marks_example_f32.c']]],
  ['maxdelay',['maxDelay',['../structarm__fir__sparse__instance__f32.html#af8b8c775f4084c36774f06c082b4c078',1,'arm_fir_sparse_instance_f32::maxDelay()'],['../structarm__fir__sparse__instance__q31.html#afdd3a1dc72132c854dc379154b68b674',1,'arm_fir_sparse_instance_q31::maxDelay()'],['../structarm__fir__sparse__instance__q15.html#ad14cc1070eecf7e1926d8f67a8273182',1,'arm_fir_sparse_instance_q15::maxDelay()'],['../structarm__fir__sparse__instance__q7.html#af74dacc1d34c078283e50f2530eb91df',1,'arm_fir_sparse_instance_q7::maxDelay()']]],
  ['mean',['mean',['../_a_r_m_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681',1,'mean():&#160;arm_class_marks_example_f32.c'],['../_g_c_c_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681',1,'mean():&#160;arm_class_marks_example_f32.c'],['../group__mean.html',1,'(Global Namespace)']]],
  ['merge_5frfft_5ff32',['merge_rfft_f32',['../arm__rfft__fast__f32_8c.html#a93258bc1e64a939a8ebd086367e459af',1,'arm_rfft_fast_f32.c']]],
  ['minimum',['Minimum',['../group___min.html',1,'']]],
  ['min_5fmarks',['min_marks',['../_a_r_m_2arm__class__marks__example__f32_8c.html#abb7687fa07ec54d8e792cfcbfe2ca809',1,'min_marks():&#160;arm_class_marks_example_f32.c'],['../_g_c_c_2arm__class__marks__example__f32_8c.html#abb7687fa07ec54d8e792cfcbfe2ca809',1,'min_marks():&#160;arm_class_marks_example_f32.c']]],
  ['mu',['mu',['../structarm__lms__instance__f32.html#ae2af43d74c93dba16b876e10c97a5b99',1,'arm_lms_instance_f32::mu()'],['../structarm__lms__instance__q15.html#aae46129d7cfd7f1c162cc502ed0a9d49',1,'arm_lms_instance_q15::mu()'],['../structarm__lms__instance__q31.html#acb6ca9996b3c5f740d5d6c8e9f4f1d46',1,'arm_lms_instance_q31::mu()'],['../structarm__lms__norm__instance__f32.html#a84401d3cfc6c40f69c08223cf341b886',1,'arm_lms_norm_instance_f32::mu()'],['../structarm__lms__norm__instance__q31.html#ad3dd2a2406e02fdaa7782ba6c3940a64',1,'arm_lms_norm_instance_q31::mu()'],['../structarm__lms__norm__instance__q15.html#a7ce00f21d11cfda6d963240641deea8c',1,'arm_lms_norm_instance_q15::mu()'],['../arm__signal__converge__example__f32_8c.html#a09bc9e6a44f0291cfcf578f2efcddfab',1,'MU():&#160;arm_signal_converge_example_f32.c']]],
  ['mult32x64',['mult32x64',['../arm__math_8h.html#a642a29d71f7951a7f6c0b797c300b711',1,'arm_math.h']]],
  ['mult_5f32x32_5fkeep32',['mult_32x32_keep32',['../arm__math_8h.html#abb4baa0192bbb6fabc9251af4b4cb322',1,'arm_math.h']]],
  ['mult_5f32x32_5fkeep32_5fr',['mult_32x32_keep32_R',['../arm__math_8h.html#a960f210642058d2b3d5368729a6e8375',1,'arm_math.h']]],
  ['multacc_5f32x32_5fkeep32',['multAcc_32x32_keep32',['../arm__math_8h.html#a58454519e12e8157f0a1c36071333655',1,'arm_math.h']]],
  ['multacc_5f32x32_5fkeep32_5fr',['multAcc_32x32_keep32_R',['../arm__math_8h.html#aba3e538352fc7f9d6d15f9a18d469399',1,'arm_math.h']]],
  ['multoutput',['multOutput',['../_a_r_m_2arm__dotproduct__example__f32_8c.html#ad0bfd425dfe1ff2bda80fb957e464098',1,'multOutput():&#160;arm_dotproduct_example_f32.c'],['../_g_c_c_2arm__dotproduct__example__f32_8c.html#ad0bfd425dfe1ff2bda80fb957e464098',1,'multOutput():&#160;arm_dotproduct_example_f32.c']]],
  ['multsub_5f32x32_5fkeep32',['multSub_32x32_keep32',['../arm__math_8h.html#a9ec66f3082a4c65c78075638255f42ab',1,'arm_math.h']]],
  ['multsub_5f32x32_5fkeep32_5fr',['multSub_32x32_keep32_R',['../arm__math_8h.html#a668fbf1cd1c3bc8faf1b1c83964ade23',1,'arm_math.h']]]
];
