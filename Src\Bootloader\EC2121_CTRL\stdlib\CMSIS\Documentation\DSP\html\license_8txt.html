<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-DSP: license.txt File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.4</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li class="current"><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('license_8txt.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">license.txt File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:afc8082d353720b09f7f84a3e99b3a915"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY OR CONSEQUENTIAL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#afc8082d353720b09f7f84a3e99b3a915">DAMAGES</a> (<a class="el" href="license_8txt.html#ada26d66c5114ffca1170ee8b231c879d">INCLUDING</a>, BUT NOT LIMITED <a class="el" href="license_8txt.html#ab825d0b655b4239ccc38eaf201996c49">TO</a>, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;LOSS OF USE, DATA, OR PROFITS;OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF <a class="el" href="license_8txt.html#abdaf89273b995cd2d18e467160bb019b">LIABILITY</a></td></tr>
<tr class="separator:afc8082d353720b09f7f84a3e99b3a915"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a939bfb6f16767ac1b4ccd807a5d099a2"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY OR CONSEQUENTIAL WHETHER <br class="typebreak"/>
IN STRICT OR&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#a939bfb6f16767ac1b4ccd807a5d099a2">TORT</a> (<a class="el" href="license_8txt.html#ada26d66c5114ffca1170ee8b231c879d">INCLUDING</a> NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE</td></tr>
<tr class="separator:a939bfb6f16767ac1b4ccd807a5d099a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ab135b2121e9191734cb723af1165eb77"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#ab135b2121e9191734cb723af1165eb77">license</a></td></tr>
<tr class="separator:ab135b2121e9191734cb723af1165eb77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2329c741a6626579aa45877de35f8ff0"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#a2329c741a6626579aa45877de35f8ff0">modification</a></td></tr>
<tr class="separator:a2329c741a6626579aa45877de35f8ff0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30cf0b06853538c11b0c0d2d578d749e"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#a30cf0b06853538c11b0c0d2d578d749e">met</a></td></tr>
<tr class="separator:a30cf0b06853538c11b0c0d2d578d749e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59d3e1cb1e71d555eac80f55a2d7d9c4"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#a59d3e1cb1e71d555eac80f55a2d7d9c4">notice</a></td></tr>
<tr class="separator:a59d3e1cb1e71d555eac80f55a2d7d9c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24ec3fd7c1df8a2f4785d5c8791bad99"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a></td></tr>
<tr class="separator:a24ec3fd7c1df8a2f4785d5c8791bad99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada26d66c5114ffca1170ee8b231c879d"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#ada26d66c5114ffca1170ee8b231c879d">INCLUDING</a></td></tr>
<tr class="separator:ada26d66c5114ffca1170ee8b231c879d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab825d0b655b4239ccc38eaf201996c49"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#ab825d0b655b4239ccc38eaf201996c49">TO</a></td></tr>
<tr class="separator:ab825d0b655b4239ccc38eaf201996c49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9afdc0bba232070031f8010f3a4d6dd"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#ad9afdc0bba232070031f8010f3a4d6dd">DIRECT</a></td></tr>
<tr class="separator:ad9afdc0bba232070031f8010f3a4d6dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a095f9237ce367023024c8ccb8f8229d0"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#a095f9237ce367023024c8ccb8f8229d0">INDIRECT</a></td></tr>
<tr class="separator:a095f9237ce367023024c8ccb8f8229d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:accf7a56ff94c1269298ca951d17edc13"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#accf7a56ff94c1269298ca951d17edc13">INCIDENTAL</a></td></tr>
<tr class="separator:accf7a56ff94c1269298ca951d17edc13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaee2f5441fbc21b3aa611c5192f505ae"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#aaee2f5441fbc21b3aa611c5192f505ae">SPECIAL</a></td></tr>
<tr class="separator:aaee2f5441fbc21b3aa611c5192f505ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae63ad52c9dceab675153066e674486d9"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#ae63ad52c9dceab675153066e674486d9">EXEMPLARY</a></td></tr>
<tr class="separator:ae63ad52c9dceab675153066e674486d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10f0467b31dafce8635381fbfc31df83"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY OR CONSEQUENTIAL WHETHER <br class="typebreak"/>
IN&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#a10f0467b31dafce8635381fbfc31df83">CONTRACT</a></td></tr>
<tr class="separator:a10f0467b31dafce8635381fbfc31df83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdaf89273b995cd2d18e467160bb019b"><td class="memItemLeft" align="right" valign="top">All files contained in the <br class="typebreak"/>
folders CMSIS DSP Lib Source <br class="typebreak"/>
and CMSIS DSP Lib Examples are <br class="typebreak"/>
guided by the following with <br class="typebreak"/>
or without are permitted <br class="typebreak"/>
provided that the following <br class="typebreak"/>
conditions are this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer Redistributions in <br class="typebreak"/>
binary form must reproduce the <br class="typebreak"/>
above copyright this list of <br class="typebreak"/>
conditions and the following <br class="typebreak"/>
disclaimer in the <br class="typebreak"/>
documentation and or other <br class="typebreak"/>
materials provided with the <br class="typebreak"/>
distribution Neither the name <br class="typebreak"/>
of ARM nor the names of its <br class="typebreak"/>
contributors may be used to <br class="typebreak"/>
endorse or promote products <br class="typebreak"/>
derived from this software <br class="typebreak"/>
without specific prior written <br class="typebreak"/>
permission THIS SOFTWARE IS <br class="typebreak"/>
PROVIDED BY THE COPYRIGHT <br class="typebreak"/>
HOLDERS AND CONTRIBUTORS AS IS <br class="typebreak"/>
AND ANY EXPRESS OR IMPLIED BUT <br class="typebreak"/>
NOT LIMITED THE IMPLIED <br class="typebreak"/>
<a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY <br class="typebreak"/>
AND FITNESS FOR A PARTICULAR <br class="typebreak"/>
PURPOSE ARE DISCLAIMED IN NO <br class="typebreak"/>
EVENT SHALL COPYRIGHT HOLDERS <br class="typebreak"/>
AND CONTRIBUTORS BE LIABLE FOR <br class="typebreak"/>
ANY OR CONSEQUENTIAL WHETHER <br class="typebreak"/>
IN STRICT&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="license_8txt.html#abdaf89273b995cd2d18e467160bb019b">LIABILITY</a></td></tr>
<tr class="separator:abdaf89273b995cd2d18e467160bb019b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="afc8082d353720b09f7f84a3e99b3a915"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY OR CONSEQUENTIAL DAMAGES </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="license_8txt.html#ada26d66c5114ffca1170ee8b231c879d">INCLUDING</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">BUT NOT LIMITED&#160;</td>
          <td class="paramname"><em>TO</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;LOSS OF&#160;</td>
          <td class="paramname"><em>USE</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">DATA&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">OR PROFITS;OR BUSINESS&#160;</td>
          <td class="paramname"><em>INTERRUPTION</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a939bfb6f16767ac1b4ccd807a5d099a2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY OR CONSEQUENTIAL WHETHER IN STRICT OR TORT </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="license_8txt.html#ada26d66c5114ffca1170ee8b231c879d">INCLUDING</a> NEGLIGENCE OR&#160;</td>
          <td class="paramname"><em>OTHERWISE</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="a10f0467b31dafce8635381fbfc31df83"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY OR CONSEQUENTIAL WHETHER IN CONTRACT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad9afdc0bba232070031f8010f3a4d6dd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY DIRECT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae63ad52c9dceab675153066e674486d9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY EXEMPLARY</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="accf7a56ff94c1269298ca951d17edc13"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY INCIDENTAL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ada26d66c5114ffca1170ee8b231c879d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED INCLUDING</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a095f9237ce367023024c8ccb8f8229d0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY INDIRECT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abdaf89273b995cd2d18e467160bb019b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY OR CONSEQUENTIAL WHETHER IN STRICT LIABILITY</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab135b2121e9191734cb723af1165eb77"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following license</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a30cf0b06853538c11b0c0d2d578d749e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are met</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2329c741a6626579aa45877de35f8ff0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without modification</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a59d3e1cb1e71d555eac80f55a2d7d9c4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright notice</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aaee2f5441fbc21b3aa611c5192f505ae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED THE IMPLIED <a class="el" href="license_8txt.html#a24ec3fd7c1df8a2f4785d5c8791bad99">WARRANTIES</a> OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY SPECIAL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab825d0b655b4239ccc38eaf201996c49"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED BUT NOT LIMITED TO</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a24ec3fd7c1df8a2f4785d5c8791bad99"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">All files contained in the folders CMSIS DSP Lib Source and CMSIS DSP Lib Examples are guided by the following with or without are permitted provided that the following conditions are this list of conditions and the following disclaimer Redistributions in binary form must reproduce the above copyright this list of conditions and the following disclaimer in the documentation and or other materials provided with the distribution Neither the name of ARM nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY EXPRESS OR IMPLIED WARRANTIES</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="license_8txt.html">license.txt</a></li>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:59 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
