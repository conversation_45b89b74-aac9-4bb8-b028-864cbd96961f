@echo off
color 3f
::GAZER

set curruentdir=%cd%
set projectdirApp=%cd%
set targetdir=%1
set targetnameApp=%projectdirApp%\boot.ewp

set objlogApp=compileApp.txt

SET CC=
if exist "C:\Program Files (x86)\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe" (SET CC="C:\Program Files (x86)\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe")
if exist "D:\Program Files (x86)\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe" (SET CC="D:\Program Files (x86)\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe")
if exist "E:\Program Files (x86)\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe" (SET CC="E:\Program Files (x86)\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe")
if exist "F:\Program Files (x86)\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe" (SET CC="F:\Program Files (x86)\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe")
if exist "C:\Program Files\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe" (SET CC="C:\Program Files\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe")
if exist "D:\Program Files\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe" (SET CC="D:\Program Files\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe")
if exist "E:\Program Files\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe" (SET CC="E:\Program Files\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe")
if exist "F:\Program Files\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe" (SET CC="F:\Program Files\IAR Systems\Embedded Workbench 7.3\common\bin\iarbuild.exe")

if %CC%=="" (
    @echo 没找到IAR的安装位置，请修改批处理以指定其位置
    goto compile_failed
)

@REM 获取版本号
set inputname=Version.h

for /f "tokens=5*" %%i in (%inputname%) do (
set 	"s=%%i"
goto next
)
:next
set version=%s:~0,-2%
@REM 获取版本号↑
echo %s:~0,-2%

echo.   
echo.    

:compile
@REM 删除已经存在的文件夹
if exist %curruentdir%\version rd /S /Q %curruentdir%\version
if exist %curruentdir%\WallBoxAC_%version% rd /S /Q %curruentdir%\WallBoxAC_%version%


@REM 删除已经存在的.bin .aut .out文件
if exist %projectdirApp%\WallBoxAC\Exe\*.bin  del /f /Q %projectdirApp%\WallBoxAC\Exe\*.bin 
if exist %projectdirApp%\WallBoxAC\Exe\*.hex  del /f /Q %projectdirApp%\WallBoxAC\Exe\*.hex 
if exist %projectdirApp%\WallBoxAC\Exe\*.aut  del /f /Q %projectdirApp%\WallBoxAC\Exe\*.aut
if exist %projectdirApp%\WallBoxAC\Exe\*.out  del /f /Q %projectdirApp%\WallBoxAC\Exe\*.out


echo ************** 开始编译 WallBoxAC 下位机BOOT ****************
SET configPrj=WallBoxAC
echo %targetnameApp%
%CC% %targetnameApp% -build %configPrj% -log errors


if errorlevel 1 goto compile_App_failed

if not exist %projectdirApp%\WallBoxAC\Exe\WallBoxAC_BOOT.bin goto compile_App_failed
if not exist %projectdirApp%\WallBoxAC\Exe\WallBoxAC_BOOT.out goto compile_App_failed

echo 固件编译完成

@REM 创建version文件夹用来放固件
mkdir %curruentdir%\version


echo REM 拷贝固件到version文件夹下
if exist %projectdirApp%\WallBoxAC\Exe\WallBoxAC_BOOT.bin copy  %projectdirApp%\WallBoxAC\Exe\WallBoxAC_BOOT.bin    %curruentdir%\version\Boot_ECC01_%version%.bin

if errorlevel 1 goto compile_App_failed
@REM echo.
@REM if exist %curruentdir%\%objlog%  type %curruentdir%\%objlog%  
@REM echo.
@REM find  /C  "0 Error" %currentdir%\%objlog% >NUL
@REM if %ERRORLEVEL% EQU 1  goto compile_failed
 echo.
 echo ***************  编译 WallBoxAC 下位机BOOT成功 ***************
 echo.
 echo.
@REM	 pause
goto end

:compile_Del_failed
	echo 删除旧文件失败
	goto compile_failed

:compile_App_failed
@REM	for /f "tokens=*" %%c in (%objlogApp%) do (
@REM	    echo %%c
@REM	)
	echo 编译固件失败
	goto compile_failed

:compile_failed
	echo *************** 编译 WallBoxAC 下位机BOOT失败 ***************
@REM	pause

    exit 1
:end
