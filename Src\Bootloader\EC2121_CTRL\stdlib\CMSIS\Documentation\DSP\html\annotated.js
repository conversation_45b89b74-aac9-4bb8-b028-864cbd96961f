var annotated =
[
    [ "arm_bilinear_interp_instance_f32", "structarm__bilinear__interp__instance__f32.html", "structarm__bilinear__interp__instance__f32" ],
    [ "arm_bilinear_interp_instance_q15", "structarm__bilinear__interp__instance__q15.html", "structarm__bilinear__interp__instance__q15" ],
    [ "arm_bilinear_interp_instance_q31", "structarm__bilinear__interp__instance__q31.html", "structarm__bilinear__interp__instance__q31" ],
    [ "arm_bilinear_interp_instance_q7", "structarm__bilinear__interp__instance__q7.html", "structarm__bilinear__interp__instance__q7" ],
    [ "arm_biquad_cas_df1_32x64_ins_q31", "structarm__biquad__cas__df1__32x64__ins__q31.html", "structarm__biquad__cas__df1__32x64__ins__q31" ],
    [ "arm_biquad_cascade_df2T_instance_f32", "structarm__biquad__cascade__df2_t__instance__f32.html", "structarm__biquad__cascade__df2_t__instance__f32" ],
    [ "arm_biquad_cascade_df2T_instance_f64", "structarm__biquad__cascade__df2_t__instance__f64.html", "structarm__biquad__cascade__df2_t__instance__f64" ],
    [ "arm_biquad_cascade_stereo_df2T_instance_f32", "structarm__biquad__cascade__stereo__df2_t__instance__f32.html", "structarm__biquad__cascade__stereo__df2_t__instance__f32" ],
    [ "arm_biquad_casd_df1_inst_f32", "structarm__biquad__casd__df1__inst__f32.html", "structarm__biquad__casd__df1__inst__f32" ],
    [ "arm_biquad_casd_df1_inst_q15", "structarm__biquad__casd__df1__inst__q15.html", "structarm__biquad__casd__df1__inst__q15" ],
    [ "arm_biquad_casd_df1_inst_q31", "structarm__biquad__casd__df1__inst__q31.html", "structarm__biquad__casd__df1__inst__q31" ],
    [ "arm_cfft_instance_f32", "structarm__cfft__instance__f32.html", "structarm__cfft__instance__f32" ],
    [ "arm_cfft_instance_q15", "structarm__cfft__instance__q15.html", "structarm__cfft__instance__q15" ],
    [ "arm_cfft_instance_q31", "structarm__cfft__instance__q31.html", "structarm__cfft__instance__q31" ],
    [ "arm_cfft_radix2_instance_f32", "structarm__cfft__radix2__instance__f32.html", "structarm__cfft__radix2__instance__f32" ],
    [ "arm_cfft_radix2_instance_q15", "structarm__cfft__radix2__instance__q15.html", "structarm__cfft__radix2__instance__q15" ],
    [ "arm_cfft_radix2_instance_q31", "structarm__cfft__radix2__instance__q31.html", "structarm__cfft__radix2__instance__q31" ],
    [ "arm_cfft_radix4_instance_f32", "structarm__cfft__radix4__instance__f32.html", "structarm__cfft__radix4__instance__f32" ],
    [ "arm_cfft_radix4_instance_q15", "structarm__cfft__radix4__instance__q15.html", "structarm__cfft__radix4__instance__q15" ],
    [ "arm_cfft_radix4_instance_q31", "structarm__cfft__radix4__instance__q31.html", "structarm__cfft__radix4__instance__q31" ],
    [ "arm_dct4_instance_f32", "structarm__dct4__instance__f32.html", "structarm__dct4__instance__f32" ],
    [ "arm_dct4_instance_q15", "structarm__dct4__instance__q15.html", "structarm__dct4__instance__q15" ],
    [ "arm_dct4_instance_q31", "structarm__dct4__instance__q31.html", "structarm__dct4__instance__q31" ],
    [ "arm_fir_decimate_instance_f32", "structarm__fir__decimate__instance__f32.html", "structarm__fir__decimate__instance__f32" ],
    [ "arm_fir_decimate_instance_q15", "structarm__fir__decimate__instance__q15.html", "structarm__fir__decimate__instance__q15" ],
    [ "arm_fir_decimate_instance_q31", "structarm__fir__decimate__instance__q31.html", "structarm__fir__decimate__instance__q31" ],
    [ "arm_fir_instance_f32", "structarm__fir__instance__f32.html", "structarm__fir__instance__f32" ],
    [ "arm_fir_instance_q15", "structarm__fir__instance__q15.html", "structarm__fir__instance__q15" ],
    [ "arm_fir_instance_q31", "structarm__fir__instance__q31.html", "structarm__fir__instance__q31" ],
    [ "arm_fir_instance_q7", "structarm__fir__instance__q7.html", "structarm__fir__instance__q7" ],
    [ "arm_fir_interpolate_instance_f32", "structarm__fir__interpolate__instance__f32.html", "structarm__fir__interpolate__instance__f32" ],
    [ "arm_fir_interpolate_instance_q15", "structarm__fir__interpolate__instance__q15.html", "structarm__fir__interpolate__instance__q15" ],
    [ "arm_fir_interpolate_instance_q31", "structarm__fir__interpolate__instance__q31.html", "structarm__fir__interpolate__instance__q31" ],
    [ "arm_fir_lattice_instance_f32", "structarm__fir__lattice__instance__f32.html", "structarm__fir__lattice__instance__f32" ],
    [ "arm_fir_lattice_instance_q15", "structarm__fir__lattice__instance__q15.html", "structarm__fir__lattice__instance__q15" ],
    [ "arm_fir_lattice_instance_q31", "structarm__fir__lattice__instance__q31.html", "structarm__fir__lattice__instance__q31" ],
    [ "arm_fir_sparse_instance_f32", "structarm__fir__sparse__instance__f32.html", "structarm__fir__sparse__instance__f32" ],
    [ "arm_fir_sparse_instance_q15", "structarm__fir__sparse__instance__q15.html", "structarm__fir__sparse__instance__q15" ],
    [ "arm_fir_sparse_instance_q31", "structarm__fir__sparse__instance__q31.html", "structarm__fir__sparse__instance__q31" ],
    [ "arm_fir_sparse_instance_q7", "structarm__fir__sparse__instance__q7.html", "structarm__fir__sparse__instance__q7" ],
    [ "arm_iir_lattice_instance_f32", "structarm__iir__lattice__instance__f32.html", "structarm__iir__lattice__instance__f32" ],
    [ "arm_iir_lattice_instance_q15", "structarm__iir__lattice__instance__q15.html", "structarm__iir__lattice__instance__q15" ],
    [ "arm_iir_lattice_instance_q31", "structarm__iir__lattice__instance__q31.html", "structarm__iir__lattice__instance__q31" ],
    [ "arm_linear_interp_instance_f32", "structarm__linear__interp__instance__f32.html", "structarm__linear__interp__instance__f32" ],
    [ "arm_lms_instance_f32", "structarm__lms__instance__f32.html", "structarm__lms__instance__f32" ],
    [ "arm_lms_instance_q15", "structarm__lms__instance__q15.html", "structarm__lms__instance__q15" ],
    [ "arm_lms_instance_q31", "structarm__lms__instance__q31.html", "structarm__lms__instance__q31" ],
    [ "arm_lms_norm_instance_f32", "structarm__lms__norm__instance__f32.html", "structarm__lms__norm__instance__f32" ],
    [ "arm_lms_norm_instance_q15", "structarm__lms__norm__instance__q15.html", "structarm__lms__norm__instance__q15" ],
    [ "arm_lms_norm_instance_q31", "structarm__lms__norm__instance__q31.html", "structarm__lms__norm__instance__q31" ],
    [ "arm_matrix_instance_f32", "structarm__matrix__instance__f32.html", "structarm__matrix__instance__f32" ],
    [ "arm_matrix_instance_f64", "structarm__matrix__instance__f64.html", "structarm__matrix__instance__f64" ],
    [ "arm_matrix_instance_q15", "structarm__matrix__instance__q15.html", "structarm__matrix__instance__q15" ],
    [ "arm_matrix_instance_q31", "structarm__matrix__instance__q31.html", "structarm__matrix__instance__q31" ],
    [ "arm_pid_instance_f32", "structarm__pid__instance__f32.html", "structarm__pid__instance__f32" ],
    [ "arm_pid_instance_q15", "structarm__pid__instance__q15.html", "structarm__pid__instance__q15" ],
    [ "arm_pid_instance_q31", "structarm__pid__instance__q31.html", "structarm__pid__instance__q31" ],
    [ "arm_rfft_fast_instance_f32", "structarm__rfft__fast__instance__f32.html", "structarm__rfft__fast__instance__f32" ],
    [ "arm_rfft_instance_f32", "structarm__rfft__instance__f32.html", "structarm__rfft__instance__f32" ],
    [ "arm_rfft_instance_q15", "structarm__rfft__instance__q15.html", "structarm__rfft__instance__q15" ],
    [ "arm_rfft_instance_q31", "structarm__rfft__instance__q31.html", "structarm__rfft__instance__q31" ]
];