<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_func.html#index_a"><span>a</span></a></li>
      <li><a href="globals_func_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_func_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_func_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_func_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_func_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_func_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_func_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_func_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_func_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_func_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_func_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_func_0x71.html#index_q"><span>q</span></a></li>
      <li class="current"><a href="globals_func_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_func_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_func_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_func_0x76.html#index_v"><span>v</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_func_0x72.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_r"></a>- r -</h3><ul>
<li>arm_radix2_butterfly_f32()
: <a class="el" href="arm__cfft__radix2__f32_8c.html#a04631e102b5209af3402b225b1abe868">arm_cfft_radix2_f32.c</a>
</li>
<li>arm_radix2_butterfly_inverse_f32()
: <a class="el" href="arm__cfft__radix2__f32_8c.html#abda34af152e515a95ac38470ac053b77">arm_cfft_radix2_f32.c</a>
</li>
<li>arm_radix2_butterfly_inverse_q15()
: <a class="el" href="arm__cfft__radix2__q15_8c.html#a91ff93fa10757b3872680fec4835c412">arm_cfft_radix2_q15.c</a>
</li>
<li>arm_radix2_butterfly_inverse_q31()
: <a class="el" href="arm__cfft__radix2__q31_8c.html#a4d665f67080455b95cafec04009fd52d">arm_cfft_radix2_q31.c</a>
</li>
<li>arm_radix2_butterfly_q15()
: <a class="el" href="arm__cfft__radix2__q15_8c.html#a521780ba2fd3450cbf02784e38859699">arm_cfft_radix2_q15.c</a>
</li>
<li>arm_radix2_butterfly_q31()
: <a class="el" href="arm__cfft__radix2__q31_8c.html#a740f4fe69e6148d22fc99f374d304e7e">arm_cfft_radix2_q31.c</a>
</li>
<li>arm_radix4_butterfly_f32()
: <a class="el" href="group__group_transforms.html#gae239ddf995d1607115f9e84d5c069b9c">arm_rfft_f32.c</a>
, <a class="el" href="group__group_transforms.html#gae239ddf995d1607115f9e84d5c069b9c">arm_cfft_radix4_f32.c</a>
</li>
<li>arm_radix4_butterfly_inverse_f32()
: <a class="el" href="arm__cfft__radix4__f32_8c.html#a2a78df6e4bbf080624f2b6349224ec93">arm_cfft_radix4_f32.c</a>
, <a class="el" href="arm__rfft__f32_8c.html#a2a78df6e4bbf080624f2b6349224ec93">arm_rfft_f32.c</a>
</li>
<li>arm_radix4_butterfly_inverse_q15()
: <a class="el" href="arm__cfft__q15_8c.html#a734ecff00f21a6a10e9ec437c8987eb1">arm_cfft_q15.c</a>
, <a class="el" href="arm__cfft__radix4__q15_8c.html#aad04e8439d17dab5617bf1be268bb391">arm_cfft_radix4_q15.c</a>
</li>
<li>arm_radix4_butterfly_inverse_q31()
: <a class="el" href="arm__cfft__q31_8c.html#ac9c7c553114c1201a3a987a11b8a6d01">arm_cfft_q31.c</a>
, <a class="el" href="arm__cfft__radix4__q31_8c.html#ac9c7c553114c1201a3a987a11b8a6d01">arm_cfft_radix4_q31.c</a>
</li>
<li>arm_radix4_butterfly_q15()
: <a class="el" href="arm__cfft__radix4__q15_8c.html#a2d01d2045f280c32036da97d33c52440">arm_cfft_radix4_q15.c</a>
, <a class="el" href="arm__cfft__q15_8c.html#abf1a2f9aa9f44ad5da1f0dbae8b54f2f">arm_cfft_q15.c</a>
</li>
<li>arm_radix4_butterfly_q31()
: <a class="el" href="arm__cfft__q31_8c.html#ac12f1e7f159d5741358cdc36830a0395">arm_cfft_q31.c</a>
, <a class="el" href="arm__cfft__radix4__q31_8c.html#ac12f1e7f159d5741358cdc36830a0395">arm_cfft_radix4_q31.c</a>
</li>
<li>arm_radix8_butterfly_f32()
: <a class="el" href="arm__cfft__f32_8c.html#a72350c6eaa1eef8796ab43c1497c6b9c">arm_cfft_f32.c</a>
, <a class="el" href="arm__cfft__radix8__f32_8c.html#a72350c6eaa1eef8796ab43c1497c6b9c">arm_cfft_radix8_f32.c</a>
</li>
<li>arm_recip_q15()
: <a class="el" href="arm__math_8h.html#a1c66e370a6ae91aaafbaec5e979198d7">arm_math.h</a>
</li>
<li>arm_recip_q31()
: <a class="el" href="arm__math_8h.html#a43140f04ca94c2a7394e7a222e2d8fb4">arm_math.h</a>
</li>
<li>arm_rfft_f32()
: <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32.c</a>
, <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_math.h</a>
</li>
<li>arm_rfft_fast_f32()
: <a class="el" href="group___real_f_f_t.html#ga180d8b764d59cbb85d37a2d5f7cd9799">arm_rfft_fast_f32.c</a>
, <a class="el" href="group___real_f_f_t.html#ga180d8b764d59cbb85d37a2d5f7cd9799">arm_math.h</a>
</li>
<li>arm_rfft_fast_init_f32()
: <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32.c</a>
, <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_math.h</a>
</li>
<li>arm_rfft_init_f32()
: <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32.c</a>
, <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_math.h</a>
</li>
<li>arm_rfft_init_q15()
: <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_math.h</a>
, <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15.c</a>
</li>
<li>arm_rfft_init_q31()
: <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31.c</a>
, <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_math.h</a>
</li>
<li>arm_rfft_q15()
: <a class="el" href="group___real_f_f_t.html#ga00e615f5db21736ad5b27fb6146f3fc5">arm_math.h</a>
, <a class="el" href="group___real_f_f_t.html#ga00e615f5db21736ad5b27fb6146f3fc5">arm_rfft_q15.c</a>
</li>
<li>arm_rfft_q31()
: <a class="el" href="group___real_f_f_t.html#gabaeab5646aeea9844e6d42ca8c73fe3a">arm_rfft_q31.c</a>
, <a class="el" href="group___real_f_f_t.html#gabaeab5646aeea9844e6d42ca8c73fe3a">arm_math.h</a>
</li>
<li>arm_rms_f32()
: <a class="el" href="group___r_m_s.html#ga0e3ab1b57da32d45388d1fa90d7fd88c">arm_rms_f32.c</a>
, <a class="el" href="group___r_m_s.html#ga0e3ab1b57da32d45388d1fa90d7fd88c">arm_math.h</a>
</li>
<li>arm_rms_q15()
: <a class="el" href="group___r_m_s.html#gaf5b836b72dda9e5dfbbd17c7906fd13f">arm_math.h</a>
, <a class="el" href="group___r_m_s.html#gaf5b836b72dda9e5dfbbd17c7906fd13f">arm_rms_q15.c</a>
</li>
<li>arm_rms_q31()
: <a class="el" href="group___r_m_s.html#gae33015fda23fc44e7ead5e5ed7e8d314">arm_math.h</a>
, <a class="el" href="group___r_m_s.html#gae33015fda23fc44e7ead5e5ed7e8d314">arm_rms_q31.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
