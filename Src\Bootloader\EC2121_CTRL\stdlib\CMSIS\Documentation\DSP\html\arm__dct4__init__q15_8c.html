<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_dct4_init_q15.c File Reference</title>
<title>CMSIS-DSP: arm_dct4_init_q15.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__dct4__init__q15_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">arm_dct4_init_q15.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga966fd1b66a80873964533703ab5dc054"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15</a> (<a class="el" href="structarm__dct4__instance__q15.html">arm_dct4_instance_q15</a> *S, <a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a> *S_RFFT, <a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a> *S_CFFT, uint16_t N, uint16_t Nby2, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> normalize)</td></tr>
<tr class="memdesc:ga966fd1b66a80873964533703ab5dc054"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 DCT4/IDCT4.  <a href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054"></a><br/></td></tr>
<tr class="separator:ga966fd1b66a80873964533703ab5dc054"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:gaa4ff5e6f062efb1d1ec8c6c2207c3727"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaa4ff5e6f062efb1d1ec8c6c2207c3727">WeightsQ15_128</a> [256]</td></tr>
<tr class="separator:gaa4ff5e6f062efb1d1ec8c6c2207c3727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc8ee250fc217d6cb5c84dd7c1eb6d31"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gadc8ee250fc217d6cb5c84dd7c1eb6d31">WeightsQ15_512</a> [1024]</td></tr>
<tr class="separator:gadc8ee250fc217d6cb5c84dd7c1eb6d31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2235ec700d0d6925d9733f48541d46f5"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga2235ec700d0d6925d9733f48541d46f5">WeightsQ15_2048</a> [4096]</td></tr>
<tr class="separator:ga2235ec700d0d6925d9733f48541d46f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4fdc60621eb306984a82ce8b2d645bb7"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga4fdc60621eb306984a82ce8b2d645bb7">WeightsQ15_8192</a> [16384]</td></tr>
<tr class="separator:ga4fdc60621eb306984a82ce8b2d645bb7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1477edd21c7b08b0b59a564f6c24d6c5"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga1477edd21c7b08b0b59a564f6c24d6c5">cos_factorsQ15_128</a> [128]</td></tr>
<tr class="separator:ga1477edd21c7b08b0b59a564f6c24d6c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac056c3d026058eab3ba650828ff5642f"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gac056c3d026058eab3ba650828ff5642f">cos_factorsQ15_512</a> [512]</td></tr>
<tr class="separator:gac056c3d026058eab3ba650828ff5642f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeee5df7c1be2374441868ecbbc6c7e5d"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaeee5df7c1be2374441868ecbbc6c7e5d">cos_factorsQ15_2048</a> [2048]</td></tr>
<tr class="separator:gaeee5df7c1be2374441868ecbbc6c7e5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga988ff0563cc9df7848c9348871ac6c07"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga988ff0563cc9df7848c9348871ac6c07">cos_factorsQ15_8192</a> [8192]</td></tr>
<tr class="separator:ga988ff0563cc9df7848c9348871ac6c07"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_7e8aa87db1ad6b3d9b1f25792e7c5208.html">Source</a></li><li class="navelem"><a class="el" href="dir_9c857f0e41082f634e50072d001e0d4f.html">TransformFunctions</a></li><li class="navelem"><a class="el" href="arm__dct4__init__q15_8c.html">arm_dct4_init_q15.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
