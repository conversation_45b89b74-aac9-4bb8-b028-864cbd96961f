#include <stdio.h>
#include <string.h>
//#include "Board.h"
#include "CTimer.h"
#include "FlashExternal.h"
#include "FlashInternal.h"
#include "UpdateApp.h"
#include "DebugConsole.h"

#include "Version.h"

extern void msDelay(uint32_t ms);


__no_init uint32_t g_fHotStart;
extern bool    g_bDiag;

CTimer ledTimer;

#define SCK_High()                  {GPIO_SetBits(GPIOE, GPIO_Pin_5);}
#define SCK_Low()                   {GPIO_ResetBits(GPIOE, GPIO_Pin_5);}
#define RCK_High()                  {GPIO_SetBits(GPIOE, GPIO_Pin_4);}
#define RCK_Low()                   {GPIO_ResetBits(GPIOE, GPIO_Pin_4);}
#define SDA_High()                  {GPIO_SetBits(GPIOE, GPIO_Pin_6);}
#define SDA_Low()                   {GPIO_ResetBits(GPIOE, GPIO_Pin_6);}

void delay_us(uint32_t us)
{
    uint32_t i, j;

    for (i = 0; i < us; i++)
    {
        for (j = 0; j < 40; j++)
        {;}
    }
}

void LedInit(void)
{
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(GPIOE, &GPIO_InitStructure);

    RCK_Low();

    for (uint8_t i = 0; i < 4 * 3; i++)
    {
        SCK_Low();

        SDA_High();

        delay_us(1);
        SCK_High();
        delay_us(1);
    }

    RCK_High();	
}

void SetPowerLedColor(uint8_t color)
{
	uint8_t rgb[12] = {0};
	switch(color)
	{
		case COLOR_NO:
		{
			rgb[3] = 0;
			rgb[4] = 0;
			rgb[5] = 0;
			break;
		}
		case COLOR_YELLOW:
		{
			rgb[3] = 0;
			rgb[4] = 1;
			rgb[5] = 1;
			break;
		}
		case COLOR_BLUE:
		{
			rgb[3] = 1;
			rgb[4] = 0;
			rgb[5] = 0;
			break;
		}
		default:
		{
			rgb[3] = 0;
			rgb[4] = 0;
			rgb[5] = 0;
			break;
		}
	}

    RCK_Low();

    for (uint8_t i = 0; i < 12; i++)
    {
        SCK_Low();

        if(rgb[i])
        {
			
			SDA_Low();
		}
		else
		{
			SDA_High();
		}

        delay_us(1);
        SCK_High();
        delay_us(1);
    }

    RCK_High();		
}

void LedBlink(uint8_t color)
{
	static bool ledOn = false;

	if(ledTimer.IsOverFlow())
	{
		if(ledOn)
		{
			SetPowerLedColor(COLOR_NO);
			ledOn = false;
		}
		else
		{
			SetPowerLedColor(color);
			ledOn = true;
		}
		ledTimer.Start();
	}
}


int main()
{
    kprintf("\n ctrl boot is start\n");
    LedInit();

	ledTimer.SetPeriodMs(200);
	ledTimer.Start();
	
    CTimer testTimer1;
    testTimer1.DelayMs(1000);
 
    uint8_t RecvBuff[64] = { 0 };
    GetFwVersion((char *)RecvBuff);
    kprintf("\n boot is in---%s\n",RecvBuff); 
    
    BSP_SERIAL_FLASH_Init();//初始化外部flash的spi端口
    uint32_t uID = BSP_SERIAL_FLASH_ReadID();
    if(IsOtaUpdata())//判断ota升级标志是否有效
    {   kprintf("\n OtaUpdata need in--->\n"); 
        if(ReadUpdateDataHead(0))//判断外部flash存储的app是否有效,擦除app
        {
            kprintf("\n chk flash is ok,update in 0--->\n"); 
            if(UpdateProcess(0))//将外部flash搬入app，设置跳转标志，清除ota升级标志
            {
                kprintf("\n NVIC_SystemReset in 0--->\n"); 
                BSP_SERIAL_FLASH_DeInit();
                NVIC_SystemReset();
            }
        }

		SetPowerLedColor(COLOR_BLUE); //到这里来就是升级发生了错误，根据灯语需求蓝牙常亮5S
		testTimer1.DelayMs(5000);
    }
    
    if(false == IsJumpToApp())
    {
        kprintf("\n JumpToApp is false !!!\n");
        if(ReadUpdateDataHead(0))//判断外部flash存储的app是否有效,擦除app
        {
              kprintf("\n chk flash is ok,update in 1--->\n"); 
            if(UpdateProcess(0))//将外部flash搬入app，设置跳转标志，清除ota升级标志
            {
                kprintf("\n NVIC_SystemReset in 1--->\n"); 
                BSP_SERIAL_FLASH_DeInit();
                NVIC_SystemReset();
            }
        }
    }

	SetPowerLedColor(COLOR_YELLOW); //到这里来就是boot启动失败，根据灯语需求黄灯常亮
	
    delay_us(1000);
    kprintf("\n boot is error\n");
    delay_us(10000);
    
    while(1)
    {
        //NVIC_SystemReset();
    }
}


