/******************************************************************************

Copyright (c) 2020, AUTEL Ltd,.Co

All rights reserved.

2020.4.x A19173 guanqitai V1.0

*******************************************************************************/

#include "FlashExternal.h"
#include "CTimer.h"
#include "DebugConsole.h"
//#include "MemDebug.h"


//#define FLASH_SPI_SECTOR_SIZE        0x1000
//#define REMOVE_END_ADDRESS           0x0091FFFF //原定义在flashtask.h中

uint32_t FuncGetTimeFlag = 0;
uint32_t FuncNow = 0;
uint32_t DeltaTime = 0xffffffff;

static uint8_t SpiSendByte(uint8_t byte)
{
    uint16_t retry = 0;
    while(SPI_I2S_GetFlagStatus(FLASH_SPIx, SPI_I2S_FLAG_TXE) == RESET)  //检查指定的SPI标志位设置与否:发送缓存空标志位
    {
        retry++;
        if(retry > 20000)
        {
            return 0;
        }
    }
    SPI_I2S_SendData(FLASH_SPIx, byte); //通过外设SPIx发送一个数据
    retry = 0;
    while(SPI_I2S_GetFlagStatus(FLASH_SPIx, SPI_I2S_FLAG_RXNE) == RESET)  //检查指定的SPI标志位设置与否:接受缓存非空标志位
    {
        retry++;
        if(retry > 20000)
        {
            return 0;
        }
    }
    return SPI_I2S_ReceiveData(FLASH_SPIx); //返回通过SPIx最近接收的数据
}

//4Kbytes为一个Sector
//16个Sector（扇区）为一个Block
//W25X128
//容量为16M字节，共有32个Block,512个Sector

/**
  * @brief  Initializes peripherals used by the Serial FLASH device.
  */
void BSP_SERIAL_FLASH_Init(void)
{
    SPI_InitTypeDef  SPI_InitStructure;
    GPIO_InitTypeDef GPIO_InitStructure;
    FLASH_SPI_APBxClock_FUN(FLASH_SPI_CLK, ENABLE);
    FLASH_SPI_SCK_APBxClock_FUN(FLASH_SPI_SCK_CLK, ENABLE);
    FLASH_SPI_MISO_APBxClock_FUN(FLASH_SPI_MISO_CLK, ENABLE);
    FLASH_SPI_MOSI_APBxClock_FUN(FLASH_SPI_MOSI_CLK, ENABLE);
    FLASH_SPI_CS_APBxClock_FUN(FLASH_SPI_CS_CLK, ENABLE);
    GPIO_InitStructure.GPIO_Pin = FLASH_SPI_CS_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(FLASH_SPI_CS_PORT, &GPIO_InitStructure);
    FLASH_SPI_CS_DISABLE();
    GPIO_InitStructure.GPIO_Pin = FLASH_SPI_SCK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(FLASH_SPI_SCK_PORT, &GPIO_InitStructure);
    GPIO_InitStructure.GPIO_Pin = FLASH_SPI_MISO_PIN;
    GPIO_Init(FLASH_SPI_MISO_PORT, &GPIO_InitStructure);
    GPIO_InitStructure.GPIO_Pin = FLASH_SPI_MOSI_PIN;
    GPIO_Init(FLASH_SPI_MOSI_PORT, &GPIO_InitStructure);
    GPIO_PinAFConfig(FLASH_SPI_SCK_PORT, FLASH_SPI_SCK_SOURCE, GPIO_AF_SPI3);
    GPIO_PinAFConfig(FLASH_SPI_MISO_PORT, FLASH_SPI_MISO_SOURCE, GPIO_AF_SPI3);
    GPIO_PinAFConfig(FLASH_SPI_MOSI_PORT, FLASH_SPI_MOSI_SOURCE, GPIO_AF_SPI3);
    SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex;//设置SPI单向或者双向的数据模式:SPI设置为双线双向全双工
    SPI_InitStructure.SPI_Mode = SPI_Mode_Master;           //设置SPI工作模式:设置为主SPI
    SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b;
    SPI_InitStructure.SPI_CPOL = SPI_CPOL_High;
    SPI_InitStructure.SPI_CPHA = SPI_CPHA_2Edge;//SPI_CPHA_1Edge;//
    SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;
    SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_256;//SPI_BaudRatePrescaler_64;//SPI_BaudRatePrescaler_256;//定义波特率预分频值为256
    SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;
    SPI_InitStructure.SPI_CRCPolynomial = 7;
    SPI_Init(FLASH_SPIx, &SPI_InitStructure);
    SPI_Cmd(FLASH_SPIx, ENABLE);
}

void BSP_SERIAL_FLASH_DeInit(void)
{
    SPI_I2S_DeInit(FLASH_SPIx);
}

/**
  * @brief  Set flash spi cs level to low.
  */
void FLASH_SPI_CS_LOW(void)
{
    FLASH_SPI_CS_ENABLE();
}

/**
  * @brief  Set flash spi cs level to high.
  */
void FLASH_SPI_CS_HIGH(void)
{
    FLASH_SPI_CS_DISABLE();
}

/**
  * @brief  Read a byte from the FLASH SPI.
  * @retval uint8_t (The received byte).
  */
uint8_t FLASH_SPI_IO_ReadByte(void)
{
    return SpiSendByte(FLASH_SPI_DUMMY_BYTE);
}

/**
  * @brief  Write a byte on the FLASH SPI.
  * @param  Data: byte to send.
  * @retval uint8_t (The received byte).
  */
uint8_t FLASH_SPI_IO_WriteByte(uint8_t Data)
{
    return SpiSendByte(Data);
}

/**
  * @brief  Read data from FLASH SPI driver
  * @param  MemAddress: Internal memory address
  * @param  pBuffer: Pointer to data buffer
  * @param  BufferSize: Amount of data to be read
  * @retval true or false
  */
bool FLASH_SPI_IO_ReadData(uint32_t MemAddress, uint8_t* pBuffer, uint32_t BufferSize)
{
    /*!< Select the FLASH: Chip Select low */
    FLASH_SPI_CS_LOW();
    /*!< Send "Read from Memory " instruction */
    FLASH_SPI_IO_WriteByte(FLASH_SPI_CMD_READ);
    /*!< Send ReadAddr high nibble address byte to read from */
    FLASH_SPI_IO_WriteByte((MemAddress & 0xFF0000) >> 16);
    /*!< Send ReadAddr medium nibble address byte to read from */
    FLASH_SPI_IO_WriteByte((MemAddress & 0xFF00) >> 8);
    /*!< Send ReadAddr low nibble address byte to read from */
    FLASH_SPI_IO_WriteByte(MemAddress & 0xFF);
    while(BufferSize--)  /*!< while there is data to be read */
    {
        /*!< Read a byte from the FLASH */
        *pBuffer = FLASH_SPI_IO_WriteByte(FLASH_SPI_DUMMY_BYTE);
        /*!< Point to the next location where the byte read will be saved */
        pBuffer++;
    }
    /*!< Deselect the FLASH: Chip Select high */
    FLASH_SPI_CS_HIGH();
    return true;
}

/**
  * @brief  Select the FLASH SPI and send "Write Enable" instruction
  */
void FLASH_SPI_IO_WriteEnable(void)
{
    /*!< Select the FLASH: Chip Select low */
    FLASH_SPI_CS_LOW();
    /*!< Send "Write Enable" instruction */
    FLASH_SPI_IO_WriteByte(FLASH_SPI_CMD_WREN);
    /*!< Select the FLASH: Chip Select low */
    FLASH_SPI_CS_HIGH();
    /*!< Select the FLASH: Chip Select low */
    FLASH_SPI_CS_LOW();
}

/**
  * @brief  Wait response from the FLASH SPI and Deselect the device
  * @retval HAL_StatusTypeDef HAL Status
  */
bool FLASH_SPI_IO_WaitForWriteEnd(void)
{
    /*!< Select the FLASH: Chip Select low */
    FLASH_SPI_CS_HIGH();
    /*!< Select the FLASH: Chip Select low */
    FLASH_SPI_CS_LOW();
    uint8_t flashstatus = 0;
    /*!< Send "Read Status Register" instruction */
    FLASH_SPI_IO_WriteByte(FLASH_SPI_CMD_RDSR);
    /*!< Loop as long as the memory is busy with a write cycle */
    do
    {
        /*!< Send a dummy byte to generate the clock needed by the FLASH
        and put the value of the status register in FLASH_Status variable */
        flashstatus = FLASH_SPI_IO_WriteByte(FLASH_SPI_DUMMY_BYTE);
    }
    while((flashstatus & FLASH_SPI_WIP_FLAG) == SET);  /* Write in progress */
    /*!< Deselect the FLASH: Chip Select high */
    FLASH_SPI_CS_HIGH();
    return true;
}

/**
  * @brief  Reads FLASH SPI identification.
  * @retval FLASH identification
  */
uint32_t FLASH_SPI_IO_ReadID(void)
{
    uint32_t Temp = 0;
    uint32_t MemAddress = 0x00;
    /*!< Select the FLASH: Chip Select low */
    FLASH_SPI_CS_LOW();
    /*!< Send "RDID " instruction */
    FLASH_SPI_IO_WriteByte(FLASH_SPI_CMD_RDID);
    FLASH_SPI_IO_WriteByte((MemAddress & 0xFF0000) >> 16);
    /*!< Send ReadAddr medium nibble address byte to read from */
    FLASH_SPI_IO_WriteByte((MemAddress & 0xFF00) >> 8);
    /*!< Send ReadAddr low nibble address byte to read from */
    FLASH_SPI_IO_WriteByte(MemAddress & 0xFF);
    /*!< Read a byte from the FLASH */
    //Temp0 = SPIx_Write(FLASH_SPI_DUMMY_BYTE);
    /*!< Read a byte from the FLASH */
    Temp |= FLASH_SPI_IO_WriteByte(0xFF) << 8;
    /*!< Read a byte from the FLASH */
    Temp |= FLASH_SPI_IO_WriteByte(0xFF);
    /*!< Deselect the FLASH: Chip Select high */
    FLASH_SPI_CS_HIGH();
    return Temp;
}

/**
  * @brief  Writes more than one byte to the FLASH with a single WRITE cycle
  *         (Page WRITE sequence).
  * @note   The number of byte can't exceed the FLASH page size (FLASH_SPI_PAGESIZE).
  * @param  pData: pointer to the buffer  containing the data to be written
  *         to the FLASH.
  * @param  uwStartAddress: FLASH's internal address to write to.
  * @param  uwDataSize: number of bytes to write to the FLASH, must be equal
  *         or less than "FLASH_SPI_PAGESIZE" value.
  * @retval FLASH_OK (0x00) if operation is correctly performed, else
  *         return FLASH_ERROR (0x01).
  */
uint8_t BSP_SERIAL_FLASH_WritePage(uint32_t uwStartAddress, uint8_t* pData, uint32_t uwDataSize)
{
    /*!< Select the FLASH  and send "Write Enable" instruction */
    uint8_t ret = 0;
    FLASH_SPI_IO_WriteEnable();
    /*!< Send "Write to Memory " instruction */
    FLASH_SPI_IO_WriteByte(FLASH_SPI_CMD_WRITE);
    /*!< Send uwStartAddress high nibble address byte to write to */
    FLASH_SPI_IO_WriteByte((uwStartAddress & 0xFF0000) >> 16);
    /*!< Send uwStartAddress medium nibble address byte to write to */
    FLASH_SPI_IO_WriteByte((uwStartAddress & 0xFF00) >> 8);
    /*!< Send uwStartAddress low nibble address byte to write to */
    FLASH_SPI_IO_WriteByte(uwStartAddress & 0xFF);
    /*!< while there is data to be written on the FLASH */
    while(uwDataSize--)
    {
        /*!< Send the current byte */
        FLASH_SPI_IO_WriteByte(*pData);
        /*!< Point on the next byte to be written */
        pData++;
    }
    /*!< Wait the end of Flash writing */
    if(FLASH_SPI_IO_WaitForWriteEnd() != true)
    {
        ret = FLASH_ERROR;
    }
    else
    {
        ret = FLASH_OK;
    }
    return ret;
}

/**
  * @brief  Writes datas to the FLASH no check.
  * @param  pData: pointer to the buffer  containing the data to be written
  *         to the FLASH.
  * @param  uwStartAddress: FLASH's internal address to write to.
  * @param  uwDataSize: number of bytes to write to the FLASH.
  */
uint8_t BSP_SERIAL_FLASH_WriteNoCheck(uint32_t uwStartAddress, uint8_t* pData, uint32_t uwDataSize)
{
    uint16_t usPageRemain;
    usPageRemain = FLASH_SPI_PAGESIZE - uwStartAddress % FLASH_SPI_PAGESIZE;
    if(uwDataSize <= usPageRemain)
    {
        usPageRemain = uwDataSize;
    }
    while(1)
    {
        if(BSP_SERIAL_FLASH_WritePage(uwStartAddress, pData, usPageRemain) != FLASH_OK)
        {
            return FLASH_ERROR;
        }
        if(uwDataSize == usPageRemain)
        {
            return FLASH_OK;
        }
        else
        {
            pData += usPageRemain;
            uwStartAddress += usPageRemain;
            uwDataSize -= usPageRemain;
            if(uwDataSize > FLASH_SPI_PAGESIZE)
            {
                usPageRemain = FLASH_SPI_PAGESIZE;
            }
            else
            {
                usPageRemain = uwDataSize;
            }
        }
    }
}

/**
  * @brief  Writes datas to the FLASH.
  * @param  pData: pointer to the buffer  containing the data to be written
  *         to the FLASH.
  * @param  uwStartAddress: FLASH's internal address to write to.
  * @param  uwDataSize: number of bytes to write to the FLASH.
  */
uint8_t BSP_SERIAL_FLASH_WriteData(uint32_t uwStartAddress, uint8_t* pData, uint16_t uwDataSize)
{
    uint32_t Secpos = 0;
    uint16_t Secoff = 0;
    uint16_t Secremain = 0;
    uint16_t i = 0;
    uint8_t SPI_FLASH_BUF[FLASH_SPI_SECTOR_SIZE] = {0};
    Secpos = uwStartAddress / FLASH_SPI_SECTOR_SIZE;
    Secoff = uwStartAddress % FLASH_SPI_SECTOR_SIZE;
    Secremain = FLASH_SPI_SECTOR_SIZE - Secoff;
    if(uwDataSize <= Secremain)
    {
        Secremain = uwDataSize;
    }
    while(1)
    {
        BSP_SERIAL_FLASH_ReadData(Secpos * FLASH_SPI_SECTOR_SIZE, SPI_FLASH_BUF, FLASH_SPI_SECTOR_SIZE);
        for(i = 0; i < Secremain; i++)
        {
            if(SPI_FLASH_BUF[Secoff + i] != FLASH_SPI_DUMMY_BYTE)
            {
                break;
            }
        }
        if(i < Secremain)
        {
            BSP_SERIAL_FLASH_EraseSector(Secpos);
            for(i = 0; i < Secremain; i++)
            {
                SPI_FLASH_BUF[i + Secoff] = pData[i];
            }
            if(BSP_SERIAL_FLASH_WriteNoCheck(Secpos * FLASH_SPI_SECTOR_SIZE, SPI_FLASH_BUF, FLASH_SPI_SECTOR_SIZE) != FLASH_OK)
            {
                return FLASH_ERROR;
            }
        }
        else
        {
            if(BSP_SERIAL_FLASH_WriteNoCheck(uwStartAddress, pData, Secremain) != FLASH_OK)
            {
                return FLASH_ERROR;
            }
        }
        if
        (uwDataSize == Secremain)
        {
            return FLASH_OK;
        }
        else
        {
            Secpos++;
            Secoff = 0;
            pData += Secremain;
            uwStartAddress += Secremain;
            uwDataSize -= Secremain;
            if(uwDataSize > FLASH_SPI_SECTOR_SIZE)
            {
                Secremain = FLASH_SPI_SECTOR_SIZE;
            }
            else
            {
                Secremain = uwDataSize;
            }
        }
    }
}

/**
  * @brief  Erases the entire FLASH.
  * @retval FLASH_OK (0x00) if operation is correctly performed, else
  *         return FLASH_ERROR (0x01).
  */
uint8_t BSP_SERIAL_FLASH_EraseChip(void)
{
    uint8_t ret = 0;
    kprintfOnlyChar("\r\n------Warring!!!----FLASH_EraseChip\r\n");
    /*!< Bulk Erase */
    FLASH_SPI_IO_WaitForWriteEnd();
    /*!< Select the FLASH  and send "Write Enable" instruction */
    FLASH_SPI_IO_WriteEnable();
    /*!< Send Bulk Erase instruction  */
    FLASH_SPI_IO_WriteByte(FLASH_SPI_CMD_CHIPE);
    /*!< Wait the end of Flash writing and Deselect the FLASH*/
    if(FLASH_SPI_IO_WaitForWriteEnd() != true)
    {
        ret = FLASH_ERROR;
    }
    else
    {
        ret = FLASH_OK;
    }
    return ret;
}

/**
  * @brief  Erases the specified FLASH sector.
  * @param  SectorAddr: address of the sector to erase.
  *         This parameter indicates the sector number, not the actual address.
  *         The parameter value range is 0~4095.
  * @retval FLASH_OK (0x00) if operation is correctly performed, else
  *         return FLASH_ERROR (0x01).
  */
uint8_t BSP_SERIAL_FLASH_EraseSector(uint32_t SectorAddr)
{
    /*!< Sector Erase */
    SectorAddr *= FLASH_SPI_SECTOR_SIZE;
    /*!< Select the FLASH  and send "Write Enable" instruction */
    FLASH_SPI_IO_WriteEnable();
    /*!< Send Sector Erase instruction */
    FLASH_SPI_IO_WriteByte(FLASH_SPI_CMD_SE);
    /*!< Send SectorAddr high nibble address byte */
    FLASH_SPI_IO_WriteByte((SectorAddr & 0xFF0000) >> 16);
    /*!< Send SectorAddr medium nibble address byte */
    FLASH_SPI_IO_WriteByte((SectorAddr & 0xFF00) >> 8);
    /*!< Send SectorAddr low nibble address byte */
    FLASH_SPI_IO_WriteByte(SectorAddr & 0xFF);
    /*!< Wait the end of Flash writing and Deselect the FLASH*/
    if(FLASH_SPI_IO_WaitForWriteEnd() != true)
    {
        return FLASH_ERROR;
    }

    return FLASH_OK;
}

/**
  * @brief  Reads a block of data from the FLASH.
  * @param  pData: pointer to the buffer that receives the data read from the FLASH.
  * @param  uwStartAddress: FLASH's internal address to read from.
  * @param  uwDataSize: number of bytes to read from the FLASH.
  * @retval FLASH_OK (0x00) if operation is correctly performed, else
  *         return FLASH_ERROR (0x01).
  */
uint8_t BSP_SERIAL_FLASH_ReadData(uint32_t uwStartAddress, uint8_t* pData, uint32_t uwDataSize)
{
    uint8_t ret = 0;
    if(FLASH_SPI_IO_ReadData(uwStartAddress, pData, uwDataSize) != true)
    {
        ret = FLASH_ERROR;
    }
    else
    {
        ret = FLASH_OK;
    }
    return ret;
}

/**
  * @brief  Reads FLASH identification.
  * @retval FLASH identification
  */
uint32_t BSP_SERIAL_FLASH_ReadID(void)
{
    uint32_t ret = 0;
    ret = FLASH_SPI_IO_ReadID();
    return ret;
}
