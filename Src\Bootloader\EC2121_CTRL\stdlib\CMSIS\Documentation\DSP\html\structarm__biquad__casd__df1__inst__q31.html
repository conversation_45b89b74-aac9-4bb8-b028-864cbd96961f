<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_biquad_casd_df1_inst_q31 Struct Reference</title>
<title>CMSIS-DSP: arm_biquad_casd_df1_inst_q31 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('structarm__biquad__casd__df1__inst__q31.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">arm_biquad_casd_df1_inst_q31 Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Instance structure for the Q31 Biquad cascade filter.  
 <a href="structarm__biquad__casd__df1__inst__q31.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a2c2b579f1df1d8273a5d9d945c27e1b2"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a2c2b579f1df1d8273a5d9d945c27e1b2">numStages</a></td></tr>
<tr class="separator:a2c2b579f1df1d8273a5d9d945c27e1b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5dcf4727f58eb4e8e8b392508d8657bb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a5dcf4727f58eb4e8e8b392508d8657bb">pState</a></td></tr>
<tr class="separator:a5dcf4727f58eb4e8e8b392508d8657bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa62366c632f3b5305086f841f079dbd2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__biquad__casd__df1__inst__q31.html#aa62366c632f3b5305086f841f079dbd2">pCoeffs</a></td></tr>
<tr class="separator:aa62366c632f3b5305086f841f079dbd2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a636c7fbe09ec4bef0bc0a4b4e2151cbe"><td class="memItemLeft" align="right" valign="top">uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a636c7fbe09ec4bef0bc0a4b4e2151cbe">postShift</a></td></tr>
<tr class="separator:a636c7fbe09ec4bef0bc0a4b4e2151cbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<div class="textblock"><dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_graphic_equalizer_example_q31_8c-example.html#_a17">arm_graphic_equalizer_example_q31.c</a>.</dd>
</dl></div><h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="a2c2b579f1df1d8273a5d9d945c27e1b2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t arm_biquad_casd_df1_inst_q31::numStages</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>number of 2nd order stages in the filter. Overall order is 2*numStages. </p>

<p>Referenced by <a class="el" href="group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3">arm_biquad_cascade_df1_fast_q31()</a>, <a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b">arm_biquad_cascade_df1_init_q31()</a>, and <a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f">arm_biquad_cascade_df1_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="aa62366c632f3b5305086f841f079dbd2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>* arm_biquad_casd_df1_inst_q31::pCoeffs</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Points to the array of coefficients. The array is of length 5*numStages. </p>

<p>Referenced by <a class="el" href="group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3">arm_biquad_cascade_df1_fast_q31()</a>, <a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b">arm_biquad_cascade_df1_init_q31()</a>, and <a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f">arm_biquad_cascade_df1_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a636c7fbe09ec4bef0bc0a4b4e2151cbe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t arm_biquad_casd_df1_inst_q31::postShift</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Additional shift, in bits, applied to each output sample. </p>

<p>Referenced by <a class="el" href="group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3">arm_biquad_cascade_df1_fast_q31()</a>, <a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b">arm_biquad_cascade_df1_init_q31()</a>, and <a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f">arm_biquad_cascade_df1_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a5dcf4727f58eb4e8e8b392508d8657bb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>* arm_biquad_casd_df1_inst_q31::pState</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Points to the array of state coefficients. The array is of length 4*numStages. </p>

<p>Referenced by <a class="el" href="group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3">arm_biquad_cascade_df1_fast_q31()</a>, <a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b">arm_biquad_cascade_df1_init_q31()</a>, and <a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f">arm_biquad_cascade_df1_q31()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structarm__biquad__casd__df1__inst__q31.html">arm_biquad_casd_df1_inst_q31</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
