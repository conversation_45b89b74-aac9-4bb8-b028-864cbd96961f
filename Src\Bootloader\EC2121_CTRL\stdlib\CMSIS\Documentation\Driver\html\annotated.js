var annotated =
[
    [ "ARM_DRIVER_ETH_MAC", "group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c", "group__eth__mac__interface__gr" ],
    [ "ARM_DRIVER_ETH_PHY", "group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y", "group__eth__phy__interface__gr" ],
    [ "ARM_DRIVER_FLASH", "group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h", "group__flash__interface__gr" ],
    [ "ARM_DRIVER_I2C", "group__i2c__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___i2_c", "group__i2c__interface__gr" ],
    [ "ARM_DRIVER_MCI", "group__mci__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___m_c_i", "group__mci__interface__gr" ],
    [ "ARM_DRIVER_NAND", "group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d", "group__nand__interface__gr" ],
    [ "ARM_DRIVER_SPI", "group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i", "group__spi__interface__gr" ],
    [ "ARM_DRIVER_USART", "group__usart__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_a_r_t", "group__usart__interface__gr" ],
    [ "ARM_DRIVER_USBD", "group__usbd__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_d", "group__usbd__interface__gr" ],
    [ "ARM_DRIVER_USBH", "group__usbh__host__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_h", "group__usbh__host__gr" ],
    [ "ARM_DRIVER_USBH_HCI", "group__usbh__hci__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_h___h_c_i", "group__usbh__hci__gr" ],
    [ "ARM_DRIVER_VERSION", "group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n", "group__common__drv__gr" ],
    [ "ARM_ETH_LINK_INFO", "group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o", "group__eth__interface__gr" ],
    [ "ARM_ETH_MAC_ADDR", "group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r", "group__eth__interface__gr" ],
    [ "ARM_ETH_MAC_CAPABILITIES", "group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s", "group__eth__mac__interface__gr" ],
    [ "ARM_ETH_MAC_TIME", "group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e", "group__eth__mac__interface__gr" ],
    [ "ARM_FLASH_CAPABILITIES", "group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s", "group__flash__interface__gr" ],
    [ "ARM_FLASH_INFO", "group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o", "group__flash__interface__gr" ],
    [ "ARM_FLASH_SECTOR", "group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r", "group__flash__interface__gr" ],
    [ "ARM_FLASH_STATUS", "group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s", "group__flash__interface__gr" ],
    [ "ARM_I2C_CAPABILITIES", "group__i2c__interface__gr.html#struct_a_r_m___i2_c___c_a_p_a_b_i_l_i_t_i_e_s", "group__i2c__interface__gr" ],
    [ "ARM_I2C_STATUS", "group__i2c__interface__gr.html#struct_a_r_m___i2_c___s_t_a_t_u_s", "group__i2c__interface__gr" ],
    [ "ARM_MCI_CAPABILITIES", "group__mci__interface__gr.html#struct_a_r_m___m_c_i___c_a_p_a_b_i_l_i_t_i_e_s", "group__mci__interface__gr" ],
    [ "ARM_MCI_STATUS", "group__mci__interface__gr.html#struct_a_r_m___m_c_i___s_t_a_t_u_s", "group__mci__interface__gr" ],
    [ "ARM_NAND_CAPABILITIES", "group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s", "group__nand__interface__gr" ],
    [ "ARM_NAND_ECC_INFO", "group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o", "group__nand__interface__gr" ],
    [ "ARM_NAND_STATUS", "group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s", "group__nand__interface__gr" ],
    [ "ARM_SPI_CAPABILITIES", "group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s", "group__spi__interface__gr" ],
    [ "ARM_SPI_STATUS", "group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s", "group__spi__interface__gr" ],
    [ "ARM_USART_CAPABILITIES", "group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___c_a_p_a_b_i_l_i_t_i_e_s", "group__usart__interface__gr" ],
    [ "ARM_USART_MODEM_STATUS", "group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___m_o_d_e_m___s_t_a_t_u_s", "group__usart__interface__gr" ],
    [ "ARM_USART_STATUS", "group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___s_t_a_t_u_s", "group__usart__interface__gr" ],
    [ "ARM_USBD_CAPABILITIES", "group__usbd__interface__gr.html#struct_a_r_m___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s", "group__usbd__interface__gr" ],
    [ "ARM_USBD_STATE", "group__usbd__interface__gr.html#struct_a_r_m___u_s_b_d___s_t_a_t_e", "group__usbd__interface__gr" ],
    [ "ARM_USBH_CAPABILITIES", "group__usbh__host__gr.html#struct_a_r_m___u_s_b_h___c_a_p_a_b_i_l_i_t_i_e_s", "group__usbh__host__gr" ],
    [ "ARM_USBH_HCI_CAPABILITIES", "group__usbh__hci__gr.html#struct_a_r_m___u_s_b_h___h_c_i___c_a_p_a_b_i_l_i_t_i_e_s", "group__usbh__hci__gr" ],
    [ "ARM_USBH_PORT_STATE", "group__usbh__host__gr.html#struct_a_r_m___u_s_b_h___p_o_r_t___s_t_a_t_e", "group__usbh__host__gr" ]
];