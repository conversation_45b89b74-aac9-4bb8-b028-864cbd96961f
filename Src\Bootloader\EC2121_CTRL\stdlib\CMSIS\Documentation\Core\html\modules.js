var modules =
[
    [ "Peripheral Access", "group__peripheral__gr.html", null ],
    [ "System and Clock Configuration", "group__system__init__gr.html", "group__system__init__gr" ],
    [ "Interrupts and Exceptions (NVIC)", "group___n_v_i_c__gr.html", "group___n_v_i_c__gr" ],
    [ "Core Register Access", "group___core___register__gr.html", "group___core___register__gr" ],
    [ "Intrinsic Functions for CPU Instructions", "group__intrinsic___c_p_u__gr.html", "group__intrinsic___c_p_u__gr" ],
    [ "Intrinsic Functions for SIMD Instructions [only Cortex-M4 and Cortex-M7]", "group__intrinsic___s_i_m_d__gr.html", "group__intrinsic___s_i_m_d__gr" ],
    [ "Systick Timer (SYSTICK)", "group___sys_tick__gr.html", "group___sys_tick__gr" ],
    [ "Debug Access", "group___i_t_m___debug__gr.html", "group___i_t_m___debug__gr" ],
    [ "FPU Functions (only Cortex-M7)", "group__fpu__functions__m7.html", "group__fpu__functions__m7" ],
    [ "Cache Functions  (only Cortex-M7)", "group__cache__functions__m7.html", "group__cache__functions__m7" ]
];