<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_func.html#index_a"><span>a</span></a></li>
      <li class="current"><a href="globals_func_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_func_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_func_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_func_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_func_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_func_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_func_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_func_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_func_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_func_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_func_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_func_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_func_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_func_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_func_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_func_0x76.html#index_v"><span>v</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_func_0x62.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_b"></a>- b -</h3><ul>
<li>arm_bilinear_interp_f32()
: <a class="el" href="group___bilinear_interpolate.html#gab49a4c0f64854903d996d01ba38f711a">arm_math.h</a>
</li>
<li>arm_bilinear_interp_q15()
: <a class="el" href="group___bilinear_interpolate.html#gaa8dffbc2a01bb7accf231384498ec85e">arm_math.h</a>
</li>
<li>arm_bilinear_interp_q31()
: <a class="el" href="group___bilinear_interpolate.html#ga202a033c8a2ad3678b136f93153b6d13">arm_math.h</a>
</li>
<li>arm_bilinear_interp_q7()
: <a class="el" href="group___bilinear_interpolate.html#gade8db9706a3ae9ad03b2750a239d2ee6">arm_math.h</a>
</li>
<li>arm_biquad_cas_df1_32x64_init_q31()
: <a class="el" href="group___biquad_cascade_d_f1__32x64.html#ga44900cecb8083afcaabf905ffcd656bb">arm_biquad_cascade_df1_32x64_init_q31.c</a>
, <a class="el" href="group___biquad_cascade_d_f1__32x64.html#ga44900cecb8083afcaabf905ffcd656bb">arm_math.h</a>
</li>
<li>arm_biquad_cas_df1_32x64_q31()
: <a class="el" href="group___biquad_cascade_d_f1__32x64.html#ga953a83e69685de6575cff37feb358a93">arm_math.h</a>
, <a class="el" href="group___biquad_cascade_d_f1__32x64.html#ga953a83e69685de6575cff37feb358a93">arm_biquad_cascade_df1_32x64_q31.c</a>
</li>
<li>arm_biquad_cascade_df1_f32()
: <a class="el" href="group___biquad_cascade_d_f1.html#gaa0dbe330d763e3c1d8030b3ef12d5bdc">arm_biquad_cascade_df1_f32.c</a>
, <a class="el" href="group___biquad_cascade_d_f1.html#gaa0dbe330d763e3c1d8030b3ef12d5bdc">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df1_fast_q15()
: <a class="el" href="group___biquad_cascade_d_f1.html#gaffb9792c0220882efd4c58f3c6a05fd7">arm_biquad_cascade_df1_fast_q15.c</a>
, <a class="el" href="group___biquad_cascade_d_f1.html#gaffb9792c0220882efd4c58f3c6a05fd7">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df1_fast_q31()
: <a class="el" href="group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3">arm_biquad_cascade_df1_fast_q31.c</a>
, <a class="el" href="group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df1_init_f32()
: <a class="el" href="group___biquad_cascade_d_f1.html#ga8e73b69a788e681a61bccc8959d823c5">arm_math.h</a>
, <a class="el" href="group___biquad_cascade_d_f1.html#ga8e73b69a788e681a61bccc8959d823c5">arm_biquad_cascade_df1_init_f32.c</a>
</li>
<li>arm_biquad_cascade_df1_init_q15()
: <a class="el" href="group___biquad_cascade_d_f1.html#gad54c724132f6d742a444eb6df0e9c731">arm_biquad_cascade_df1_init_q15.c</a>
, <a class="el" href="group___biquad_cascade_d_f1.html#gad54c724132f6d742a444eb6df0e9c731">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df1_init_q31()
: <a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b">arm_biquad_cascade_df1_init_q31.c</a>
, <a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df1_q15()
: <a class="el" href="group___biquad_cascade_d_f1.html#gadd66a0aefdc645031d607b0a5b37a942">arm_biquad_cascade_df1_q15.c</a>
, <a class="el" href="group___biquad_cascade_d_f1.html#gadd66a0aefdc645031d607b0a5b37a942">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df1_q31()
: <a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f">arm_biquad_cascade_df1_q31.c</a>
, <a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df2T_f32()
: <a class="el" href="group___biquad_cascade_d_f2_t.html#ga114f373fbc16a314e9f293c7c7649c7f">arm_biquad_cascade_df2T_f32.c</a>
, <a class="el" href="group___biquad_cascade_d_f2_t.html#ga114f373fbc16a314e9f293c7c7649c7f">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df2T_f64()
: <a class="el" href="group___biquad_cascade_d_f2_t.html#gaa8735dda5f3f36d0936283794c2aa771">arm_biquad_cascade_df2T_f64.c</a>
, <a class="el" href="group___biquad_cascade_d_f2_t.html#gaa8735dda5f3f36d0936283794c2aa771">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df2T_init_f32()
: <a class="el" href="group___biquad_cascade_d_f2_t.html#ga70eaddf317a4a8bde6bd6a97df67fedd">arm_biquad_cascade_df2T_init_f32.c</a>
, <a class="el" href="group___biquad_cascade_d_f2_t.html#ga70eaddf317a4a8bde6bd6a97df67fedd">arm_math.h</a>
</li>
<li>arm_biquad_cascade_df2T_init_f64()
: <a class="el" href="group___biquad_cascade_d_f2_t.html#ga12dc5d8e8892806ad70e79ca2ff9f86e">arm_math.h</a>
, <a class="el" href="group___biquad_cascade_d_f2_t.html#ga12dc5d8e8892806ad70e79ca2ff9f86e">arm_biquad_cascade_df2T_init_f64.c</a>
</li>
<li>arm_biquad_cascade_stereo_df2T_f32()
: <a class="el" href="group___biquad_cascade_d_f2_t.html#gac75de449c3e4f733477d81bd0ada5eec">arm_biquad_cascade_stereo_df2T_f32.c</a>
, <a class="el" href="group___biquad_cascade_d_f2_t.html#gac75de449c3e4f733477d81bd0ada5eec">arm_math.h</a>
</li>
<li>arm_biquad_cascade_stereo_df2T_init_f32()
: <a class="el" href="group___biquad_cascade_d_f2_t.html#ga405197c89fe4d34003efd23786296425">arm_math.h</a>
, <a class="el" href="group___biquad_cascade_d_f2_t.html#ga405197c89fe4d34003efd23786296425">arm_biquad_cascade_stereo_df2T_init_f32.c</a>
</li>
<li>arm_bitreversal_16()
: <a class="el" href="arm__cfft__q15_8c.html#a773957c278f4d9e728711f27e8a6e278">arm_cfft_q15.c</a>
</li>
<li>arm_bitreversal_32()
: <a class="el" href="arm__cfft__q31_8c.html#ac8e7ebe1cb131a5b0f55d0464640591f">arm_cfft_q31.c</a>
, <a class="el" href="arm__cfft__f32_8c.html#ac8e7ebe1cb131a5b0f55d0464640591f">arm_cfft_f32.c</a>
</li>
<li>arm_bitreversal_f32()
: <a class="el" href="arm__bitreversal_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b">arm_bitreversal.c</a>
, <a class="el" href="arm__cfft__radix2__f32_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b">arm_cfft_radix2_f32.c</a>
, <a class="el" href="arm__cfft__radix4__f32_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b">arm_cfft_radix4_f32.c</a>
, <a class="el" href="arm__rfft__f32_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b">arm_rfft_f32.c</a>
</li>
<li>arm_bitreversal_q15()
: <a class="el" href="arm__cfft__radix2__q15_8c.html#a73f48eaea9297605705ae25d3405343e">arm_cfft_radix2_q15.c</a>
, <a class="el" href="arm__cfft__radix4__q15_8c.html#a73f48eaea9297605705ae25d3405343e">arm_cfft_radix4_q15.c</a>
, <a class="el" href="arm__bitreversal_8c.html#a12a07b49948c354172ae07358309a4a5">arm_bitreversal.c</a>
</li>
<li>arm_bitreversal_q31()
: <a class="el" href="arm__cfft__radix4__q31_8c.html#a3fab577d25c3a517973c8c214f66f268">arm_cfft_radix4_q31.c</a>
, <a class="el" href="arm__cfft__radix2__q31_8c.html#a3fab577d25c3a517973c8c214f66f268">arm_cfft_radix2_q31.c</a>
, <a class="el" href="arm__bitreversal_8c.html#a27618705158b5c42db5fb0a381f8efc1">arm_bitreversal.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
