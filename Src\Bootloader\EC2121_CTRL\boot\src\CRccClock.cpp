
#include "CRccClock.h"
//#include "MemDebug.h"
typedef struct
{
    uint32_t       RCC_CLOCK;
    uint8_t       RCC_PERIPH;//时钟类型
    uint8_t       RCC_CLOCK_COUNT;
} RCC_CLOCK_MAP;


RCC_CLOCK_MAP RccClockMap[] =
{
    //时钟资源表
    //
    {RCC_AHB1Periph_GPIOA        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOB        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOC        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOD        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOE        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOF        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOG        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOH        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOI        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOJ        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_GPIOK        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_CRC          ,RCC_AHB1,    0},
    {RCC_AHB1Periph_FLITF        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_SRAM1        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_SRAM2        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_BKPSRAM      ,RCC_AHB1,    0},
    {RCC_AHB1Periph_SRAM3        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_CCMDATARAMEN ,RCC_AHB1,    0},
    {RCC_AHB1Periph_DMA1         ,RCC_AHB1,    0},
    {RCC_AHB1Periph_DMA2         ,RCC_AHB1,    0},
    {RCC_AHB1Periph_DMA2D        ,RCC_AHB1,    0},
    {RCC_AHB1Periph_ETH_MAC      ,RCC_AHB1,    0},
    {RCC_AHB1Periph_ETH_MAC_Tx   ,RCC_AHB1,    0},
    {RCC_AHB1Periph_ETH_MAC_Rx   ,RCC_AHB1,    0},
    {RCC_AHB1Periph_ETH_MAC_PTP  ,RCC_AHB1,    0},
    {RCC_AHB1Periph_OTG_HS       ,RCC_AHB1,    0},
    {RCC_AHB1Periph_OTG_HS_ULPI  ,RCC_AHB1,    0},
    
    {RCC_AHB2Periph_DCMI   ,RCC_AHB2,    0},
    {RCC_AHB2Periph_CRYP   ,RCC_AHB2,    0},
    {RCC_AHB2Periph_HASH   ,RCC_AHB2,    0},
    {RCC_AHB2Periph_RNG    ,RCC_AHB2,    0},
    {RCC_AHB2Periph_OTG_FS ,RCC_AHB2,    0},

    {RCC_AHB3Periph_FSMC,RCC_AHB3,    0},

    {RCC_APB1Periph_TIM2   ,RCC_APB1,    0},
    {RCC_APB1Periph_TIM3   ,RCC_APB1,    0},
    {RCC_APB1Periph_TIM4   ,RCC_APB1,    0},
    {RCC_APB1Periph_TIM5   ,RCC_APB1,    0},
    {RCC_APB1Periph_TIM6   ,RCC_APB1,    0},
    {RCC_APB1Periph_TIM7   ,RCC_APB1,    0},
    {RCC_APB1Periph_TIM12  ,RCC_APB1,    0},
    {RCC_APB1Periph_TIM13  ,RCC_APB1,    0},
    {RCC_APB1Periph_TIM14  ,RCC_APB1,    0},
    {RCC_APB1Periph_SPI2   ,RCC_APB1,    0},
    {RCC_APB1Periph_SPI3   ,RCC_APB1,    0},
    {RCC_APB1Periph_USART2 ,RCC_APB1,    0},
    {RCC_APB1Periph_USART3 ,RCC_APB1,    0},
    {RCC_APB1Periph_UART4  ,RCC_APB1,    0},
    {RCC_APB1Periph_UART5  ,RCC_APB1,    0},
    {RCC_APB1Periph_I2C1   ,RCC_APB1,    0},
    {RCC_APB1Periph_I2C2   ,RCC_APB1,    0},
    {RCC_APB1Periph_I2C3   ,RCC_APB1,    0},
    {RCC_APB1Periph_CAN1   ,RCC_APB1,    0},
    {RCC_APB1Periph_CAN2   ,RCC_APB1,    0},
    {RCC_APB1Periph_PWR    ,RCC_APB1,    0},
    {RCC_APB1Periph_DAC    ,RCC_APB1,    0},
    {RCC_APB1Periph_UART7  ,RCC_APB1,    0},
    {RCC_APB1Periph_UART8  ,RCC_APB1,    0},

    {RCC_APB2Periph_TIM1   ,RCC_APB2,    0},
    {RCC_APB2Periph_TIM8   ,RCC_APB2,    0},
    {RCC_APB2Periph_USART1 ,RCC_APB2,    0},
    {RCC_APB2Periph_USART6 ,RCC_APB2,    0},
    {RCC_APB2Periph_ADC    ,RCC_APB2,    0},
    {RCC_APB2Periph_ADC1   ,RCC_APB2,    0},
    {RCC_APB2Periph_ADC2   ,RCC_APB2,    0},
    {RCC_APB2Periph_ADC3   ,RCC_APB2,    0},
    {RCC_APB2Periph_SDIO   ,RCC_APB2,    0},
    {RCC_APB2Periph_SPI1   ,RCC_APB2,    0},
    {RCC_APB2Periph_SPI4   ,RCC_APB2,    0},
    {RCC_APB2Periph_SYSCFG ,RCC_APB2,    0},
    {RCC_APB2Periph_TIM9   ,RCC_APB2,    0},
    {RCC_APB2Periph_TIM10  ,RCC_APB2,    0},
    {RCC_APB2Periph_TIM11  ,RCC_APB2,    0},
    {RCC_APB2Periph_SPI5   ,RCC_APB2,    0},
    {RCC_APB2Periph_SPI6   ,RCC_APB2,    0},
    {RCC_APB2Periph_SAI1   ,RCC_APB2,    0},
    {RCC_APB2Periph_LTDC   ,RCC_APB2,    0}

};
//*****************************************************************************************************
//功能描述：使能时钟
//参数说明：RccClock：时钟源，Periph：时钟类型
//返　　回：
//特殊说明：本函数的目的是防止多个位置使用相同的时钟，其中一个使用完毕关闭时钟，导致其他使用该时钟的地方出现异常
//          本函数有个注意事项，如果同一个地方反复调用本函数，又没有相应数量的diable，无法失能时钟，使用时一定要注意！！！！
//作　　者：赵命华20210411
//*****************************************************************************************************

void EnableRccClock(uint32_t RccClock,uint8_t Periph)//RccClock并不是唯一，只有相同periph的RccClock才是唯一的
{
    uint8_t matchFlag = 0;
    uint8_t actionFlag = 0;
    if(Periph >= RCC_NOT_USE)
    {
        while(1);//时钟类型输入参数非法,或者需要添加新类型
    }
    for(uint8_t i=0;i < sizeof(RccClockMap)/sizeof(RCC_CLOCK_MAP);i++)
    {
        if(RccClockMap[i].RCC_CLOCK == RccClock && RccClockMap[i].RCC_PERIPH == Periph)
        {
            matchFlag = 1;//匹配到时钟
            
            if(RccClockMap[i].RCC_CLOCK_COUNT > 100)
            {
                while(1);//时钟使用次数超过100次（不可能用这么多次）
            }
            if(RccClockMap[i].RCC_CLOCK_COUNT == 0)
            {
                actionFlag = 1;
            }
            RccClockMap[i].RCC_CLOCK_COUNT++;//时钟使用次数
            break;
        }
    }
    if(matchFlag == 0)
    {
        while(1);//时钟源舒服参数非法,或者需要添加新时钟源
    }
    if(actionFlag == 1)
    {
        if(Periph == RCC_APB1)
        {
            RCC_APB1PeriphClockCmd(RccClock,ENABLE);
        }
        else if(Periph == RCC_APB2)
        {
            RCC_APB2PeriphClockCmd(RccClock,ENABLE);
        }
        else if(Periph == RCC_AHB1)
        {
            RCC_AHB1PeriphClockCmd(RccClock,ENABLE);
        }
        else if(Periph == RCC_AHB2)
        {
            RCC_AHB2PeriphClockCmd(RccClock,ENABLE);
        }
        else if(Periph == RCC_AHB3)
        {
            RCC_AHB3PeriphClockCmd(RccClock,ENABLE);
        }
    }
}
void DisableRccClock(uint32_t RccClock,uint8_t Periph)
{
    uint8_t matchFlag = 0;
    uint8_t actionFlag = 0;
    if(Periph >= RCC_NOT_USE)
    {
        while(1);//时钟类型输入参数非法,或者需要添加新类型
    }
    for(uint8_t i=0;i < sizeof(RccClockMap)/sizeof(RCC_CLOCK_MAP);i++)
    {
        if(RccClockMap[i].RCC_CLOCK == RccClock && RccClockMap[i].RCC_PERIPH == Periph)
        {
            matchFlag = 1;//匹配到时钟
            if(RccClockMap[i].RCC_CLOCK_COUNT > 0)
            {
                RccClockMap[i].RCC_CLOCK_COUNT--;//时钟使用计数
                if(RccClockMap[i].RCC_CLOCK_COUNT == 0)//最后一个使用者调用本函数，关闭该外设时钟
                {
                    actionFlag = 1;
                }
            }
            break;
        }
    }
    if(matchFlag == 0)
    {
        while(1);//时钟源舒服参数非法,或者需要添加新时钟源
    }
    if(actionFlag == 1)
    {
        if(Periph == RCC_APB1)
        {
            RCC_APB1PeriphClockCmd(RccClock,DISABLE);
        }
        else if(Periph == RCC_APB2)
        {
            RCC_APB2PeriphClockCmd(RccClock,DISABLE);
        }
        else if(Periph == RCC_AHB1)
        {
            RCC_AHB1PeriphClockCmd(RccClock,DISABLE);
        }
        else if(Periph == RCC_AHB2)
        {
            RCC_AHB2PeriphClockCmd(RccClock,DISABLE);
        }
        else if(Periph == RCC_AHB3)
        {
            RCC_AHB3PeriphClockCmd(RccClock,DISABLE);
        }
    }
}


