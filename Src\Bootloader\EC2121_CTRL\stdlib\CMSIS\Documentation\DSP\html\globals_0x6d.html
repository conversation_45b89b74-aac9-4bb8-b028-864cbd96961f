<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__"><span>_</span></a></li>
      <li><a href="globals_0x61.html#index_a"><span>a</span></a></li>
      <li><a href="globals_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_0x6c.html#index_l"><span>l</span></a></li>
      <li class="current"><a href="globals_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="globals_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="globals_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="globals_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_0x6d.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index_m"></a>- m -</h3><ul>
<li>arm_mat_add_f32()
: <a class="el" href="group___matrix_add.html#ga04bbf64a5f9c9e57dd1efb26a768aba1">arm_mat_add_f32.c</a>
, <a class="el" href="group___matrix_add.html#ga04bbf64a5f9c9e57dd1efb26a768aba1">arm_math.h</a>
</li>
<li>arm_mat_add_q15()
: <a class="el" href="group___matrix_add.html#ga147e90b7c12a162735ab8824127a33ee">arm_mat_add_q15.c</a>
, <a class="el" href="group___matrix_add.html#ga147e90b7c12a162735ab8824127a33ee">arm_math.h</a>
</li>
<li>arm_mat_add_q31()
: <a class="el" href="group___matrix_add.html#ga7d9d7d81a0832a17b831aad1e4a5dc16">arm_mat_add_q31.c</a>
, <a class="el" href="group___matrix_add.html#ga7d9d7d81a0832a17b831aad1e4a5dc16">arm_math.h</a>
</li>
<li>arm_mat_cmplx_mult_f32()
: <a class="el" href="group___cmplx_matrix_mult.html#ga1adb839ac84445b8c2f04efa43faef35">arm_mat_cmplx_mult_f32.c</a>
, <a class="el" href="group___cmplx_matrix_mult.html#ga1adb839ac84445b8c2f04efa43faef35">arm_math.h</a>
</li>
<li>arm_mat_cmplx_mult_q15()
: <a class="el" href="group___cmplx_matrix_mult.html#ga63066615e7d6f6a44f4358725092419e">arm_mat_cmplx_mult_q15.c</a>
, <a class="el" href="group___cmplx_matrix_mult.html#ga63066615e7d6f6a44f4358725092419e">arm_math.h</a>
</li>
<li>arm_mat_cmplx_mult_q31()
: <a class="el" href="group___cmplx_matrix_mult.html#gaaf3c0b171ca8412c77bab9fa90804737">arm_mat_cmplx_mult_q31.c</a>
, <a class="el" href="group___cmplx_matrix_mult.html#gaaf3c0b171ca8412c77bab9fa90804737">arm_math.h</a>
</li>
<li>arm_mat_init_f32()
: <a class="el" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27">arm_mat_init_f32.c</a>
, <a class="el" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27">arm_math.h</a>
</li>
<li>arm_mat_init_q15()
: <a class="el" href="group___matrix_init.html#ga31a7c2b991803d49719393eb2d53dc26">arm_mat_init_q15.c</a>
, <a class="el" href="group___matrix_init.html#ga31a7c2b991803d49719393eb2d53dc26">arm_math.h</a>
</li>
<li>arm_mat_init_q31()
: <a class="el" href="group___matrix_init.html#ga48a5e5d37e1f062cc57fcfaf683343cc">arm_mat_init_q31.c</a>
, <a class="el" href="group___matrix_init.html#ga48a5e5d37e1f062cc57fcfaf683343cc">arm_math.h</a>
</li>
<li>arm_mat_inverse_f32()
: <a class="el" href="group___matrix_inv.html#ga542be7aabbf7a2297a4b62cf212910e3">arm_mat_inverse_f32.c</a>
, <a class="el" href="group___matrix_inv.html#ga542be7aabbf7a2297a4b62cf212910e3">arm_math.h</a>
</li>
<li>arm_mat_inverse_f64()
: <a class="el" href="group___matrix_inv.html#gaede2367c02df083cc915ddd5d8fae838">arm_mat_inverse_f64.c</a>
, <a class="el" href="group___matrix_inv.html#gaede2367c02df083cc915ddd5d8fae838">arm_math.h</a>
</li>
<li>arm_mat_mult_f32()
: <a class="el" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0">arm_mat_mult_f32.c</a>
, <a class="el" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0">arm_math.h</a>
</li>
<li>arm_mat_mult_fast_q15()
: <a class="el" href="group___matrix_mult.html#ga08f37d93a5bfef0c5000dc5e0a411f93">arm_mat_mult_fast_q15.c</a>
, <a class="el" href="group___matrix_mult.html#ga08f37d93a5bfef0c5000dc5e0a411f93">arm_math.h</a>
</li>
<li>arm_mat_mult_fast_q31()
: <a class="el" href="group___matrix_mult.html#ga2785e8c1b785348b0c439b56aaf585a3">arm_mat_mult_fast_q31.c</a>
, <a class="el" href="group___matrix_mult.html#ga2785e8c1b785348b0c439b56aaf585a3">arm_math.h</a>
</li>
<li>arm_mat_mult_q15()
: <a class="el" href="group___matrix_mult.html#ga3657b99a9667945373e520dbac0f4516">arm_mat_mult_q15.c</a>
, <a class="el" href="arm__math_8h.html#a7521d59196189bb6dde26e8cdfb66e21">arm_math.h</a>
</li>
<li>arm_mat_mult_q31()
: <a class="el" href="group___matrix_mult.html#ga2ec612a8c2c4916477fb9bc1ab548a6e">arm_mat_mult_q31.c</a>
, <a class="el" href="group___matrix_mult.html#ga2ec612a8c2c4916477fb9bc1ab548a6e">arm_math.h</a>
</li>
<li>arm_mat_scale_f32()
: <a class="el" href="group___matrix_scale.html#ga9cb4e385b18c9a0b9cbc940c1067ca12">arm_math.h</a>
, <a class="el" href="group___matrix_scale.html#ga9cb4e385b18c9a0b9cbc940c1067ca12">arm_mat_scale_f32.c</a>
</li>
<li>arm_mat_scale_q15()
: <a class="el" href="group___matrix_scale.html#ga7521769e2cf1c3d9c4656138cd2ae2ca">arm_mat_scale_q15.c</a>
, <a class="el" href="group___matrix_scale.html#ga7521769e2cf1c3d9c4656138cd2ae2ca">arm_math.h</a>
</li>
<li>arm_mat_scale_q31()
: <a class="el" href="group___matrix_scale.html#ga609743821ee81fa8c34c4bcdc1ed9744">arm_mat_scale_q31.c</a>
, <a class="el" href="group___matrix_scale.html#ga609743821ee81fa8c34c4bcdc1ed9744">arm_math.h</a>
</li>
<li>arm_mat_sub_f32()
: <a class="el" href="group___matrix_sub.html#gac8b72fb70246ccfee3b372002345732c">arm_mat_sub_f32.c</a>
, <a class="el" href="group___matrix_sub.html#gac8b72fb70246ccfee3b372002345732c">arm_math.h</a>
</li>
<li>arm_mat_sub_q15()
: <a class="el" href="group___matrix_sub.html#gaf647776a425b7f9dd0aca3e11d81f02f">arm_mat_sub_q15.c</a>
, <a class="el" href="group___matrix_sub.html#gaf647776a425b7f9dd0aca3e11d81f02f">arm_math.h</a>
</li>
<li>arm_mat_sub_q31()
: <a class="el" href="group___matrix_sub.html#ga39f42e0e3b7f115fbb909d6ff4e1329d">arm_mat_sub_q31.c</a>
, <a class="el" href="group___matrix_sub.html#ga39f42e0e3b7f115fbb909d6ff4e1329d">arm_math.h</a>
</li>
<li>arm_mat_trans_f32()
: <a class="el" href="group___matrix_trans.html#gad7dd9f108429da13d3864696ceeec789">arm_mat_trans_f32.c</a>
, <a class="el" href="group___matrix_trans.html#gad7dd9f108429da13d3864696ceeec789">arm_math.h</a>
</li>
<li>arm_mat_trans_q15()
: <a class="el" href="group___matrix_trans.html#ga4f4f821cc695fd0ef9061d702e08050a">arm_mat_trans_q15.c</a>
, <a class="el" href="group___matrix_trans.html#ga4f4f821cc695fd0ef9061d702e08050a">arm_math.h</a>
</li>
<li>arm_mat_trans_q31()
: <a class="el" href="group___matrix_trans.html#ga30a4d49489ac67ff98a46b9f58f73bf1">arm_mat_trans_q31.c</a>
, <a class="el" href="group___matrix_trans.html#ga30a4d49489ac67ff98a46b9f58f73bf1">arm_math.h</a>
</li>
<li>arm_max_f32()
: <a class="el" href="group___max.html#ga5b89d1b04575aeec494f678695fb87d8">arm_max_f32.c</a>
, <a class="el" href="group___max.html#ga5b89d1b04575aeec494f678695fb87d8">arm_math.h</a>
</li>
<li>arm_max_q15()
: <a class="el" href="group___max.html#gac132856c68f4bf2a056eaad5921c7880">arm_max_q15.c</a>
, <a class="el" href="group___max.html#gac132856c68f4bf2a056eaad5921c7880">arm_math.h</a>
</li>
<li>arm_max_q31()
: <a class="el" href="group___max.html#gaff7cbd4e955382def06724cc4cc85795">arm_max_q31.c</a>
, <a class="el" href="group___max.html#gaff7cbd4e955382def06724cc4cc85795">arm_math.h</a>
</li>
<li>arm_max_q7()
: <a class="el" href="group___max.html#ga6afd64d381b5c232de59163ebfe71e35">arm_max_q7.c</a>
, <a class="el" href="group___max.html#ga6afd64d381b5c232de59163ebfe71e35">arm_math.h</a>
</li>
<li>arm_mean_f32()
: <a class="el" href="group__mean.html#ga74ce08c49ab61e57bd50c3a0ca1fdb2b">arm_mean_f32.c</a>
, <a class="el" href="group__mean.html#ga74ce08c49ab61e57bd50c3a0ca1fdb2b">arm_math.h</a>
</li>
<li>arm_mean_q15()
: <a class="el" href="group__mean.html#gac882495d5f098819fd3939c1ef7795b3">arm_mean_q15.c</a>
, <a class="el" href="group__mean.html#gac882495d5f098819fd3939c1ef7795b3">arm_math.h</a>
</li>
<li>arm_mean_q31()
: <a class="el" href="group__mean.html#gacf2526d8c2d75e486e8f0b0e31877ad0">arm_mean_q31.c</a>
, <a class="el" href="group__mean.html#gacf2526d8c2d75e486e8f0b0e31877ad0">arm_math.h</a>
</li>
<li>arm_mean_q7()
: <a class="el" href="group__mean.html#gaebc707ee539020357c25da4c75b52eb7">arm_mean_q7.c</a>
, <a class="el" href="group__mean.html#gaebc707ee539020357c25da4c75b52eb7">arm_math.h</a>
</li>
<li>arm_min_f32()
: <a class="el" href="group___min.html#gaf62b1673740fc516ea64daf777b7d74a">arm_min_f32.c</a>
, <a class="el" href="group___min.html#gaf62b1673740fc516ea64daf777b7d74a">arm_math.h</a>
</li>
<li>arm_min_q15()
: <a class="el" href="group___min.html#gad065e37535ebb726750ac1545cb3fa6f">arm_min_q15.c</a>
, <a class="el" href="group___min.html#gad065e37535ebb726750ac1545cb3fa6f">arm_math.h</a>
</li>
<li>arm_min_q31()
: <a class="el" href="group___min.html#gab20faeceb5ff5d2d9dd628c2ecf41303">arm_min_q31.c</a>
, <a class="el" href="group___min.html#gab20faeceb5ff5d2d9dd628c2ecf41303">arm_math.h</a>
</li>
<li>arm_min_q7()
: <a class="el" href="group___min.html#ga3631d38ac8d715fc14f6f1b343f4c4ed">arm_math.h</a>
, <a class="el" href="group___min.html#ga3631d38ac8d715fc14f6f1b343f4c4ed">arm_min_q7.c</a>
</li>
<li>arm_mult_f32()
: <a class="el" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32">arm_math.h</a>
, <a class="el" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32">arm_mult_f32.c</a>
</li>
<li>arm_mult_q15()
: <a class="el" href="group___basic_mult.html#gafb0778d27ed98a2a6f2ecb7d48cc8c75">arm_math.h</a>
, <a class="el" href="group___basic_mult.html#gafb0778d27ed98a2a6f2ecb7d48cc8c75">arm_mult_q15.c</a>
</li>
<li>arm_mult_q31()
: <a class="el" href="group___basic_mult.html#ga3528c0f54a0607acc603f0490d3ca6c6">arm_mult_q31.c</a>
, <a class="el" href="group___basic_mult.html#ga3528c0f54a0607acc603f0490d3ca6c6">arm_math.h</a>
</li>
<li>arm_mult_q7()
: <a class="el" href="group___basic_mult.html#ga16677275ed83ff0878da531e875c27ef">arm_mult_q7.c</a>
, <a class="el" href="group___basic_mult.html#ga16677275ed83ff0878da531e875c27ef">arm_math.h</a>
</li>
<li>M0
: <a class="el" href="arm__convolution__example_2_g_c_c_2_abstract_8txt.html#adc5db9f8d5aa735dbce2dc35f184d85e">arm_convolution_example/GCC/Abstract.txt</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_abstract_8txt.html#ad0415ef995ebc3fc2ad584da9907dcb5">arm_fft_bin_example/GCC/Abstract.txt</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_abstract_8txt.html#a59a24f1db2c97fc0ad7948b4a74267ee">arm_class_marks_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_abstract_8txt.html#a59a24f1db2c97fc0ad7948b4a74267ee">arm_class_marks_example/GCC/Abstract.txt</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_abstract_8txt.html#abf726d1cea9345acb0021535d4fdd5af">arm_fir_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_abstract_8txt.html#a8c8cf1e0f2ebd6135c79d5338a60899c">arm_variance_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_abstract_8txt.html#a80f8916aca7a5a98fcfc39d1ef743b37">arm_linear_interp_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_abstract_8txt.html#aafffda4c4e9b93a580e5a8cc96c11e37">arm_dotproduct_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_abstract_8txt.html#ac378b27aa1fc4fe4ac9a4dfa2d185553">arm_graphic_equalizer_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_abstract_8txt.html#ad0415ef995ebc3fc2ad584da9907dcb5">arm_fft_bin_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_abstract_8txt.html#adc5db9f8d5aa735dbce2dc35f184d85e">arm_convolution_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_abstract_8txt.html#aafffda4c4e9b93a580e5a8cc96c11e37">arm_dotproduct_example/GCC/Abstract.txt</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_abstract_8txt.html#a267aee43e600168b057c1aa126029002">arm_matrix_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_abstract_8txt.html#ae39c147711857790fd5c3cc7a75ef0c3">arm_sin_cos_example/ARM/Abstract.txt</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_abstract_8txt.html#a20d2ac954144a7f2d4aced0816eecef6">arm_signal_converge_example/ARM/Abstract.txt</a>
</li>
<li>main()
: <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">ARM/arm_dotproduct_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">GCC/arm_class_marks_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">ARM/arm_convolution_example_f32.c</a>
, <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">arm_fir_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">GCC/arm_convolution_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">ARM/arm_fft_bin_example_f32.c</a>
, <a class="el" href="arm__variance__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">arm_variance_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">GCC/arm_dotproduct_example_f32.c</a>
, <a class="el" href="arm__linear__interp__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">arm_linear_interp_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">ARM/arm_class_marks_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">GCC/arm_fft_bin_example_f32.c</a>
, <a class="el" href="arm__graphic__equalizer__example__q31_8c.html#a52d2cba30e6946c95578be946ac12a65">arm_graphic_equalizer_example_q31.c</a>
, <a class="el" href="arm__sin__cos__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">arm_sin_cos_example_f32.c</a>
, <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">arm_signal_converge_example_f32.c</a>
, <a class="el" href="arm__matrix__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">arm_matrix_example_f32.c</a>
</li>
<li>MAX_BLOCKSIZE
: <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">ARM/arm_dotproduct_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__dotproduct__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">GCC/arm_dotproduct_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">GCC/arm_convolution_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">ARM/arm_convolution_example_f32.c</a>
, <a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">arm_variance_example_f32.c</a>
, <a class="el" href="arm__sin__cos__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">arm_sin_cos_example_f32.c</a>
</li>
<li>max_marks
: <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#aad32888fa966b3d9db9c31bcbba9d9ef">ARM/arm_class_marks_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#aad32888fa966b3d9db9c31bcbba9d9ef">GCC/arm_class_marks_example_f32.c</a>
</li>
<li>mean
: <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681">ARM/arm_class_marks_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681">GCC/arm_class_marks_example_f32.c</a>
</li>
<li>merge_rfft_f32()
: <a class="el" href="arm__rfft__fast__f32_8c.html#a93258bc1e64a939a8ebd086367e459af">arm_rfft_fast_f32.c</a>
</li>
<li>min_marks
: <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#abb7687fa07ec54d8e792cfcbfe2ca809">GCC/arm_class_marks_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abb7687fa07ec54d8e792cfcbfe2ca809">ARM/arm_class_marks_example_f32.c</a>
</li>
<li>MU
: <a class="el" href="arm__signal__converge__example__f32_8c.html#a09bc9e6a44f0291cfcf578f2efcddfab">arm_signal_converge_example_f32.c</a>
</li>
<li>mult32x64()
: <a class="el" href="arm__math_8h.html#a642a29d71f7951a7f6c0b797c300b711">arm_math.h</a>
</li>
<li>mult_32x32_keep32
: <a class="el" href="arm__math_8h.html#abb4baa0192bbb6fabc9251af4b4cb322">arm_math.h</a>
</li>
<li>mult_32x32_keep32_R
: <a class="el" href="arm__math_8h.html#a960f210642058d2b3d5368729a6e8375">arm_math.h</a>
</li>
<li>multAcc_32x32_keep32
: <a class="el" href="arm__math_8h.html#a58454519e12e8157f0a1c36071333655">arm_math.h</a>
</li>
<li>multAcc_32x32_keep32_R
: <a class="el" href="arm__math_8h.html#aba3e538352fc7f9d6d15f9a18d469399">arm_math.h</a>
</li>
<li>multOutput
: <a class="el" href="_g_c_c_2arm__dotproduct__example__f32_8c.html#ad0bfd425dfe1ff2bda80fb957e464098">GCC/arm_dotproduct_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#ad0bfd425dfe1ff2bda80fb957e464098">ARM/arm_dotproduct_example_f32.c</a>
</li>
<li>multSub_32x32_keep32
: <a class="el" href="arm__math_8h.html#a9ec66f3082a4c65c78075638255f42ab">arm_math.h</a>
</li>
<li>multSub_32x32_keep32_R
: <a class="el" href="arm__math_8h.html#a668fbf1cd1c3bc8faf1b1c83964ade23">arm_math.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
