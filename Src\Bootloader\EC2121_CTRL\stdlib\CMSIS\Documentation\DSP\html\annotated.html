<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Data Structures</title>
<title>CMSIS-DSP: Data Structures</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('annotated.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Data Structures</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here are the data structures with brief descriptions:</div><div class="directory">
<table class="directory">
<tr id="row_0_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__bilinear__interp__instance__f32.html" target="_self">arm_bilinear_interp_instance_f32</a></td><td class="desc">Instance structure for the floating-point bilinear interpolation function</td></tr>
<tr id="row_1_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__bilinear__interp__instance__q15.html" target="_self">arm_bilinear_interp_instance_q15</a></td><td class="desc">Instance structure for the Q15 bilinear interpolation function</td></tr>
<tr id="row_2_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__bilinear__interp__instance__q31.html" target="_self">arm_bilinear_interp_instance_q31</a></td><td class="desc">Instance structure for the Q31 bilinear interpolation function</td></tr>
<tr id="row_3_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__bilinear__interp__instance__q7.html" target="_self">arm_bilinear_interp_instance_q7</a></td><td class="desc">Instance structure for the Q15 bilinear interpolation function</td></tr>
<tr id="row_4_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__biquad__cas__df1__32x64__ins__q31.html" target="_self">arm_biquad_cas_df1_32x64_ins_q31</a></td><td class="desc">Instance structure for the high precision Q31 Biquad cascade filter</td></tr>
<tr id="row_5_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html" target="_self">arm_biquad_cascade_df2T_instance_f32</a></td><td class="desc">Instance structure for the floating-point transposed direct form II Biquad cascade filter</td></tr>
<tr id="row_6_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html" target="_self">arm_biquad_cascade_df2T_instance_f64</a></td><td class="desc">Instance structure for the floating-point transposed direct form II Biquad cascade filter</td></tr>
<tr id="row_7_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html" target="_self">arm_biquad_cascade_stereo_df2T_instance_f32</a></td><td class="desc">Instance structure for the floating-point transposed direct form II Biquad cascade filter</td></tr>
<tr id="row_8_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__biquad__casd__df1__inst__f32.html" target="_self">arm_biquad_casd_df1_inst_f32</a></td><td class="desc">Instance structure for the floating-point Biquad cascade filter</td></tr>
<tr id="row_9_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__biquad__casd__df1__inst__q15.html" target="_self">arm_biquad_casd_df1_inst_q15</a></td><td class="desc">Instance structure for the Q15 Biquad cascade filter</td></tr>
<tr id="row_10_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__biquad__casd__df1__inst__q31.html" target="_self">arm_biquad_casd_df1_inst_q31</a></td><td class="desc">Instance structure for the Q31 Biquad cascade filter</td></tr>
<tr id="row_11_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__instance__f32.html" target="_self">arm_cfft_instance_f32</a></td><td class="desc">Instance structure for the floating-point CFFT/CIFFT function</td></tr>
<tr id="row_12_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__instance__q15.html" target="_self">arm_cfft_instance_q15</a></td><td class="desc">Instance structure for the fixed-point CFFT/CIFFT function</td></tr>
<tr id="row_13_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__instance__q31.html" target="_self">arm_cfft_instance_q31</a></td><td class="desc">Instance structure for the fixed-point CFFT/CIFFT function</td></tr>
<tr id="row_14_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__radix2__instance__f32.html" target="_self">arm_cfft_radix2_instance_f32</a></td><td class="desc">Instance structure for the floating-point CFFT/CIFFT function</td></tr>
<tr id="row_15_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__radix2__instance__q15.html" target="_self">arm_cfft_radix2_instance_q15</a></td><td class="desc">Instance structure for the Q15 CFFT/CIFFT function</td></tr>
<tr id="row_16_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__radix2__instance__q31.html" target="_self">arm_cfft_radix2_instance_q31</a></td><td class="desc">Instance structure for the Radix-2 Q31 CFFT/CIFFT function</td></tr>
<tr id="row_17_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__radix4__instance__f32.html" target="_self">arm_cfft_radix4_instance_f32</a></td><td class="desc">Instance structure for the floating-point CFFT/CIFFT function</td></tr>
<tr id="row_18_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__radix4__instance__q15.html" target="_self">arm_cfft_radix4_instance_q15</a></td><td class="desc">Instance structure for the Q15 CFFT/CIFFT function</td></tr>
<tr id="row_19_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__cfft__radix4__instance__q31.html" target="_self">arm_cfft_radix4_instance_q31</a></td><td class="desc">Instance structure for the Q31 CFFT/CIFFT function</td></tr>
<tr id="row_20_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__dct4__instance__f32.html" target="_self">arm_dct4_instance_f32</a></td><td class="desc">Instance structure for the floating-point DCT4/IDCT4 function</td></tr>
<tr id="row_21_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__dct4__instance__q15.html" target="_self">arm_dct4_instance_q15</a></td><td class="desc">Instance structure for the Q15 DCT4/IDCT4 function</td></tr>
<tr id="row_22_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__dct4__instance__q31.html" target="_self">arm_dct4_instance_q31</a></td><td class="desc">Instance structure for the Q31 DCT4/IDCT4 function</td></tr>
<tr id="row_23_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__decimate__instance__f32.html" target="_self">arm_fir_decimate_instance_f32</a></td><td class="desc">Instance structure for the floating-point FIR decimator</td></tr>
<tr id="row_24_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__decimate__instance__q15.html" target="_self">arm_fir_decimate_instance_q15</a></td><td class="desc">Instance structure for the Q15 FIR decimator</td></tr>
<tr id="row_25_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__decimate__instance__q31.html" target="_self">arm_fir_decimate_instance_q31</a></td><td class="desc">Instance structure for the Q31 FIR decimator</td></tr>
<tr id="row_26_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__instance__f32.html" target="_self">arm_fir_instance_f32</a></td><td class="desc">Instance structure for the floating-point FIR filter</td></tr>
<tr id="row_27_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__instance__q15.html" target="_self">arm_fir_instance_q15</a></td><td class="desc">Instance structure for the Q15 FIR filter</td></tr>
<tr id="row_28_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__instance__q31.html" target="_self">arm_fir_instance_q31</a></td><td class="desc">Instance structure for the Q31 FIR filter</td></tr>
<tr id="row_29_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__instance__q7.html" target="_self">arm_fir_instance_q7</a></td><td class="desc">Instance structure for the Q7 FIR filter</td></tr>
<tr id="row_30_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__interpolate__instance__f32.html" target="_self">arm_fir_interpolate_instance_f32</a></td><td class="desc">Instance structure for the floating-point FIR interpolator</td></tr>
<tr id="row_31_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__interpolate__instance__q15.html" target="_self">arm_fir_interpolate_instance_q15</a></td><td class="desc">Instance structure for the Q15 FIR interpolator</td></tr>
<tr id="row_32_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__interpolate__instance__q31.html" target="_self">arm_fir_interpolate_instance_q31</a></td><td class="desc">Instance structure for the Q31 FIR interpolator</td></tr>
<tr id="row_33_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__lattice__instance__f32.html" target="_self">arm_fir_lattice_instance_f32</a></td><td class="desc">Instance structure for the floating-point FIR lattice filter</td></tr>
<tr id="row_34_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__lattice__instance__q15.html" target="_self">arm_fir_lattice_instance_q15</a></td><td class="desc">Instance structure for the Q15 FIR lattice filter</td></tr>
<tr id="row_35_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__lattice__instance__q31.html" target="_self">arm_fir_lattice_instance_q31</a></td><td class="desc">Instance structure for the Q31 FIR lattice filter</td></tr>
<tr id="row_36_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__sparse__instance__f32.html" target="_self">arm_fir_sparse_instance_f32</a></td><td class="desc">Instance structure for the floating-point sparse FIR filter</td></tr>
<tr id="row_37_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__sparse__instance__q15.html" target="_self">arm_fir_sparse_instance_q15</a></td><td class="desc">Instance structure for the Q15 sparse FIR filter</td></tr>
<tr id="row_38_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__sparse__instance__q31.html" target="_self">arm_fir_sparse_instance_q31</a></td><td class="desc">Instance structure for the Q31 sparse FIR filter</td></tr>
<tr id="row_39_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__fir__sparse__instance__q7.html" target="_self">arm_fir_sparse_instance_q7</a></td><td class="desc">Instance structure for the Q7 sparse FIR filter</td></tr>
<tr id="row_40_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__iir__lattice__instance__f32.html" target="_self">arm_iir_lattice_instance_f32</a></td><td class="desc">Instance structure for the floating-point IIR lattice filter</td></tr>
<tr id="row_41_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__iir__lattice__instance__q15.html" target="_self">arm_iir_lattice_instance_q15</a></td><td class="desc">Instance structure for the Q15 IIR lattice filter</td></tr>
<tr id="row_42_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__iir__lattice__instance__q31.html" target="_self">arm_iir_lattice_instance_q31</a></td><td class="desc">Instance structure for the Q31 IIR lattice filter</td></tr>
<tr id="row_43_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__linear__interp__instance__f32.html" target="_self">arm_linear_interp_instance_f32</a></td><td class="desc">Instance structure for the floating-point Linear Interpolate function</td></tr>
<tr id="row_44_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__lms__instance__f32.html" target="_self">arm_lms_instance_f32</a></td><td class="desc">Instance structure for the floating-point LMS filter</td></tr>
<tr id="row_45_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__lms__instance__q15.html" target="_self">arm_lms_instance_q15</a></td><td class="desc">Instance structure for the Q15 LMS filter</td></tr>
<tr id="row_46_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__lms__instance__q31.html" target="_self">arm_lms_instance_q31</a></td><td class="desc">Instance structure for the Q31 LMS filter</td></tr>
<tr id="row_47_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__lms__norm__instance__f32.html" target="_self">arm_lms_norm_instance_f32</a></td><td class="desc">Instance structure for the floating-point normalized LMS filter</td></tr>
<tr id="row_48_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__lms__norm__instance__q15.html" target="_self">arm_lms_norm_instance_q15</a></td><td class="desc">Instance structure for the Q15 normalized LMS filter</td></tr>
<tr id="row_49_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__lms__norm__instance__q31.html" target="_self">arm_lms_norm_instance_q31</a></td><td class="desc">Instance structure for the Q31 normalized LMS filter</td></tr>
<tr id="row_50_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__matrix__instance__f32.html" target="_self">arm_matrix_instance_f32</a></td><td class="desc">Instance structure for the floating-point matrix structure</td></tr>
<tr id="row_51_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__matrix__instance__f64.html" target="_self">arm_matrix_instance_f64</a></td><td class="desc">Instance structure for the floating-point matrix structure</td></tr>
<tr id="row_52_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__matrix__instance__q15.html" target="_self">arm_matrix_instance_q15</a></td><td class="desc">Instance structure for the Q15 matrix structure</td></tr>
<tr id="row_53_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__matrix__instance__q31.html" target="_self">arm_matrix_instance_q31</a></td><td class="desc">Instance structure for the Q31 matrix structure</td></tr>
<tr id="row_54_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__pid__instance__f32.html" target="_self">arm_pid_instance_f32</a></td><td class="desc">Instance structure for the floating-point PID Control</td></tr>
<tr id="row_55_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__pid__instance__q15.html" target="_self">arm_pid_instance_q15</a></td><td class="desc">Instance structure for the Q15 PID Control</td></tr>
<tr id="row_56_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__pid__instance__q31.html" target="_self">arm_pid_instance_q31</a></td><td class="desc">Instance structure for the Q31 PID Control</td></tr>
<tr id="row_57_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__rfft__fast__instance__f32.html" target="_self">arm_rfft_fast_instance_f32</a></td><td class="desc">Instance structure for the floating-point RFFT/RIFFT function</td></tr>
<tr id="row_58_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__rfft__instance__f32.html" target="_self">arm_rfft_instance_f32</a></td><td class="desc">Instance structure for the floating-point RFFT/RIFFT function</td></tr>
<tr id="row_59_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__rfft__instance__q15.html" target="_self">arm_rfft_instance_q15</a></td><td class="desc">Instance structure for the Q15 RFFT/RIFFT function</td></tr>
<tr id="row_60_" class="even"><td class="entry"><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="structarm__rfft__instance__q31.html" target="_self">arm_rfft_instance_q31</a></td><td class="desc">Instance structure for the Q31 RFFT/RIFFT function</td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
