
#ifndef PRODUCT_INFO_H
#define PRODUCT_INFO_H

#if !defined (CFG_MF_ISO_CAN)
#define PRODUCT_PROTOCOL_J1850
#endif

#if !defined (CFG_MF_GM) && !defined (CFG_MF_CHRYSLER)
#define PRODUCT_PROTOCOL_KLINE
#endif

#if defined (CFG_MF_GM)
#define PRODUCT_PROTOCOL_SWCAN
#endif

#if defined (CFG_MF_CHRYSLER)
#define PRODUCT_PROTOCOL_SCI
#endif

#if defined (CFG_MF_FORD) || defined (CFG_MF_JLR)
#define PRODUCT_PROTOCOL_PWM
#endif

#if defined (CFG_MF_TOYOTA) || defined (CFG_MF_CHRYSLER) || defined (CFG_MF_GM)
#define PRODUCT_PROTOCOL_VPW
#endif

#ifdef PRODUCT_PROTOCOL_KLINE
#define PRODUCT_PROTOCOL_KLINE_PS
#endif

#if !defined (CFG_MF_FORD)
#define PRODUCT_PROTOCOL_CAN_PS
#endif


#endif
