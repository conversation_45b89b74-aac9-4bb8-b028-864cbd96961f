<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Ethernet Interface</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__eth__interface__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Content</a> &#124;
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#enum-members">Enumerations</a>  </div>
  <div class="headertitle">
<div class="title">Ethernet Interface</div>  </div>
</div><!--header-->
<div class="contents">

<p>Ethernet common definitions (Driver_ETH.h)  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Content</h2></td></tr>
<tr class="memitem:group__eth__mac__interface__gr"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html">Ethernet MAC Interface</a></td></tr>
<tr class="memdesc:group__eth__mac__interface__gr"><td class="mdescLeft">&#160;</td><td class="mdescRight">Driver API for Ethernet MAC Peripheral (Driver_ETH_MAC.h) <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__eth__phy__interface__gr"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html">Ethernet PHY Interface</a></td></tr>
<tr class="memdesc:group__eth__phy__interface__gr"><td class="mdescLeft">&#160;</td><td class="mdescRight">Driver API for Ethernet PHY Peripheral (Driver_ETH_PHY.h) <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o">ARM_ETH_LINK_INFO</a></td></tr>
<tr class="memdesc:struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ethernet link information.  <a href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___e_t_h___m_a_c___a_d_d_r"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a></td></tr>
<tr class="memdesc:struct_a_r_m___e_t_h___m_a_c___a_d_d_r"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ethernet MAC Address.  <a href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___e_t_h___m_a_c___a_d_d_r"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:gacf7db5320eb841b462a4af3c56cc9291"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291">ARM_ETH_LINK_STATE</a> { <br/>
&#160;&#160;<a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291a5f635c9352db6cb6fa9ad95660850487">ARM_ETH_LINK_DOWN</a>, 
<br/>
&#160;&#160;<a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291ab5e5b02c3c8a5a0fefcf69f3be7e31c1">ARM_ETH_LINK_UP</a>
<br/>
 }</td></tr>
<tr class="memdesc:gacf7db5320eb841b462a4af3c56cc9291"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ethernet link state.  <a href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291">More...</a><br/></td></tr>
<tr class="separator:gacf7db5320eb841b462a4af3c56cc9291"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Ethernet common definitions (Driver_ETH.h) </p>
<p><b>Ethernet</b> is a networking technology for exchanging data packages between computer systems. Several microcontrollers integrate an Ethernet MAC (Media Access Control) data-link layer that interfaces to an Ethernet PHY (Physical Interface Transceiver).</p>
<p>Wikipedia offers more information about the <a href="http://en.wikipedia.org/wiki/Ethernet" target="_blank"><b>Ethernet</b></a>.</p>
<p><b>Ethernet Structure</b></p>
<p>The Ethernet PHY connects typically to the Ethernet MAC using an MII (Media Independent Interface) or RMII (Reduced Media Independent Interface).</p>
<p><br/>
 </p>
<div class="image">
<img src="EthernetSchematic.png" alt="EthernetSchematic.png"/>
<div class="caption">
Block Diagram of a typical Ethernet Interface</div></div>
<p> <b>Ethernet API</b></p>
<p>The following header files define the Application Programming Interface (API) for the <b>Ethernet</b> interface:</p>
<ul>
<li><b>Driver_ETH.h</b> : Common definitions of the Ethernet PHY and MAC part</li>
<li><b>Driver_ETH_MAC.h</b> : API for the Ethernet MAC</li>
<li><b>Driver_ETH_PHY.h</b> : API for the Ethernet PHY</li>
</ul>
<p>The driver implementation of the Ethernet MAC is a typical part of a Device Family Pack (DFP) that supports the peripherals of the microcontroller family. The driver implementation of the Ethernet PHY is a typical part of a <b>Network</b> Software Pack, since PHY is typically not integrated into the microcontroller.</p>
<p><b>Driver Functions</b></p>
<p>The driver functions are published in the access struct as explained in <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a></p>
<ul>
<li><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c">ARM_DRIVER_ETH_MAC</a> : access struct for <b>Ethernet MAC</b> driver functions.</li>
<li><a class="el" href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y">ARM_DRIVER_ETH_PHY</a> : access struct for <b>Ethernet PHY</b> driver functions.</li>
</ul>
<p>Both drivers are used in combination and usually the Ethernet MAC provides a media interface to the Ethernet PHY. A typical setup sequence for the drivers is shown below:</p>
<p><b>Example Code</b></p>
<p>The following example code shows the usage of the Ethernet interface.</p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c" title="Access structure of the Ethernet MAC Driver.">ARM_DRIVER_ETH_MAC</a> Driver_ETH_MAC0;</div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y" title="Access structure of the Ethernet PHY Driver.">ARM_DRIVER_ETH_PHY</a> Driver_ETH_PHY0;</div>
<div class="line"> </div>
<div class="line"><span class="keyword">static</span> <a class="code" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c" title="Access structure of the Ethernet MAC Driver.">ARM_DRIVER_ETH_MAC</a> *mac;</div>
<div class="line"><span class="keyword">static</span> <a class="code" href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y" title="Access structure of the Ethernet PHY Driver.">ARM_DRIVER_ETH_PHY</a> *phy;</div>
<div class="line"><span class="keyword">static</span> <a class="code" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r" title="Ethernet MAC Address.">ARM_ETH_MAC_ADDR</a>          own_mac_address;</div>
<div class="line"><span class="keyword">static</span> <a class="code" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s" title="Ethernet MAC Capabilities.">ARM_ETH_MAC_CAPABILITIES</a>  capabilities;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> ethernet_mac_notify (uint32_t event)  {</div>
<div class="line">  <span class="keywordflow">switch</span> (event)  {</div>
<div class="line">     :</div>
<div class="line">  }  </div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> initialize_ethernet_interface (<span class="keywordtype">void</span>) {</div>
<div class="line">  mac = &amp;Driver_ETH_MAC0;</div>
<div class="line">  phy = &amp;Driver_ETH_PHY0;</div>
<div class="line"> </div>
<div class="line">  <span class="comment">// Initialize Media Access Controller</span></div>
<div class="line">  capabilities = mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a9fd725bb058c584a9ced9c579561cdf1" title="Pointer to ARM_ETH_MAC_GetCapabilities : Get driver capabilities.">GetCapabilities</a> ();</div>
<div class="line">   </div>
<div class="line">  mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#aa34417c70cb8b43567c59aa530866cc7" title="Pointer to ARM_ETH_MAC_Initialize : Initialize Ethernet MAC Device.">Initialize</a> (ethernet_mac_notify);</div>
<div class="line">  mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef" title="Pointer to ARM_ETH_MAC_PowerControl : Control Ethernet MAC Device Power.">PowerControl</a> (<a class="code" href="_driver___common_8h.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5abed52b77a9ce4775570e44a842b1295e" title="Power on: full operation at maximum performance.">ARM_POWER_FULL</a>);</div>
<div class="line"> </div>
<div class="line">  <span class="keywordflow">if</span> (capabilities.<a class="code" href="group__eth__mac__interface__gr.html#a7fdea04bacd9c0e12792751055ef6238" title="1 = driver provides serialized MAC address">mac_address</a> == 0)  {</div>
<div class="line">    <span class="comment">// populate own_mac_address with the address to use</span></div>
<div class="line">    mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#ac640f929dc4d5bde3e4282c75b25c00d" title="Pointer to ARM_ETH_MAC_SetMacAddress : Set Ethernet MAC Address.">SetMacAddress</a>(&amp;own_mac_address);</div>
<div class="line">  }</div>
<div class="line">  <span class="keywordflow">else</span> {</div>
<div class="line">    mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a02837059933cd04b04bf795a7138f218" title="Pointer to ARM_ETH_MAC_GetMacAddress : Get Ethernet MAC Address.">GetMacAddress</a>(&amp;own_mac_address);</div>
<div class="line">  }</div>
<div class="line"> </div>
<div class="line">  <span class="comment">// Initialize Physical Media Interface</span></div>
<div class="line">  <span class="keywordflow">if</span> (phy-&gt;<a class="code" href="group__eth__phy__interface__gr.html#a9f9e7173bf8fed4d774fa48da53739ba" title="Pointer to ARM_ETH_PHY_Initialize : Initialize PHY Device.">Initialize</a> (mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a0f2ddb734e4242077275761400b26e35" title="Pointer to ARM_ETH_MAC_PHY_Read : Read Ethernet PHY Register through Management Interface.">PHY_Read</a>, mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#ac3efe9bdc31c3b1d7fd8eb82bbfb4c13" title="Pointer to ARM_ETH_MAC_PHY_Write : Write Ethernet PHY Register through Management Interface...">PHY_Write</a>) == <a class="code" href="group__execution__status.html#ga85752c5de59e8adeb001e35ff5be6be7" title="Operation succeeded.">ARM_DRIVER_OK</a>) {</div>
<div class="line">    phy-&gt;<a class="code" href="group__eth__phy__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef" title="Pointer to ARM_ETH_PHY_PowerControl : Control PHY Device Power.">PowerControl</a> (<a class="code" href="_driver___common_8h.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5abed52b77a9ce4775570e44a842b1295e" title="Power on: full operation at maximum performance.">ARM_POWER_FULL</a>);</div>
<div class="line">    phy-&gt;<a class="code" href="group__eth__phy__interface__gr.html#a7dfc7cf346c80e7fdb2fe4cea2c61161" title="Pointer to ARM_ETH_PHY_SetInterface : Set Ethernet Media Interface.">SetInterface</a> (capabilities.<a class="code" href="group__eth__mac__interface__gr.html#a3c5cb74e086417a01d0079f847a3fc8d" title="Ethernet Media Interface type.">media_interface</a>);</div>
<div class="line">    phy-&gt;<a class="code" href="group__eth__phy__interface__gr.html#ae6686344f4d6afa0881d1e545c898a3d" title="Pointer to ARM_ETH_PHY_SetMode : Set Ethernet PHY Device Operation mode.">SetMode</a> (<a class="code" href="group__eth__phy__mode__ctrls.html#ga6a8c54f8fed3e5f68bd04eb715d10ab9" title="Auto Negotiation mode.">ARM_ETH_PHY_AUTO_NEGOTIATE</a>);</div>
<div class="line">  }</div>
<div class="line">    :</div>
<div class="line">    :</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keyword">static</span> <a class="code" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291" title="Ethernet link state.">ARM_ETH_LINK_STATE</a> ethernet_link;   <span class="comment">// current link status</span></div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> ethernet_check_link_status (<span class="keywordtype">void</span>) {</div>
<div class="line">  <a class="code" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291" title="Ethernet link state.">ARM_ETH_LINK_STATE</a> link;</div>
<div class="line"> </div>
<div class="line">  link = phy-&gt;<a class="code" href="group__eth__phy__interface__gr.html#a0e25b2f267edc874f1bd785175fcf08a" title="Pointer to ARM_ETH_PHY_GetLinkState : Get Ethernet PHY Device Link state.">GetLinkState</a> ();</div>
<div class="line">  <span class="keywordflow">if</span> (link == ethernet_link) {    </div>
<div class="line">    <span class="keywordflow">return</span>;                                <span class="comment">// link state unchanged</span></div>
<div class="line">  }</div>
<div class="line">                                           <span class="comment">// link state changed</span></div>
<div class="line">  ethernet_link = link;   </div>
<div class="line">  <span class="keywordflow">if</span> (link == <a class="code" href="_driver___e_t_h_8h.html#gacf7db5320eb841b462a4af3c56cc9291ab5e5b02c3c8a5a0fefcf69f3be7e31c1" title="Link is up.">ARM_ETH_LINK_UP</a>) {      <span class="comment">// start transfer</span></div>
<div class="line">    <a class="code" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o" title="Ethernet link information.">ARM_ETH_LINK_INFO</a> info = phy-&gt;<a class="code" href="group__eth__phy__interface__gr.html#ac162bfaf93512fa0966bfbb923c45463" title="Pointer to ARM_ETH_PHY_GetLinkInfo : Get Ethernet PHY Device Link information.">GetLinkInfo</a> ();</div>
<div class="line">    mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_ETH_MAC_Control : Control Ethernet Interface.">Control</a>(<a class="code" href="group__eth__mac__ctrls.html#ga7819c7a1aa7bbc13dc42d0fd7e75a23c" title="Configure MAC; arg = configuration.">ARM_ETH_MAC_CONFIGURE</a>,</div>
<div class="line">                 info.<a class="code" href="group__eth__interface__gr.html#a220859a8b5da0232739a11cbe7f79fc5" title="Link speed: 0= 10 MBit, 1= 100 MBit, 2= 1 GBit.">speed</a>  &lt;&lt; <a class="code" href="_driver___e_t_h___m_a_c_8h.html#ad7fd5c5f4d4f39a56466c2d34cb699ef">ARM_ETH_MAC_SPEED_Pos</a>  |</div>
<div class="line">                 info.<a class="code" href="group__eth__interface__gr.html#a44b6cae894d7311dcdae7e93969c3c09" title="Duplex mode: 0= Half, 1= Full.">duplex</a> &lt;&lt; <a class="code" href="_driver___e_t_h___m_a_c_8h.html#a245688f6265e8d017435dc6d2c233b87">ARM_ETH_MAC_DUPLEX_Pos</a> |</div>
<div class="line">                 <a class="code" href="group__eth__mac__configuration__ctrls.html#ga43792feab641c3c87eafb943351ab0f4" title="Accept frames with Broadcast address.">ARM_ETH_MAC_ADDRESS_BROADCAST</a>);</div>
<div class="line">    mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_ETH_MAC_Control : Control Ethernet Interface.">Control</a>(<a class="code" href="group__eth__mac__ctrls.html#ga3a98c8a7ee5ed4b1ffd250eecaeefe5c" title="Transmitter; arg: 0=disabled, 1=enabled.">ARM_ETH_MAC_CONTROL_TX</a>, 1);</div>
<div class="line">    mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_ETH_MAC_Control : Control Ethernet Interface.">Control</a>(<a class="code" href="group__eth__mac__ctrls.html#gae0964364b81b38b6e1fbf7196f3be869" title="Receiver; arg: 0=disabled, 1=enabled.">ARM_ETH_MAC_CONTROL_RX</a>, 1);</div>
<div class="line">  }</div>
<div class="line">  <span class="keywordflow">else</span> {                                   <span class="comment">// stop transfer</span></div>
<div class="line">    mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_ETH_MAC_Control : Control Ethernet Interface.">Control</a>(<a class="code" href="group__eth__mac__ctrls.html#ga530812ef349a2e297f23de72e660fe27" title="Flush buffer; arg = ARM_ETH_MAC_FLUSH_...">ARM_ETH_MAC_FLUSH</a>, <a class="code" href="group__eth__mac__flush__flag__ctrls.html#ga2d10ff33f4f4927820c6a17a2262b120" title="Flush Transmit buffer.">ARM_ETH_MAC_FLUSH_TX</a> | <a class="code" href="group__eth__mac__flush__flag__ctrls.html#gac18950811038319960756f063e1ef6d4" title="Flush Receive buffer.">ARM_ETH_MAC_FLUSH_RX</a>);</div>
<div class="line">    mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_ETH_MAC_Control : Control Ethernet Interface.">Control</a>(<a class="code" href="group__eth__mac__ctrls.html#ga3a98c8a7ee5ed4b1ffd250eecaeefe5c" title="Transmitter; arg: 0=disabled, 1=enabled.">ARM_ETH_MAC_CONTROL_TX</a>, 0);</div>
<div class="line">    mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a6e0f47a92f626a971c5197fca6545505" title="Pointer to ARM_ETH_MAC_Control : Control Ethernet Interface.">Control</a>(<a class="code" href="group__eth__mac__ctrls.html#gae0964364b81b38b6e1fbf7196f3be869" title="Receiver; arg: 0=disabled, 1=enabled.">ARM_ETH_MAC_CONTROL_RX</a>, 0);</div>
<div class="line">  }</div>
<div class="line">}</div>
</div><!-- fragment --> <hr/><h2 class="groupheader">Data Structure Documentation</h2>
<a name="struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o" id="struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_ETH_LINK_INFO</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Ethernet link information. </p>
<p>The Ethernet Link information provides parameters about the current established communication.</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__eth__phy__interface__gr.html#ga8c79dcd7a12656403f3befab3c8605a2">ARM_ETH_PHY_GetLinkInfo</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a44b6cae894d7311dcdae7e93969c3c09"></a>uint32_t</td>
<td class="fieldname">
duplex: 1</td>
<td class="fielddoc">
Duplex mode: 0= Half, 1= Full. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a220859a8b5da0232739a11cbe7f79fc5"></a>uint32_t</td>
<td class="fieldname">
speed: 2</td>
<td class="fielddoc">
Link speed: 0= 10 MBit, 1= 100 MBit, 2= 1 GBit. </td></tr>
</table>

</div>
</div>
<a name="struct_a_r_m___e_t_h___m_a_c___a_d_d_r" id="struct_a_r_m___e_t_h___m_a_c___a_d_d_r"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_ETH_MAC_ADDR</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Ethernet MAC Address. </p>
<p>Stores the MAC Address of the Ethernet interface as defined by IEEE 802. Wikipedia offers more information about the <a href="http://en.wikipedia.org/wiki/MAC_address" target="_blank"><b>MAC Address</b></a>.</p>
<p><b>Parameter for:</b></p>
<ul>
<li><a class="el" href="group__eth__mac__interface__gr.html#ga66308c1e791952047e974bd653037fae">ARM_ETH_MAC_GetMacAddress</a>, <a class="el" href="group__eth__mac__interface__gr.html#ga7cc3d17c7312c5032202dfd9a915f24a">ARM_ETH_MAC_SetMacAddress</a>, <a class="el" href="group__eth__mac__interface__gr.html#ga150fe30290275a4b32756f94208124e8">ARM_ETH_MAC_SetAddressFilter</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ab590318ac859d0e57e15c3dd6c62a605"></a>uint8_t</td>
<td class="fieldname">
b[6]</td>
<td class="fielddoc">
MAC Address (6 bytes), MSB first. </td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a class="anchor" id="gacf7db5320eb841b462a4af3c56cc9291"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291">ARM_ETH_LINK_STATE</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Ethernet link state. </p>
<p>The Ethernet Link status shows if the communication is currently established (up) or interrupted (down).</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__eth__phy__interface__gr.html#ga4085cd24ebe33b78d51a3c003da4a5ba">ARM_ETH_PHY_GetLinkState</a> </li>
</ul>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><em><a class="anchor" id="gacf7db5320eb841b462a4af3c56cc9291a5f635c9352db6cb6fa9ad95660850487"></a>ARM_ETH_LINK_DOWN</em>&nbsp;</td><td class="fielddoc">
<p>Link is down. </p>
</td></tr>
<tr><td class="fieldname"><em><a class="anchor" id="gacf7db5320eb841b462a4af3c56cc9291ab5e5b02c3c8a5a0fefcf69f3be7e31c1"></a>ARM_ETH_LINK_UP</em>&nbsp;</td><td class="fielddoc">
<p>Link is up. </p>
</td></tr>
</table>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
