<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_convolution_example_f32.c File Reference</title>
<title>CMSIS-DSP: arm_convolution_example_f32.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_g_c_c_2arm__convolution__example__f32_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">GCC/arm_convolution_example_f32.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:af8a1d2ed31f7c9a00fec46a798edb61b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a></td></tr>
<tr class="separator:af8a1d2ed31f7c9a00fec46a798edb61b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fd2b1bcd7ddcf506237987ad780f495"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a3fd2b1bcd7ddcf506237987ad780f495">DELTA</a></td></tr>
<tr class="separator:a3fd2b1bcd7ddcf506237987ad780f495"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af08ec3fef897d77c6817638bf0e0c5c6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#af08ec3fef897d77c6817638bf0e0c5c6">SNR_THRESHOLD</a></td></tr>
<tr class="separator:af08ec3fef897d77c6817638bf0e0c5c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a52d2cba30e6946c95578be946ac12a65"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main</a> (void)</td></tr>
<tr class="separator:a52d2cba30e6946c95578be946ac12a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:aed74eacd4b96cc7f71b64d18f2e95705"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">Ak</a> [<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td></tr>
<tr class="separator:aed74eacd4b96cc7f71b64d18f2e95705"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88a0167516ae7ed66203fd60e6ddeea3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3">Bk</a> [<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td></tr>
<tr class="separator:a88a0167516ae7ed66203fd60e6ddeea3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13521f3164dc55679f43b7cb2e41e098"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098">AxB</a> [<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a> *2]</td></tr>
<tr class="separator:a13521f3164dc55679f43b7cb2e41e098"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7db2f016e1afcb524a2fdc3c5a3cb640"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a7db2f016e1afcb524a2fdc3c5a3cb640">testInputA_f32</a> [64]</td></tr>
<tr class="separator:a7db2f016e1afcb524a2fdc3c5a3cb640"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb22287e7e096b677e352dfd363ba60d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#acb22287e7e096b677e352dfd363ba60d">testInputB_f32</a> [64]</td></tr>
<tr class="separator:acb22287e7e096b677e352dfd363ba60d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ede41b07b8766013744c8fdbb80af75"><td class="memItemLeft" align="right" valign="top">const float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">testRefOutput_f32</a> [127]</td></tr>
<tr class="separator:a7ede41b07b8766013744c8fdbb80af75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace48ed566e2cd6a680f0681192e6af28"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a></td></tr>
<tr class="separator:ace48ed566e2cd6a680f0681192e6af28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aea71286f498978c5ed3775609b974fc8"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a></td></tr>
<tr class="separator:aea71286f498978c5ed3775609b974fc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c49c44c8bc5c432d220d33a26b4b589"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a9c49c44c8bc5c432d220d33a26b4b589">outLen</a></td></tr>
<tr class="separator:a9c49c44c8bc5c432d220d33a26b4b589"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af06013f588a7003278de222913c9d819"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a></td></tr>
<tr class="separator:af06013f588a7003278de222913c9d819"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="a3fd2b1bcd7ddcf506237987ad780f495"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define DELTA</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af8a1d2ed31f7c9a00fec46a798edb61b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MAX_BLOCKSIZE</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="af08ec3fef897d77c6817638bf0e0c5c6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SNR_THRESHOLD</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="a52d2cba30e6946c95578be946ac12a65"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t main </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>References <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">Ak</a>, <a class="el" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26">arm_cfft_radix4_f32()</a>, <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32()</a>, <a class="el" href="group___cmplx_by_cmplx_mult.html#ga14b47080054a1ba1250a86805be1ff6b">arm_cmplx_mult_cmplx_f32()</a>, <a class="el" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3">arm_copy_f32()</a>, <a class="el" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94">arm_fill_f32()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_snr_f32()</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098">AxB</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3">Bk</a>, <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a9c49c44c8bc5c432d220d33a26b4b589">outLen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a>, <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#af08ec3fef897d77c6817638bf0e0c5c6">SNR_THRESHOLD</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7db2f016e1afcb524a2fdc3c5a3cb640">testInputA_f32</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#acb22287e7e096b677e352dfd363ba60d">testInputB_f32</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">testRefOutput_f32</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="aed74eacd4b96cc7f71b64d18f2e95705"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> Ak[<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a13521f3164dc55679f43b7cb2e41e098"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> AxB[<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a> *2]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a88a0167516ae7ed66203fd60e6ddeea3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> Bk[<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9c49c44c8bc5c432d220d33a26b4b589"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t outLen</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af06013f588a7003278de222913c9d819"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> snr</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ace48ed566e2cd6a680f0681192e6af28"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t srcALen</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aea71286f498978c5ed3775609b974fc8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t srcBLen</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7db2f016e1afcb524a2fdc3c5a3cb640"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testInputA_f32[64]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acb22287e7e096b677e352dfd363ba60d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testInputB_f32[64]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7ede41b07b8766013744c8fdbb80af75"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const float testRefOutput_f32[127]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_50f4d4f91ce5cd72cb6928b47e85a7f8.html">Examples</a></li><li class="navelem"><a class="el" href="dir_0bd53153155fe3870c529e4f415d4a7e.html">arm_convolution_example</a></li><li class="navelem"><a class="el" href="dir_9ec4a86676306e0fd116a00bf2906438.html">GCC</a></li><li class="navelem"><a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html">arm_convolution_example_f32.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
