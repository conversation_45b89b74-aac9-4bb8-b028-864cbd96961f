#ifndef ERRORCODE_H_HEADER_INCLUDED_B980E8A1
#define ERRORCODE_H_HEADER_INCLUDED_B980E8A1

#include "Globel.h"


class CErrorCode
{
public:   

    enum tagERR_CODE{
        STATUS_NOERROR             = 0x00, 
        ERR_NOT_SUPPORTED          = 0x01, 
        ERR_INVALID_CHANNEL_ID     = 0x02, 
        ERR_INVALID_PROTOCOL_ID    = 0x03, 
        ERR_NULL_PARAMETER         = 0x04, 
        ERR_INVALID_IOCTL_VALUE    = 0x05, 
        ERR_INVALID_FLAGS          = 0x06, 
        ERR_FAILED                 = 0x07, 
        ERR_DEVICE_NOT_CONNECTED   = 0x08, 
        ERR_TIMEOUT                = 0x09, 
        ERR_INVALID_MSG            = 0x0A, 
        ERR_INVALID_TIME_INTERVAL  = 0x0B, 
        ERR_EXCEEDED_LIMIT         = 0x0C, 
        ERR_INVALID_MSG_ID         = 0x0D, 
        ERR_DEVICE_IN_USE          = 0x0E, 
        ERR_INVALID_IOCTL_ID       = 0x0F, 
        ERR_BUFFER_EMPTY           = 0x10, 
        ERR_BUFFER_FULL            = 0x11, 
        ERR_BUFFER_OVERFLOW        = 0x12, 
        ERR_PIN_INVALID            = 0x13, 
        ERR_CHANNEL_IN_USE         = 0x14, 
        ERR_MSG_PROTOCOL_ID        = 0x15, 
        ERR_INVALID_FILTER_ID      = 0x16, 
        ERR_NO_FLOW_CONTROL        = 0x17, 
        ERR_NOT_UNIQUE             = 0x18, 
        ERR_INVALID_BAUDRATE       = 0x19, 
        ERR_INVALID_DEVICE_ID      = 0x1A,

        ERR_INVALID_IOCTL_PARAM_ID = 0x1E,
        ERR_VOLTAGE_IN_USE           = 0x1F,
        ERR_PIN_IN_USE               = 0x20,

        ERR_FLASH_BUSY    =0X100,
        ERR_FLASH__PG = 0X101,
        ERR_FLASH__WRP = 0x102,
        ERR_FLASH_TIMEOUT = 0x104,

        ERR_ADDRESS_NOT_CLAIMED       = 0x00010000,
        ERR_NO_CONNECTION_ESTABLISHED,
        ERR_RESOURCE_IN_USE
    };

    enum tagEXTRAERR_CODE{
        EXTRA_ERR_DEFAULT            = 0x00,
        EXTRA_ERR_NOT_POLL_MSG       = 0x01,
        EXTRA_ERR_FAST_INI			 = 0x02,
        EXTRA_ERR_FIVE_BAUD_INI	     = 0x03
    };

public:

    bool Unsuccess();


    bool SetLastError(uint32_t ErrorCode, uint32_t ExtErrorCode = EXTRA_ERR_DEFAULT);


    uint32_t GetLastError();

    
    uint32_t GetLastExtraError();

    
    void Clear();

    
    bool IsType(uint32_t ErrorCode);

private:
    // ֵ
    uint32_t m_ErrorCode;
    uint32_t m_ExtraErrorCode;

};

#endif
