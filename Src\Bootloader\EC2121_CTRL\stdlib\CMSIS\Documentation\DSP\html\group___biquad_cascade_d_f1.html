<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Biquad Cascade IIR Filters Using Direct Form I Structure</title>
<title>CMSIS-DSP: Biquad Cascade IIR Filters Using Direct Form I Structure</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___biquad_cascade_d_f1.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Biquad Cascade IIR Filters Using Direct Form I Structure</div>  </div>
<div class="ingroups"><a class="el" href="group__group_filters.html">Filtering Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaa0dbe330d763e3c1d8030b3ef12d5bdc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f1.html#gaa0dbe330d763e3c1d8030b3ef12d5bdc">arm_biquad_cascade_df1_f32</a> (const <a class="el" href="structarm__biquad__casd__df1__inst__f32.html">arm_biquad_casd_df1_inst_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gaa0dbe330d763e3c1d8030b3ef12d5bdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point Biquad cascade filter.  <a href="#gaa0dbe330d763e3c1d8030b3ef12d5bdc"></a><br/></td></tr>
<tr class="separator:gaa0dbe330d763e3c1d8030b3ef12d5bdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaffb9792c0220882efd4c58f3c6a05fd7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f1.html#gaffb9792c0220882efd4c58f3c6a05fd7">arm_biquad_cascade_df1_fast_q15</a> (const <a class="el" href="structarm__biquad__casd__df1__inst__q15.html">arm_biquad_casd_df1_inst_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gaffb9792c0220882efd4c58f3c6a05fd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fast but less precise processing function for the Q15 Biquad cascade filter for Cortex-M3 and Cortex-M4.  <a href="#gaffb9792c0220882efd4c58f3c6a05fd7"></a><br/></td></tr>
<tr class="separator:gaffb9792c0220882efd4c58f3c6a05fd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga456390f5e448afad3a38bed7d6e380e3"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3">arm_biquad_cascade_df1_fast_q31</a> (const <a class="el" href="structarm__biquad__casd__df1__inst__q31.html">arm_biquad_casd_df1_inst_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga456390f5e448afad3a38bed7d6e380e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fast but less precise processing function for the Q31 Biquad cascade filter for Cortex-M3 and Cortex-M4.  <a href="#ga456390f5e448afad3a38bed7d6e380e3"></a><br/></td></tr>
<tr class="separator:ga456390f5e448afad3a38bed7d6e380e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e73b69a788e681a61bccc8959d823c5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f1.html#ga8e73b69a788e681a61bccc8959d823c5">arm_biquad_cascade_df1_init_f32</a> (<a class="el" href="structarm__biquad__casd__df1__inst__f32.html">arm_biquad_casd_df1_inst_f32</a> *S, uint8_t numStages, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pState)</td></tr>
<tr class="memdesc:ga8e73b69a788e681a61bccc8959d823c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point Biquad cascade filter.  <a href="#ga8e73b69a788e681a61bccc8959d823c5"></a><br/></td></tr>
<tr class="separator:ga8e73b69a788e681a61bccc8959d823c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad54c724132f6d742a444eb6df0e9c731"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f1.html#gad54c724132f6d742a444eb6df0e9c731">arm_biquad_cascade_df1_init_q15</a> (<a class="el" href="structarm__biquad__casd__df1__inst__q15.html">arm_biquad_casd_df1_inst_q15</a> *S, uint8_t numStages, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState, int8_t postShift)</td></tr>
<tr class="memdesc:gad54c724132f6d742a444eb6df0e9c731"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 Biquad cascade filter.  <a href="#gad54c724132f6d742a444eb6df0e9c731"></a><br/></td></tr>
<tr class="separator:gad54c724132f6d742a444eb6df0e9c731"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf42a44f9b16d61e636418c83eefe577b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b">arm_biquad_cascade_df1_init_q31</a> (<a class="el" href="structarm__biquad__casd__df1__inst__q31.html">arm_biquad_casd_df1_inst_q31</a> *S, uint8_t numStages, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pState, int8_t postShift)</td></tr>
<tr class="memdesc:gaf42a44f9b16d61e636418c83eefe577b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 Biquad cascade filter.  <a href="#gaf42a44f9b16d61e636418c83eefe577b"></a><br/></td></tr>
<tr class="separator:gaf42a44f9b16d61e636418c83eefe577b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd66a0aefdc645031d607b0a5b37a942"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f1.html#gadd66a0aefdc645031d607b0a5b37a942">arm_biquad_cascade_df1_q15</a> (const <a class="el" href="structarm__biquad__casd__df1__inst__q15.html">arm_biquad_casd_df1_inst_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gadd66a0aefdc645031d607b0a5b37a942"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q15 Biquad cascade filter.  <a href="#gadd66a0aefdc645031d607b0a5b37a942"></a><br/></td></tr>
<tr class="separator:gadd66a0aefdc645031d607b0a5b37a942"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27b0c54da702713976e5202d20b4473f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f">arm_biquad_cascade_df1_q31</a> (const <a class="el" href="structarm__biquad__casd__df1__inst__q31.html">arm_biquad_casd_df1_inst_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga27b0c54da702713976e5202d20b4473f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q31 Biquad cascade filter.  <a href="#ga27b0c54da702713976e5202d20b4473f"></a><br/></td></tr>
<tr class="separator:ga27b0c54da702713976e5202d20b4473f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This set of functions implements arbitrary order recursive (IIR) filters. The filters are implemented as a cascade of second order Biquad sections. The functions support Q15, Q31 and floating-point data types. Fast version of Q15 and Q31 also supported on CortexM4 and Cortex-M3.</p>
<p>The functions operate on blocks of input and output data and each call to the function processes <code>blockSize</code> samples through the filter. <code>pSrc</code> points to the array of input data and <code>pDst</code> points to the array of output data. Both arrays contain <code>blockSize</code> values.</p>
<dl class="section user"><dt>Algorithm </dt><dd>Each Biquad stage implements a second order filter using the difference equation: <pre>    
    y[n] = b0 * x[n] + b1 * x[n-1] + b2 * x[n-2] + a1 * y[n-1] + a2 * y[n-2]    
</pre> A Direct Form I algorithm is used with 5 coefficients and 4 state variables per stage. <div class="image">
<img src="Biquad.gif" alt="Biquad.gif"/>
<div class="caption">
Single Biquad filter stage</div></div>
 Coefficients <code>b0, b1 and b2 </code> multiply the input signal <code>x[n]</code> and are referred to as the feedforward coefficients. Coefficients <code>a1</code> and <code>a2</code> multiply the output signal <code>y[n]</code> and are referred to as the feedback coefficients. Pay careful attention to the sign of the feedback coefficients. Some design tools use the difference equation <pre>    
    y[n] = b0 * x[n] + b1 * x[n-1] + b2 * x[n-2] - a1 * y[n-1] - a2 * y[n-2]    
</pre> In this case the feedback coefficients <code>a1</code> and <code>a2</code> must be negated when used with the CMSIS DSP Library.</dd></dl>
<dl class="section user"><dt></dt><dd>Higher order filters are realized as a cascade of second order sections. <code>numStages</code> refers to the number of second order stages used. For example, an 8th order filter would be realized with <code>numStages=4</code> second order stages. <div class="image">
<img src="BiquadCascade.gif" alt="BiquadCascade.gif"/>
<div class="caption">
8th order filter using a cascade of Biquad stages</div></div>
 A 9th order filter would be realized with <code>numStages=5</code> second order stages with the coefficients for one of the stages configured as a first order filter (<code>b2=0</code> and <code>a2=0</code>).</dd></dl>
<dl class="section user"><dt></dt><dd>The <code>pState</code> points to state variables array. Each Biquad stage has 4 state variables <code>x[n-1], x[n-2], y[n-1],</code> and <code>y[n-2]</code>. The state variables are arranged in the <code>pState</code> array as: <pre>    
    {x[n-1], x[n-2], y[n-1], y[n-2]}    
</pre></dd></dl>
<dl class="section user"><dt></dt><dd>The 4 state variables for stage 1 are first, then the 4 state variables for stage 2, and so on. The state array has a total length of <code>4*numStages</code> values. The state variables are updated after each block of data is processed, the coefficients are untouched.</dd></dl>
<dl class="section user"><dt>Instance Structure </dt><dd>The coefficients and state variables for a filter are stored together in an instance data structure. A separate instance structure must be defined for each filter. Coefficient arrays may be shared among several instances while state variable arrays cannot be shared. There are separate instance structure declarations for each of the 3 supported data types.</dd></dl>
<dl class="section user"><dt>Init Functions </dt><dd>There is also an associated initialization function for each data type. The initialization function performs following operations:<ul>
<li>Sets the values of the internal structure fields.</li>
<li>Zeros out the values in the state buffer. To do this manually without calling the init function, assign the follow subfields of the instance structure: numStages, pCoeffs, pState. Also set all of the values in pState to zero.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Use of the initialization function is optional. However, if the initialization function is used, then the instance structure cannot be placed into a const data section. To place an instance structure into a const data section, the instance structure must be manually initialized. Set the values in the state buffer to zeros before static initialization. The code below statically initializes each of the 3 different data type filter instance structures <pre>    
    <a class="el" href="structarm__biquad__casd__df1__inst__f32.html" title="Instance structure for the floating-point Biquad cascade filter.">arm_biquad_casd_df1_inst_f32</a> S1 = {numStages, pState, pCoeffs};    
    <a class="el" href="structarm__biquad__casd__df1__inst__q15.html" title="Instance structure for the Q15 Biquad cascade filter.">arm_biquad_casd_df1_inst_q15</a> S2 = {numStages, pState, pCoeffs, postShift};    
    <a class="el" href="structarm__biquad__casd__df1__inst__q31.html" title="Instance structure for the Q31 Biquad cascade filter.">arm_biquad_casd_df1_inst_q31</a> S3 = {numStages, pState, pCoeffs, postShift};    
</pre> where <code>numStages</code> is the number of Biquad stages in the filter; <code>pState</code> is the address of the state buffer; <code>pCoeffs</code> is the address of the coefficient buffer; <code>postShift</code> shift to be applied.</dd></dl>
<dl class="section user"><dt>Fixed-Point Behavior </dt><dd>Care must be taken when using the Q15 and Q31 versions of the Biquad Cascade filter functions. Following issues must be considered:<ul>
<li>Scaling of coefficients</li>
<li>Filter gain</li>
<li>Overflow and saturation</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd><b>Scaling of coefficients: </b> Filter coefficients are represented as fractional values and coefficients are restricted to lie in the range <code>[-1 +1)</code>. The fixed-point functions have an additional scaling parameter <code>postShift</code> which allow the filter coefficients to exceed the range <code>[+1 -1)</code>. At the output of the filter's accumulator is a shift register which shifts the result by <code>postShift</code> bits. <div class="image">
<img src="BiquadPostshift.gif" alt="BiquadPostshift.gif"/>
<div class="caption">
Fixed-point Biquad with shift by postShift bits after accumulator</div></div>
 This essentially scales the filter coefficients by <code>2^postShift</code>. For example, to realize the coefficients <pre>    
   {1.5, -0.8, 1.2, 1.6, -0.9}    
</pre> set the pCoeffs array to: <pre>    
   {0.75, -0.4, 0.6, 0.8, -0.45}    
</pre> and set <code>postShift=1</code></dd></dl>
<dl class="section user"><dt></dt><dd><b>Filter gain: </b> The frequency response of a Biquad filter is a function of its coefficients. It is possible for the gain through the filter to exceed 1.0 meaning that the filter increases the amplitude of certain frequencies. This means that an input signal with amplitude &lt; 1.0 may result in an output &gt; 1.0 and these are saturated or overflowed based on the implementation of the filter. To avoid this behavior the filter needs to be scaled down such that its peak gain &lt; 1.0 or the input signal must be scaled down so that the combination of input and filter are never overflowed.</dd></dl>
<dl class="section user"><dt></dt><dd><b>Overflow and saturation: </b> For Q15 and Q31 versions, it is described separately as part of the function specific documentation below. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaa0dbe330d763e3c1d8030b3ef12d5bdc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df1_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__biquad__casd__df1__inst__f32.html">arm_biquad_casd_df1_inst_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point Biquad cascade structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#af69820c37a87252c46453e4cfe120585">arm_biquad_casd_df1_inst_f32::numStages</a>, <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#af9df3820576fb921809d1462c9c6d16c">arm_biquad_casd_df1_inst_f32::pCoeffs</a>, and <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#a8c245d79e0d8cfabc82409d4b54fb682">arm_biquad_casd_df1_inst_f32::pState</a>.</p>

</div>
</div>
<a class="anchor" id="gaffb9792c0220882efd4c58f3c6a05fd7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df1_fast_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__biquad__casd__df1__inst__q15.html">arm_biquad_casd_df1_inst_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 Biquad cascade structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>This fast version uses a 32-bit accumulator with 2.30 format. The accumulator maintains full precision of the intermediate multiplication results but provides only a single guard bit. Thus, if the accumulator result overflows it wraps around and distorts the result. In order to avoid overflows completely the input signal must be scaled down by two bits and lie in the range [-0.25 +0.25). The 2.30 accumulator is then shifted by <code>postShift</code> bits and the result truncated to 1.15 format by discarding the low 16 bits.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer to the function <code><a class="el" href="group___biquad_cascade_d_f1.html#gadd66a0aefdc645031d607b0a5b37a942" title="Processing function for the Q15 Biquad cascade filter.">arm_biquad_cascade_df1_q15()</a></code> for a slower implementation of this function which uses 64-bit accumulation to avoid wrap around distortion. Both the slow and the fast versions use the same instance structure. Use the function <code><a class="el" href="group___biquad_cascade_d_f1.html#gad54c724132f6d742a444eb6df0e9c731" title="Initialization function for the Q15 Biquad cascade filter.">arm_biquad_cascade_df1_init_q15()</a></code> to initialize the filter structure. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#ad6d95e70abcf4ff1300181415ad92153">arm_biquad_casd_df1_inst_q15::numStages</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#a1edaacdebb5b09d7635bf20c779855fc">arm_biquad_casd_df1_inst_q15::pCoeffs</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#ada7e9d6269e6ed4eacf8f68729e9832d">arm_biquad_casd_df1_inst_q15::postShift</a>, and <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#a5481104ef2f8f81360b80b47d69ae932">arm_biquad_casd_df1_inst_q15::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga456390f5e448afad3a38bed7d6e380e3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df1_fast_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__biquad__casd__df1__inst__q31.html">arm_biquad_casd_df1_inst_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 Biquad cascade structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>This function is optimized for speed at the expense of fixed-point precision and overflow protection. The result of each 1.31 x 1.31 multiplication is truncated to 2.30 format. These intermediate results are added to a 2.30 accumulator. Finally, the accumulator is saturated and converted to a 1.31 result. The fast version has the same overflow behavior as the standard version and provides less precision since it discards the low 32 bits of each multiplication result. In order to avoid overflows completely the input signal must be scaled down by two bits and lie in the range [-0.25 +0.25). Use the intialization function <a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b" title="Initialization function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_init_q31()</a> to initialize filter structure.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer to the function <code><a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f" title="Processing function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_q31()</a></code> for a slower implementation of this function which uses 64-bit accumulation to provide higher precision. Both the slow and the fast versions use the same instance structure. Use the function <code><a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b" title="Initialization function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_init_q31()</a></code> to initialize the filter structure. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a960f210642058d2b3d5368729a6e8375">mult_32x32_keep32_R</a>, <a class="el" href="arm__math_8h.html#aba3e538352fc7f9d6d15f9a18d469399">multAcc_32x32_keep32_R</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a2c2b579f1df1d8273a5d9d945c27e1b2">arm_biquad_casd_df1_inst_q31::numStages</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#aa62366c632f3b5305086f841f079dbd2">arm_biquad_casd_df1_inst_q31::pCoeffs</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a636c7fbe09ec4bef0bc0a4b4e2151cbe">arm_biquad_casd_df1_inst_q31::postShift</a>, and <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a5dcf4727f58eb4e8e8b392508d8657bb">arm_biquad_casd_df1_inst_q31::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga8e73b69a788e681a61bccc8959d823c5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df1_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__biquad__casd__df1__inst__f32.html">arm_biquad_casd_df1_inst_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the floating-point Biquad cascade structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of 2nd order stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the filter coefficients array. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state array. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Coefficient and State Ordering:</b></p>
<dl class="section user"><dt></dt><dd>The coefficients are stored in the array <code>pCoeffs</code> in the following order: <pre>    
    {b10, b11, b12, a11, a12, b20, b21, b22, a21, a22, ...}    
</pre></dd></dl>
<dl class="section user"><dt></dt><dd>where <code>b1x</code> and <code>a1x</code> are the coefficients for the first stage, <code>b2x</code> and <code>a2x</code> are the coefficients for the second stage, and so on. The <code>pCoeffs</code> array contains a total of <code>5*numStages</code> values.</dd></dl>
<dl class="section user"><dt></dt><dd>The <code>pState</code> is a pointer to state array. Each Biquad stage has 4 state variables <code>x[n-1], x[n-2], y[n-1],</code> and <code>y[n-2]</code>. The state variables are arranged in the <code>pState</code> array as: <pre>    
    {x[n-1], x[n-2], y[n-1], y[n-2]}    
</pre> The 4 state variables for stage 1 are first, then the 4 state variables for stage 2, and so on. The state array has a total length of <code>4*numStages</code> values. The state variables are updated after each block of data is processed; the coefficients are untouched. </dd></dl>

<p>References <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#af69820c37a87252c46453e4cfe120585">arm_biquad_casd_df1_inst_f32::numStages</a>, <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#af9df3820576fb921809d1462c9c6d16c">arm_biquad_casd_df1_inst_f32::pCoeffs</a>, and <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#a8c245d79e0d8cfabc82409d4b54fb682">arm_biquad_casd_df1_inst_f32::pState</a>.</p>

</div>
</div>
<a class="anchor" id="gad54c724132f6d742a444eb6df0e9c731"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df1_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__biquad__casd__df1__inst__q15.html">arm_biquad_casd_df1_inst_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int8_t&#160;</td>
          <td class="paramname"><em>postShift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q15 Biquad cascade structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of 2nd order stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">postShift</td><td>Shift to be applied to the accumulator result. Varies according to the coefficients format </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Coefficient and State Ordering:</b></p>
<dl class="section user"><dt></dt><dd>The coefficients are stored in the array <code>pCoeffs</code> in the following order: <pre>    
    {b10, 0, b11, b12, a11, a12, b20, 0, b21, b22, a21, a22, ...}    
</pre> where <code>b1x</code> and <code>a1x</code> are the coefficients for the first stage, <code>b2x</code> and <code>a2x</code> are the coefficients for the second stage, and so on. The <code>pCoeffs</code> array contains a total of <code>6*numStages</code> values. The zero coefficient between <code>b1</code> and <code>b2</code> facilities use of 16-bit SIMD instructions on the Cortex-M4.</dd></dl>
<dl class="section user"><dt></dt><dd>The state variables are stored in the array <code>pState</code>. Each Biquad stage has 4 state variables <code>x[n-1], x[n-2], y[n-1],</code> and <code>y[n-2]</code>. The state variables are arranged in the <code>pState</code> array as: <pre>    
    {x[n-1], x[n-2], y[n-1], y[n-2]}    
</pre> The 4 state variables for stage 1 are first, then the 4 state variables for stage 2, and so on. The state array has a total length of <code>4*numStages</code> values. The state variables are updated after each block of data is processed; the coefficients are untouched. </dd></dl>

<p>References <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#ad6d95e70abcf4ff1300181415ad92153">arm_biquad_casd_df1_inst_q15::numStages</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#a1edaacdebb5b09d7635bf20c779855fc">arm_biquad_casd_df1_inst_q15::pCoeffs</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#ada7e9d6269e6ed4eacf8f68729e9832d">arm_biquad_casd_df1_inst_q15::postShift</a>, and <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#a5481104ef2f8f81360b80b47d69ae932">arm_biquad_casd_df1_inst_q15::pState</a>.</p>

</div>
</div>
<a class="anchor" id="gaf42a44f9b16d61e636418c83eefe577b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df1_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__biquad__casd__df1__inst__q31.html">arm_biquad_casd_df1_inst_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int8_t&#160;</td>
          <td class="paramname"><em>postShift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q31 Biquad cascade structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of 2nd order stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the filter coefficients buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">postShift</td><td>Shift to be applied after the accumulator. Varies according to the coefficients format </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Coefficient and State Ordering:</b></p>
<dl class="section user"><dt></dt><dd>The coefficients are stored in the array <code>pCoeffs</code> in the following order: <pre>    
    {b10, b11, b12, a11, a12, b20, b21, b22, a21, a22, ...}    
</pre> where <code>b1x</code> and <code>a1x</code> are the coefficients for the first stage, <code>b2x</code> and <code>a2x</code> are the coefficients for the second stage, and so on. The <code>pCoeffs</code> array contains a total of <code>5*numStages</code> values.</dd></dl>
<dl class="section user"><dt></dt><dd>The <code>pState</code> points to state variables array. Each Biquad stage has 4 state variables <code>x[n-1], x[n-2], y[n-1],</code> and <code>y[n-2]</code>. The state variables are arranged in the <code>pState</code> array as: <pre>    
    {x[n-1], x[n-2], y[n-1], y[n-2]}    
</pre> The 4 state variables for stage 1 are first, then the 4 state variables for stage 2, and so on. The state array has a total length of <code>4*numStages</code> values. The state variables are updated after each block of data is processed; the coefficients are untouched. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_graphic_equalizer_example_q31_8c-example.html#a21">arm_graphic_equalizer_example_q31.c</a>.</dd>
</dl>
<p>References <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a2c2b579f1df1d8273a5d9d945c27e1b2">arm_biquad_casd_df1_inst_q31::numStages</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#aa62366c632f3b5305086f841f079dbd2">arm_biquad_casd_df1_inst_q31::pCoeffs</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a636c7fbe09ec4bef0bc0a4b4e2151cbe">arm_biquad_casd_df1_inst_q31::postShift</a>, and <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a5dcf4727f58eb4e8e8b392508d8657bb">arm_biquad_casd_df1_inst_q31::pState</a>.</p>

<p>Referenced by <a class="el" href="arm__graphic__equalizer__example__q31_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="gadd66a0aefdc645031d607b0a5b37a942"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df1_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__biquad__casd__df1__inst__q15.html">arm_biquad_casd_df1_inst_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 Biquad cascade structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 64-bit internal accumulator. Both coefficients and state variables are represented in 1.15 format and multiplications yield a 2.30 result. The 2.30 intermediate results are accumulated in a 64-bit accumulator in 34.30 format. There is no risk of internal overflow with this approach and the full precision of intermediate multiplications is preserved. The accumulator is then shifted by <code>postShift</code> bits to truncate the result to 1.15 format by discarding the low 16 bits. Finally, the result is saturated to 1.15 format.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer to the function <code><a class="el" href="group___biquad_cascade_d_f1.html#gaffb9792c0220882efd4c58f3c6a05fd7" title="Fast but less precise processing function for the Q15 Biquad cascade filter for Cortex-M3 and Cortex-...">arm_biquad_cascade_df1_fast_q15()</a></code> for a faster but less precise implementation of this filter for Cortex-M3 and Cortex-M4. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#ad6d95e70abcf4ff1300181415ad92153">arm_biquad_casd_df1_inst_q15::numStages</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#a1edaacdebb5b09d7635bf20c779855fc">arm_biquad_casd_df1_inst_q15::pCoeffs</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#ada7e9d6269e6ed4eacf8f68729e9832d">arm_biquad_casd_df1_inst_q15::postShift</a>, and <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#a5481104ef2f8f81360b80b47d69ae932">arm_biquad_casd_df1_inst_q15::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga27b0c54da702713976e5202d20b4473f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_biquad_cascade_df1_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__biquad__casd__df1__inst__q31.html">arm_biquad_casd_df1_inst_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 Biquad cascade structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using an internal 64-bit accumulator. The accumulator has a 2.62 format and maintains full precision of the intermediate multiplication results but provides only a single guard bit. Thus, if the accumulator result overflows it wraps around rather than clip. In order to avoid overflows completely the input signal must be scaled down by 2 bits and lie in the range [-0.25 +0.25). After all 5 multiply-accumulates are performed, the 2.62 accumulator is shifted by <code>postShift</code> bits and the result truncated to 1.31 format by discarding the low 32 bits.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer to the function <code><a class="el" href="group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3" title="Fast but less precise processing function for the Q31 Biquad cascade filter for Cortex-M3 and Cortex-...">arm_biquad_cascade_df1_fast_q31()</a></code> for a faster but less precise implementation of this filter for Cortex-M3 and Cortex-M4. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_graphic_equalizer_example_q31_8c-example.html#a26">arm_graphic_equalizer_example_q31.c</a>.</dd>
</dl>
<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a2c2b579f1df1d8273a5d9d945c27e1b2">arm_biquad_casd_df1_inst_q31::numStages</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#aa62366c632f3b5305086f841f079dbd2">arm_biquad_casd_df1_inst_q31::pCoeffs</a>, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a636c7fbe09ec4bef0bc0a4b4e2151cbe">arm_biquad_casd_df1_inst_q31::postShift</a>, and <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a5dcf4727f58eb4e8e8b392508d8657bb">arm_biquad_casd_df1_inst_q31::pState</a>.</p>

<p>Referenced by <a class="el" href="arm__graphic__equalizer__example__q31_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
