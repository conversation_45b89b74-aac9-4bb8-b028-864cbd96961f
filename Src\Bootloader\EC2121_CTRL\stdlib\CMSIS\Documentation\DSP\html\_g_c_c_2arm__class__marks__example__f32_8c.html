<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_class_marks_example_f32.c File Reference</title>
<title>CMSIS-DSP: arm_class_marks_example_f32.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_g_c_c_2arm__class__marks__example__f32_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">GCC/arm_class_marks_example_f32.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a821d6c5973940580f5a045e7cf64b7f2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a821d6c5973940580f5a045e7cf64b7f2">USE_STATIC_INIT</a></td></tr>
<tr class="separator:a821d6c5973940580f5a045e7cf64b7f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc004a7fade488e72310fd96c0a101dc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a></td></tr>
<tr class="separator:abc004a7fade488e72310fd96c0a101dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d89ac0707e7c9363544986d47a70bd3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a9d89ac0707e7c9363544986d47a70bd3">NUMSTUDENTS</a></td></tr>
<tr class="separator:a9d89ac0707e7c9363544986d47a70bd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b02f9b34bf2cd4d12633f5bf30771ec"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a7b02f9b34bf2cd4d12633f5bf30771ec">NUMSUBJECTS</a></td></tr>
<tr class="separator:a7b02f9b34bf2cd4d12633f5bf30771ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a196718f834091385d38586a0ce4009dc"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a> ()</td></tr>
<tr class="separator:a196718f834091385d38586a0ce4009dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a0153222efa82b7f1a0ea3835921bf921"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a0153222efa82b7f1a0ea3835921bf921">testMarks_f32</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td></tr>
<tr class="separator:a0153222efa82b7f1a0ea3835921bf921"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a993b9b2a1faf43b319c1c6d58b26e7a1"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a993b9b2a1faf43b319c1c6d58b26e7a1">testUnity_f32</a> [4]</td></tr>
<tr class="separator:a993b9b2a1faf43b319c1c6d58b26e7a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd4d61aad5f35a4e42d580004e2f9a1d"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td></tr>
<tr class="separator:afd4d61aad5f35a4e42d580004e2f9a1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3b66d06b4af1af4ea2740d0ccf4e7bd"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a></td></tr>
<tr class="separator:ab3b66d06b4af1af4ea2740d0ccf4e7bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d01884f63bcb694226ca7c24980757a"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a3d01884f63bcb694226ca7c24980757a">numSubjects</a></td></tr>
<tr class="separator:a3d01884f63bcb694226ca7c24980757a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad32888fa966b3d9db9c31bcbba9d9ef"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#aad32888fa966b3d9db9c31bcbba9d9ef">max_marks</a></td></tr>
<tr class="separator:aad32888fa966b3d9db9c31bcbba9d9ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb7687fa07ec54d8e792cfcbfe2ca809"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#abb7687fa07ec54d8e792cfcbfe2ca809">min_marks</a></td></tr>
<tr class="separator:abb7687fa07ec54d8e792cfcbfe2ca809"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc9290716b3c97381ce52d14b4b01681"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681">mean</a></td></tr>
<tr class="separator:acc9290716b3c97381ce52d14b4b01681"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a150b0cf729b51893379f5b5548d4f989"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a150b0cf729b51893379f5b5548d4f989">std</a></td></tr>
<tr class="separator:a150b0cf729b51893379f5b5548d4f989"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3bd39c4335d84be071cc1eaa9b0a8642"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a3bd39c4335d84be071cc1eaa9b0a8642">var</a></td></tr>
<tr class="separator:a3bd39c4335d84be071cc1eaa9b0a8642"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a798cf43a3725d7df2fcaf3f328969f53"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a798cf43a3725d7df2fcaf3f328969f53">student_num</a></td></tr>
<tr class="separator:a798cf43a3725d7df2fcaf3f328969f53"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="a9d89ac0707e7c9363544986d47a70bd3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NUMSTUDENTS</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a7b02f9b34bf2cd4d12633f5bf30771ec"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NUMSUBJECTS</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main()</a>.</p>

</div>
</div>
<a class="anchor" id="abc004a7fade488e72310fd96c0a101dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define TEST_LENGTH_SAMPLES</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a821d6c5973940580f5a045e7cf64b7f2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define USE_STATIC_INIT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="a196718f834091385d38586a0ce4009dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t main </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>References <a class="el" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27">arm_mat_init_f32()</a>, <a class="el" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0">arm_mat_mult_f32()</a>, <a class="el" href="group___max.html#ga5b89d1b04575aeec494f678695fb87d8">arm_max_f32()</a>, <a class="el" href="group__mean.html#ga74ce08c49ab61e57bd50c3a0ca1fdb2b">arm_mean_f32()</a>, <a class="el" href="group___min.html#gaf62b1673740fc516ea64daf777b7d74a">arm_min_f32()</a>, <a class="el" href="group___s_t_d.html#ga4969b5b5f3d001377bc401a3ee99dfc2">arm_std_f32()</a>, <a class="el" href="group__variance.html#ga393f26c5a3bfa05624fb8d32232a6d96">arm_var_f32()</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#aad32888fa966b3d9db9c31bcbba9d9ef">max_marks</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681">mean</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abb7687fa07ec54d8e792cfcbfe2ca809">min_marks</a>, <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a9d89ac0707e7c9363544986d47a70bd3">NUMSTUDENTS</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a>, <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a7b02f9b34bf2cd4d12633f5bf30771ec">NUMSUBJECTS</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a3d01884f63bcb694226ca7c24980757a">numSubjects</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a150b0cf729b51893379f5b5548d4f989">std</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a798cf43a3725d7df2fcaf3f328969f53">student_num</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a0153222efa82b7f1a0ea3835921bf921">testMarks_f32</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a993b9b2a1faf43b319c1c6d58b26e7a1">testUnity_f32</a>, and <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a3bd39c4335d84be071cc1eaa9b0a8642">var</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="aad32888fa966b3d9db9c31bcbba9d9ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> max_marks</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acc9290716b3c97381ce52d14b4b01681"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> mean</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abb7687fa07ec54d8e792cfcbfe2ca809"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> min_marks</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab3b66d06b4af1af4ea2740d0ccf4e7bd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t numStudents</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3d01884f63bcb694226ca7c24980757a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t numSubjects</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a150b0cf729b51893379f5b5548d4f989"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> std</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a798cf43a3725d7df2fcaf3f328969f53"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t student_num</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0153222efa82b7f1a0ea3835921bf921"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testMarks_f32[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afd4d61aad5f35a4e42d580004e2f9a1d"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testOutput[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a993b9b2a1faf43b319c1c6d58b26e7a1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testUnity_f32[4]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3bd39c4335d84be071cc1eaa9b0a8642"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> var</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_50f4d4f91ce5cd72cb6928b47e85a7f8.html">Examples</a></li><li class="navelem"><a class="el" href="dir_56cec670f0bb78d679862f48f54d3df2.html">arm_class_marks_example</a></li><li class="navelem"><a class="el" href="dir_51a4a1a9da33f8f5fceef63d25a48bab.html">GCC</a></li><li class="navelem"><a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html">arm_class_marks_example_f32.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
