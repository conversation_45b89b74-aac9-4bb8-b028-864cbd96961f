
#include "ErrorCode.h"


bool CErrorCode::SetLastError(uint32_t ErrorCode, uint32_t ExtraErrorCode)
{
    m_ErrorCode = ErrorCode;
    
    m_ExtraErrorCode = ExtraErrorCode;
    
    return false;
}

uint32_t CErrorCode::GetLastError()
{
    return m_ErrorCode;
}

uint32_t CErrorCode::GetLastExtraError()
{
    return m_ExtraErrorCode;
}

void CErrorCode::Clear()
{
    m_ErrorCode = STATUS_NOERROR;
    
    m_ExtraErrorCode = EXTRA_ERR_DEFAULT;
}

bool CErrorCode::IsType(uint32_t ErrorCode)
{
    return (ErrorCode == m_ErrorCode);
}

bool CErrorCode::Unsuccess()
{
    return (m_ErrorCode != STATUS_NOERROR);
}
