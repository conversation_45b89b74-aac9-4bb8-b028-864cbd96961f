<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>math_helper.h File Reference</title>
<title>CMSIS-DSP: math_helper.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__fir__example_2_a_r_m_2math__helper_8h.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">arm_fir_example/ARM/math_helper.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:aeea2952e70a1040a6efa555564bbeeab"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab">arm_snr_f32</a> (float *pRef, float *pTest, uint32_t buffSize)</td></tr>
<tr class="memdesc:aeea2952e70a1040a6efa555564bbeeab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Caluclation of SNR.  <a href="#aeea2952e70a1040a6efa555564bbeeab"></a><br/></td></tr>
<tr class="separator:aeea2952e70a1040a6efa555564bbeeab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23f94b0fbfed6d620f38e26bc64cf2f8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_float_to_q12_20</a> (float *pIn, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pOut, uint32_t numSamples)</td></tr>
<tr class="memdesc:a23f94b0fbfed6d620f38e26bc64cf2f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts float to fixed in q12.20 format.  <a href="#a23f94b0fbfed6d620f38e26bc64cf2f8"></a><br/></td></tr>
<tr class="separator:a23f94b0fbfed6d620f38e26bc64cf2f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8209be1b8081e833c3ec2e85ad2255b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_provide_guard_bits_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *input_buf, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, uint32_t guard_bits)</td></tr>
<tr class="memdesc:ac8209be1b8081e833c3ec2e85ad2255b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provide guard bits for Input buffer.  <a href="#ac8209be1b8081e833c3ec2e85ad2255b"></a><br/></td></tr>
<tr class="separator:ac8209be1b8081e833c3ec2e85ad2255b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aead320f821f927000386d9d7d5ad6d27"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_provide_guard_bits_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *input_buf, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, uint32_t guard_bits)</td></tr>
<tr class="memdesc:aead320f821f927000386d9d7d5ad6d27"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provide guard bits for Input buffer.  <a href="#aead320f821f927000386d9d7d5ad6d27"></a><br/></td></tr>
<tr class="separator:aead320f821f927000386d9d7d5ad6d27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23cdb5202efd9233f4e92b5f22287eac"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac">arm_float_to_q14</a> (float *pIn, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pOut, uint32_t numSamples)</td></tr>
<tr class="memdesc:a23cdb5202efd9233f4e92b5f22287eac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts float to fixed q14.  <a href="#a23cdb5202efd9233f4e92b5f22287eac"></a><br/></td></tr>
<tr class="separator:a23cdb5202efd9233f4e92b5f22287eac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a098c587b93469a7a6bcc521d42fdf6f9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_float_to_q29</a> (float *pIn, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pOut, uint32_t numSamples)</td></tr>
<tr class="memdesc:a098c587b93469a7a6bcc521d42fdf6f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts float to fixed q30 format.  <a href="#a098c587b93469a7a6bcc521d42fdf6f9"></a><br/></td></tr>
<tr class="separator:a098c587b93469a7a6bcc521d42fdf6f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa1049b3adb14331612bb762237391625"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625">arm_float_to_q28</a> (float *pIn, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pOut, uint32_t numSamples)</td></tr>
<tr class="memdesc:aa1049b3adb14331612bb762237391625"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts float to fixed q28 format.  <a href="#aa1049b3adb14331612bb762237391625"></a><br/></td></tr>
<tr class="separator:aa1049b3adb14331612bb762237391625"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16764fdbc174a79f04b07032cf902079"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079">arm_float_to_q30</a> (float *pIn, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pOut, uint32_t numSamples)</td></tr>
<tr class="memdesc:a16764fdbc174a79f04b07032cf902079"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts float to fixed q30 format.  <a href="#a16764fdbc174a79f04b07032cf902079"></a><br/></td></tr>
<tr class="separator:a16764fdbc174a79f04b07032cf902079"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab9768d92bb94894d8294047bdf76a16a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a">arm_clip_f32</a> (float *pIn, uint32_t numSamples)</td></tr>
<tr class="memdesc:ab9768d92bb94894d8294047bdf76a16a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clip the float values to +/- 1.  <a href="#ab9768d92bb94894d8294047bdf76a16a"></a><br/></td></tr>
<tr class="separator:ab9768d92bb94894d8294047bdf76a16a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60ff6e0b31a5e9105c7280797e457742"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742">arm_calc_guard_bits</a> (uint32_t num_adds)</td></tr>
<tr class="memdesc:a60ff6e0b31a5e9105c7280797e457742"><td class="mdescLeft">&#160;</td><td class="mdescRight">Caluclates number of guard bits.  <a href="#a60ff6e0b31a5e9105c7280797e457742"></a><br/></td></tr>
<tr class="separator:a60ff6e0b31a5e9105c7280797e457742"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13580a6ff7a8a68146de727bdf8fba88"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_apply_guard_bits</a> (<a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pIn, uint32_t numSamples, uint32_t guard_bits)</td></tr>
<tr class="memdesc:a13580a6ff7a8a68146de727bdf8fba88"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts Q15 to floating-point.  <a href="#a13580a6ff7a8a68146de727bdf8fba88"></a><br/></td></tr>
<tr class="separator:a13580a6ff7a8a68146de727bdf8fba88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a64d5207c035db13cddde479317dd131e"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e">arm_compare_fixed_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pIn, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pOut, uint32_t numSamples)</td></tr>
<tr class="memdesc:a64d5207c035db13cddde479317dd131e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compare MATLAB Reference Output and ARM Test output.  <a href="#a64d5207c035db13cddde479317dd131e"></a><br/></td></tr>
<tr class="separator:a64d5207c035db13cddde479317dd131e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32f9f3d19e53161382c5bd39e3df50fb"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_compare_fixed_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pIn, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pOut, uint32_t numSamples)</td></tr>
<tr class="memdesc:a32f9f3d19e53161382c5bd39e3df50fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compare MATLAB Reference Output and ARM Test output.  <a href="#a32f9f3d19e53161382c5bd39e3df50fb"></a><br/></td></tr>
<tr class="separator:a32f9f3d19e53161382c5bd39e3df50fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c94faac575a175e824d5f9879c97c68"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68">arm_calc_2pow</a> (uint32_t guard_bits)</td></tr>
<tr class="memdesc:a7c94faac575a175e824d5f9879c97c68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculates pow(2, numShifts)  <a href="#a7c94faac575a175e824d5f9879c97c68"></a><br/></td></tr>
<tr class="separator:a7c94faac575a175e824d5f9879c97c68"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="a13580a6ff7a8a68146de727bdf8fba88"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_apply_guard_bits </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>guard_bits</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uint32_t</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none </dd></dl>

</div>
</div>
<a class="anchor" id="a7c94faac575a175e824d5f9879c97c68"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t arm_calc_2pow </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numShifts</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uint32_t</td><td>number of shifts </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>pow(2, numShifts) </dd></dl>

</div>
</div>
<a class="anchor" id="a60ff6e0b31a5e9105c7280797e457742"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t arm_calc_guard_bits </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>num_adds</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uint32_t</td><td>number of additions </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function Caluclates the number of guard bits depending on the numtaps </dd></dl>

</div>
</div>
<a class="anchor" id="ab9768d92bb94894d8294047bdf76a16a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_clip_f32 </td>
          <td>(</td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pIn</td><td>input buffer </td></tr>
    <tr><td class="paramname">numSamples</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function converts floating point values to fixed point values </dd></dl>

</div>
</div>
<a class="anchor" id="a64d5207c035db13cddde479317dd131e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t arm_compare_fixed_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">q15_t*</td><td>Pointer to Ref buffer </td></tr>
    <tr><td class="paramname">q15_t*</td><td>Pointer to Test buffer </td></tr>
    <tr><td class="paramname">uint32_t</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none </dd></dl>

</div>
</div>
<a class="anchor" id="a32f9f3d19e53161382c5bd39e3df50fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t arm_compare_fixed_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">q31_t*</td><td>Pointer to Ref buffer </td></tr>
    <tr><td class="paramname">q31_t*</td><td>Pointer to Test buffer </td></tr>
    <tr><td class="paramname">uint32_t</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none </dd></dl>

</div>
</div>
<a class="anchor" id="a23f94b0fbfed6d620f38e26bc64cf2f8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_float_to_q12_20 </td>
          <td>(</td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uint32_t</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function converts floating point values to fixed point(q12.20) values </dd></dl>

</div>
</div>
<a class="anchor" id="a23cdb5202efd9233f4e92b5f22287eac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_float_to_q14 </td>
          <td>(</td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uint32_t</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function converts floating point values to fixed point values </dd></dl>

</div>
</div>
<a class="anchor" id="aa1049b3adb14331612bb762237391625"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_float_to_q28 </td>
          <td>(</td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uint32_t</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function converts floating point values to fixed point values </dd></dl>

</div>
</div>
<a class="anchor" id="a098c587b93469a7a6bcc521d42fdf6f9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_float_to_q29 </td>
          <td>(</td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uint32_t</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function converts floating point values to fixed point values </dd></dl>

</div>
</div>
<a class="anchor" id="a16764fdbc174a79f04b07032cf902079"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_float_to_q30 </td>
          <td>(</td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>pIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>numSamples</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uint32_t</td><td>number of samples in the buffer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function converts floating point values to fixed point values </dd></dl>

</div>
</div>
<a class="anchor" id="ac8209be1b8081e833c3ec2e85ad2255b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_provide_guard_bits_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>input_buf</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>guard_bits</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">q15_t*</td><td>Pointer to input buffer </td></tr>
    <tr><td class="paramname">uint32_t</td><td>blockSize </td></tr>
    <tr><td class="paramname">uint32_t</td><td>guard_bits </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function Provides the guard bits for the buffer to avoid overflow </dd></dl>

</div>
</div>
<a class="anchor" id="aead320f821f927000386d9d7d5ad6d27"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_provide_guard_bits_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>input_buf</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>guard_bits</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">q31_t*</td><td>Pointer to input buffer </td></tr>
    <tr><td class="paramname">uint32_t</td><td>blockSize </td></tr>
    <tr><td class="paramname">uint32_t</td><td>guard_bits </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none The function Provides the guard bits for the buffer to avoid overflow </dd></dl>

</div>
</div>
<a class="anchor" id="aeea2952e70a1040a6efa555564bbeeab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float arm_snr_f32 </td>
          <td>(</td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>pRef</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>pTest</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>buffSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">float*</td><td>Pointer to the reference buffer </td></tr>
    <tr><td class="paramname">float*</td><td>Pointer to the test buffer </td></tr>
    <tr><td class="paramname">uint32_t</td><td>total number of samples </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>float SNR The function Caluclates signal to noise ratio for the reference output and test output </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_50f4d4f91ce5cd72cb6928b47e85a7f8.html">Examples</a></li><li class="navelem"><a class="el" href="dir_dcc7392e27ceedcb8fca5c4cd07c4b5c.html">arm_fir_example</a></li><li class="navelem"><a class="el" href="dir_68b896fe322128a858fd31091509f536.html">ARM</a></li><li class="navelem"><a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html">math_helper.h</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
