<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Ethernet PHY Interface</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__eth__phy__interface__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Content</a> &#124;
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Ethernet PHY Interface<div class="ingroups"><a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Driver API for Ethernet PHY Peripheral (Driver_ETH_PHY.h)  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Content</h2></td></tr>
<tr class="memitem:group__eth__phy__mode__ctrls"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html">Ethernet PHY Mode</a></td></tr>
<tr class="memdesc:group__eth__phy__mode__ctrls"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specify operation modes of the Ethernet PHY interface. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y">ARM_DRIVER_ETH_PHY</a></td></tr>
<tr class="memdesc:struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access structure of the Ethernet PHY Driver.  <a href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga987d5dd36f179192721c03df37d93e87"><td class="memItemLeft" align="right" valign="top">typedef int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#ga987d5dd36f179192721c03df37d93e87">ARM_ETH_PHY_Read_t</a> )(uint8_t phy_addr, uint8_t reg_addr, uint16_t *data)</td></tr>
<tr class="memdesc:ga987d5dd36f179192721c03df37d93e87"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">ARM_ETH_MAC_PHY_Read</a> : Read Ethernet PHY Register.  <a href="#ga987d5dd36f179192721c03df37d93e87">More...</a><br/></td></tr>
<tr class="separator:ga987d5dd36f179192721c03df37d93e87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf690fde16281b25f2ffa07f9c4e8e240"><td class="memItemLeft" align="right" valign="top">typedef int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#gaf690fde16281b25f2ffa07f9c4e8e240">ARM_ETH_PHY_Write_t</a> )(uint8_t phy_addr, uint8_t reg_addr, uint16_t data)</td></tr>
<tr class="memdesc:gaf690fde16281b25f2ffa07f9c4e8e240"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">ARM_ETH_MAC_PHY_Write</a> : Write Ethernet PHY Register.  <a href="#gaf690fde16281b25f2ffa07f9c4e8e240">More...</a><br/></td></tr>
<tr class="separator:gaf690fde16281b25f2ffa07f9c4e8e240"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga6850d33d699d9deee4e983a2c99e9734"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#ga6850d33d699d9deee4e983a2c99e9734">ARM_ETH_PHY_GetVersion</a> (void)</td></tr>
<tr class="memdesc:ga6850d33d699d9deee4e983a2c99e9734"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver version.  <a href="#ga6850d33d699d9deee4e983a2c99e9734">More...</a><br/></td></tr>
<tr class="separator:ga6850d33d699d9deee4e983a2c99e9734"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacf2332a7fa2d84694b8e5f0838135589"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a> (<a class="el" href="group__eth__phy__interface__gr.html#ga987d5dd36f179192721c03df37d93e87">ARM_ETH_PHY_Read_t</a> fn_read, <a class="el" href="group__eth__phy__interface__gr.html#gaf690fde16281b25f2ffa07f9c4e8e240">ARM_ETH_PHY_Write_t</a> fn_write)</td></tr>
<tr class="memdesc:gacf2332a7fa2d84694b8e5f0838135589"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize Ethernet PHY Device.  <a href="#gacf2332a7fa2d84694b8e5f0838135589">More...</a><br/></td></tr>
<tr class="separator:gacf2332a7fa2d84694b8e5f0838135589"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga26ea7e1e9825b959284241ebff6eea3f"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#ga26ea7e1e9825b959284241ebff6eea3f">ARM_ETH_PHY_Uninitialize</a> (void)</td></tr>
<tr class="memdesc:ga26ea7e1e9825b959284241ebff6eea3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">De-initialize Ethernet PHY Device.  <a href="#ga26ea7e1e9825b959284241ebff6eea3f">More...</a><br/></td></tr>
<tr class="separator:ga26ea7e1e9825b959284241ebff6eea3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba0f92561754dad8f8f03feb1cf2855e"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#gaba0f92561754dad8f8f03feb1cf2855e">ARM_ETH_PHY_PowerControl</a> (<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:gaba0f92561754dad8f8f03feb1cf2855e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control Ethernet PHY Device Power.  <a href="#gaba0f92561754dad8f8f03feb1cf2855e">More...</a><br/></td></tr>
<tr class="separator:gaba0f92561754dad8f8f03feb1cf2855e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaedd8b5650a1259d572a1f303d3e2c01c"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#gaedd8b5650a1259d572a1f303d3e2c01c">ARM_ETH_PHY_SetInterface</a> (uint32_t interface)</td></tr>
<tr class="memdesc:gaedd8b5650a1259d572a1f303d3e2c01c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set Ethernet Media Interface.  <a href="#gaedd8b5650a1259d572a1f303d3e2c01c">More...</a><br/></td></tr>
<tr class="separator:gaedd8b5650a1259d572a1f303d3e2c01c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9aa688c951f01ed9ca7c88cf51be8a09"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09">ARM_ETH_PHY_SetMode</a> (uint32_t mode)</td></tr>
<tr class="memdesc:ga9aa688c951f01ed9ca7c88cf51be8a09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set Ethernet PHY Device Operation mode.  <a href="#ga9aa688c951f01ed9ca7c88cf51be8a09">More...</a><br/></td></tr>
<tr class="separator:ga9aa688c951f01ed9ca7c88cf51be8a09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4085cd24ebe33b78d51a3c003da4a5ba"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291">ARM_ETH_LINK_STATE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#ga4085cd24ebe33b78d51a3c003da4a5ba">ARM_ETH_PHY_GetLinkState</a> (void)</td></tr>
<tr class="memdesc:ga4085cd24ebe33b78d51a3c003da4a5ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get Ethernet PHY Device Link state.  <a href="#ga4085cd24ebe33b78d51a3c003da4a5ba">More...</a><br/></td></tr>
<tr class="separator:ga4085cd24ebe33b78d51a3c003da4a5ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8c79dcd7a12656403f3befab3c8605a2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o">ARM_ETH_LINK_INFO</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#ga8c79dcd7a12656403f3befab3c8605a2">ARM_ETH_PHY_GetLinkInfo</a> (void)</td></tr>
<tr class="memdesc:ga8c79dcd7a12656403f3befab3c8605a2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get Ethernet PHY Device Link information.  <a href="#ga8c79dcd7a12656403f3befab3c8605a2">More...</a><br/></td></tr>
<tr class="separator:ga8c79dcd7a12656403f3befab3c8605a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Driver API for Ethernet PHY Peripheral (Driver_ETH_PHY.h) </p>
<p>The following section describes the Ethernet PHY Interface as defined in the Driver_ETH_PHY.h header file. </p>
<hr/><h2 class="groupheader">Data Structure Documentation</h2>
<a name="struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y" id="struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_DRIVER_ETH_PHY</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Access structure of the Ethernet PHY Driver. </p>
<p>The functions of the Ethernet PHY are accessed by function pointers exposed by this structure. Refer to <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a> for overview information.</p>
<p>Each instance of an Ethernet PHY provides such an access struct. The instance is identified by a postfix number in the symbol name of the access struct, for example:</p>
<ul>
<li><b>Driver_ETH_PHY0</b> is the name of the access struct of the first instance (no. 0).</li>
<li><b>Driver_ETH_PHY1</b> is the name of the access struct of the second instance (no. 1).</li>
</ul>
<p>A configuration setting in the middleware allows connecting the middleware to a specific driver instance <b>Driver_ETH_PHY<em>n</em></b>. The default is <span class="XML-Token">0</span>, which connects a middleware to the first instance of a driver. </p>
</div><table class="memberdecls">
<tr><td colspan="2"><h3>Data Fields</h3></td></tr>
<tr class="memitem:a8834b281da48583845c044a81566c1b3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#a8834b281da48583845c044a81566c1b3">GetVersion</a> )(void)</td></tr>
<tr class="memdesc:a8834b281da48583845c044a81566c1b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga6850d33d699d9deee4e983a2c99e9734">ARM_ETH_PHY_GetVersion</a> : Get driver version.  <a href="#a8834b281da48583845c044a81566c1b3">More...</a><br/></td></tr>
<tr class="separator:a8834b281da48583845c044a81566c1b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f9e7173bf8fed4d774fa48da53739ba"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#a9f9e7173bf8fed4d774fa48da53739ba">Initialize</a> )(<a class="el" href="group__eth__phy__interface__gr.html#ga987d5dd36f179192721c03df37d93e87">ARM_ETH_PHY_Read_t</a> fn_read, <a class="el" href="group__eth__phy__interface__gr.html#gaf690fde16281b25f2ffa07f9c4e8e240">ARM_ETH_PHY_Write_t</a> fn_write)</td></tr>
<tr class="memdesc:a9f9e7173bf8fed4d774fa48da53739ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a> : Initialize PHY Device.  <a href="#a9f9e7173bf8fed4d774fa48da53739ba">More...</a><br/></td></tr>
<tr class="separator:a9f9e7173bf8fed4d774fa48da53739ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcf20681a1402869ecb5c6447fada17b"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#adcf20681a1402869ecb5c6447fada17b">Uninitialize</a> )(void)</td></tr>
<tr class="memdesc:adcf20681a1402869ecb5c6447fada17b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga26ea7e1e9825b959284241ebff6eea3f">ARM_ETH_PHY_Uninitialize</a> : De-initialize PHY Device.  <a href="#adcf20681a1402869ecb5c6447fada17b">More...</a><br/></td></tr>
<tr class="separator:adcf20681a1402869ecb5c6447fada17b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba8f1c8019af95ffe19c32403e3240ef"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">PowerControl</a> )(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:aba8f1c8019af95ffe19c32403e3240ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__phy__interface__gr.html#gaba0f92561754dad8f8f03feb1cf2855e">ARM_ETH_PHY_PowerControl</a> : Control PHY Device Power.  <a href="#aba8f1c8019af95ffe19c32403e3240ef">More...</a><br/></td></tr>
<tr class="separator:aba8f1c8019af95ffe19c32403e3240ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dfc7cf346c80e7fdb2fe4cea2c61161"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#a7dfc7cf346c80e7fdb2fe4cea2c61161">SetInterface</a> )(uint32_t interface)</td></tr>
<tr class="memdesc:a7dfc7cf346c80e7fdb2fe4cea2c61161"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__phy__interface__gr.html#gaedd8b5650a1259d572a1f303d3e2c01c">ARM_ETH_PHY_SetInterface</a> : Set Ethernet Media Interface.  <a href="#a7dfc7cf346c80e7fdb2fe4cea2c61161">More...</a><br/></td></tr>
<tr class="separator:a7dfc7cf346c80e7fdb2fe4cea2c61161"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6686344f4d6afa0881d1e545c898a3d"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#ae6686344f4d6afa0881d1e545c898a3d">SetMode</a> )(uint32_t mode)</td></tr>
<tr class="memdesc:ae6686344f4d6afa0881d1e545c898a3d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09">ARM_ETH_PHY_SetMode</a> : Set Ethernet PHY Device Operation mode.  <a href="#ae6686344f4d6afa0881d1e545c898a3d">More...</a><br/></td></tr>
<tr class="separator:ae6686344f4d6afa0881d1e545c898a3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e25b2f267edc874f1bd785175fcf08a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291">ARM_ETH_LINK_STATE</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#a0e25b2f267edc874f1bd785175fcf08a">GetLinkState</a> )(void)</td></tr>
<tr class="memdesc:a0e25b2f267edc874f1bd785175fcf08a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga4085cd24ebe33b78d51a3c003da4a5ba">ARM_ETH_PHY_GetLinkState</a> : Get Ethernet PHY Device Link state.  <a href="#a0e25b2f267edc874f1bd785175fcf08a">More...</a><br/></td></tr>
<tr class="separator:a0e25b2f267edc874f1bd785175fcf08a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac162bfaf93512fa0966bfbb923c45463"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o">ARM_ETH_LINK_INFO</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__interface__gr.html#ac162bfaf93512fa0966bfbb923c45463">GetLinkInfo</a> )(void)</td></tr>
<tr class="memdesc:ac162bfaf93512fa0966bfbb923c45463"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga8c79dcd7a12656403f3befab3c8605a2">ARM_ETH_PHY_GetLinkInfo</a> : Get Ethernet PHY Device Link information.  <a href="#ac162bfaf93512fa0966bfbb923c45463">More...</a><br/></td></tr>
<tr class="separator:ac162bfaf93512fa0966bfbb923c45463"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h4 class="groupheader">Field Documentation</h4>
<a class="anchor" id="ac162bfaf93512fa0966bfbb923c45463"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o">ARM_ETH_LINK_INFO</a>(* GetLinkInfo)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga8c79dcd7a12656403f3befab3c8605a2">ARM_ETH_PHY_GetLinkInfo</a> : Get Ethernet PHY Device Link information. </p>

</div>
</div>
<a class="anchor" id="a0e25b2f267edc874f1bd785175fcf08a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291">ARM_ETH_LINK_STATE</a>(* GetLinkState)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga4085cd24ebe33b78d51a3c003da4a5ba">ARM_ETH_PHY_GetLinkState</a> : Get Ethernet PHY Device Link state. </p>

</div>
</div>
<a class="anchor" id="a8834b281da48583845c044a81566c1b3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(* GetVersion)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga6850d33d699d9deee4e983a2c99e9734">ARM_ETH_PHY_GetVersion</a> : Get driver version. </p>

</div>
</div>
<a class="anchor" id="a9f9e7173bf8fed4d774fa48da53739ba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Initialize)(<a class="el" href="group__eth__phy__interface__gr.html#ga987d5dd36f179192721c03df37d93e87">ARM_ETH_PHY_Read_t</a> fn_read, <a class="el" href="group__eth__phy__interface__gr.html#gaf690fde16281b25f2ffa07f9c4e8e240">ARM_ETH_PHY_Write_t</a> fn_write)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a> : Initialize PHY Device. </p>

</div>
</div>
<a class="anchor" id="aba8f1c8019af95ffe19c32403e3240ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* PowerControl)(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__phy__interface__gr.html#gaba0f92561754dad8f8f03feb1cf2855e">ARM_ETH_PHY_PowerControl</a> : Control PHY Device Power. </p>

</div>
</div>
<a class="anchor" id="a7dfc7cf346c80e7fdb2fe4cea2c61161"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* SetInterface)(uint32_t interface)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__phy__interface__gr.html#gaedd8b5650a1259d572a1f303d3e2c01c">ARM_ETH_PHY_SetInterface</a> : Set Ethernet Media Interface. </p>

</div>
</div>
<a class="anchor" id="ae6686344f4d6afa0881d1e545c898a3d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* SetMode)(uint32_t mode)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09">ARM_ETH_PHY_SetMode</a> : Set Ethernet PHY Device Operation mode. </p>

</div>
</div>
<a class="anchor" id="adcf20681a1402869ecb5c6447fada17b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Uninitialize)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__phy__interface__gr.html#ga26ea7e1e9825b959284241ebff6eea3f">ARM_ETH_PHY_Uninitialize</a> : De-initialize PHY Device. </p>

</div>
</div>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="ga987d5dd36f179192721c03df37d93e87"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ARM_ETH_PHY_Read_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">ARM_ETH_MAC_PHY_Read</a> : Read Ethernet PHY Register. </p>
<p>Provides the typedef for the register read function <a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">ARM_ETH_MAC_PHY_Read</a>.</p>
<p><b>Parameter for:</b></p>
<ul>
<li><a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a> </li>
</ul>

</div>
</div>
<a class="anchor" id="gaf690fde16281b25f2ffa07f9c4e8e240"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ARM_ETH_PHY_Write_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">ARM_ETH_MAC_PHY_Write</a> : Write Ethernet PHY Register. </p>
<p>Provides the typedef for the register write function <a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">ARM_ETH_MAC_PHY_Write</a>.</p>
<p><b>Parameter for:</b></p>
<ul>
<li><a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a> </li>
</ul>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga8c79dcd7a12656403f3befab3c8605a2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o">ARM_ETH_LINK_INFO</a> ARM_ETH_PHY_GetLinkInfo </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get Ethernet PHY Device Link information. </p>
<dl class="section return"><dt>Returns</dt><dd>current link parameters <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o">ARM_ETH_LINK_INFO</a></dd></dl>
<p>The function <a class="el" href="group__eth__phy__interface__gr.html#ga8c79dcd7a12656403f3befab3c8605a2">ARM_ETH_PHY_GetLinkInfo</a> retrieves information about the current established communication mode (half/full duplex) and communication speed.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="ga4085cd24ebe33b78d51a3c003da4a5ba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291">ARM_ETH_LINK_STATE</a> ARM_ETH_PHY_GetLinkState </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get Ethernet PHY Device Link state. </p>
<dl class="section return"><dt>Returns</dt><dd>current link status <a class="el" href="group__eth__interface__gr.html#gacf7db5320eb841b462a4af3c56cc9291">ARM_ETH_LINK_STATE</a></dd></dl>
<p>The function <a class="el" href="group__eth__phy__interface__gr.html#ga4085cd24ebe33b78d51a3c003da4a5ba">ARM_ETH_PHY_GetLinkState</a> retrieves the connection status of the physical Ethernet link.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="ga6850d33d699d9deee4e983a2c99e9734"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a> ARM_ETH_PHY_GetVersion </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver version. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></dd></dl>
<p>Returns version information of the driver implementation in <a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></p>
<ul>
<li>API version is the version of the CMSIS-Driver specification used to implement this driver.</li>
<li>Driver version is source code version of the actual driver implementation.</li>
</ul>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y" title="Access structure of the Ethernet PHY Driver.">ARM_DRIVER_ETH_PHY</a> Driver_ETH_PHY0;</div>
<div class="line"><a class="code" href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y" title="Access structure of the Ethernet PHY Driver.">ARM_DRIVER_ETH_PHY</a> *drv_info;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> setup_ethernet_phy (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n" title="Driver Version.">ARM_DRIVER_VERSION</a>  version;</div>
<div class="line"> </div>
<div class="line">  drv_info = &amp;Driver_ETH_PHY0;  </div>
<div class="line">  version = drv_info-&gt;<a class="code" href="group__eth__phy__interface__gr.html#a8834b281da48583845c044a81566c1b3" title="Pointer to ARM_ETH_PHY_GetVersion : Get driver version.">GetVersion</a> ();</div>
<div class="line">  <span class="keywordflow">if</span> (version.<a class="code" href="group__common__drv__gr.html#ad180da20fbde1d3dafc074af87c19540" title="API version.">api</a> &lt; 0x10A)   {      <span class="comment">// requires at minimum API version 1.10 or higher</span></div>
<div class="line">    <span class="comment">// error handling</span></div>
<div class="line">    <span class="keywordflow">return</span>;</div>
<div class="line">  }</div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="gacf2332a7fa2d84694b8e5f0838135589"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_PHY_Initialize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__eth__phy__interface__gr.html#ga987d5dd36f179192721c03df37d93e87">ARM_ETH_PHY_Read_t</a>&#160;</td>
          <td class="paramname"><em>fn_read</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__eth__phy__interface__gr.html#gaf690fde16281b25f2ffa07f9c4e8e240">ARM_ETH_PHY_Write_t</a>&#160;</td>
          <td class="paramname"><em>fn_write</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize Ethernet PHY Device. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">fn_read</td><td>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">ARM_ETH_MAC_PHY_Read</a> </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fn_write</td><td>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">ARM_ETH_MAC_PHY_Write</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a> initializes the Ethernet PHY interface. It is called when the middleware component starts operation.</p>
<p>The <a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a> function performs the following operations:</p>
<ul>
<li>Initializes the resources needed for Ethernet PHY peripheral.</li>
<li>Registers the <a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">ARM_ETH_MAC_PHY_Read</a> register read access function.</li>
<li>Registers the <a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">ARM_ETH_MAC_PHY_Write</a> register write access function.</li>
</ul>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="gaba0f92561754dad8f8f03feb1cf2855e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_PHY_PowerControl </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a>&#160;</td>
          <td class="paramname"><em>state</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control Ethernet PHY Device Power. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">state</td><td>Power state </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Allows you to control the power modes of the Ethernet PHY interface.</p>
<p><em>state</em> can be:</p>
<ul>
<li>ARM_POWER_OFF: Ethernet PHY peripheral is turned off.</li>
<li>ARM_POWER_FULL: Ethernet PHY peripheral is turned on and fully operational.</li>
<li>ARM_POWER_LOW: Ethernet PHY peripheral is in low power mode.</li>
</ul>
<p>If <em>state</em> specifies an unsupported mode, the function returns <a class="el" href="group__execution__status.html#ga2efa59e480d82697795439220e6884e4">ARM_DRIVER_ERROR_UNSUPPORTED</a>.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="gaedd8b5650a1259d572a1f303d3e2c01c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_PHY_SetInterface </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>interface</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set Ethernet Media Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">interface</td><td>Media Interface type </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__phy__interface__gr.html#gaedd8b5650a1259d572a1f303d3e2c01c">ARM_ETH_PHY_SetInterface</a> specifies the Media Independent Interface type (<em>ARM_ETH_INTERFACE_xxx</em>) that is used by the interface between Ethernet MAC and Ethernet PHY. The Media Independent Interface type is obtained by the function <a class="el" href="group__eth__mac__interface__gr.html#ga2b13b230502736d8c7679b359dff20d0">ARM_ETH_MAC_GetCapabilities</a>.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="ga9aa688c951f01ed9ca7c88cf51be8a09"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_PHY_SetMode </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>mode</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set Ethernet PHY Device Operation mode. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">mode</td><td>Operation Mode </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09">ARM_ETH_PHY_SetMode</a> defines the operation mode of the Ethernet PHY.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions</li>
</ul>
<p><b>Mode configuration parameters</b> specify bits for speed modes, link modes, checksum, and frame handling.</p>
<table class="doxtable">
<tr>
<th align="left">Mode Parameters: Mode Bits </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__eth__phy__mode__ctrls.html#gabc7acc4ebe828c3d0825400e14ad20f0">ARM_ETH_PHY_SPEED_10M</a> </td><td align="left">Set the link speed to <span class="XML-Token">10 Mbps</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__phy__mode__ctrls.html#gad1e8b2c8c210fa36949db9a34a993657">ARM_ETH_PHY_SPEED_100M</a> </td><td align="left">Set the link speed to <span class="XML-Token">100 Mbps</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__phy__mode__ctrls.html#ga046605398ceae99a176e6f82432ae710">ARM_ETH_PHY_SPEED_1G</a> </td><td align="left">Set the link speed to <span class="XML-Token">1 Gbps</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__phy__mode__ctrls.html#gace797b3cd143be22f47c3ef61b20e14d">ARM_ETH_PHY_DUPLEX_HALF</a> </td><td align="left">Set the link mode to half duplex link </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__phy__mode__ctrls.html#ga5d06a94867c89cd311b6e279669321e3">ARM_ETH_PHY_DUPLEX_FULL</a> </td><td align="left">Set the link mode to full duplex link </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__phy__mode__ctrls.html#ga6a8c54f8fed3e5f68bd04eb715d10ab9">ARM_ETH_PHY_AUTO_NEGOTIATE</a> </td><td align="left">Set the interface to Auto Negotiation mode </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__phy__mode__ctrls.html#ga5f7e46cda8ab3c774fe7ce0a8a1ba3ec">ARM_ETH_PHY_LOOPBACK</a> </td><td align="left">Set the interface into a Loop-back test mode </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__phy__mode__ctrls.html#ga8d68719e07c7af449b57c5df802376c8">ARM_ETH_PHY_ISOLATE</a> </td><td align="left">Isolate the PHY interface from MII interface </td></tr>
</table>

</div>
</div>
<a class="anchor" id="ga26ea7e1e9825b959284241ebff6eea3f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_PHY_Uninitialize </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>De-initialize Ethernet PHY Device. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__phy__interface__gr.html#ga26ea7e1e9825b959284241ebff6eea3f">ARM_ETH_PHY_Uninitialize</a> de-initializes the resources of Ethernet PHY interface.</p>
<p>It is called when the middleware component stops operation and releases the software resources used by the interface. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
