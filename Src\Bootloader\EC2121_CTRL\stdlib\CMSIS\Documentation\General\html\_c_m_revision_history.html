<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Revision History</title>
<title>CMSIS: Revision History</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS
   &#160;<span id="projectnumber">Version 4.3.0</span>
   </div>
   <div id="projectbrief">Cortex Microcontroller Software Interface Standard</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_c_m_revision_history.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">Revision History </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>The following table shows the overall high-level history of the various CMSIS releases. In addition, each CMSIS component has its own release history:</p>
<ul>
<li><a href="../../Core/html/core_revision_history.html"><b>CORE Revision History</b></a></li>
<li><a href="../../Driver/html/driver_revision_history.html"><b>Driver Revision History</b></a></li>
<li><a href="../../DSP/html/_change_log_pg.html"><b>DSP Revision History (Change Log)</b></a></li>
<li><a href="../../RTOS/html/rtos_revision_history.html"><b>RTOS API Revision History</b></a></li>
<li><a href="../../RTX/html/rtx_revision_history.html"><b>RTX Revision History</b></a></li>
<li><a href="../../Pack/html/pack_revision_history.html"><b>Pack Revision History</b></a></li>
<li><a href="../../SVD/html/svd_revision_history.html"><b>SVD Revision History</b></a></li>
</ul>
<table  class="cmtable" summary="Revision History">
<tr>
<th>Version </th><th>Description  </th></tr>
<tr>
<td>4.3.0 </td><td>Maintenance release adding SAI CMSIS-Driver and fixing defects. See component's revision history for more details.<ul>
<li>CMSIS-CORE 4.10.0</li>
<li>CMSIS-Driver 2.02.0</li>
<li>CMSIS-DSP 1.4.5</li>
<li>CMSIS-RTOS RTX 4.78.0</li>
<li>CMSIS-PACK 1.3.3</li>
<li>CMSIS-SVD (unchanged)   </li>
</ul>
</td></tr>
<tr>
<td>4.2 </td><td>Introducing processor support for Cortex-M7.   </td></tr>
<tr>
<td>4.1 </td><td>Enhancements in CMSIS-Pack and CMSIS-Driver.<br/>
 Added: PackChk validation utility<br/>
 Removed support for GNU: Sourcery G++ Lite Edition for ARM  </td></tr>
<tr>
<td>4.0 </td><td>First release in CMSIS-Pack format.<br/>
 Added specifications for CMSIS-Pack, CMSIS-Driver  </td></tr>
<tr>
<td>3.30 </td><td>Maintenance release with enhancements in each component  </td></tr>
<tr>
<td>3.20 </td><td>Maintenance release with enhancements in each component  </td></tr>
<tr>
<td>3.01 </td><td>Added support for Cortex-M0+ processors  </td></tr>
<tr>
<td>3.00 </td><td>Added support for SC000 and SC300 processors<br/>
 Added support for GNU GCC Compiler<br/>
 Added CMSIS-RTOS API  </td></tr>
<tr>
<td>2.10 </td><td>Added CMSIS-DSP Library  </td></tr>
<tr>
<td>2.0 </td><td>Added support for Cortex-M4 processor  </td></tr>
<tr>
<td>1.30 </td><td>Reworked CMSIS startup concept  </td></tr>
<tr>
<td>1.01 </td><td>Added support for Cortex-M0 processor  </td></tr>
<tr>
<td>1.00 </td><td>Initial release of CMSIS-CORE for Cortex-M3 processor  </td></tr>
</table>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:52 for CMSIS by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
