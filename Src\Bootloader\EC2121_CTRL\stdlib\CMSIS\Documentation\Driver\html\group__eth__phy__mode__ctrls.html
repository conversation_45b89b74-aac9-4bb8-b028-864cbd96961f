<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Ethernet PHY Mode</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__eth__phy__mode__ctrls.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">Ethernet PHY Mode<div class="ingroups"><a class="el" href="group__eth__phy__interface__gr.html">Ethernet PHY Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Specify operation modes of the Ethernet PHY interface.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gabc7acc4ebe828c3d0825400e14ad20f0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html#gabc7acc4ebe828c3d0825400e14ad20f0">ARM_ETH_PHY_SPEED_10M</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a1f834c4c785d7f69b1eaca011ee298ec">ARM_ETH_SPEED_10M</a>   &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a13700fab82aa60a3357614faa0619e97">ARM_ETH_PHY_SPEED_Pos</a>)</td></tr>
<tr class="memdesc:gabc7acc4ebe828c3d0825400e14ad20f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">10 Mbps link speed  <a href="#gabc7acc4ebe828c3d0825400e14ad20f0">More...</a><br/></td></tr>
<tr class="separator:gabc7acc4ebe828c3d0825400e14ad20f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1e8b2c8c210fa36949db9a34a993657"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html#gad1e8b2c8c210fa36949db9a34a993657">ARM_ETH_PHY_SPEED_100M</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a3bddfc4cf5645f8568d9cb6621fd606a">ARM_ETH_SPEED_100M</a>  &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a13700fab82aa60a3357614faa0619e97">ARM_ETH_PHY_SPEED_Pos</a>)</td></tr>
<tr class="memdesc:gad1e8b2c8c210fa36949db9a34a993657"><td class="mdescLeft">&#160;</td><td class="mdescRight">100 Mbps link speed  <a href="#gad1e8b2c8c210fa36949db9a34a993657">More...</a><br/></td></tr>
<tr class="separator:gad1e8b2c8c210fa36949db9a34a993657"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga046605398ceae99a176e6f82432ae710"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html#ga046605398ceae99a176e6f82432ae710">ARM_ETH_PHY_SPEED_1G</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a218f470079b7c04de6776d467a53772a">ARM_ETH_SPEED_1G</a>    &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a13700fab82aa60a3357614faa0619e97">ARM_ETH_PHY_SPEED_Pos</a>)</td></tr>
<tr class="memdesc:ga046605398ceae99a176e6f82432ae710"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 Gpbs link speed  <a href="#ga046605398ceae99a176e6f82432ae710">More...</a><br/></td></tr>
<tr class="separator:ga046605398ceae99a176e6f82432ae710"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace797b3cd143be22f47c3ef61b20e14d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html#gace797b3cd143be22f47c3ef61b20e14d">ARM_ETH_PHY_DUPLEX_HALF</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#acb15afc2bfe61c56049b7279d6eae8fe">ARM_ETH_DUPLEX_HALF</a> &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#aebafadc356d8e58407db156a5dac743f">ARM_ETH_PHY_DUPLEX_Pos</a>)</td></tr>
<tr class="memdesc:gace797b3cd143be22f47c3ef61b20e14d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Half duplex link.  <a href="#gace797b3cd143be22f47c3ef61b20e14d">More...</a><br/></td></tr>
<tr class="separator:gace797b3cd143be22f47c3ef61b20e14d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5d06a94867c89cd311b6e279669321e3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html#ga5d06a94867c89cd311b6e279669321e3">ARM_ETH_PHY_DUPLEX_FULL</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a7848c83cd1fd6b2645c17919c2990354">ARM_ETH_DUPLEX_FULL</a> &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#aebafadc356d8e58407db156a5dac743f">ARM_ETH_PHY_DUPLEX_Pos</a>)</td></tr>
<tr class="memdesc:ga5d06a94867c89cd311b6e279669321e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Full duplex link.  <a href="#ga5d06a94867c89cd311b6e279669321e3">More...</a><br/></td></tr>
<tr class="separator:ga5d06a94867c89cd311b6e279669321e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6a8c54f8fed3e5f68bd04eb715d10ab9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html#ga6a8c54f8fed3e5f68bd04eb715d10ab9">ARM_ETH_PHY_AUTO_NEGOTIATE</a>&#160;&#160;&#160;(1UL &lt;&lt; 3)</td></tr>
<tr class="memdesc:ga6a8c54f8fed3e5f68bd04eb715d10ab9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Auto Negotiation mode.  <a href="#ga6a8c54f8fed3e5f68bd04eb715d10ab9">More...</a><br/></td></tr>
<tr class="separator:ga6a8c54f8fed3e5f68bd04eb715d10ab9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5f7e46cda8ab3c774fe7ce0a8a1ba3ec"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html#ga5f7e46cda8ab3c774fe7ce0a8a1ba3ec">ARM_ETH_PHY_LOOPBACK</a>&#160;&#160;&#160;(1UL &lt;&lt; 4)</td></tr>
<tr class="memdesc:ga5f7e46cda8ab3c774fe7ce0a8a1ba3ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Loop-back test mode.  <a href="#ga5f7e46cda8ab3c774fe7ce0a8a1ba3ec">More...</a><br/></td></tr>
<tr class="separator:ga5f7e46cda8ab3c774fe7ce0a8a1ba3ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d68719e07c7af449b57c5df802376c8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__phy__mode__ctrls.html#ga8d68719e07c7af449b57c5df802376c8">ARM_ETH_PHY_ISOLATE</a>&#160;&#160;&#160;(1UL &lt;&lt; 5)</td></tr>
<tr class="memdesc:ga8d68719e07c7af449b57c5df802376c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Isolate PHY from MII interface.  <a href="#ga8d68719e07c7af449b57c5df802376c8">More...</a><br/></td></tr>
<tr class="separator:ga8d68719e07c7af449b57c5df802376c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Specify operation modes of the Ethernet PHY interface. </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="ga6a8c54f8fed3e5f68bd04eb715d10ab9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_PHY_AUTO_NEGOTIATE&#160;&#160;&#160;(1UL &lt;&lt; 3)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Auto Negotiation mode. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09" title="Set Ethernet PHY Device Operation mode.">ARM_ETH_PHY_SetMode</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5d06a94867c89cd311b6e279669321e3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_PHY_DUPLEX_FULL&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a7848c83cd1fd6b2645c17919c2990354">ARM_ETH_DUPLEX_FULL</a> &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#aebafadc356d8e58407db156a5dac743f">ARM_ETH_PHY_DUPLEX_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Full duplex link. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09" title="Set Ethernet PHY Device Operation mode.">ARM_ETH_PHY_SetMode</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gace797b3cd143be22f47c3ef61b20e14d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_PHY_DUPLEX_HALF&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#acb15afc2bfe61c56049b7279d6eae8fe">ARM_ETH_DUPLEX_HALF</a> &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#aebafadc356d8e58407db156a5dac743f">ARM_ETH_PHY_DUPLEX_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Half duplex link. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09" title="Set Ethernet PHY Device Operation mode.">ARM_ETH_PHY_SetMode</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8d68719e07c7af449b57c5df802376c8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_PHY_ISOLATE&#160;&#160;&#160;(1UL &lt;&lt; 5)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Isolate PHY from MII interface. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09" title="Set Ethernet PHY Device Operation mode.">ARM_ETH_PHY_SetMode</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5f7e46cda8ab3c774fe7ce0a8a1ba3ec"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_PHY_LOOPBACK&#160;&#160;&#160;(1UL &lt;&lt; 4)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Loop-back test mode. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09" title="Set Ethernet PHY Device Operation mode.">ARM_ETH_PHY_SetMode</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad1e8b2c8c210fa36949db9a34a993657"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_PHY_SPEED_100M&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a3bddfc4cf5645f8568d9cb6621fd606a">ARM_ETH_SPEED_100M</a>  &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a13700fab82aa60a3357614faa0619e97">ARM_ETH_PHY_SPEED_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>100 Mbps link speed </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09" title="Set Ethernet PHY Device Operation mode.">ARM_ETH_PHY_SetMode</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabc7acc4ebe828c3d0825400e14ad20f0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_PHY_SPEED_10M&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a1f834c4c785d7f69b1eaca011ee298ec">ARM_ETH_SPEED_10M</a>   &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a13700fab82aa60a3357614faa0619e97">ARM_ETH_PHY_SPEED_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>10 Mbps link speed </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09" title="Set Ethernet PHY Device Operation mode.">ARM_ETH_PHY_SetMode</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga046605398ceae99a176e6f82432ae710"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_PHY_SPEED_1G&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a218f470079b7c04de6776d467a53772a">ARM_ETH_SPEED_1G</a>    &lt;&lt; <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a13700fab82aa60a3357614faa0619e97">ARM_ETH_PHY_SPEED_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>1 Gpbs link speed </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09" title="Set Ethernet PHY Device Operation mode.">ARM_ETH_PHY_SetMode</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
