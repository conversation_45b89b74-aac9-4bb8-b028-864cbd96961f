<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="classes.html"><span>Data&#160;Structure&#160;Index</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li class="current"><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions_0x67.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a class="anchor" id="index_g"></a>- g -</h3><ul>
<li>general_call
: <a class="el" href="group__i2c__interface__gr.html#ab65804439f6f5beda8da30381b0ad22d">ARM_I2C_STATUS</a>
</li>
<li>GetCapabilities
: <a class="el" href="group__eth__mac__interface__gr.html#a9fd725bb058c584a9ced9c579561cdf1">ARM_DRIVER_ETH_MAC</a>
, <a class="el" href="group__mci__interface__gr.html#a5648b4224e0346ba5e20fefc7e83aee8">ARM_DRIVER_MCI</a>
, <a class="el" href="group__usart__interface__gr.html#a9cec078ea816ece7b2d989f35caadb12">ARM_DRIVER_USART</a>
, <a class="el" href="group__usbd__interface__gr.html#a52045edf0f555a0f0ecdf37a5e169f7a">ARM_DRIVER_USBD</a>
, <a class="el" href="group__nand__interface__gr.html#adab9d081aee3e5d1f83c6911e45ceaa6">ARM_DRIVER_NAND</a>
, <a class="el" href="group__usbh__host__gr.html#aaba1d9458e32389c21c3d899f9cb9313">ARM_DRIVER_USBH</a>
, <a class="el" href="group__usbh__hci__gr.html#a7a41769405bb3bb4cc9eaba26cf220d4">ARM_DRIVER_USBH_HCI</a>
, <a class="el" href="group__i2c__interface__gr.html#a5dfa74ca82e0af995d43da61e08c3103">ARM_DRIVER_I2C</a>
, <a class="el" href="group__flash__interface__gr.html#a25076bd7274af5d3b0af6380ed1f0331">ARM_DRIVER_FLASH</a>
, <a class="el" href="group__spi__interface__gr.html#a065b5fc24d0204692f0f95a44351ac1e">ARM_DRIVER_SPI</a>
</li>
<li>GetDataCount
: <a class="el" href="group__i2c__interface__gr.html#ad421a9b9b07fd6d3e6537396c2b98788">ARM_DRIVER_I2C</a>
, <a class="el" href="group__spi__interface__gr.html#ad1d892ab3932f65cd7cdf2d0a91ae5da">ARM_DRIVER_SPI</a>
</li>
<li>GetDeviceBusy
: <a class="el" href="group__nand__interface__gr.html#ac9bc93fb1a089c6ac71428122f3a072e">ARM_DRIVER_NAND</a>
</li>
<li>GetFrameNumber
: <a class="el" href="group__usbh__host__gr.html#a31d1785d6d46f75241ebbf6b5a6b4919">ARM_DRIVER_USBH</a>
, <a class="el" href="group__usbd__interface__gr.html#a31d1785d6d46f75241ebbf6b5a6b4919">ARM_DRIVER_USBD</a>
</li>
<li>GetInfo
: <a class="el" href="group__flash__interface__gr.html#ae64d4ee61b7a7ee0b38a0ef2b61f1db2">ARM_DRIVER_FLASH</a>
</li>
<li>GetLinkInfo
: <a class="el" href="group__eth__phy__interface__gr.html#ac162bfaf93512fa0966bfbb923c45463">ARM_DRIVER_ETH_PHY</a>
</li>
<li>GetLinkState
: <a class="el" href="group__eth__phy__interface__gr.html#a0e25b2f267edc874f1bd785175fcf08a">ARM_DRIVER_ETH_PHY</a>
</li>
<li>GetMacAddress
: <a class="el" href="group__eth__mac__interface__gr.html#a02837059933cd04b04bf795a7138f218">ARM_DRIVER_ETH_MAC</a>
</li>
<li>GetModemStatus
: <a class="el" href="group__usart__interface__gr.html#a517a7a98a444126734782beb4951a9db">ARM_DRIVER_USART</a>
</li>
<li>GetRxCount
: <a class="el" href="group__usart__interface__gr.html#a758c7822edf6ac18f82eb33c9dc09d71">ARM_DRIVER_USART</a>
</li>
<li>GetRxFrameSize
: <a class="el" href="group__eth__mac__interface__gr.html#a3286cc9c7624168b162aa3ce3cbe135e">ARM_DRIVER_ETH_MAC</a>
</li>
<li>GetRxFrameTime
: <a class="el" href="group__eth__mac__interface__gr.html#a8ae5a588bf4055bba3de73cfba78f7e8">ARM_DRIVER_ETH_MAC</a>
</li>
<li>GetStatus
: <a class="el" href="group__nand__interface__gr.html#aa43ee108ee5bf29e40485ca89b34188b">ARM_DRIVER_NAND</a>
, <a class="el" href="group__i2c__interface__gr.html#ad4b47653bc47cdb02965dd311e88b96a">ARM_DRIVER_I2C</a>
, <a class="el" href="group__mci__interface__gr.html#a2dc63353d6869c0ea2d3d29155c88b49">ARM_DRIVER_MCI</a>
, <a class="el" href="group__flash__interface__gr.html#afc1db6f33f777784f3c95efc816d4856">ARM_DRIVER_FLASH</a>
, <a class="el" href="group__spi__interface__gr.html#a7305e7248420cdb4b02ceba87672178d">ARM_DRIVER_SPI</a>
, <a class="el" href="group__usart__interface__gr.html#a055ad4095356a022886828009a980316">ARM_DRIVER_USART</a>
</li>
<li>GetTxCount
: <a class="el" href="group__usart__interface__gr.html#a0b28b2c21016702f50c28655653099df">ARM_DRIVER_USART</a>
</li>
<li>GetTxFrameTime
: <a class="el" href="group__eth__mac__interface__gr.html#acf081f5020f4ef1435bcff7333a70b93">ARM_DRIVER_ETH_MAC</a>
</li>
<li>GetVersion
: <a class="el" href="group__usbh__hci__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_USBH_HCI</a>
, <a class="el" href="group__mci__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_MCI</a>
, <a class="el" href="group__eth__phy__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_ETH_PHY</a>
, <a class="el" href="group__i2c__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_I2C</a>
, <a class="el" href="group__spi__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_SPI</a>
, <a class="el" href="group__nand__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_NAND</a>
, <a class="el" href="group__usbd__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_USBD</a>
, <a class="el" href="group__eth__mac__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_ETH_MAC</a>
, <a class="el" href="group__usart__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_USART</a>
, <a class="el" href="group__usbh__host__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_USBH</a>
, <a class="el" href="group__flash__interface__gr.html#a8834b281da48583845c044a81566c1b3">ARM_DRIVER_FLASH</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
