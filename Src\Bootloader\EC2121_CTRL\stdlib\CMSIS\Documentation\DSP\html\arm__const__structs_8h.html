<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_const_structs.h File Reference</title>
<title>CMSIS-DSP: arm_const_structs.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__const__structs_8h.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">arm_const_structs.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a27127e9d3deb59df12747233b1b9ea31"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a27127e9d3deb59df12747233b1b9ea31">arm_cfft_sR_f32_len16</a></td></tr>
<tr class="separator:a27127e9d3deb59df12747233b1b9ea31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5fed2b5e0cc4cb5b8675f14daf226a25"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a5fed2b5e0cc4cb5b8675f14daf226a25">arm_cfft_sR_f32_len32</a></td></tr>
<tr class="separator:a5fed2b5e0cc4cb5b8675f14daf226a25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af94d90db836f662321946154c76b5b80"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#af94d90db836f662321946154c76b5b80">arm_cfft_sR_f32_len64</a></td></tr>
<tr class="separator:af94d90db836f662321946154c76b5b80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad283193397ba476465a330db9a955973"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#ad283193397ba476465a330db9a955973">arm_cfft_sR_f32_len128</a></td></tr>
<tr class="separator:ad283193397ba476465a330db9a955973"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb2f0a0be605963264217cc10b7bd3b2"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#aeb2f0a0be605963264217cc10b7bd3b2">arm_cfft_sR_f32_len256</a></td></tr>
<tr class="separator:aeb2f0a0be605963264217cc10b7bd3b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15f6e533f5cfeb014839303d8ed52e19"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a15f6e533f5cfeb014839303d8ed52e19">arm_cfft_sR_f32_len512</a></td></tr>
<tr class="separator:a15f6e533f5cfeb014839303d8ed52e19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a05abc294a9159abbd6ffb4f188fe18b1"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a05abc294a9159abbd6ffb4f188fe18b1">arm_cfft_sR_f32_len1024</a></td></tr>
<tr class="separator:a05abc294a9159abbd6ffb4f188fe18b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d2fad347dcadc47377e1226231b9f62"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a8d2fad347dcadc47377e1226231b9f62">arm_cfft_sR_f32_len2048</a></td></tr>
<tr class="separator:a8d2fad347dcadc47377e1226231b9f62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01d2dbdb8193d43c2b7f003f9cb9a39d"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a01d2dbdb8193d43c2b7f003f9cb9a39d">arm_cfft_sR_f32_len4096</a></td></tr>
<tr class="separator:a01d2dbdb8193d43c2b7f003f9cb9a39d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1336431c4d2a88d32c42308cfe2defa1"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a1336431c4d2a88d32c42308cfe2defa1">arm_cfft_sR_q31_len16</a></td></tr>
<tr class="separator:a1336431c4d2a88d32c42308cfe2defa1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c083c013ef17920cf8f28dc6f139a39"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a4c083c013ef17920cf8f28dc6f139a39">arm_cfft_sR_q31_len32</a></td></tr>
<tr class="separator:a4c083c013ef17920cf8f28dc6f139a39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad11668a5662334e0bc6a2811c9cb1047"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#ad11668a5662334e0bc6a2811c9cb1047">arm_cfft_sR_q31_len64</a></td></tr>
<tr class="separator:ad11668a5662334e0bc6a2811c9cb1047"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a2fcdb54300f75ef1fafe02954e9a61"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a9a2fcdb54300f75ef1fafe02954e9a61">arm_cfft_sR_q31_len128</a></td></tr>
<tr class="separator:a9a2fcdb54300f75ef1fafe02954e9a61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f2de67938bd228918e40f60f18dd6b5"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a3f2de67938bd228918e40f60f18dd6b5">arm_cfft_sR_q31_len256</a></td></tr>
<tr class="separator:a3f2de67938bd228918e40f60f18dd6b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa337272cf78aaf6075e7e19d0a097d6f"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#aa337272cf78aaf6075e7e19d0a097d6f">arm_cfft_sR_q31_len512</a></td></tr>
<tr class="separator:aa337272cf78aaf6075e7e19d0a097d6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada9813a027999f3cff066c9f7b5df51b"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#ada9813a027999f3cff066c9f7b5df51b">arm_cfft_sR_q31_len1024</a></td></tr>
<tr class="separator:ada9813a027999f3cff066c9f7b5df51b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a420622d75b277070784083ddd44b95fb"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a420622d75b277070784083ddd44b95fb">arm_cfft_sR_q31_len2048</a></td></tr>
<tr class="separator:a420622d75b277070784083ddd44b95fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfc9595f40a1c7aaba85e1328d824b1c"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#abfc9595f40a1c7aaba85e1328d824b1c">arm_cfft_sR_q31_len4096</a></td></tr>
<tr class="separator:abfc9595f40a1c7aaba85e1328d824b1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ed661717c58b18f3e557daa72f2b91b"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a7ed661717c58b18f3e557daa72f2b91b">arm_cfft_sR_q15_len16</a></td></tr>
<tr class="separator:a7ed661717c58b18f3e557daa72f2b91b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d5426a822a6017235b5e10119606a90"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a8d5426a822a6017235b5e10119606a90">arm_cfft_sR_q15_len32</a></td></tr>
<tr class="separator:a8d5426a822a6017235b5e10119606a90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95c216e7dcfd59a8d40ef55ac223a749"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a95c216e7dcfd59a8d40ef55ac223a749">arm_cfft_sR_q15_len64</a></td></tr>
<tr class="separator:a95c216e7dcfd59a8d40ef55ac223a749"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a736a97efd37c6386dab8db730904f69b"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a736a97efd37c6386dab8db730904f69b">arm_cfft_sR_q15_len128</a></td></tr>
<tr class="separator:a736a97efd37c6386dab8db730904f69b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad80be0db1ea40c66b079404c48d2dcf4"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#ad80be0db1ea40c66b079404c48d2dcf4">arm_cfft_sR_q15_len256</a></td></tr>
<tr class="separator:ad80be0db1ea40c66b079404c48d2dcf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a273b91ec86bb2bd8ac14e69252d487fb"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a273b91ec86bb2bd8ac14e69252d487fb">arm_cfft_sR_q15_len512</a></td></tr>
<tr class="separator:a273b91ec86bb2bd8ac14e69252d487fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad343fb2e4cba826f092f9d72c4adc831"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#ad343fb2e4cba826f092f9d72c4adc831">arm_cfft_sR_q15_len1024</a></td></tr>
<tr class="separator:ad343fb2e4cba826f092f9d72c4adc831"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a92c94dc79c66ec66c95f793aedb964b9"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#a92c94dc79c66ec66c95f793aedb964b9">arm_cfft_sR_q15_len2048</a></td></tr>
<tr class="separator:a92c94dc79c66ec66c95f793aedb964b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab57c118edaa3260f7f16686152845b18"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__const__structs_8h.html#ab57c118edaa3260f7f16686152845b18">arm_cfft_sR_q15_len4096</a></td></tr>
<tr class="separator:ab57c118edaa3260f7f16686152845b18"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="a05abc294a9159abbd6ffb4f188fe18b1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len1024</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ad283193397ba476465a330db9a955973"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len128</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a27127e9d3deb59df12747233b1b9ea31"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len16</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8d2fad347dcadc47377e1226231b9f62"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len2048</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aeb2f0a0be605963264217cc10b7bd3b2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len256</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5fed2b5e0cc4cb5b8675f14daf226a25"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len32</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a01d2dbdb8193d43c2b7f003f9cb9a39d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len4096</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a15f6e533f5cfeb014839303d8ed52e19"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len512</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af94d90db836f662321946154c76b5b80"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> arm_cfft_sR_f32_len64</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad343fb2e4cba826f092f9d72c4adc831"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len1024</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a736a97efd37c6386dab8db730904f69b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len128</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a7ed661717c58b18f3e557daa72f2b91b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len16</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a92c94dc79c66ec66c95f793aedb964b9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len2048</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ad80be0db1ea40c66b079404c48d2dcf4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len256</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a8d5426a822a6017235b5e10119606a90"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len32</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ab57c118edaa3260f7f16686152845b18"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len4096</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a273b91ec86bb2bd8ac14e69252d487fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len512</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a95c216e7dcfd59a8d40ef55ac223a749"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> arm_cfft_sR_q15_len64</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ada9813a027999f3cff066c9f7b5df51b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len1024</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a9a2fcdb54300f75ef1fafe02954e9a61"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len128</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a1336431c4d2a88d32c42308cfe2defa1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len16</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a420622d75b277070784083ddd44b95fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len2048</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a3f2de67938bd228918e40f60f18dd6b5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len256</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a4c083c013ef17920cf8f28dc6f139a39"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len32</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="abfc9595f40a1c7aaba85e1328d824b1c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len4096</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="aa337272cf78aaf6075e7e19d0a097d6f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len512</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ad11668a5662334e0bc6a2811c9cb1047"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> arm_cfft_sR_q31_len64</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_856524284ebe840938865dc061f982fb.html">Include</a></li><li class="navelem"><a class="el" href="arm__const__structs_8h.html">arm_const_structs.h</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
