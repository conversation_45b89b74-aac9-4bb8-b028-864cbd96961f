<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Convolution</title>
<title>CMSIS-DSP: Convolution</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___conv.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Convolution</div>  </div>
<div class="ingroups"><a class="el" href="group__group_filters.html">Filtering Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga3f860dc98c6fc4cafc421e4a2aed3c89"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#ga3f860dc98c6fc4cafc421e4a2aed3c89">arm_conv_f32</a> (<a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst)</td></tr>
<tr class="memdesc:ga3f860dc98c6fc4cafc421e4a2aed3c89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of floating-point sequences.  <a href="#ga3f860dc98c6fc4cafc421e4a2aed3c89"></a><br/></td></tr>
<tr class="separator:ga3f860dc98c6fc4cafc421e4a2aed3c89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf16f490d245391ec18a42adc73d6d749"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#gaf16f490d245391ec18a42adc73d6d749">arm_conv_fast_opt_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch1, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch2)</td></tr>
<tr class="memdesc:gaf16f490d245391ec18a42adc73d6d749"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of Q15 sequences (fast version) for Cortex-M3 and Cortex-M4.  <a href="#gaf16f490d245391ec18a42adc73d6d749"></a><br/></td></tr>
<tr class="separator:gaf16f490d245391ec18a42adc73d6d749"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad75ca978ce906e04abdf86a8d76306d4"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#gad75ca978ce906e04abdf86a8d76306d4">arm_conv_fast_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst)</td></tr>
<tr class="memdesc:gad75ca978ce906e04abdf86a8d76306d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of Q15 sequences (fast version) for Cortex-M3 and Cortex-M4.  <a href="#gad75ca978ce906e04abdf86a8d76306d4"></a><br/></td></tr>
<tr class="separator:gad75ca978ce906e04abdf86a8d76306d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga51112dcdf9b3624eb05182cdc4da9ec0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#ga51112dcdf9b3624eb05182cdc4da9ec0">arm_conv_fast_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst)</td></tr>
<tr class="memdesc:ga51112dcdf9b3624eb05182cdc4da9ec0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of Q31 sequences (fast version) for Cortex-M3 and Cortex-M4.  <a href="#ga51112dcdf9b3624eb05182cdc4da9ec0"></a><br/></td></tr>
<tr class="separator:ga51112dcdf9b3624eb05182cdc4da9ec0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac77dbcaef5c754cac27eab96c4753a3c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#gac77dbcaef5c754cac27eab96c4753a3c">arm_conv_opt_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch1, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch2)</td></tr>
<tr class="memdesc:gac77dbcaef5c754cac27eab96c4753a3c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of Q15 sequences.  <a href="#gac77dbcaef5c754cac27eab96c4753a3c"></a><br/></td></tr>
<tr class="separator:gac77dbcaef5c754cac27eab96c4753a3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4c7cf073e89d6d57cc4e711f078c3f68"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#ga4c7cf073e89d6d57cc4e711f078c3f68">arm_conv_opt_q7</a> (<a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pDst, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch1, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratch2)</td></tr>
<tr class="memdesc:ga4c7cf073e89d6d57cc4e711f078c3f68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of Q7 sequences.  <a href="#ga4c7cf073e89d6d57cc4e711f078c3f68"></a><br/></td></tr>
<tr class="separator:ga4c7cf073e89d6d57cc4e711f078c3f68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaccd6a89b0ff7a94df64610598e6e6893"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#gaccd6a89b0ff7a94df64610598e6e6893">arm_conv_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst)</td></tr>
<tr class="memdesc:gaccd6a89b0ff7a94df64610598e6e6893"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of Q15 sequences.  <a href="#gaccd6a89b0ff7a94df64610598e6e6893"></a><br/></td></tr>
<tr class="separator:gaccd6a89b0ff7a94df64610598e6e6893"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga946b58da734f1e4e78c91fcaab4b12b6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#ga946b58da734f1e4e78c91fcaab4b12b6">arm_conv_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst)</td></tr>
<tr class="memdesc:ga946b58da734f1e4e78c91fcaab4b12b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of Q31 sequences.  <a href="#ga946b58da734f1e4e78c91fcaab4b12b6"></a><br/></td></tr>
<tr class="separator:ga946b58da734f1e4e78c91fcaab4b12b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2070cb792a167e78dbad8d06b97cdab"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___conv.html#gae2070cb792a167e78dbad8d06b97cdab">arm_conv_q7</a> (<a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrcA, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrcB, uint32_t <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pDst)</td></tr>
<tr class="memdesc:gae2070cb792a167e78dbad8d06b97cdab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convolution of Q7 sequences.  <a href="#gae2070cb792a167e78dbad8d06b97cdab"></a><br/></td></tr>
<tr class="separator:gae2070cb792a167e78dbad8d06b97cdab"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Convolution is a mathematical operation that operates on two finite length vectors to generate a finite length output vector. Convolution is similar to correlation and is frequently used in filtering and data analysis. The CMSIS DSP library contains functions for convolving Q7, Q15, Q31, and floating-point data types. The library also provides fast versions of the Q15 and Q31 functions on Cortex-M4 and Cortex-M3.</p>
<dl class="section user"><dt>Algorithm </dt><dd>Let <code>a[n]</code> and <code>b[n]</code> be sequences of length <code>srcALen</code> and <code>srcBLen</code> samples respectively. Then the convolution</dd></dl>
<pre>    
                  c[n] = a[n] * b[n]    
</pre><dl class="section user"><dt></dt><dd>is defined as <div class="image">
<img src="ConvolutionEquation.gif" alt="ConvolutionEquation.gif"/>
</div>
 </dd></dl>
<dl class="section user"><dt></dt><dd>Note that <code>c[n]</code> is of length <code>srcALen + srcBLen - 1</code> and is defined over the interval <code>n=0, 1, 2, ..., srcALen + srcBLen - 2</code>. <code>pSrcA</code> points to the first input vector of length <code>srcALen</code> and <code>pSrcB</code> points to the second input vector of length <code>srcBLen</code>. The output result is written to <code>pDst</code> and the calling function must allocate <code>srcALen+srcBLen-1</code> words for the result.</dd></dl>
<dl class="section user"><dt></dt><dd>Conceptually, when two signals <code>a[n]</code> and <code>b[n]</code> are convolved, the signal <code>b[n]</code> slides over <code>a[n]</code>. For each offset <code>n</code>, the overlapping portions of a[n] and b[n] are multiplied and summed together.</dd></dl>
<dl class="section user"><dt></dt><dd>Note that convolution is a commutative operation:</dd></dl>
<pre>    
                  a[n] * b[n] = b[n] * a[n].    
</pre><dl class="section user"><dt></dt><dd>This means that switching the A and B arguments to the convolution functions has no effect.</dd></dl>
<p><b>Fixed-Point Behavior</b></p>
<dl class="section user"><dt></dt><dd>Convolution requires summing up a large number of intermediate products. As such, the Q7, Q15, and Q31 functions run a risk of overflow and saturation. Refer to the function specific documentation below for further details of the particular algorithm used.</dd></dl>
<p><b>Fast Versions</b></p>
<dl class="section user"><dt></dt><dd>Fast versions are supported for Q31 and Q15. Cycles for Fast versions are less compared to Q31 and Q15 of conv and the design requires the input signals should be scaled down to avoid intermediate overflows.</dd></dl>
<p><b>Opt Versions</b></p>
<dl class="section user"><dt></dt><dd>Opt versions are supported for Q15 and Q7. Design uses internal scratch buffer for getting good optimisation. These versions are optimised in cycles and consumes more memory(Scratch memory) compared to Q15 and Q7 versions </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga3f860dc98c6fc4cafc421e4a2aed3c89"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
<a class="anchor" id="gaf16f490d245391ec18a42adc73d6d749"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_fast_opt_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch1</td><td>points to scratch buffer of size max(srcALen, srcBLen) + 2*min(srcALen, srcBLen) - 2. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch2</td><td>points to scratch buffer of size min(srcALen, srcBLen). </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Restrictions </dt><dd>If the silicon does not support unaligned memory access enable the macro UNALIGNED_SUPPORT_DISABLE In this case input, output, scratch1 and scratch2 buffers should be aligned by 32-bit</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>This fast version uses a 32-bit accumulator with 2.30 format. The accumulator maintains full precision of the intermediate multiplication results but provides only a single guard bit. There is no saturation on intermediate additions. Thus, if the accumulator overflows it wraps around and distorts the result. The input signals should be scaled down to avoid intermediate overflows. Scale down the inputs by log2(min(srcALen, srcBLen)) (log2 is read as log to the base 2) times to avoid overflows, as maximum of min(srcALen, srcBLen) number of additions are carried internally. The 2.30 accumulator is right shifted by 15 bits and then saturated to 1.15 format to yield the final result.</dd></dl>
<dl class="section user"><dt></dt><dd>See <code><a class="el" href="group___conv.html#gaccd6a89b0ff7a94df64610598e6e6893" title="Convolution of Q15 sequences.">arm_conv_q15()</a></code> for a slower implementation of this function which uses 64-bit accumulation to avoid wrap around distortion. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0">arm_copy_q15()</a>, <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_fill_q15()</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
<a class="anchor" id="gad75ca978ce906e04abdf86a8d76306d4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_fast_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>This fast version uses a 32-bit accumulator with 2.30 format. The accumulator maintains full precision of the intermediate multiplication results but provides only a single guard bit. There is no saturation on intermediate additions. Thus, if the accumulator overflows it wraps around and distorts the result. The input signals should be scaled down to avoid intermediate overflows. Scale down the inputs by log2(min(srcALen, srcBLen)) (log2 is read as log to the base 2) times to avoid overflows, as maximum of min(srcALen, srcBLen) number of additions are carried internally. The 2.30 accumulator is right shifted by 15 bits and then saturated to 1.15 format to yield the final result.</dd></dl>
<dl class="section user"><dt></dt><dd>See <code><a class="el" href="group___conv.html#gaccd6a89b0ff7a94df64610598e6e6893" title="Convolution of Q15 sequences.">arm_conv_q15()</a></code> for a slower implementation of this function which uses 64-bit accumulation to avoid wrap around distortion. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
<a class="anchor" id="ga51112dcdf9b3624eb05182cdc4da9ec0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_fast_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>This function is optimized for speed at the expense of fixed-point precision and overflow protection. The result of each 1.31 x 1.31 multiplication is truncated to 2.30 format. These intermediate results are accumulated in a 32-bit register in 2.30 format. Finally, the accumulator is saturated and converted to a 1.31 result.</dd></dl>
<dl class="section user"><dt></dt><dd>The fast version has the same overflow behavior as the standard version but provides less precision since it discards the low 32 bits of each multiplication result. In order to avoid overflows completely the input signals must be scaled down. Scale down the inputs by log2(min(srcALen, srcBLen)) (log2 is read as log to the base 2) times to avoid overflows, as maximum of min(srcALen, srcBLen) number of additions are carried internally.</dd></dl>
<dl class="section user"><dt></dt><dd>See <code><a class="el" href="group___conv.html#ga946b58da734f1e4e78c91fcaab4b12b6" title="Convolution of Q31 sequences.">arm_conv_q31()</a></code> for a slower implementation of this function which uses 64-bit accumulation to provide higher precision. </dd></dl>

<p>References <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
<a class="anchor" id="gac77dbcaef5c754cac27eab96c4753a3c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_opt_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch1</td><td>points to scratch buffer of size max(srcALen, srcBLen) + 2*min(srcALen, srcBLen) - 2. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch2</td><td>points to scratch buffer of size min(srcALen, srcBLen). </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Restrictions </dt><dd>If the silicon does not support unaligned memory access enable the macro UNALIGNED_SUPPORT_DISABLE In this case input, output, scratch1 and scratch2 buffers should be aligned by 32-bit</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 64-bit internal accumulator. Both inputs are in 1.15 format and multiplications yield a 2.30 result. The 2.30 intermediate results are accumulated in a 64-bit accumulator in 34.30 format. This approach provides 33 guard bits and there is no risk of overflow. The 34.30 result is then truncated to 34.15 format by discarding the low 15 bits and then saturated to 1.15 format.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer to <code><a class="el" href="group___conv.html#gad75ca978ce906e04abdf86a8d76306d4" title="Convolution of Q15 sequences (fast version) for Cortex-M3 and Cortex-M4.">arm_conv_fast_q15()</a></code> for a faster but less precise version of this function for Cortex-M3 and Cortex-M4. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0">arm_copy_q15()</a>, <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_fill_q15()</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
<a class="anchor" id="ga4c7cf073e89d6d57cc4e711f078c3f68"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_opt_q7 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratch2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch1</td><td>points to scratch buffer(of type q15_t) of size max(srcALen, srcBLen) + 2*min(srcALen, srcBLen) - 2. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratch2</td><td>points to scratch buffer (of type q15_t) of size min(srcALen, srcBLen). </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Restrictions </dt><dd>If the silicon does not support unaligned memory access enable the macro UNALIGNED_SUPPORT_DISABLE In this case input, output, scratch1 and scratch2 buffers should be aligned by 32-bit</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 32-bit internal accumulator. Both the inputs are represented in 1.7 format and multiplications yield a 2.14 result. The 2.14 intermediate results are accumulated in a 32-bit accumulator in 18.14 format. This approach provides 17 guard bits and there is no risk of overflow as long as <code>max(srcALen, srcBLen)&lt;131072</code>. The 18.14 result is then truncated to 18.7 format by discarding the low 7 bits and then saturated to 1.7 format. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a3ebff224ad44c217fde9f530342e2960">__PACKq7</a>, <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_fill_q15()</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
<a class="anchor" id="gaccd6a89b0ff7a94df64610598e6e6893"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 64-bit internal accumulator. Both inputs are in 1.15 format and multiplications yield a 2.30 result. The 2.30 intermediate results are accumulated in a 64-bit accumulator in 34.30 format. This approach provides 33 guard bits and there is no risk of overflow. The 34.30 result is then truncated to 34.15 format by discarding the low 15 bits and then saturated to 1.15 format.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer to <code><a class="el" href="group___conv.html#gad75ca978ce906e04abdf86a8d76306d4" title="Convolution of Q15 sequences (fast version) for Cortex-M3 and Cortex-M4.">arm_conv_fast_q15()</a></code> for a faster but less precise version of this function for Cortex-M3 and Cortex-M4.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer the function <code><a class="el" href="group___conv.html#gac77dbcaef5c754cac27eab96c4753a3c" title="Convolution of Q15 sequences.">arm_conv_opt_q15()</a></code> for a faster implementation of this function using scratch buffers. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">_SIMD32_OFFSET</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
<a class="anchor" id="ga946b58da734f1e4e78c91fcaab4b12b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The function is implemented using an internal 64-bit accumulator. The accumulator has a 2.62 format and maintains full precision of the intermediate multiplication results but provides only a single guard bit. There is no saturation on intermediate additions. Thus, if the accumulator overflows it wraps around and distorts the result. The input signals should be scaled down to avoid intermediate overflows. Scale down the inputs by log2(min(srcALen, srcBLen)) (log2 is read as log to the base 2) times to avoid overflows, as maximum of min(srcALen, srcBLen) number of additions are carried internally. The 2.62 accumulator is right shifted by 31 bits and saturated to 1.31 format to yield the final result.</dd></dl>
<dl class="section user"><dt></dt><dd>See <code><a class="el" href="group___conv.html#ga51112dcdf9b3624eb05182cdc4da9ec0" title="Convolution of Q31 sequences (fast version) for Cortex-M3 and Cortex-M4.">arm_conv_fast_q31()</a></code> for a faster but less precise implementation of this function for Cortex-M3 and Cortex-M4. </dd></dl>

<p>References <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
<a class="anchor" id="gae2070cb792a167e78dbad8d06b97cdab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_conv_q7 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcALen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>srcBLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcALen</td><td>length of the first input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input sequence. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">srcBLen</td><td>length of the second input sequence. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the location where the output result is written. Length srcALen+srcBLen-1. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 32-bit internal accumulator. Both the inputs are represented in 1.7 format and multiplications yield a 2.14 result. The 2.14 intermediate results are accumulated in a 32-bit accumulator in 18.14 format. This approach provides 17 guard bits and there is no risk of overflow as long as <code>max(srcALen, srcBLen)&lt;131072</code>. The 18.14 result is then truncated to 18.7 format by discarding the low 7 bits and then saturated to 1.7 format.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer the function <code><a class="el" href="group___conv.html#ga4c7cf073e89d6d57cc4e711f078c3f68" title="Convolution of Q7 sequences.">arm_conv_opt_q7()</a></code> for a faster implementation of this function. </dd></dl>

<p>References <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
