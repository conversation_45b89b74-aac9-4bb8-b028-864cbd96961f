<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_common_tables.h File Reference</title>
<title>CMSIS-DSP: arm_common_tables.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__common__tables_8h.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">arm_common_tables.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a9bf8c85e4c91b9b55818b3d650d2c761"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a9bf8c85e4c91b9b55818b3d650d2c761">twiddleCoef</a></td></tr>
<tr class="separator:a9bf8c85e4c91b9b55818b3d650d2c761"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52289ebb691669410fbc40d1a8a1562a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a52289ebb691669410fbc40d1a8a1562a">ARMBITREVINDEXTABLE__16_TABLE_LENGTH</a></td></tr>
<tr class="separator:a52289ebb691669410fbc40d1a8a1562a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e12fc7073f15899078a1b2d8f4afb4c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a6e12fc7073f15899078a1b2d8f4afb4c">ARMBITREVINDEXTABLE__32_TABLE_LENGTH</a></td></tr>
<tr class="separator:a6e12fc7073f15899078a1b2d8f4afb4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73e1987baf5282c699168bccf635930e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a73e1987baf5282c699168bccf635930e">ARMBITREVINDEXTABLE__64_TABLE_LENGTH</a></td></tr>
<tr class="separator:a73e1987baf5282c699168bccf635930e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb73376f7efda869394aab2acef4291c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#abb73376f7efda869394aab2acef4291c">ARMBITREVINDEXTABLE_128_TABLE_LENGTH</a></td></tr>
<tr class="separator:abb73376f7efda869394aab2acef4291c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7dc18c3b4f8d76f5a29f7b182007934"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#aa7dc18c3b4f8d76f5a29f7b182007934">ARMBITREVINDEXTABLE_256_TABLE_LENGTH</a></td></tr>
<tr class="separator:aa7dc18c3b4f8d76f5a29f7b182007934"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab21231782baf177ef3edad11aeba5a4f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#ab21231782baf177ef3edad11aeba5a4f">ARMBITREVINDEXTABLE_512_TABLE_LENGTH</a></td></tr>
<tr class="separator:ab21231782baf177ef3edad11aeba5a4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3b3659a55efaf414757d15e6c0ea9cc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#af3b3659a55efaf414757d15e6c0ea9cc">ARMBITREVINDEXTABLE1024_TABLE_LENGTH</a></td></tr>
<tr class="separator:af3b3659a55efaf414757d15e6c0ea9cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1137f42be79c5941e942b58e262b5225"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a1137f42be79c5941e942b58e262b5225">ARMBITREVINDEXTABLE2048_TABLE_LENGTH</a></td></tr>
<tr class="separator:a1137f42be79c5941e942b58e262b5225"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af08eb635c0e1cf0ab3e29931f9bf1492"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#af08eb635c0e1cf0ab3e29931f9bf1492">ARMBITREVINDEXTABLE4096_TABLE_LENGTH</a></td></tr>
<tr class="separator:af08eb635c0e1cf0ab3e29931f9bf1492"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1dfdb9f7a5ad88ba7105c6cbc7e2c76e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a1dfdb9f7a5ad88ba7105c6cbc7e2c76e">ARMBITREVINDEXTABLE_FIXED___16_TABLE_LENGTH</a></td></tr>
<tr class="separator:a1dfdb9f7a5ad88ba7105c6cbc7e2c76e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa9ecdc043a73fa12c941cbe6613f9fa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#aaa9ecdc043a73fa12c941cbe6613f9fa">ARMBITREVINDEXTABLE_FIXED___32_TABLE_LENGTH</a></td></tr>
<tr class="separator:aaa9ecdc043a73fa12c941cbe6613f9fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae53dc7c3198f9cfb5393e3a2644a12ac"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#ae53dc7c3198f9cfb5393e3a2644a12ac">ARMBITREVINDEXTABLE_FIXED___64_TABLE_LENGTH</a></td></tr>
<tr class="separator:ae53dc7c3198f9cfb5393e3a2644a12ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3b70f6b0a87ecd706fc51bb3551977b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#aa3b70f6b0a87ecd706fc51bb3551977b">ARMBITREVINDEXTABLE_FIXED__128_TABLE_LENGTH</a></td></tr>
<tr class="separator:aa3b70f6b0a87ecd706fc51bb3551977b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0711126d0e162366ec7d0ebcb2a4420"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#ac0711126d0e162366ec7d0ebcb2a4420">ARMBITREVINDEXTABLE_FIXED__256_TABLE_LENGTH</a></td></tr>
<tr class="separator:ac0711126d0e162366ec7d0ebcb2a4420"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5486cba85dce51ffbfe6c0475882cc82"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a5486cba85dce51ffbfe6c0475882cc82">ARMBITREVINDEXTABLE_FIXED__512_TABLE_LENGTH</a></td></tr>
<tr class="separator:a5486cba85dce51ffbfe6c0475882cc82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab78db333c5f36a927cf5f6b492e93dd3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#ab78db333c5f36a927cf5f6b492e93dd3">ARMBITREVINDEXTABLE_FIXED_1024_TABLE_LENGTH</a></td></tr>
<tr class="separator:ab78db333c5f36a927cf5f6b492e93dd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dbfc9019953b525d83184a50f9976cc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a7dbfc9019953b525d83184a50f9976cc">ARMBITREVINDEXTABLE_FIXED_2048_TABLE_LENGTH</a></td></tr>
<tr class="separator:a7dbfc9019953b525d83184a50f9976cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acbf48883fbb31d3dc71d232aa8e8f91f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#acbf48883fbb31d3dc71d232aa8e8f91f">ARMBITREVINDEXTABLE_FIXED_4096_TABLE_LENGTH</a></td></tr>
<tr class="separator:acbf48883fbb31d3dc71d232aa8e8f91f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:gae247e83ad50d474107254e25b36ad42b"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">armBitRevTable</a> [1024]</td></tr>
<tr class="separator:gae247e83ad50d474107254e25b36ad42b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d3642e4ee33e3ada57ff11ecda1498"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a56d3642e4ee33e3ada57ff11ecda1498">armRecipTableQ15</a> [64]</td></tr>
<tr class="separator:a56d3642e4ee33e3ada57ff11ecda1498"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae6056f6c4e8f7e494445196bf864479"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#aae6056f6c4e8f7e494445196bf864479">armRecipTableQ31</a> [64]</td></tr>
<tr class="separator:aae6056f6c4e8f7e494445196bf864479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae75e243ec61706427314270f222e0c8e"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae75e243ec61706427314270f222e0c8e">twiddleCoef_16</a> [32]</td></tr>
<tr class="separator:gae75e243ec61706427314270f222e0c8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78a72c85d88185de98050c930cfc76e3"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga78a72c85d88185de98050c930cfc76e3">twiddleCoef_32</a> [64]</td></tr>
<tr class="separator:ga78a72c85d88185de98050c930cfc76e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f3c6d98c7e66393b4ef3ac63746e43d"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga4f3c6d98c7e66393b4ef3ac63746e43d">twiddleCoef_64</a> [128]</td></tr>
<tr class="separator:ga4f3c6d98c7e66393b4ef3ac63746e43d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga948433536dafaac1381decfccf4e2d9c"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga948433536dafaac1381decfccf4e2d9c">twiddleCoef_128</a> [256]</td></tr>
<tr class="separator:ga948433536dafaac1381decfccf4e2d9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe813758a03a798e972359a092315be4"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafe813758a03a798e972359a092315be4">twiddleCoef_256</a> [512]</td></tr>
<tr class="separator:gafe813758a03a798e972359a092315be4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8830f0c068ab2cc19f2f87d220fa148"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gad8830f0c068ab2cc19f2f87d220fa148">twiddleCoef_512</a> [1024]</td></tr>
<tr class="separator:gad8830f0c068ab2cc19f2f87d220fa148"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27c056eb130a4333d1cc5dd43ec738b1"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga27c056eb130a4333d1cc5dd43ec738b1">twiddleCoef_1024</a> [2048]</td></tr>
<tr class="separator:ga27c056eb130a4333d1cc5dd43ec738b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga23e7f30421a7905b21c2015429779633"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga23e7f30421a7905b21c2015429779633">twiddleCoef_2048</a> [4096]</td></tr>
<tr class="separator:ga23e7f30421a7905b21c2015429779633"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae0182d1dd3b2f21aad4e38a815a0bd40"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae0182d1dd3b2f21aad4e38a815a0bd40">twiddleCoef_4096</a> [8192]</td></tr>
<tr class="separator:gae0182d1dd3b2f21aad4e38a815a0bd40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef4697e1ba348c4ac9358f2b9e279e93"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaef4697e1ba348c4ac9358f2b9e279e93">twiddleCoef_16_q31</a> [24]</td></tr>
<tr class="separator:gaef4697e1ba348c4ac9358f2b9e279e93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8ba78d5e6ef4bdc58e8f0044e0664a0a"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8ba78d5e6ef4bdc58e8f0044e0664a0a">twiddleCoef_32_q31</a> [48]</td></tr>
<tr class="separator:ga8ba78d5e6ef4bdc58e8f0044e0664a0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e0a7e941a25a0d74b2e6590307de47e"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6e0a7e941a25a0d74b2e6590307de47e">twiddleCoef_64_q31</a> [96]</td></tr>
<tr class="separator:ga6e0a7e941a25a0d74b2e6590307de47e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafecf9ed9873415d9f5f17f37b30c7250"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafecf9ed9873415d9f5f17f37b30c7250">twiddleCoef_128_q31</a> [192]</td></tr>
<tr class="separator:gafecf9ed9873415d9f5f17f37b30c7250"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef1ea005053b715b851cf5f908168ede"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaef1ea005053b715b851cf5f908168ede">twiddleCoef_256_q31</a> [384]</td></tr>
<tr class="separator:gaef1ea005053b715b851cf5f908168ede"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga416c61b2f08542a39111e06b0378bebe"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga416c61b2f08542a39111e06b0378bebe">twiddleCoef_512_q31</a> [768]</td></tr>
<tr class="separator:ga416c61b2f08542a39111e06b0378bebe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga514443c44b62b8b3d240afefebcda310"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga514443c44b62b8b3d240afefebcda310">twiddleCoef_1024_q31</a> [1536]</td></tr>
<tr class="separator:ga514443c44b62b8b3d240afefebcda310"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c5767de9f5a409fd0c2027e6ac67179"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9c5767de9f5a409fd0c2027e6ac67179">twiddleCoef_2048_q31</a> [3072]</td></tr>
<tr class="separator:ga9c5767de9f5a409fd0c2027e6ac67179"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga67c0890317deab3391e276f22c1fc400"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga67c0890317deab3391e276f22c1fc400">twiddleCoef_4096_q31</a> [6144]</td></tr>
<tr class="separator:ga67c0890317deab3391e276f22c1fc400"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e4e2e05f4a3112184c96cb3308d6c39"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8e4e2e05f4a3112184c96cb3308d6c39">twiddleCoef_16_q15</a> [24]</td></tr>
<tr class="separator:ga8e4e2e05f4a3112184c96cb3308d6c39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac194a4fe04a19051ae1811f69c6e5df2"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gac194a4fe04a19051ae1811f69c6e5df2">twiddleCoef_32_q15</a> [48]</td></tr>
<tr class="separator:gac194a4fe04a19051ae1811f69c6e5df2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0cc411e0b3c82078e85cfdf1b84290f"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaa0cc411e0b3c82078e85cfdf1b84290f">twiddleCoef_64_q15</a> [96]</td></tr>
<tr class="separator:gaa0cc411e0b3c82078e85cfdf1b84290f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabfdd1c5cd2b3f96da5fe5f07c707a8e5"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gabfdd1c5cd2b3f96da5fe5f07c707a8e5">twiddleCoef_128_q15</a> [192]</td></tr>
<tr class="separator:gabfdd1c5cd2b3f96da5fe5f07c707a8e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6099ae5262a0a3a8d9ce1e6da02f0c2e"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6099ae5262a0a3a8d9ce1e6da02f0c2e">twiddleCoef_256_q15</a> [384]</td></tr>
<tr class="separator:ga6099ae5262a0a3a8d9ce1e6da02f0c2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6152621af210f847128c6f38958fa385"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6152621af210f847128c6f38958fa385">twiddleCoef_512_q15</a> [768]</td></tr>
<tr class="separator:ga6152621af210f847128c6f38958fa385"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a0ec95d866fe96b740e77d6e1356b59"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8a0ec95d866fe96b740e77d6e1356b59">twiddleCoef_1024_q15</a> [1536]</td></tr>
<tr class="separator:ga8a0ec95d866fe96b740e77d6e1356b59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd16ce08ffd1048c385e0534a3b19cbb"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gadd16ce08ffd1048c385e0534a3b19cbb">twiddleCoef_2048_q15</a> [3072]</td></tr>
<tr class="separator:gadd16ce08ffd1048c385e0534a3b19cbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9b409d6995eab17805b1d1881d4bc652"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9b409d6995eab17805b1d1881d4bc652">twiddleCoef_4096_q15</a> [6144]</td></tr>
<tr class="separator:ga9b409d6995eab17805b1d1881d4bc652"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5992afe8574289cd71921651b80bd57d"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a5992afe8574289cd71921651b80bd57d">twiddleCoef_rfft_32</a> [32]</td></tr>
<tr class="separator:a5992afe8574289cd71921651b80bd57d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2759d8789e1e6ae2ba7fb8d7f5e9c2ab"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a2759d8789e1e6ae2ba7fb8d7f5e9c2ab">twiddleCoef_rfft_64</a> [64]</td></tr>
<tr class="separator:a2759d8789e1e6ae2ba7fb8d7f5e9c2ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af089dd2fe1a543d40a3325982bf45e7c"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#af089dd2fe1a543d40a3325982bf45e7c">twiddleCoef_rfft_128</a> [128]</td></tr>
<tr class="separator:af089dd2fe1a543d40a3325982bf45e7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c5c161dd469d8e6806664956dae31f9"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a5c5c161dd469d8e6806664956dae31f9">twiddleCoef_rfft_256</a> [256]</td></tr>
<tr class="separator:a5c5c161dd469d8e6806664956dae31f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94bd2fc98798f87003fef5cd0c04d1f5"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a94bd2fc98798f87003fef5cd0c04d1f5">twiddleCoef_rfft_512</a> [512]</td></tr>
<tr class="separator:a94bd2fc98798f87003fef5cd0c04d1f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7d8d3aa9898d557385748a13c959a4c"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#aa7d8d3aa9898d557385748a13c959a4c">twiddleCoef_rfft_1024</a> [1024]</td></tr>
<tr class="separator:aa7d8d3aa9898d557385748a13c959a4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a749a5995ebd433a163f7adc474dabcaa"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a749a5995ebd433a163f7adc474dabcaa">twiddleCoef_rfft_2048</a> [2048]</td></tr>
<tr class="separator:a749a5995ebd433a163f7adc474dabcaa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8013d68dd2476c86b77173bb98b87b29"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a8013d68dd2476c86b77173bb98b87b29">twiddleCoef_rfft_4096</a> [4096]</td></tr>
<tr class="separator:a8013d68dd2476c86b77173bb98b87b29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ab065857509fe5780d79fdcdce801cb"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a5ab065857509fe5780d79fdcdce801cb">armBitRevIndexTable16</a> [<a class="el" href="arm__common__tables_8h.html#a52289ebb691669410fbc40d1a8a1562a">ARMBITREVINDEXTABLE__16_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a5ab065857509fe5780d79fdcdce801cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afae094ea3df14c134012c4cb7b816637"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#afae094ea3df14c134012c4cb7b816637">armBitRevIndexTable32</a> [<a class="el" href="arm__common__tables_8h.html#a6e12fc7073f15899078a1b2d8f4afb4c">ARMBITREVINDEXTABLE__32_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:afae094ea3df14c134012c4cb7b816637"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafcb5c9203dada88ed6d1bdcf16aaba4"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#aafcb5c9203dada88ed6d1bdcf16aaba4">armBitRevIndexTable64</a> [<a class="el" href="arm__common__tables_8h.html#a73e1987baf5282c699168bccf635930e">ARMBITREVINDEXTABLE__64_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:aafcb5c9203dada88ed6d1bdcf16aaba4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04711bbb245f2ac7202db666eaaf10f2"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a04711bbb245f2ac7202db666eaaf10f2">armBitRevIndexTable128</a> [<a class="el" href="arm__common__tables_8h.html#abb73376f7efda869394aab2acef4291c">ARMBITREVINDEXTABLE_128_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a04711bbb245f2ac7202db666eaaf10f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77b17c8e7539af315c57de27610d8407"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a77b17c8e7539af315c57de27610d8407">armBitRevIndexTable256</a> [<a class="el" href="arm__common__tables_8h.html#aa7dc18c3b4f8d76f5a29f7b182007934">ARMBITREVINDEXTABLE_256_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a77b17c8e7539af315c57de27610d8407"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a297a311183fb6d17d7ee0152ad1e43f3"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a297a311183fb6d17d7ee0152ad1e43f3">armBitRevIndexTable512</a> [<a class="el" href="arm__common__tables_8h.html#ab21231782baf177ef3edad11aeba5a4f">ARMBITREVINDEXTABLE_512_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a297a311183fb6d17d7ee0152ad1e43f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae69b72fb0be5dab9a0ea76e9b6995cb6"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#ae69b72fb0be5dab9a0ea76e9b6995cb6">armBitRevIndexTable1024</a> [<a class="el" href="arm__common__tables_8h.html#af3b3659a55efaf414757d15e6c0ea9cc">ARMBITREVINDEXTABLE1024_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:ae69b72fb0be5dab9a0ea76e9b6995cb6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68b7fcd07ae5433082e600dc7e7c7430"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a68b7fcd07ae5433082e600dc7e7c7430">armBitRevIndexTable2048</a> [<a class="el" href="arm__common__tables_8h.html#a1137f42be79c5941e942b58e262b5225">ARMBITREVINDEXTABLE2048_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a68b7fcd07ae5433082e600dc7e7c7430"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6bd23609f5bb10182e8eae65400541b"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#ac6bd23609f5bb10182e8eae65400541b">armBitRevIndexTable4096</a> [<a class="el" href="arm__common__tables_8h.html#af08eb635c0e1cf0ab3e29931f9bf1492">ARMBITREVINDEXTABLE4096_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:ac6bd23609f5bb10182e8eae65400541b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c3b510a7d499dccaaea1ff164397ffb"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a6c3b510a7d499dccaaea1ff164397ffb">armBitRevIndexTable_fixed_16</a> [<a class="el" href="arm__common__tables_8h.html#a1dfdb9f7a5ad88ba7105c6cbc7e2c76e">ARMBITREVINDEXTABLE_FIXED___16_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a6c3b510a7d499dccaaea1ff164397ffb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59710415522cc38defa30402021f1f6b"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a59710415522cc38defa30402021f1f6b">armBitRevIndexTable_fixed_32</a> [<a class="el" href="arm__common__tables_8h.html#aaa9ecdc043a73fa12c941cbe6613f9fa">ARMBITREVINDEXTABLE_FIXED___32_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a59710415522cc38defa30402021f1f6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af9e1bbd7d535806a170786b069863b47"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#af9e1bbd7d535806a170786b069863b47">armBitRevIndexTable_fixed_64</a> [<a class="el" href="arm__common__tables_8h.html#ae53dc7c3198f9cfb5393e3a2644a12ac">ARMBITREVINDEXTABLE_FIXED___64_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:af9e1bbd7d535806a170786b069863b47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa10281deffc0cb708a08d55cfa513507"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#aa10281deffc0cb708a08d55cfa513507">armBitRevIndexTable_fixed_128</a> [<a class="el" href="arm__common__tables_8h.html#aa3b70f6b0a87ecd706fc51bb3551977b">ARMBITREVINDEXTABLE_FIXED__128_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:aa10281deffc0cb708a08d55cfa513507"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a721d01114016584629f03c0af37dd21e"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a721d01114016584629f03c0af37dd21e">armBitRevIndexTable_fixed_256</a> [<a class="el" href="arm__common__tables_8h.html#ac0711126d0e162366ec7d0ebcb2a4420">ARMBITREVINDEXTABLE_FIXED__256_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a721d01114016584629f03c0af37dd21e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a03d1de7bfc5f318bc4fcfddd920bcb5a"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a03d1de7bfc5f318bc4fcfddd920bcb5a">armBitRevIndexTable_fixed_512</a> [<a class="el" href="arm__common__tables_8h.html#a5486cba85dce51ffbfe6c0475882cc82">ARMBITREVINDEXTABLE_FIXED__512_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a03d1de7bfc5f318bc4fcfddd920bcb5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a579beb19201fab01210c37253447fa52"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a579beb19201fab01210c37253447fa52">armBitRevIndexTable_fixed_1024</a> [<a class="el" href="arm__common__tables_8h.html#ab78db333c5f36a927cf5f6b492e93dd3">ARMBITREVINDEXTABLE_FIXED_1024_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a579beb19201fab01210c37253447fa52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad888a207e20f601ed80b2ad43428c8cc"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#ad888a207e20f601ed80b2ad43428c8cc">armBitRevIndexTable_fixed_2048</a> [<a class="el" href="arm__common__tables_8h.html#a7dbfc9019953b525d83184a50f9976cc">ARMBITREVINDEXTABLE_FIXED_2048_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:ad888a207e20f601ed80b2ad43428c8cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2db644df1e878209441166cbb8d0db4f"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a2db644df1e878209441166cbb8d0db4f">armBitRevIndexTable_fixed_4096</a> [<a class="el" href="arm__common__tables_8h.html#acbf48883fbb31d3dc71d232aa8e8f91f">ARMBITREVINDEXTABLE_FIXED_4096_TABLE_LENGTH</a>]</td></tr>
<tr class="separator:a2db644df1e878209441166cbb8d0db4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1dec82d596780f1a66ef4f76f137c1d9"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a1dec82d596780f1a66ef4f76f137c1d9">sinTable_f32</a> [<a class="el" href="arm__math_8h.html#afcb9147c96853bea484cfc2dde07463d">FAST_MATH_TABLE_SIZE</a>+1]</td></tr>
<tr class="separator:a1dec82d596780f1a66ef4f76f137c1d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8bfccee9e1c0042cf4a765f4b19d097d"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a8bfccee9e1c0042cf4a765f4b19d097d">sinTable_q31</a> [<a class="el" href="arm__math_8h.html#afcb9147c96853bea484cfc2dde07463d">FAST_MATH_TABLE_SIZE</a>+1]</td></tr>
<tr class="separator:a8bfccee9e1c0042cf4a765f4b19d097d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9cbcfe313f61add745ebfeddb4fecd55"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__common__tables_8h.html#a9cbcfe313f61add745ebfeddb4fecd55">sinTable_q15</a> [<a class="el" href="arm__math_8h.html#afcb9147c96853bea484cfc2dde07463d">FAST_MATH_TABLE_SIZE</a>+1]</td></tr>
<tr class="separator:a9cbcfe313f61add745ebfeddb4fecd55"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="af3b3659a55efaf414757d15e6c0ea9cc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE1024_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a1137f42be79c5941e942b58e262b5225"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE2048_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="af08eb635c0e1cf0ab3e29931f9bf1492"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE4096_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abb73376f7efda869394aab2acef4291c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_128_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="aa7dc18c3b4f8d76f5a29f7b182007934"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_256_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ab21231782baf177ef3edad11aeba5a4f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_512_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a52289ebb691669410fbc40d1a8a1562a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE__16_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a6e12fc7073f15899078a1b2d8f4afb4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE__32_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a73e1987baf5282c699168bccf635930e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE__64_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ab78db333c5f36a927cf5f6b492e93dd3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED_1024_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7dbfc9019953b525d83184a50f9976cc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED_2048_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acbf48883fbb31d3dc71d232aa8e8f91f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED_4096_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa3b70f6b0a87ecd706fc51bb3551977b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED__128_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac0711126d0e162366ec7d0ebcb2a4420"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED__256_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5486cba85dce51ffbfe6c0475882cc82"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED__512_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1dfdb9f7a5ad88ba7105c6cbc7e2c76e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED___16_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aaa9ecdc043a73fa12c941cbe6613f9fa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED___32_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae53dc7c3198f9cfb5393e3a2644a12ac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARMBITREVINDEXTABLE_FIXED___64_TABLE_LENGTH</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9bf8c85e4c91b9b55818b3d650d2c761"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define twiddleCoef</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7">arm_cfft_radix2_init_f32()</a>, and <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32()</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="ae69b72fb0be5dab9a0ea76e9b6995cb6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable1024[<a class="el" href="arm__common__tables_8h.html#af3b3659a55efaf414757d15e6c0ea9cc">ARMBITREVINDEXTABLE1024_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a04711bbb245f2ac7202db666eaaf10f2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable128[<a class="el" href="arm__common__tables_8h.html#abb73376f7efda869394aab2acef4291c">ARMBITREVINDEXTABLE_128_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a5ab065857509fe5780d79fdcdce801cb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable16[<a class="el" href="arm__common__tables_8h.html#a52289ebb691669410fbc40d1a8a1562a">ARMBITREVINDEXTABLE__16_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a68b7fcd07ae5433082e600dc7e7c7430"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable2048[<a class="el" href="arm__common__tables_8h.html#a1137f42be79c5941e942b58e262b5225">ARMBITREVINDEXTABLE2048_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a77b17c8e7539af315c57de27610d8407"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable256[<a class="el" href="arm__common__tables_8h.html#aa7dc18c3b4f8d76f5a29f7b182007934">ARMBITREVINDEXTABLE_256_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="afae094ea3df14c134012c4cb7b816637"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable32[<a class="el" href="arm__common__tables_8h.html#a6e12fc7073f15899078a1b2d8f4afb4c">ARMBITREVINDEXTABLE__32_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ac6bd23609f5bb10182e8eae65400541b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable4096[<a class="el" href="arm__common__tables_8h.html#af08eb635c0e1cf0ab3e29931f9bf1492">ARMBITREVINDEXTABLE4096_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a297a311183fb6d17d7ee0152ad1e43f3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable512[<a class="el" href="arm__common__tables_8h.html#ab21231782baf177ef3edad11aeba5a4f">ARMBITREVINDEXTABLE_512_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="aafcb5c9203dada88ed6d1bdcf16aaba4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable64[<a class="el" href="arm__common__tables_8h.html#a73e1987baf5282c699168bccf635930e">ARMBITREVINDEXTABLE__64_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a579beb19201fab01210c37253447fa52"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_1024[<a class="el" href="arm__common__tables_8h.html#ab78db333c5f36a927cf5f6b492e93dd3">ARMBITREVINDEXTABLE_FIXED_1024_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa10281deffc0cb708a08d55cfa513507"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_128[<a class="el" href="arm__common__tables_8h.html#aa3b70f6b0a87ecd706fc51bb3551977b">ARMBITREVINDEXTABLE_FIXED__128_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6c3b510a7d499dccaaea1ff164397ffb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_16[<a class="el" href="arm__common__tables_8h.html#a1dfdb9f7a5ad88ba7105c6cbc7e2c76e">ARMBITREVINDEXTABLE_FIXED___16_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad888a207e20f601ed80b2ad43428c8cc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_2048[<a class="el" href="arm__common__tables_8h.html#a7dbfc9019953b525d83184a50f9976cc">ARMBITREVINDEXTABLE_FIXED_2048_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a721d01114016584629f03c0af37dd21e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_256[<a class="el" href="arm__common__tables_8h.html#ac0711126d0e162366ec7d0ebcb2a4420">ARMBITREVINDEXTABLE_FIXED__256_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a59710415522cc38defa30402021f1f6b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_32[<a class="el" href="arm__common__tables_8h.html#aaa9ecdc043a73fa12c941cbe6613f9fa">ARMBITREVINDEXTABLE_FIXED___32_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2db644df1e878209441166cbb8d0db4f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_4096[<a class="el" href="arm__common__tables_8h.html#acbf48883fbb31d3dc71d232aa8e8f91f">ARMBITREVINDEXTABLE_FIXED_4096_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a03d1de7bfc5f318bc4fcfddd920bcb5a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_512[<a class="el" href="arm__common__tables_8h.html#a5486cba85dce51ffbfe6c0475882cc82">ARMBITREVINDEXTABLE_FIXED__512_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af9e1bbd7d535806a170786b069863b47"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevIndexTable_fixed_64[<a class="el" href="arm__common__tables_8h.html#ae53dc7c3198f9cfb5393e3a2644a12ac">ARMBITREVINDEXTABLE_FIXED___64_TABLE_LENGTH</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a56d3642e4ee33e3ada57ff11ecda1498"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> armRecipTableQ15[64]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>end of CFFT_CIFFT group </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga213ab1ee2e154cc2fa30d667b1994b89">arm_lms_norm_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="aae6056f6c4e8f7e494445196bf864479"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> armRecipTableQ31[64]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a1dec82d596780f1a66ef4f76f137c1d9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> sinTable_f32[<a class="el" href="arm__math_8h.html#afcb9147c96853bea484cfc2dde07463d">FAST_MATH_TABLE_SIZE</a>+1]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for the generation of the floating-point sine table: <pre>
tableSize = 512;    
for(n = 0; n &lt; (tableSize + 1); n++)    
{    
     sinTable[n]=sin(2*pi*n/tableSize);    
}</pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where pi value is 3.14159265358979 </dd></dl>

<p>Referenced by <a class="el" href="group__cos.html#gace15287f9c64b9b4084d1c797d4c49d8">arm_cos_f32()</a>, <a class="el" href="group___sin_cos.html#ga4420d45c37d58c310ef9ae1b5fe58020">arm_sin_cos_f32()</a>, and <a class="el" href="group__sin.html#gae164899c4a3fc0e946dc5d55555fe541">arm_sin_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a9cbcfe313f61add745ebfeddb4fecd55"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> sinTable_q15[<a class="el" href="arm__math_8h.html#afcb9147c96853bea484cfc2dde07463d">FAST_MATH_TABLE_SIZE</a>+1]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Table values are in Q15 (1.15 fixed-point format) and generation is done in three steps. First, generate sin values in floating point: <pre>
tableSize = 512;      
for(n = 0; n &lt; (tableSize + 1); n++)    
{    
     sinTable[n]= sin(2*pi*n/tableSize);    
} </pre> where pi value is 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Second, convert floating-point to Q15 (Fixed point): (sinTable[i] * pow(2, 15)) </dd></dl>
<dl class="section user"><dt></dt><dd>Finally, round to the nearest integer value: sinTable[i] += (sinTable[i] &gt; 0 ? 0.5 :-0.5); </dd></dl>

<p>Referenced by <a class="el" href="group__cos.html#gadfd60c24def501638c0d5db20f4c869b">arm_cos_q15()</a>, and <a class="el" href="group__sin.html#ga1fc6d6640be6cfa688a8bea0a48397ee">arm_sin_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a8bfccee9e1c0042cf4a765f4b19d097d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> sinTable_q31[<a class="el" href="arm__math_8h.html#afcb9147c96853bea484cfc2dde07463d">FAST_MATH_TABLE_SIZE</a>+1]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Table values are in Q31 (1.31 fixed-point format) and generation is done in three steps. First, generate sin values in floating point: <pre>
tableSize = 512;      
for(n = 0; n &lt; (tableSize + 1); n++)    
{    
     sinTable[n]= sin(2*pi*n/tableSize);    
} </pre> where pi value is 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Second, convert floating-point to Q31 (Fixed point): (sinTable[i] * pow(2, 31)) </dd></dl>
<dl class="section user"><dt></dt><dd>Finally, round to the nearest integer value: sinTable[i] += (sinTable[i] &gt; 0 ? 0.5 :-0.5); </dd></dl>

<p>Referenced by <a class="el" href="group__cos.html#gad80f121949ef885a77d83ab36e002567">arm_cos_q31()</a>, <a class="el" href="group___sin_cos.html#gae9e4ddebff9d4eb5d0a093e28e0bc504">arm_sin_cos_q31()</a>, and <a class="el" href="group__sin.html#ga57aade7d8892585992cdc6375bd82f9c">arm_sin_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="aa7d8d3aa9898d557385748a13c959a4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_rfft_1024[1024]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="af089dd2fe1a543d40a3325982bf45e7c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_rfft_128[128]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a749a5995ebd433a163f7adc474dabcaa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_rfft_2048[2048]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a5c5c161dd469d8e6806664956dae31f9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_rfft_256[256]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a5992afe8574289cd71921651b80bd57d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_rfft_32[32]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point RFFT Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>TW = exp(2*pi*i*[0:L/2-1]/L - pi/2*i).' </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>Real and Imag values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a8013d68dd2476c86b77173bb98b87b29"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_rfft_4096[4096]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a94bd2fc98798f87003fef5cd0c04d1f5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_rfft_512[512]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a2759d8789e1e6ae2ba7fb8d7f5e9c2ab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_rfft_64[64]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_856524284ebe840938865dc061f982fb.html">Include</a></li><li class="navelem"><a class="el" href="arm__common__tables_8h.html">arm_common_tables.h</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
