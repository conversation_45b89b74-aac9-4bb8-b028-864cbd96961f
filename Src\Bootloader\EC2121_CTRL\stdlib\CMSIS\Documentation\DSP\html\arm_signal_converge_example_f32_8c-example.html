<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_signal_converge_example_f32.c</title>
<title>CMSIS-DSP: arm_signal_converge_example_f32.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_signal_converge_example_f32_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_signal_converge_example_f32.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* $Date:         17. January 2013</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.0</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment">* Title:         arm_signal_converge_example_f32.c</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Description:   Example code demonstrating convergence of an adaptive</span></div>
<div class="line"><span class="comment">*                filter.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment"> * -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;math_helper.h&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** Global defines for the simulation</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#define TEST_LENGTH_SAMPLES 1536</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define NUMTAPS               32</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define BLOCKSIZE             32</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define DELTA_ERROR         0.000001f</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define DELTA_COEFF         0.0001f</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define MU                  0.5f</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor">#define NUMFRAMES (TEST_LENGTH_SAMPLES / BLOCKSIZE)</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Declare FIR state buffers and structure</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a358ec4e79689e6d3787b89fe78bdb772">firStateF32</a>[<a name="a1"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a> + <a name="a2"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>];</div>
<div class="line"><a name="_a3"></a><a class="code" href="structarm__fir__instance__f32.html" title="Instance structure for the floating-point FIR filter.">arm_fir_instance_f32</a> <a name="a4"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a652d3507a776117b4860b3e18f2d2d64">LPF_instance</a>;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Declare LMSNorm state buffers and structure</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a5"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a706980f6f654d199c61e08e7814bd0a1">lmsStateF32</a>[<a class="code" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a> + <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>];</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a6"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a276e8a27484cf9389dabf047e76992ed">errOutput</a>[<a name="a7"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>];</div>
<div class="line"><a name="_a8"></a><a class="code" href="structarm__lms__norm__instance__f32.html" title="Instance structure for the floating-point normalized LMS filter.">arm_lms_norm_instance_f32</a> <a name="a9"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a519f9b4db839245f3bf2075ff4c17605">lmsNorm_instance</a>;</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Function Declarations for Signal Convergence Example</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a10"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#ac786d43cbc17bb09738447034ff8e22e">test_signal_converge_example</a>( <span class="keywordtype">void</span> );</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Internal functions</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a11"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a8f521e839d4fad24a4f12a18dfeae5d4">test_signal_converge</a>(<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>* <a name="a12"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a>,</div>
<div class="line">                        uint32_t <a name="a13"></a><a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>);</div>
<div class="line"></div>
<div class="line"><span class="keywordtype">void</span> <a name="a14"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#afd2975c4763ec935771e6f63bfe7758b">getinput</a>(<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>* input,</div>
<div class="line">     uint32_t fr_cnt,</div>
<div class="line">          uint32_t <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>);</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* External Declarations for FIR F32 module Test</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a15"></a><a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>[<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>];</div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a16"></a><a class="code" href="arm__signal__converge__data_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a>[32];</div>
<div class="line"><span class="keyword">extern</span> <span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a17"></a><a class="code" href="arm__signal__converge__data_8c.html#aede8780f021b7f5c33df0c5ee2183ee6">FIRCoeff_f32</a>[32];</div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="structarm__lms__norm__instance__f32.html" title="Instance structure for the floating-point normalized LMS filter.">arm_lms_norm_instance_f32</a> <a class="code" href="arm__signal__converge__example__f32_8c.html#a519f9b4db839245f3bf2075ff4c17605">lmsNorm_instance</a>;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Declare I/O buffers</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a18"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a>[<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>];</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a19"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>[<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>];</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a20"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>[<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>];</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a class="code" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a>[<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Signal converge test</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">int32_t <a name="a21"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">  uint32_t i;</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a22"></a><a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>;</div>
<div class="line">  uint32_t index;</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> minValue;</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialize the LMSNorm data structure */</span></div>
<div class="line">  <a name="a23"></a><a class="code" href="group___l_m_s___n_o_r_m.html#gac7ccbaea863882056eee815456464670" title="Initialization function for floating-point normalized LMS filter.">arm_lms_norm_init_f32</a>(&amp;lmsNorm_instance, <a class="code" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>, <a class="code" href="arm__signal__converge__data_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#a706980f6f654d199c61e08e7814bd0a1">lmsStateF32</a>, <a name="a24"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a09bc9e6a44f0291cfcf578f2efcddfab">MU</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialize the FIR data structure */</span></div>
<div class="line">  <a name="a25"></a><a class="code" href="group___f_i_r.html#ga98d13def6427e29522829f945d0967db" title="Initialization function for the floating-point FIR filter.">arm_fir_init_f32</a>(&amp;LPF_instance, <a class="code" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>, (<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="arm__signal__converge__data_8c.html#aede8780f021b7f5c33df0c5ee2183ee6">FIRCoeff_f32</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#a358ec4e79689e6d3787b89fe78bdb772">firStateF32</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  * Loop over the frames of data and execute each of the processing</span></div>
<div class="line"><span class="comment">  * functions in the system.</span></div>
<div class="line"><span class="comment">  * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">for</span>(i=0; i &lt; <a name="a26"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a4b6b859e1e3f6021a360390be287ca2c">NUMFRAMES</a>; i++)</div>
<div class="line">  {</div>
<div class="line">    <span class="comment">/* Read the input data - uniformly distributed random noise - into wire1 */</span></div>
<div class="line">    <a name="a27"></a><a class="code" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3" title="Copies the elements of a floating-point vector.">arm_copy_f32</a>(<a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a> + (i * <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>), <a class="code" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a>, BLOCKSIZE);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* Execute the FIR processing function.  Input wire1 and output wire2 */</span></div>
<div class="line">    <a name="a28"></a><a class="code" href="group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a" title="Processing function for the floating-point FIR filter.">arm_fir_f32</a>(&amp;LPF_instance, <a class="code" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* Execute the LMS Norm processing function*/</span></div>
<div class="line"></div>
<div class="line">    <a name="a29"></a><a class="code" href="group___l_m_s___n_o_r_m.html#ga2418c929087c6eba719758eaae3f3300" title="Processing function for floating-point normalized LMS filter.">arm_lms_norm_f32</a>(&amp;lmsNorm_instance, <span class="comment">/* LMSNorm instance */</span></div>
<div class="line">         <a class="code" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a>,                         <span class="comment">/* Input signal */</span></div>
<div class="line">         <a class="code" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>,                         <span class="comment">/* Reference Signal */</span></div>
<div class="line">         <a class="code" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>,                         <span class="comment">/* Converged Signal */</span></div>
<div class="line">         <a class="code" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a>,                    <span class="comment">/* Error Signal, this will become small as the signal converges */</span></div>
<div class="line">         <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);                    <span class="comment">/* BlockSize */</span></div>
<div class="line"></div>
<div class="line">    <span class="comment">/* apply overall gain */</span></div>
<div class="line">    <a name="a30"></a><a class="code" href="group__scale.html#ga3487af88b112f682ee90589cd419e123" title="Multiplies a floating-point vector by a scalar.">arm_scale_f32</a>(<a class="code" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>, 5, <a class="code" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);   <span class="comment">/* in-place buffer */</span></div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  status = <a name="a31"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>;</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* -------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  * Test whether the error signal has reached towards 0.</span></div>
<div class="line"><span class="comment">  * ----------------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">  <a name="a32"></a><a class="code" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02" title="Floating-point vector absolute value.">arm_abs_f32</a>(<a class="code" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line">  <a name="a33"></a><a class="code" href="group___min.html#gaf62b1673740fc516ea64daf777b7d74a" title="Minimum value of a floating-point vector.">arm_min_f32</a>(<a class="code" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>, &amp;minValue, &amp;index);</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span> (minValue &gt; <a name="a34"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a6d3c6a4484dcaac72fbfe5100c39b9b6">DELTA_ERROR</a>)</div>
<div class="line">  {</div>
<div class="line">    status = <a name="a35"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>;</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  * Test whether the filter coefficients have converged.</span></div>
<div class="line"><span class="comment">  * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">  <a name="a36"></a><a class="code" href="group___basic_sub.html#ga7f975a472de286331134227c08aad826" title="Floating-point vector subtraction.">arm_sub_f32</a>((<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="arm__signal__converge__data_8c.html#aede8780f021b7f5c33df0c5ee2183ee6">FIRCoeff_f32</a>, <a class="code" href="arm__signal__converge__data_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a>, <a class="code" href="arm__signal__converge__data_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>);</div>
<div class="line"></div>
<div class="line">  <a class="code" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02" title="Floating-point vector absolute value.">arm_abs_f32</a>(<a class="code" href="arm__signal__converge__data_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a>, <a class="code" href="arm__signal__converge__data_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>);</div>
<div class="line">  <a class="code" href="group___min.html#gaf62b1673740fc516ea64daf777b7d74a" title="Minimum value of a floating-point vector.">arm_min_f32</a>(<a class="code" href="arm__signal__converge__data_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>, &amp;minValue, &amp;index);</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span> (minValue &gt; <a name="a37"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a9156349d99957ded15d8aa3aa11723de">DELTA_COEFF</a>)</div>
<div class="line">  {</div>
<div class="line">    status = <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>;</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  * Loop here if the signals did not pass the convergence check.</span></div>
<div class="line"><span class="comment">  * This denotes a test failure</span></div>
<div class="line"><span class="comment">  * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span>( status != <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>)</div>
<div class="line">  {</div>
<div class="line">    <span class="keywordflow">while</span>(1);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
<div class="line"></div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
