<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_fir_example_f32.c File Reference</title>
<title>CMSIS-DSP: arm_fir_example_f32.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__fir__example__f32_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">arm_fir_example_f32.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:abc004a7fade488e72310fd96c0a101dc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a></td></tr>
<tr class="separator:abc004a7fade488e72310fd96c0a101dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7d1dd4deffa8e7ed6429e5dd0fe1812"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#af7d1dd4deffa8e7ed6429e5dd0fe1812">SNR_THRESHOLD_F32</a></td></tr>
<tr class="separator:af7d1dd4deffa8e7ed6429e5dd0fe1812"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad51ded0bbd705f02f73fc60c0b721ced"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#ad51ded0bbd705f02f73fc60c0b721ced">BLOCK_SIZE</a></td></tr>
<tr class="separator:ad51ded0bbd705f02f73fc60c0b721ced"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7579d94e0a80fb9d376ea6c7897f73b0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a></td></tr>
<tr class="separator:a7579d94e0a80fb9d376ea6c7897f73b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a52d2cba30e6946c95578be946ac12a65"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main</a> (void)</td></tr>
<tr class="separator:a52d2cba30e6946c95578be946ac12a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a35d190391c204b677e2839d76ede6e8b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#a35d190391c204b677e2839d76ede6e8b">testInput_f32_1kHz_15kHz</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td></tr>
<tr class="separator:a35d190391c204b677e2839d76ede6e8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12d0acaa028f1dcd964d2d188e7df331"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#a12d0acaa028f1dcd964d2d188e7df331">refOutput</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td></tr>
<tr class="separator:a12d0acaa028f1dcd964d2d188e7df331"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd4d61aad5f35a4e42d580004e2f9a1d"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td></tr>
<tr class="separator:afd4d61aad5f35a4e42d580004e2f9a1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46d61cabe5cb207f2776e1d4f8ca0f38"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#a46d61cabe5cb207f2776e1d4f8ca0f38">firStateF32</a> [<a class="el" href="arm__fir__example__f32_8c.html#ad51ded0bbd705f02f73fc60c0b721ced">BLOCK_SIZE</a>+<a class="el" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a>-1]</td></tr>
<tr class="separator:a46d61cabe5cb207f2776e1d4f8ca0f38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae070afd14f437ad1ae0a947e4403dd0e"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#ae070afd14f437ad1ae0a947e4403dd0e">firCoeffs32</a> [<a class="el" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a>]</td></tr>
<tr class="separator:ae070afd14f437ad1ae0a947e4403dd0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6558f40a619c2502fbc24c880fd4fb0"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a></td></tr>
<tr class="separator:ab6558f40a619c2502fbc24c880fd4fb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7d5613bda9a19b2ccae5d6cb79a22bc"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#af7d5613bda9a19b2ccae5d6cb79a22bc">numBlocks</a></td></tr>
<tr class="separator:af7d5613bda9a19b2ccae5d6cb79a22bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af06013f588a7003278de222913c9d819"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fir__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a></td></tr>
<tr class="separator:af06013f588a7003278de222913c9d819"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="ad51ded0bbd705f02f73fc60c0b721ced"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define BLOCK_SIZE</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fir_example_f32_8c-example.html#a5">arm_fir_example_f32.c</a>.</dd>
</dl>
</div>
</div>
<a class="anchor" id="a7579d94e0a80fb9d376ea6c7897f73b0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NUM_TAPS</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fir_example_f32_8c-example.html#a6">arm_fir_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="af7d1dd4deffa8e7ed6429e5dd0fe1812"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SNR_THRESHOLD_F32</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fir_example_f32_8c-example.html#a17">arm_fir_example_f32.c</a>, and <a class="el" href="arm_graphic_equalizer_example_q31_8c-example.html#a30">arm_graphic_equalizer_example_q31.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="abc004a7fade488e72310fd96c0a101dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define TEST_LENGTH_SAMPLES</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="a52d2cba30e6946c95578be946ac12a65"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t main </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>References <a class="el" href="group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a">arm_fir_f32()</a>, <a class="el" href="group___f_i_r.html#ga98d13def6427e29522829f945d0967db">arm_fir_init_f32()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_snr_f32()</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="arm__fir__example__f32_8c.html#ae070afd14f437ad1ae0a947e4403dd0e">firCoeffs32</a>, <a class="el" href="arm__fir__example__f32_8c.html#a46d61cabe5cb207f2776e1d4f8ca0f38">firStateF32</a>, <a class="el" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a>, <a class="el" href="arm__fir__example__f32_8c.html#af7d5613bda9a19b2ccae5d6cb79a22bc">numBlocks</a>, <a class="el" href="arm__fir__data_8c.html#aa7570f8c2e7a3c929d9d32a14a51389f">refOutput</a>, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a>, <a class="el" href="arm__fir__example__f32_8c.html#af7d1dd4deffa8e7ed6429e5dd0fe1812">SNR_THRESHOLD_F32</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="arm__fir__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>, <a class="el" href="arm__fir__data_8c.html#a143154a165358f0016714cb7f1c83970">testInput_f32_1kHz_15kHz</a>, and <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="ab6558f40a619c2502fbc24c880fd4fb0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t blockSize</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fir_example_f32_8c-example.html#a8">arm_fir_example_f32.c</a>, <a class="el" href="arm_signal_converge_example_f32_8c-example.html#a13">arm_signal_converge_example_f32.c</a>, <a class="el" href="arm_sin_cos_example_f32_8c-example.html#a3">arm_sin_cos_example_f32.c</a>, and <a class="el" href="arm_variance_example_f32_8c-example.html#a5">arm_variance_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02">arm_abs_f32()</a>, <a class="el" href="group___basic_abs.html#ga39f92964c9b649ba252e26ebe7b95594">arm_abs_q15()</a>, <a class="el" href="group___basic_abs.html#ga59eafcdcdb52da60d37f20aec6ff4577">arm_abs_q31()</a>, <a class="el" href="group___basic_abs.html#gadc30985e33fbf96802a5a7954dece3b1">arm_abs_q7()</a>, <a class="el" href="group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd">arm_add_f32()</a>, <a class="el" href="group___basic_add.html#gabb51285a41f511670bbff62fc0e1bf62">arm_add_q15()</a>, <a class="el" href="group___basic_add.html#ga24d6c3f7f8b9fae4847c0c3f26a39a3b">arm_add_q31()</a>, <a class="el" href="group___basic_add.html#gaed633f415a7840a66861debca2dfb96b">arm_add_q7()</a>, <a class="el" href="group___biquad_cascade_d_f1__32x64.html#ga953a83e69685de6575cff37feb358a93">arm_biquad_cas_df1_32x64_q31()</a>, <a class="el" href="group___biquad_cascade_d_f1.html#gaa0dbe330d763e3c1d8030b3ef12d5bdc">arm_biquad_cascade_df1_f32()</a>, <a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f">arm_biquad_cascade_df1_q31()</a>, <a class="el" href="group___biquad_cascade_d_f2_t.html#ga114f373fbc16a314e9f293c7c7649c7f">arm_biquad_cascade_df2T_f32()</a>, <a class="el" href="group___biquad_cascade_d_f2_t.html#gaa8735dda5f3f36d0936283794c2aa771">arm_biquad_cascade_df2T_f64()</a>, <a class="el" href="group___biquad_cascade_d_f2_t.html#gac75de449c3e4f733477d81bd0ada5eec">arm_biquad_cascade_stereo_df2T_f32()</a>, <a class="el" href="arm__math_8h.html#ae469fac5e1df35f8bcf1b3d7c3136484">arm_circularRead_f32()</a>, <a class="el" href="arm__math_8h.html#ad5fb134f83f2c802261f172e3dceb131">arm_circularRead_q15()</a>, <a class="el" href="arm__math_8h.html#a30aa80ea20abe71f3afa99f2f0391ed5">arm_circularRead_q7()</a>, <a class="el" href="arm__math_8h.html#a6ff56c0896ce00712ba8f2fcf72cacd3">arm_circularWrite_f32()</a>, <a class="el" href="arm__math_8h.html#a3ba2d215477e692def7fda46dda883ed">arm_circularWrite_q15()</a>, <a class="el" href="arm__math_8h.html#addba85b1f7fbd472fd00ddd9ce43aea8">arm_circularWrite_q7()</a>, <a class="el" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3">arm_copy_f32()</a>, <a class="el" href="group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0">arm_copy_q15()</a>, <a class="el" href="group__copy.html#gaddf70be7e3f87e535c324862b501f3f9">arm_copy_q31()</a>, <a class="el" href="group__copy.html#ga467579beda492aa92797529d794c88fb">arm_copy_q7()</a>, <a class="el" href="group__dot__prod.html#ga55418d4362f6ba84c327f9b4f089a8c3">arm_dot_prod_f32()</a>, <a class="el" href="group__dot__prod.html#ga436d5bed28a4b73b24acbde436a3044b">arm_dot_prod_q15()</a>, <a class="el" href="group__dot__prod.html#gab15d8fa060fc85b4d948d091b7deaa11">arm_dot_prod_q31()</a>, <a class="el" href="group__dot__prod.html#ga9c3293a50ac7ec8ba928bf8e3aaea6c1">arm_dot_prod_q7()</a>, <a class="el" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94">arm_fill_f32()</a>, <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_fill_q15()</a>, <a class="el" href="group___fill.html#ga69cc781cf337bd0a31bb85c772a35f7f">arm_fill_q31()</a>, <a class="el" href="group___fill.html#ga0465cf326ada039ed792f94b033d9ec5">arm_fill_q7()</a>, <a class="el" href="group___f_i_r___lattice.html#gae63a45a63a11a65f2eae8b8b1fe370a8">arm_fir_lattice_f32()</a>, <a class="el" href="group___f_i_r___lattice.html#gabb0ab07fd313b4d863070c3ddca51542">arm_fir_lattice_q15()</a>, <a class="el" href="group___f_i_r.html#gaadd938c68ab08967cbb5fc696f384bb5">arm_fir_q31()</a>, <a class="el" href="group___f_i_r.html#ga31c91a0bf0962327ef8f626fae68ea32">arm_fir_q7()</a>, <a class="el" href="group___f_i_r___sparse.html#ga23a9284de5ee39406713b91d18ac8838">arm_fir_sparse_f32()</a>, <a class="el" href="group___f_i_r___sparse.html#ga2bffda2e156e72427e19276cd9c3d3cc">arm_fir_sparse_q15()</a>, <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31()</a>, <a class="el" href="group___f_i_r___sparse.html#gae86c145efc2d9ec32dc6d8c1ad2ccb3c">arm_fir_sparse_q7()</a>, <a class="el" href="group__float__to__x.html#ga215456e35a18db86882e1d3f0d24e1f2">arm_float_to_q15()</a>, <a class="el" href="group__float__to__x.html#ga177704107f94564e9abe4daaa36f4554">arm_float_to_q31()</a>, <a class="el" href="group__float__to__x.html#ga44a393818cdee8dce80f2d66add25411">arm_float_to_q7()</a>, <a class="el" href="group___i_i_r___lattice.html#ga56164a0fe48619b8ceec160347bdd2ff">arm_iir_lattice_f32()</a>, <a class="el" href="group___i_i_r___lattice.html#gaeb9e9599a288832ed123183eaa8b294a">arm_iir_lattice_q15()</a>, <a class="el" href="group___i_i_r___lattice.html#ga123b26fa9156cd8d3622dd85931741ed">arm_iir_lattice_q31()</a>, <a class="el" href="group___l_m_s.html#gae266d009e682180421601627c79a3843">arm_lms_f32()</a>, <a class="el" href="group___l_m_s___n_o_r_m.html#ga2418c929087c6eba719758eaae3f3300">arm_lms_norm_f32()</a>, <a class="el" href="group___l_m_s___n_o_r_m.html#gad47486a399dedb0bc85a5990ec5cf981">arm_lms_norm_q15()</a>, <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>, <a class="el" href="group___l_m_s.html#gacde16c17eb75979f81b34e2e2a58c7ac">arm_lms_q15()</a>, <a class="el" href="group___l_m_s.html#ga6a0abfe6041253a6f91c63b383a64257">arm_lms_q31()</a>, <a class="el" href="group__mean.html#ga74ce08c49ab61e57bd50c3a0ca1fdb2b">arm_mean_f32()</a>, <a class="el" href="group__mean.html#gac882495d5f098819fd3939c1ef7795b3">arm_mean_q15()</a>, <a class="el" href="group__mean.html#gacf2526d8c2d75e486e8f0b0e31877ad0">arm_mean_q31()</a>, <a class="el" href="group__mean.html#gaebc707ee539020357c25da4c75b52eb7">arm_mean_q7()</a>, <a class="el" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32">arm_mult_f32()</a>, <a class="el" href="group___basic_mult.html#gafb0778d27ed98a2a6f2ecb7d48cc8c75">arm_mult_q15()</a>, <a class="el" href="group___basic_mult.html#ga3528c0f54a0607acc603f0490d3ca6c6">arm_mult_q31()</a>, <a class="el" href="group___basic_mult.html#ga16677275ed83ff0878da531e875c27ef">arm_mult_q7()</a>, <a class="el" href="group__negate.html#ga2e169c4de6cc6e3ba4be9473531e6657">arm_negate_f32()</a>, <a class="el" href="group__negate.html#ga0239a833d72cf00290b9723c394e5042">arm_negate_q15()</a>, <a class="el" href="group__negate.html#ga2784c6887686a73dc7c364e2e41c776c">arm_negate_q31()</a>, <a class="el" href="group__negate.html#gaae78fc079a43bdaa3055f9b32e2a1f4c">arm_negate_q7()</a>, <a class="el" href="group__offset.html#ga989dfae15235799d82f62ef9d356abb4">arm_offset_f32()</a>, <a class="el" href="group__offset.html#gab4c1d2391b599549e5a06fdfbc2747bf">arm_offset_q15()</a>, <a class="el" href="group__offset.html#gac84ec42cbbebc5c197a87d0221819acf">arm_offset_q31()</a>, <a class="el" href="group__offset.html#ga00bd9cc17c5bf905e76c91ad50886393">arm_offset_q7()</a>, <a class="el" href="group__power.html#ga993c00dd7f661d66bdb6e58426e893aa">arm_power_f32()</a>, <a class="el" href="group__power.html#ga7050c04b7515e01a75c38f1abbaf71ba">arm_power_q15()</a>, <a class="el" href="group__power.html#ga0b93d31bb5b5ed214c2b94d8a7744cd2">arm_power_q31()</a>, <a class="el" href="group__power.html#gaf969c85c5655e3d72d7b99ff188f92c9">arm_power_q7()</a>, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_provide_guard_bits_q15()</a>, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_provide_guard_bits_q31()</a>, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029">arm_provide_guard_bits_q7()</a>, <a class="el" href="group__q15__to__x.html#gaf8b0d2324de273fc430b0e61ad4e9eb2">arm_q15_to_float()</a>, <a class="el" href="group__q15__to__x.html#ga7ba2d87366990ad5380439e2b4a4c0a5">arm_q15_to_q31()</a>, <a class="el" href="group__q15__to__x.html#ga8fb31855ff8cce09c2ec9308f48ded69">arm_q15_to_q7()</a>, <a class="el" href="group__q31__to__x.html#gacf407b007a37da18e99dabd9023c56b4">arm_q31_to_float()</a>, <a class="el" href="group__q31__to__x.html#ga901dede4661365c9e7c630d3eb31c32c">arm_q31_to_q15()</a>, <a class="el" href="group__q31__to__x.html#ga7f297d1a7d776805395095fdb24a8071">arm_q31_to_q7()</a>, <a class="el" href="group__q7__to__x.html#ga656620f957b65512ed83db03fd455ec5">arm_q7_to_float()</a>, <a class="el" href="group__q7__to__x.html#gabc02597fc3f01033daf43ec0547a2f78">arm_q7_to_q15()</a>, <a class="el" href="group__q7__to__x.html#gad8958cd3cb7f521466168b46a25b7908">arm_q7_to_q31()</a>, <a class="el" href="group___r_m_s.html#ga0e3ab1b57da32d45388d1fa90d7fd88c">arm_rms_f32()</a>, <a class="el" href="group___r_m_s.html#gaf5b836b72dda9e5dfbbd17c7906fd13f">arm_rms_q15()</a>, <a class="el" href="group___r_m_s.html#gae33015fda23fc44e7ead5e5ed7e8d314">arm_rms_q31()</a>, <a class="el" href="group__scale.html#ga3487af88b112f682ee90589cd419e123">arm_scale_f32()</a>, <a class="el" href="group__scale.html#gafaac0e1927daffeb68a42719b53ea780">arm_scale_q15()</a>, <a class="el" href="group__scale.html#ga83e36cd82bf51ce35406a199e477d47c">arm_scale_q31()</a>, <a class="el" href="group__scale.html#gabc9fd3d37904c58df56492b351d21fb0">arm_scale_q7()</a>, <a class="el" href="group__shift.html#gaa1757e53279780107acc92cf100adb61">arm_shift_q15()</a>, <a class="el" href="group__shift.html#ga387dd8b7b87377378280978f16cdb13d">arm_shift_q31()</a>, <a class="el" href="group__shift.html#ga47295d08a685f7de700a48dafb4db6fb">arm_shift_q7()</a>, <a class="el" href="group___s_t_d.html#ga4969b5b5f3d001377bc401a3ee99dfc2">arm_std_f32()</a>, <a class="el" href="group___s_t_d.html#gaf9d27afa9928ff28a63cd98ea9218a72">arm_std_q15()</a>, <a class="el" href="group___s_t_d.html#ga39495e74f96116178be085c9dc7742f5">arm_std_q31()</a>, <a class="el" href="group___basic_sub.html#ga7f975a472de286331134227c08aad826">arm_sub_f32()</a>, <a class="el" href="group___basic_sub.html#ga997a8ee93088d15bda23c325d455b588">arm_sub_q15()</a>, <a class="el" href="group___basic_sub.html#ga28aa6908d092752144413e21933dc878">arm_sub_q31()</a>, <a class="el" href="group___basic_sub.html#gab09941de7dfeb247e5c29b406a435fcc">arm_sub_q7()</a>, <a class="el" href="group__variance.html#ga393f26c5a3bfa05624fb8d32232a6d96">arm_var_f32()</a>, <a class="el" href="group__variance.html#ga79dce009ed2de28a125aeb3f19631654">arm_var_q15()</a>, <a class="el" href="group__variance.html#gac02873f1c2cc80adfd799305f0e6465d">arm_var_q31()</a>, and <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ae070afd14f437ad1ae0a947e4403dd0e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> firCoeffs32[<a class="el" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fir_example_f32_8c-example.html#a7">arm_fir_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a46d61cabe5cb207f2776e1d4f8ca0f38"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> firStateF32[<a class="el" href="arm__fir__example__f32_8c.html#ad51ded0bbd705f02f73fc60c0b721ced">BLOCK_SIZE</a>+<a class="el" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a>-1]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="af7d5613bda9a19b2ccae5d6cb79a22bc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t numBlocks</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fir_example_f32_8c-example.html#a9">arm_fir_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a12d0acaa028f1dcd964d2d188e7df331"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> refOutput[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="af06013f588a7003278de222913c9d819"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> snr</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a35d190391c204b677e2839d76ede6e8b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testInput_f32_1kHz_15kHz[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__fir__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="afd4d61aad5f35a4e42d580004e2f9a1d"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testOutput[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_50f4d4f91ce5cd72cb6928b47e85a7f8.html">Examples</a></li><li class="navelem"><a class="el" href="dir_dcc7392e27ceedcb8fca5c4cd07c4b5c.html">arm_fir_example</a></li><li class="navelem"><a class="el" href="dir_68b896fe322128a858fd31091509f536.html">ARM</a></li><li class="navelem"><a class="el" href="arm__fir__example__f32_8c.html">arm_fir_example_f32.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
