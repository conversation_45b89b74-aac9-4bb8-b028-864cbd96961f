var structarm__cfft__radix2__instance__f32 =
[
    [ "bitReverseFlag", "structarm__cfft__radix2__instance__f32.html#af713b4ac5256a19bc965c89fe3005fa3", null ],
    [ "bitRevFactor", "structarm__cfft__radix2__instance__f32.html#ac1688dafa5177f6b1505abbfd0cf8b21", null ],
    [ "fftLen", "structarm__cfft__radix2__instance__f32.html#a2f915a1c29635c1623086aaaa726be8f", null ],
    [ "ifftFlag", "structarm__cfft__radix2__instance__f32.html#a8dbe98d2c924e35e0a3fed2fe948176f", null ],
    [ "onebyfftLen", "structarm__cfft__radix2__instance__f32.html#a1d3d289d47443e597d88a40effd14b8f", null ],
    [ "pBitRevTable", "structarm__cfft__radix2__instance__f32.html#a92b8fa0a151cd800436094903a5ca0a4", null ],
    [ "pTwiddle", "structarm__cfft__radix2__instance__f32.html#adb0c9d47dbfbd90a6f6ed0a05313a974", null ],
    [ "twidCoefModifier", "structarm__cfft__radix2__instance__f32.html#a411f75b6ed01690293f4f5988030ea42", null ]
];