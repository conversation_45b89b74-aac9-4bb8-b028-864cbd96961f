<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Data Fields</title>
<title>CMSIS-DSP: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6b.html#index_k"><span>k</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li class="current"><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions_0x70.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a class="anchor" id="index_p"></a>- p -</h3><ul>
<li>pBitRevTable
: <a class="el" href="structarm__cfft__radix2__instance__q15.html#ab88afeff6493be3c8b5e4530efa82d51">arm_cfft_radix2_instance_q15</a>
, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a4acf704ae0cf30b53bf0fbfae8e34a59">arm_cfft_radix4_instance_q15</a>
, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a33a3bc774c97373261699463c05dfe54">arm_cfft_radix4_instance_q31</a>
, <a class="el" href="structarm__cfft__instance__q31.html#a8a464461649f023325ced1e10470f5d0">arm_cfft_instance_q31</a>
, <a class="el" href="structarm__cfft__instance__f32.html#a21ceaf59a1bb8440af57c28d2dd9bbab">arm_cfft_instance_f32</a>
, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a92b8fa0a151cd800436094903a5ca0a4">arm_cfft_radix2_instance_f32</a>
, <a class="el" href="structarm__cfft__radix2__instance__q31.html#ada8e5264f4b22ff4c621817978994674">arm_cfft_radix2_instance_q31</a>
, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a8da0d2ca69749fde8cbb95caeac6fe6a">arm_cfft_radix4_instance_f32</a>
, <a class="el" href="structarm__cfft__instance__q15.html#ac9160b80243b99a0b6e2f75ddb5cf0ae">arm_cfft_instance_q15</a>
</li>
<li>pCfft
: <a class="el" href="structarm__rfft__instance__q15.html#a4329c15b056444746d37ff082a24d31a">arm_rfft_instance_q15</a>
, <a class="el" href="structarm__rfft__instance__q31.html#a8fe10d425b59e096c23aa4bb5caa1974">arm_rfft_instance_q31</a>
, <a class="el" href="structarm__rfft__instance__f32.html#a9f47ba9f50c81e4445ae3827b981bc05">arm_rfft_instance_f32</a>
, <a class="el" href="structarm__dct4__instance__f32.html#a018f7860b6e070af533fb7d76c7cdc32">arm_dct4_instance_f32</a>
, <a class="el" href="structarm__dct4__instance__q31.html#ac96579cfb28d08bb11dd2fe4c6303833">arm_dct4_instance_q31</a>
, <a class="el" href="structarm__dct4__instance__q15.html#a7284932ee8c36107c33815eb62eadffc">arm_dct4_instance_q15</a>
</li>
<li>pCoeffs
: <a class="el" href="structarm__fir__instance__q7.html#a0e45aedefc3fffad6cb315c5b6e5bd49">arm_fir_instance_q7</a>
, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#ae2f0180f9038c0393e1d6921bb3b878b">arm_biquad_cascade_df2T_instance_f64</a>
, <a class="el" href="structarm__fir__lattice__instance__q15.html#a78f872826140069cf67836fff87360bc">arm_fir_lattice_instance_q15</a>
, <a class="el" href="structarm__fir__instance__q15.html#a6d16db16a5f8f0db54938f2967244d9e">arm_fir_instance_q15</a>
, <a class="el" href="structarm__fir__lattice__instance__q31.html#a66c3364bf5863cd45e05f1652c3dc522">arm_fir_lattice_instance_q31</a>
, <a class="el" href="structarm__fir__lattice__instance__f32.html#a33bf5948c947f9ef80a99717cb0a0a43">arm_fir_lattice_instance_f32</a>
, <a class="el" href="structarm__fir__instance__q31.html#afaae4c884bdf11a4ec2f3b9bb2bb51d0">arm_fir_instance_q31</a>
, <a class="el" href="structarm__lms__instance__f32.html#a4795c6f7d3f17cec15c2fd09f66edd1a">arm_lms_instance_f32</a>
, <a class="el" href="structarm__lms__instance__q15.html#a42f95368b94898eb82608e1113d18cab">arm_lms_instance_q15</a>
, <a class="el" href="structarm__fir__instance__f32.html#a1c9cfca901d5902afeb640f2831488f4">arm_fir_instance_f32</a>
, <a class="el" href="structarm__lms__instance__q31.html#a4afe56e991a5416adfd462aa88bda500">arm_lms_instance_q31</a>
, <a class="el" href="structarm__lms__norm__instance__f32.html#a1ba688d90aba7de003ed4ad8e2e7ddda">arm_lms_norm_instance_f32</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#a1edaacdebb5b09d7635bf20c779855fc">arm_biquad_casd_df1_inst_q15</a>
, <a class="el" href="structarm__lms__norm__instance__q31.html#a57a64c1ff102d033c1bd05043f1d9955">arm_lms_norm_instance_q31</a>
, <a class="el" href="structarm__lms__norm__instance__q15.html#ae7bca648c75a2ffa02d87852bb78bc8a">arm_lms_norm_instance_q15</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#aa62366c632f3b5305086f841f079dbd2">arm_biquad_casd_df1_inst_q31</a>
, <a class="el" href="structarm__fir__sparse__instance__f32.html#a04af7c738dfb0882ad102fcad501d94a">arm_fir_sparse_instance_f32</a>
, <a class="el" href="structarm__fir__sparse__instance__q31.html#a093d6227f0d1597982cd083fb126f4e0">arm_fir_sparse_instance_q31</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#af9df3820576fb921809d1462c9c6d16c">arm_biquad_casd_df1_inst_f32</a>
, <a class="el" href="structarm__fir__sparse__instance__q15.html#a78a6565473b5f0b8c77c3f0f58a76069">arm_fir_sparse_instance_q15</a>
, <a class="el" href="structarm__fir__sparse__instance__q7.html#a3dac86f15e33553e8f3e19e0d712bae5">arm_fir_sparse_instance_q7</a>
, <a class="el" href="structarm__fir__decimate__instance__q15.html#a01cacab67e73945e8289075598ede14d">arm_fir_decimate_instance_q15</a>
, <a class="el" href="structarm__fir__decimate__instance__q31.html#a030d0391538c2481c5b348fd09a952ff">arm_fir_decimate_instance_q31</a>
, <a class="el" href="structarm__fir__decimate__instance__f32.html#a268a8b0e80a3d9764baf33e4bc10dde2">arm_fir_decimate_instance_f32</a>
, <a class="el" href="structarm__fir__interpolate__instance__q15.html#a767d91d61d4c0beeddd4325d28d28e24">arm_fir_interpolate_instance_q15</a>
, <a class="el" href="structarm__fir__interpolate__instance__q31.html#afa719433687e1936ec3403d0d32f06e6">arm_fir_interpolate_instance_q31</a>
, <a class="el" href="structarm__fir__interpolate__instance__f32.html#a86053b715980a93c9df630d6de5bb63c">arm_fir_interpolate_instance_f32</a>
, <a class="el" href="structarm__biquad__cas__df1__32x64__ins__q31.html#a490462d6ebe0fecfb6acbf51bed22ecf">arm_biquad_cas_df1_32x64_ins_q31</a>
, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a49a24fe1b6ad3b0b26779c32d8d80b2e">arm_biquad_cascade_df2T_instance_f32</a>
, <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a58b15644de62a632c5e9d4a563569dc6">arm_biquad_cascade_stereo_df2T_instance_f32</a>
</li>
<li>pCosFactor
: <a class="el" href="structarm__dct4__instance__f32.html#a6da1187e070801e011ce5e0582efa861">arm_dct4_instance_f32</a>
, <a class="el" href="structarm__dct4__instance__q31.html#af97204d1838925621fc82021a0c2d6c1">arm_dct4_instance_q31</a>
, <a class="el" href="structarm__dct4__instance__q15.html#ac76df681b1bd502fb4874c06f055dded">arm_dct4_instance_q15</a>
</li>
<li>pData
: <a class="el" href="structarm__matrix__instance__f32.html#af3917c032600a9dfd5ed4a96f074910a">arm_matrix_instance_f32</a>
, <a class="el" href="structarm__matrix__instance__f64.html#a5b2475f8ff1e4818955cdd18bc40a097">arm_matrix_instance_f64</a>
, <a class="el" href="structarm__matrix__instance__q15.html#a6da33a5553e634787d0f515cf8d724af">arm_matrix_instance_q15</a>
, <a class="el" href="structarm__matrix__instance__q31.html#a09a64267c0579fef086efc9059741e56">arm_matrix_instance_q31</a>
, <a class="el" href="structarm__bilinear__interp__instance__f32.html#afd1e764591c991c212d56c893efb5ea4">arm_bilinear_interp_instance_f32</a>
, <a class="el" href="structarm__bilinear__interp__instance__q31.html#a843eae0c9db5f815e77e1aaf9afea358">arm_bilinear_interp_instance_q31</a>
, <a class="el" href="structarm__bilinear__interp__instance__q15.html#a50d75b1316cee3e0dfad6dcc4c9a2954">arm_bilinear_interp_instance_q15</a>
, <a class="el" href="structarm__bilinear__interp__instance__q7.html#af05194d691bbefb02c34bafb22ca9ef0">arm_bilinear_interp_instance_q7</a>
</li>
<li>phaseLength
: <a class="el" href="structarm__fir__interpolate__instance__q15.html#ad5178a02a697a77e0d0e60705d9f0a19">arm_fir_interpolate_instance_q15</a>
, <a class="el" href="structarm__fir__interpolate__instance__q31.html#a5d243796584afc7cd6c557f00b7acca5">arm_fir_interpolate_instance_q31</a>
, <a class="el" href="structarm__fir__interpolate__instance__f32.html#a389e669e13ec56292a70db8e92194b12">arm_fir_interpolate_instance_f32</a>
</li>
<li>pkCoeffs
: <a class="el" href="structarm__iir__lattice__instance__q15.html#a41c214a1ec38d4a82fae8899d715dd29">arm_iir_lattice_instance_q15</a>
, <a class="el" href="structarm__iir__lattice__instance__q31.html#a1d30aa16aac7722936ea9dee59211863">arm_iir_lattice_instance_q31</a>
, <a class="el" href="structarm__iir__lattice__instance__f32.html#aa69fcdd3775e828d450ce1bbd978fa31">arm_iir_lattice_instance_f32</a>
</li>
<li>postShift
: <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a636c7fbe09ec4bef0bc0a4b4e2151cbe">arm_biquad_casd_df1_inst_q31</a>
, <a class="el" href="structarm__biquad__cas__df1__32x64__ins__q31.html#a8e9d58e8dba5aa3b2fc4f36d2ed07996">arm_biquad_cas_df1_32x64_ins_q31</a>
, <a class="el" href="structarm__lms__instance__q15.html#acca5fbaef4a52ae411de24c9a0b929cf">arm_lms_instance_q15</a>
, <a class="el" href="structarm__lms__instance__q31.html#a4705a8f0011bb9166e09bf5bd51e595e">arm_lms_instance_q31</a>
, <a class="el" href="structarm__lms__norm__instance__q31.html#a28d7b9e437817f83397e081967e90f3c">arm_lms_norm_instance_q31</a>
, <a class="el" href="structarm__lms__norm__instance__q15.html#aa0d435fbcf7dedb7179d4467e9b79e9f">arm_lms_norm_instance_q15</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#ada7e9d6269e6ed4eacf8f68729e9832d">arm_biquad_casd_df1_inst_q15</a>
</li>
<li>pRfft
: <a class="el" href="structarm__dct4__instance__q31.html#af1487dab5e7963b85dc0fdc6bf492542">arm_dct4_instance_q31</a>
, <a class="el" href="structarm__dct4__instance__f32.html#a978f37fc19add31af243ab5c63ae502f">arm_dct4_instance_f32</a>
, <a class="el" href="structarm__dct4__instance__q15.html#a11cf95c1cd9dd2dd5e4b81b8f88dc208">arm_dct4_instance_q15</a>
</li>
<li>pState
: <a class="el" href="structarm__biquad__cascade__df2_t__instance__f32.html#a24d223addfd926a7177088cf2efe76b1">arm_biquad_cascade_df2T_instance_f32</a>
, <a class="el" href="structarm__fir__instance__q7.html#aaddea3b9c7e16ddfd9428b7bf9f9c200">arm_fir_instance_q7</a>
, <a class="el" href="structarm__fir__instance__q15.html#aa8d25f44f45b6a6c4cf38c31569b8a01">arm_fir_instance_q15</a>
, <a class="el" href="structarm__fir__instance__f32.html#a7afcf4022e8560db9b8fd28b0d090a15">arm_fir_instance_f32</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__q15.html#a5481104ef2f8f81360b80b47d69ae932">arm_biquad_casd_df1_inst_q15</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__q31.html#a5dcf4727f58eb4e8e8b392508d8657bb">arm_biquad_casd_df1_inst_q31</a>
, <a class="el" href="structarm__fir__sparse__instance__q31.html#a830be89daa5a393b225048889aa045d1">arm_fir_sparse_instance_q31</a>
, <a class="el" href="structarm__fir__decimate__instance__q15.html#a3f7b5184bb28853ef401b001df121047">arm_fir_decimate_instance_q15</a>
, <a class="el" href="structarm__fir__decimate__instance__q31.html#a0ef0ef9e265f7ab873cfc6daa7593fdb">arm_fir_decimate_instance_q31</a>
, <a class="el" href="structarm__fir__decimate__instance__f32.html#a5bddf29aaaf2011d2e3bcec59a83f633">arm_fir_decimate_instance_f32</a>
, <a class="el" href="structarm__biquad__cascade__df2_t__instance__f64.html#a0bde57b618e3f9059b23b0de64e12ce3">arm_biquad_cascade_df2T_instance_f64</a>
, <a class="el" href="structarm__lms__norm__instance__q31.html#a6b25c96cf048b77078d62f4252a01ec4">arm_lms_norm_instance_q31</a>
, <a class="el" href="structarm__fir__interpolate__instance__q31.html#addde04514b6e6ac72be3d609f0398b1a">arm_fir_interpolate_instance_q31</a>
, <a class="el" href="structarm__fir__interpolate__instance__f32.html#a42a8ba1bda85fa86d7b6c84d3da4c75b">arm_fir_interpolate_instance_f32</a>
, <a class="el" href="structarm__lms__norm__instance__f32.html#a0bc03338687002ed5f2e4a363eb095ec">arm_lms_norm_instance_f32</a>
, <a class="el" href="structarm__iir__lattice__instance__q15.html#afd0136ab917b529554d93f41a5e04618">arm_iir_lattice_instance_q15</a>
, <a class="el" href="structarm__fir__sparse__instance__f32.html#a794af0916666d11cc564d6df08553555">arm_fir_sparse_instance_f32</a>
, <a class="el" href="structarm__fir__sparse__instance__q7.html#a18072cf3ef3666d588f0d49512f2b28f">arm_fir_sparse_instance_q7</a>
, <a class="el" href="structarm__lms__instance__q15.html#a9a575ff82c1e68cbb583083439260d08">arm_lms_instance_q15</a>
, <a class="el" href="structarm__lms__norm__instance__q15.html#aa4de490b3bdbd03561b76ee07901c8e3">arm_lms_norm_instance_q15</a>
, <a class="el" href="structarm__biquad__cascade__stereo__df2_t__instance__f32.html#a2cb00048bb1fe957a03c1ff56dfaf8f0">arm_biquad_cascade_stereo_df2T_instance_f32</a>
, <a class="el" href="structarm__fir__lattice__instance__f32.html#ae348884a1ba9b83fadccd5da640cbcaf">arm_fir_lattice_instance_f32</a>
, <a class="el" href="structarm__iir__lattice__instance__q31.html#a941282745effd26a889fbfadf4b95e6a">arm_iir_lattice_instance_q31</a>
, <a class="el" href="structarm__lms__instance__f32.html#aaf94285be2f99b5b9af40bea8dcb14b9">arm_lms_instance_f32</a>
, <a class="el" href="structarm__lms__instance__q31.html#a206d47b49de6f357f933ebe61520753c">arm_lms_instance_q31</a>
, <a class="el" href="structarm__biquad__cas__df1__32x64__ins__q31.html#a4c899cdfaf2bb955323e93637bd662e0">arm_biquad_cas_df1_32x64_ins_q31</a>
, <a class="el" href="structarm__fir__sparse__instance__q15.html#a98b92b0f5208110129b9a67b1db90408">arm_fir_sparse_instance_q15</a>
, <a class="el" href="structarm__iir__lattice__instance__f32.html#a30babe7815510219e6e3d28e6e4a5969">arm_iir_lattice_instance_f32</a>
, <a class="el" href="structarm__fir__interpolate__instance__q15.html#a26b864363fa47954248f2590e3a82a3c">arm_fir_interpolate_instance_q15</a>
, <a class="el" href="structarm__fir__lattice__instance__q15.html#a37b90dea2bc3ee7c9951a9fe74db0cbb">arm_fir_lattice_instance_q15</a>
, <a class="el" href="structarm__biquad__casd__df1__inst__f32.html#a8c245d79e0d8cfabc82409d4b54fb682">arm_biquad_casd_df1_inst_f32</a>
, <a class="el" href="structarm__fir__lattice__instance__q31.html#a08fe9494ab7cd336b791e9657adadcf6">arm_fir_lattice_instance_q31</a>
, <a class="el" href="structarm__fir__instance__q31.html#a409f39c93b744784648bdc365541444d">arm_fir_instance_q31</a>
</li>
<li>pTapDelay
: <a class="el" href="structarm__fir__sparse__instance__q15.html#aeab2855176c6efdb231a73a3672837d5">arm_fir_sparse_instance_q15</a>
, <a class="el" href="structarm__fir__sparse__instance__q7.html#ac625393c84bc0342ffdf26fc4eba1ac1">arm_fir_sparse_instance_q7</a>
, <a class="el" href="structarm__fir__sparse__instance__q31.html#ab87ae457adec8f727afefaa2599fc983">arm_fir_sparse_instance_q31</a>
, <a class="el" href="structarm__fir__sparse__instance__f32.html#aaa54ae67e5d10c6dd0d697945c638d31">arm_fir_sparse_instance_f32</a>
</li>
<li>pTwiddle
: <a class="el" href="structarm__dct4__instance__f32.html#ad13544aafad268588c62e3eb35ae662c">arm_dct4_instance_f32</a>
, <a class="el" href="structarm__cfft__radix2__instance__f32.html#adb0c9d47dbfbd90a6f6ed0a05313a974">arm_cfft_radix2_instance_f32</a>
, <a class="el" href="structarm__cfft__instance__f32.html#a59cc6f753f1498716e1444ac054c06de">arm_cfft_instance_f32</a>
, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a29dd693537e45421a36891f8439e1fba">arm_cfft_radix4_instance_q15</a>
, <a class="el" href="structarm__dct4__instance__q15.html#abc6c847e9f906781e1d5da40e9aafa76">arm_dct4_instance_q15</a>
, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a1d5bbe9a991e133f81652a77a7985d23">arm_cfft_radix2_instance_q31</a>
, <a class="el" href="structarm__dct4__instance__q31.html#a7db236e22673146bb1d2c962f0713f08">arm_dct4_instance_q31</a>
, <a class="el" href="structarm__cfft__instance__q31.html#af751114feb91de3ace8600e91bdd0872">arm_cfft_instance_q31</a>
, <a class="el" href="structarm__cfft__instance__q15.html#afdaf12ce4687cec021c5ae73d0987a3f">arm_cfft_instance_q15</a>
, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a14860c7544911702ca1fa0bf78204ef3">arm_cfft_radix4_instance_f32</a>
, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a561c22dee4cbdcfa0fd5f15106ecc306">arm_cfft_radix4_instance_q31</a>
, <a class="el" href="structarm__cfft__radix2__instance__q15.html#a3809dd15e7cbf1a054c728cfbbb0cc5a">arm_cfft_radix2_instance_q15</a>
</li>
<li>pTwiddleAReal
: <a class="el" href="structarm__rfft__instance__f32.html#a534cc7e6e9b3e3dd022fad611c762142">arm_rfft_instance_f32</a>
, <a class="el" href="structarm__rfft__instance__q15.html#affbf2de522ac029432d98e8373c0ec53">arm_rfft_instance_q15</a>
, <a class="el" href="structarm__rfft__instance__q31.html#a2a0c944e66bab92fcbe19d1c29153250">arm_rfft_instance_q31</a>
</li>
<li>pTwiddleBReal
: <a class="el" href="structarm__rfft__instance__q31.html#ae5070be4c2e0327e618f5e1f4c5b9d80">arm_rfft_instance_q31</a>
, <a class="el" href="structarm__rfft__instance__q15.html#a937d815022adc557b435ba8c6cd58b0d">arm_rfft_instance_q15</a>
, <a class="el" href="structarm__rfft__instance__f32.html#a23543ecfd027fea2477fe1eea23c3c4d">arm_rfft_instance_f32</a>
</li>
<li>pTwiddleRFFT
: <a class="el" href="structarm__rfft__fast__instance__f32.html#a9f30b04f163fabc1b24421d3c323d5fc">arm_rfft_fast_instance_f32</a>
</li>
<li>pvCoeffs
: <a class="el" href="structarm__iir__lattice__instance__q31.html#a04507e2b982b1dfa97b7b55752dea6b9">arm_iir_lattice_instance_q31</a>
, <a class="el" href="structarm__iir__lattice__instance__f32.html#afc7c8f577e6f27d097fe55f57e707f72">arm_iir_lattice_instance_f32</a>
, <a class="el" href="structarm__iir__lattice__instance__q15.html#a4c4f57f45b223abbe2a9fb727bd2cad9">arm_iir_lattice_instance_q15</a>
</li>
<li>pYData
: <a class="el" href="structarm__linear__interp__instance__f32.html#ab373001f6afad0850359c344a4d7eee4">arm_linear_interp_instance_f32</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
