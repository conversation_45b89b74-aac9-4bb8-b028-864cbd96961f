
#include "CAdc.h"
#include "CRccClock.h"


#include "MemDebug.h"



//ADC将对应管脚的参数实时通dma刷新，应用层调用接口获取到当前实时的电压数据

uint8_t CAdc::m_objCount = 0;
volatile uint16_t CAdc::m_ADC3_Value[ADC3_CALC_COUNT][ADC3_CHANEL_COUNT]={0};
volatile uint16_t CAdc::m_ADC1_Value[ADC1_CALC_COUNT][ADC1_CHANEL_COUNT]={0};



CAdc::ADC_CHANNEL_MAP CAdc::m_ADC1_ChannelMap[] =
{
    //AD1通道以及对于管脚资源表，可能会和其他外设冲突，可以在这里调整
    //
    {ADC_Channel_4  ,    1,    ADC_SampleTime_15Cycles,     GPIOA   ,    GPIO_Pin_4,     RCC_AHB1Periph_GPIOA,RCC_AHB1},//
    {ADC_Channel_8  ,    2,    ADC_SampleTime_15Cycles,     GPIOB   ,    GPIO_Pin_0,     RCC_AHB1Periph_GPIOB,RCC_AHB1},//
    {ADC_Channel_9  ,    3,    ADC_SampleTime_15Cycles,     GPIOB   ,    GPIO_Pin_1,     RCC_AHB1Periph_GPIOB,RCC_AHB1},//
    {ADC_Channel_14 ,    4,    ADC_SampleTime_15Cycles,     GPIOC   ,    GPIO_Pin_4,     RCC_AHB1Periph_GPIOC,RCC_AHB1},//
    {ADC_Channel_15 ,    5,    ADC_SampleTime_15Cycles,     GPIOC   ,    GPIO_Pin_5,     RCC_AHB1Periph_GPIOC,RCC_AHB1} //
};
CAdc::ADC_CHANNEL_MAP CAdc::m_ADC3_ChannelMap[] =
{
    //AD3通道以及对于管脚资源表，可能会和其他外设冲突，可以在这里调整
    //
    {ADC_Channel_4  ,    1 ,    ADC_SampleTime_15Cycles,     GPIOF   ,    GPIO_Pin_6 ,     RCC_AHB1Periph_GPIOF,RCC_AHB1},//
    {ADC_Channel_5  ,    2 ,    ADC_SampleTime_15Cycles,     GPIOF   ,    GPIO_Pin_7 ,     RCC_AHB1Periph_GPIOF,RCC_AHB1},//
    {ADC_Channel_6  ,    3 ,    ADC_SampleTime_15Cycles,     GPIOF   ,    GPIO_Pin_8 ,     RCC_AHB1Periph_GPIOF,RCC_AHB1},//
    {ADC_Channel_7  ,    4 ,    ADC_SampleTime_15Cycles,     GPIOF   ,    GPIO_Pin_9 ,     RCC_AHB1Periph_GPIOF,RCC_AHB1},//
    {ADC_Channel_8  ,    5 ,    ADC_SampleTime_15Cycles,     GPIOF   ,    GPIO_Pin_10,     RCC_AHB1Periph_GPIOF,RCC_AHB1},//
    {ADC_Channel_9  ,    6 ,    ADC_SampleTime_15Cycles,     GPIOF   ,    GPIO_Pin_3 ,     RCC_AHB1Periph_GPIOF,RCC_AHB1},//
    {ADC_Channel_10 ,    7 ,    ADC_SampleTime_15Cycles,     GPIOC   ,    GPIO_Pin_0 ,     RCC_AHB1Periph_GPIOC,RCC_AHB1},//
    {ADC_Channel_11 ,    8 ,    ADC_SampleTime_15Cycles,     GPIOC   ,    GPIO_Pin_1 ,     RCC_AHB1Periph_GPIOC,RCC_AHB1},//
    {ADC_Channel_12 ,    9 ,    ADC_SampleTime_15Cycles,     GPIOC   ,    GPIO_Pin_2 ,     RCC_AHB1Periph_GPIOC,RCC_AHB1},//
    {ADC_Channel_13 ,    10,    ADC_SampleTime_15Cycles,     GPIOC   ,    GPIO_Pin_3 ,     RCC_AHB1Periph_GPIOC,RCC_AHB1},//
    {ADC_Channel_15 ,    11,    ADC_SampleTime_15Cycles,     GPIOF   ,    GPIO_Pin_5 ,     RCC_AHB1Periph_GPIOF,RCC_AHB1} //
};
CAdc::ADC_MANAGER CAdc::m_AdcManager[] =
{
    //AD资源表,这里用到了DMA的通道和硬件管脚，可能会和其他外设冲突，可以在这里调整
    //
    {ADC1,RCC_APB2Periph_ADC1,RCC_APB2,   5,  &CAdc::m_ADC1_ChannelMap[0],DMA_Channel_2,RCC_AHB1Periph_DMA2,RCC_AHB1,DMA2_Stream0_IRQn,DMA2_Stream0,(uint32_t)(CAdc::m_ADC1_Value),ADC1_CALC_COUNT*ADC1_CHANEL_COUNT},
    {ADC3,RCC_APB2Periph_ADC3,RCC_APB2,  11,  &CAdc::m_ADC3_ChannelMap[0],DMA_Channel_0,RCC_AHB1Periph_DMA2,RCC_AHB1,DMA2_Stream4_IRQn,DMA2_Stream4,(uint32_t)(CAdc::m_ADC3_Value),ADC3_CALC_COUNT*ADC3_CHANEL_COUNT}
};




//*****************************************************************************************************
//功能描述：
//参数说明：
//返　　回：
//特殊说明：
//作　　者：赵命华20210406
//*****************************************************************************************************
CAdc::CAdc()
{
    GPIO_InitTypeDef GPIO_InitStructure;
    if(m_objCount == 0)
    {
        for(uint8_t i=0;i < sizeof(m_AdcManager)/sizeof(ADC_MANAGER);i++)//本循环一共设置时钟2+5+11次
        {
            EnableRccClock(m_AdcManager[i].ADC_RccClock,m_AdcManager[i].ADC_RCC_Periph);//使能ADC时钟
            EnableRccClock(m_AdcManager[i].DMA_rccClock,m_AdcManager[i].DMA_RCC_Periph);//设置DMA时钟
            for(uint8_t j=0;i < m_AdcManager[i].ADC_ChannelNum;j++)
            {
                EnableRccClock(m_AdcManager[i].pADC_CHANNEL_PARA[j].ADCx_RccClock,m_AdcManager[i].pADC_CHANNEL_PARA[j].ADCx_RCC_Periph);//使能管脚时钟
                
                GPIO_InitStructure.GPIO_Pin = m_AdcManager[i].pADC_CHANNEL_PARA[j].ADCx_PIN;//设置管脚参数
                GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;//模拟输入
                GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//50M时钟速度
                GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
                GPIO_Init(m_AdcManager[i].pADC_CHANNEL_PARA[j].ADCx_PORT, &GPIO_InitStructure);
            }
        }
    }
    m_objCount++;
}
CAdc::~CAdc()
{
    GPIO_InitTypeDef GPIO_InitStructure;
    if(m_objCount == 1)
    {
        for(uint8_t i=0;i < sizeof(m_AdcManager)/sizeof(ADC_MANAGER);i++)
        {
            DisableRccClock(m_AdcManager[i].ADC_RccClock,m_AdcManager[i].ADC_RCC_Periph);//使能ADC时钟
            DisableRccClock(m_AdcManager[i].DMA_rccClock,m_AdcManager[i].DMA_RCC_Periph);//设置DMA时钟
            for(uint8_t j=0;i < m_AdcManager[i].ADC_ChannelNum;j++)
            {
                DisableRccClock(m_AdcManager[i].pADC_CHANNEL_PARA[j].ADCx_RccClock,m_AdcManager[i].pADC_CHANNEL_PARA[j].ADCx_RCC_Periph);//失能管脚时钟

                GPIO_InitStructure.GPIO_Pin = m_AdcManager[i].pADC_CHANNEL_PARA[j].ADCx_PIN;//设置管脚参数
                GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;//模拟输入
                GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;//
                GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
                GPIO_Init(m_AdcManager[i].pADC_CHANNEL_PARA[j].ADCx_PORT, &GPIO_InitStructure);
            }
        }
    }
    m_objCount--;
}





