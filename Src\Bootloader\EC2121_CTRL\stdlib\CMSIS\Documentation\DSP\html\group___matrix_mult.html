<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Matrix Multiplication</title>
<title>CMSIS-DSP: Matrix Multiplication</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___matrix_mult.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Matrix Multiplication</div>  </div>
<div class="ingroups"><a class="el" href="group__group_matrix.html">Matrix Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga917bf0270310c1d3f0eda1fc7c0026a0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0">arm_mat_mult_f32</a> (const <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *pSrcA, const <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *pSrcB, <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *pDst)</td></tr>
<tr class="memdesc:ga917bf0270310c1d3f0eda1fc7c0026a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Floating-point matrix multiplication.  <a href="#ga917bf0270310c1d3f0eda1fc7c0026a0"></a><br/></td></tr>
<tr class="separator:ga917bf0270310c1d3f0eda1fc7c0026a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga08f37d93a5bfef0c5000dc5e0a411f93"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___matrix_mult.html#ga08f37d93a5bfef0c5000dc5e0a411f93">arm_mat_mult_fast_q15</a> (const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pSrcA, const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pSrcB, <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pDst, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState)</td></tr>
<tr class="memdesc:ga08f37d93a5bfef0c5000dc5e0a411f93"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q15 matrix multiplication (fast variant) for Cortex-M3 and Cortex-M4.  <a href="#ga08f37d93a5bfef0c5000dc5e0a411f93"></a><br/></td></tr>
<tr class="separator:ga08f37d93a5bfef0c5000dc5e0a411f93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2785e8c1b785348b0c439b56aaf585a3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___matrix_mult.html#ga2785e8c1b785348b0c439b56aaf585a3">arm_mat_mult_fast_q31</a> (const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pSrcA, const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pSrcB, <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pDst)</td></tr>
<tr class="memdesc:ga2785e8c1b785348b0c439b56aaf585a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q31 matrix multiplication (fast variant) for Cortex-M3 and Cortex-M4.  <a href="#ga2785e8c1b785348b0c439b56aaf585a3"></a><br/></td></tr>
<tr class="separator:ga2785e8c1b785348b0c439b56aaf585a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3657b99a9667945373e520dbac0f4516"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___matrix_mult.html#ga3657b99a9667945373e520dbac0f4516">arm_mat_mult_q15</a> (const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pSrcA, const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pSrcB, <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pDst, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState CMSIS_UNUSED)</td></tr>
<tr class="memdesc:ga3657b99a9667945373e520dbac0f4516"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q15 matrix multiplication.  <a href="#ga3657b99a9667945373e520dbac0f4516"></a><br/></td></tr>
<tr class="separator:ga3657b99a9667945373e520dbac0f4516"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ec612a8c2c4916477fb9bc1ab548a6e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___matrix_mult.html#ga2ec612a8c2c4916477fb9bc1ab548a6e">arm_mat_mult_q31</a> (const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pSrcA, const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pSrcB, <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pDst)</td></tr>
<tr class="memdesc:ga2ec612a8c2c4916477fb9bc1ab548a6e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q31 matrix multiplication.  <a href="#ga2ec612a8c2c4916477fb9bc1ab548a6e"></a><br/></td></tr>
<tr class="separator:ga2ec612a8c2c4916477fb9bc1ab548a6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Multiplies two matrices.</p>
<div class="image">
<img src="MatrixMultiplication.gif" alt="MatrixMultiplication.gif"/>
<div class="caption">
Multiplication of two 3 x 3 matrices</div></div>
<p>Matrix multiplication is only defined if the number of columns of the first matrix equals the number of rows of the second matrix. Multiplying an <code>M x N</code> matrix with an <code>N x P</code> matrix results in an <code>M x P</code> matrix. When matrix size checking is enabled, the functions check: (1) that the inner dimensions of <code>pSrcA</code> and <code>pSrcB</code> are equal; and (2) that the size of the output matrix equals the outer dimensions of <code>pSrcA</code> and <code>pSrcB</code>. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga917bf0270310c1d3f0eda1fc7c0026a0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_mat_mult_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input matrix structure </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to output matrix structure </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns either <code>ARM_MATH_SIZE_MISMATCH</code> or <code>ARM_MATH_SUCCESS</code> based on the outcome of size checking. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_class_marks_example_f32_8c-example.html#a17">arm_class_marks_example_f32.c</a>, and <a class="el" href="arm_matrix_example_f32_8c-example.html#a13">arm_matrix_example_f32.c</a>.</dd>
</dl>
<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">ARM_MATH_SIZE_MISMATCH</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__matrix__instance__f32.html#acdd1fb73734df68b89565c54f1dd8ae2">arm_matrix_instance_f32::numCols</a>, <a class="el" href="structarm__matrix__instance__f32.html#a23f4e34d70a82c9cad7612add5640b7b">arm_matrix_instance_f32::numRows</a>, <a class="el" href="structarm__matrix__instance__f32.html#af3917c032600a9dfd5ed4a96f074910a">arm_matrix_instance_f32::pData</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

<p>Referenced by <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ga08f37d93a5bfef0c5000dc5e0a411f93"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_mat_mult_fast_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input matrix structure </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to output matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the array for storing intermediate results </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns either <code>ARM_MATH_SIZE_MISMATCH</code> or <code>ARM_MATH_SUCCESS</code> based on the outcome of size checking.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The difference between the function <a class="el" href="group___matrix_mult.html#ga3657b99a9667945373e520dbac0f4516" title="Q15 matrix multiplication.">arm_mat_mult_q15()</a> and this fast variant is that the fast variant use a 32-bit rather than a 64-bit accumulator. The result of each 1.15 x 1.15 multiplication is truncated to 2.30 format. These intermediate results are accumulated in a 32-bit register in 2.30 format. Finally, the accumulator is saturated and converted to a 1.15 result.</dd></dl>
<dl class="section user"><dt></dt><dd>The fast version has the same overflow behavior as the standard version but provides less precision since it discards the low 16 bits of each multiplication result. In order to avoid overflows completely the input signals must be scaled down. Scale down one of the input matrices by log2(numColsA) bits to avoid overflows, as a total of numColsA additions are computed internally for each output element.</dd></dl>
<dl class="section user"><dt></dt><dd>See <code><a class="el" href="group___matrix_mult.html#ga3657b99a9667945373e520dbac0f4516" title="Q15 matrix multiplication.">arm_mat_mult_q15()</a></code> for a slower implementation of this function which uses 64-bit accumulation to provide higher precision. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">ARM_MATH_SIZE_MISMATCH</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__matrix__instance__q15.html#acbbce67ba058d8e1c867c71d57288c97">arm_matrix_instance_q15::numCols</a>, <a class="el" href="structarm__matrix__instance__q15.html#a9bac6ed54be287c4d4f01a1a28be65f5">arm_matrix_instance_q15::numRows</a>, <a class="el" href="structarm__matrix__instance__q15.html#a6da33a5553e634787d0f515cf8d724af">arm_matrix_instance_q15::pData</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga2785e8c1b785348b0c439b56aaf585a3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_mat_mult_fast_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input matrix structure </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to output matrix structure </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns either <code>ARM_MATH_SIZE_MISMATCH</code> or <code>ARM_MATH_SUCCESS</code> based on the outcome of size checking.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The difference between the function <a class="el" href="group___matrix_mult.html#ga2ec612a8c2c4916477fb9bc1ab548a6e" title="Q31 matrix multiplication.">arm_mat_mult_q31()</a> and this fast variant is that the fast variant use a 32-bit rather than a 64-bit accumulator. The result of each 1.31 x 1.31 multiplication is truncated to 2.30 format. These intermediate results are accumulated in a 32-bit register in 2.30 format. Finally, the accumulator is saturated and converted to a 1.31 result.</dd></dl>
<dl class="section user"><dt></dt><dd>The fast version has the same overflow behavior as the standard version but provides less precision since it discards the low 32 bits of each multiplication result. In order to avoid overflows completely the input signals must be scaled down. Scale down one of the input matrices by log2(numColsA) bits to avoid overflows, as a total of numColsA additions are computed internally for each output element.</dd></dl>
<dl class="section user"><dt></dt><dd>See <code><a class="el" href="group___matrix_mult.html#ga2ec612a8c2c4916477fb9bc1ab548a6e" title="Q31 matrix multiplication.">arm_mat_mult_q31()</a></code> for a slower implementation of this function which uses 64-bit accumulation to provide higher precision. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">ARM_MATH_SIZE_MISMATCH</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__matrix__instance__q31.html#abd161da7614eda927157f18b698074b1">arm_matrix_instance_q31::numCols</a>, <a class="el" href="structarm__matrix__instance__q31.html#a63bacac158a821c8cfc06088d251598c">arm_matrix_instance_q31::numRows</a>, <a class="el" href="structarm__matrix__instance__q31.html#a09a64267c0579fef086efc9059741e56">arm_matrix_instance_q31::pData</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga3657b99a9667945373e520dbac0f4516"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_mat_mult_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState&#160;</td>
          <td class="paramname"><em>CMSIS_UNUSED</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input matrix structure </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to output matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the array for storing intermediate results (Unused) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns either <code>ARM_MATH_SIZE_MISMATCH</code> or <code>ARM_MATH_SUCCESS</code> based on the outcome of size checking.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 64-bit internal accumulator. The inputs to the multiplications are in 1.15 format and multiplications yield a 2.30 result. The 2.30 intermediate results are accumulated in a 64-bit accumulator in 34.30 format. This approach provides 33 guard bits and there is no risk of overflow. The 34.30 result is then truncated to 34.15 format by discarding the low 15 bits and then saturated to 1.15 format.</dd></dl>
<dl class="section user"><dt></dt><dd>Refer to <code><a class="el" href="group___matrix_mult.html#ga08f37d93a5bfef0c5000dc5e0a411f93" title="Q15 matrix multiplication (fast variant) for Cortex-M3 and Cortex-M4.">arm_mat_mult_fast_q15()</a></code> for a faster but less precise version of this function for Cortex-M3 and Cortex-M4. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">ARM_MATH_SIZE_MISMATCH</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__matrix__instance__q15.html#acbbce67ba058d8e1c867c71d57288c97">arm_matrix_instance_q15::numCols</a>, <a class="el" href="structarm__matrix__instance__q15.html#a9bac6ed54be287c4d4f01a1a28be65f5">arm_matrix_instance_q15::numRows</a>, <a class="el" href="structarm__matrix__instance__q15.html#a6da33a5553e634787d0f515cf8d724af">arm_matrix_instance_q15::pData</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga2ec612a8c2c4916477fb9bc1ab548a6e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_mat_mult_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input matrix structure </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to output matrix structure </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns either <code>ARM_MATH_SIZE_MISMATCH</code> or <code>ARM_MATH_SUCCESS</code> based on the outcome of size checking.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b></p>
<dl class="section user"><dt></dt><dd>The function is implemented using an internal 64-bit accumulator. The accumulator has a 2.62 format and maintains full precision of the intermediate multiplication results but provides only a single guard bit. There is no saturation on intermediate additions. Thus, if the accumulator overflows it wraps around and distorts the result. The input signals should be scaled down to avoid intermediate overflows. The input is thus scaled down by log2(numColsA) bits to avoid overflows, as a total of numColsA additions are performed internally. The 2.62 accumulator is right shifted by 31 bits and saturated to 1.31 format to yield the final result.</dd></dl>
<dl class="section user"><dt></dt><dd>See <code><a class="el" href="group___matrix_mult.html#ga2785e8c1b785348b0c439b56aaf585a3" title="Q31 matrix multiplication (fast variant) for Cortex-M3 and Cortex-M4.">arm_mat_mult_fast_q31()</a></code> for a faster but less precise implementation of this function for Cortex-M3 and Cortex-M4. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">ARM_MATH_SIZE_MISMATCH</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="arm__math_8h.html#ad7373e53d3c2e1adfeafc8c2e9720b5c">clip_q63_to_q31()</a>, <a class="el" href="structarm__matrix__instance__q31.html#abd161da7614eda927157f18b698074b1">arm_matrix_instance_q31::numCols</a>, <a class="el" href="structarm__matrix__instance__q31.html#a63bacac158a821c8cfc06088d251598c">arm_matrix_instance_q31::numRows</a>, <a class="el" href="structarm__matrix__instance__q31.html#a09a64267c0579fef086efc9059741e56">arm_matrix_instance_q31::pData</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
