var _pack_format =
[
    [ "Filename Conventions", "_pack_format.html#Filenames", null ],
    [ "Pack Schema", "_pack_format.html#PackSchema", null ],
    [ "Example of a *.PDSC File", "_pack_format.html#PDSC_Example", null ],
    [ "Example of a *.PACK File", "_pack_format.html#PACK_Example", null ],
    [ "/package element", "pdsc_package_pg.html", [
      [ "/package", "pdsc_package_pg.html#element_package", null ]
    ] ],
    [ "/package/keywords element", "element_keywords.html", null ],
    [ "/package/releases element", "element_releases.html", [
      [ "/package/releases/release", "element_releases.html#element_release", null ]
    ] ],
    [ "/package/taxonomy element", "element_taxonomy.html", [
      [ "/package/taxonomy/description", "element_taxonomy.html#element_taxonomyDescription", null ]
    ] ],
    [ "/package/apis element", "pdsc_apis_pg.html", [
      [ "/package/apis", "pdsc_apis_pg.html#element_apis", null ],
      [ "/package/apis/api", "pdsc_apis_pg.html#element_api", null ]
    ] ],
    [ "/package/generators element", "pdsc_generators_pg.html", [
      [ "/package/generators", "pdsc_generators_pg.html#element_generators", null ],
      [ "/package/generators/generator", "pdsc_generators_pg.html#element_generator", null ],
      [ "/package/generators/generator/select", "pdsc_generators_pg.html#element_gen_select", null ],
      [ "/package/generators/generator/project_files", "pdsc_generators_pg.html#element_gen_project_files", null ],
      [ "/package/generators/generator/files", "pdsc_generators_pg.html#element_gen_files", null ],
      [ "/package/generators/generator/files/file", "pdsc_generators_pg.html#element_gen_file", null ]
    ] ],
    [ "/package/devices element", "pdsc_devices_pg.html", "pdsc_devices_pg" ],
    [ "/package/boards element", "pdsc_boards_pg.html", [
      [ "/package/boards", "pdsc_boards_pg.html#element_boards", null ],
      [ "/package/boards/board", "pdsc_boards_pg.html#element_board", null ],
      [ "/package/boards/board/feature", "pdsc_boards_pg.html#element_board_feature", null ],
      [ "/package/boards/board/mountedDevice", "pdsc_boards_pg.html#element_board_mountedDevice", null ],
      [ "/package/boards/board/compatibleDevice", "pdsc_boards_pg.html#element_board_compatibleDevice", null ],
      [ "/package/boards/board/image", "pdsc_boards_pg.html#element_board_image", null ],
      [ "/package/boards/board/debugInterface", "pdsc_boards_pg.html#element_board_debugInterface", null ],
      [ "/package/boards/board/book", "pdsc_boards_pg.html#element_board_book", null ]
    ] ],
    [ "/package/conditions element", "pdsc_conditions_pg.html", [
      [ "/package/conditions", "pdsc_conditions_pg.html#element_conditions", null ],
      [ "/package/conditions/condition", "pdsc_conditions_pg.html#element_condition", null ],
      [ "/package/conditions/condition/accept", "pdsc_conditions_pg.html#element_accept", null ],
      [ "/package/conditions/condition/require", "pdsc_conditions_pg.html#element_require", null ],
      [ "/package/conditions/condition/deny", "pdsc_conditions_pg.html#element_deny", null ]
    ] ],
    [ "/package/components element", "pdsc_components_pg.html", [
      [ "Component Bundle", "pdsc_components_pg.html#Component_Bundle", null ],
      [ "Component Files", "pdsc_components_pg.html#Component_Files", null ],
      [ "Component Instances", "pdsc_components_pg.html#Component_Instances", null ],
      [ "RTE_Components.h", "pdsc_components_pg.html#RTE_Components_h", null ],
      [ "/package/components", "pdsc_components_pg.html#element_components", null ],
      [ "/package/components/bundle", "pdsc_components_pg.html#element_bundle", null ],
      [ "/package/components/.../component", "pdsc_components_pg.html#element_component", null ],
      [ "/package/.../files", "pdsc_components_pg.html#element_files", null ],
      [ "/package/.../files/file", "pdsc_components_pg.html#element_file", null ]
    ] ],
    [ "/package/examples element", "pdsc_examples_pg.html", [
      [ "/package/examples", "pdsc_examples_pg.html#element_examples", null ],
      [ "/package/examples/example", "pdsc_examples_pg.html#element_example", null ],
      [ "/package/examples/example/board", "pdsc_examples_pg.html#element_example_board", null ],
      [ "/package/examples/project", "pdsc_examples_pg.html#element_example_project", null ],
      [ "/package/examples/project/environment", "pdsc_examples_pg.html#element_example_project_env", null ],
      [ "/package/examples/example/attributes", "pdsc_examples_pg.html#element_example_attributes", null ],
      [ "/package/examples/example/attributes/component", "pdsc_examples_pg.html#element_example_attribute_component", null ]
    ] ],
    [ "Debug Access Sequences", "pdsc__sequence_name_enum_pg.html", [
      [ "Usage of Debug Access Sequences", "pdsc__sequence_name_enum_pg.html#usage_of_sequences", null ],
      [ "Default Debug Access Sequences", "pdsc__sequence_name_enum_pg.html#default_sequences", null ]
    ] ]
];