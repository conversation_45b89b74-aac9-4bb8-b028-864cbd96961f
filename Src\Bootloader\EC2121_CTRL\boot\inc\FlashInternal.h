#ifndef __FLASH_INTERNAL_H_
#define __FLASH_INTERNAL_H_

#include "Globel.h"

#define FLASH_PROG_START_ADD            (0x8010000)
#define FLASH_BOOT_START_ADD            (0x8000000)
#define FLASH_SHARE_DATA_ADD            (0x800C000)
#define FLASH_PROG_END_ADD              (0x80FFFFF)

#define FIRMWARE_INFO_BASE_ADDR         0x800C000                      /* 固件程序信息预留16KByte大小 */

#define FIRMWARE_SIZE_ADDR              (FIRMWARE_INFO_BASE_ADDR)       /* 固件程序大小的起始位置 */
#define FIRMWARE_CRC_ADDR               (FIRMWARE_INFO_BASE_ADDR + 4)   /* 固件程序校验值 */
#define FIRMWARE_OK_ADDR                (FIRMWARE_INFO_BASE_ADDR + 8)   /* 固件程序校验值 */

#define FIRMWARE_OK_FLAG                0x12345678                      /* 升级完成标志 */

//FLASH 扇区的起始地址
#define ADDR_FLASH_SECTOR_0     ((u32)0x08000000) 	//扇区0起始地址, 16 Kbytes  
#define ADDR_FLASH_SECTOR_1     ((u32)0x08004000) 	//扇区1起始地址, 16 Kbytes  
#define ADDR_FLASH_SECTOR_2     ((u32)0x08008000) 	//扇区2起始地址, 16 Kbytes  
#define ADDR_FLASH_SECTOR_3     ((u32)0x0800C000) 	//扇区3起始地址, 16 Kbytes  
#define ADDR_FLASH_SECTOR_4     ((u32)0x08010000) 	//扇区4起始地址, 64 Kbytes  
#define ADDR_FLASH_SECTOR_5     ((u32)0x08020000) 	//扇区5起始地址, 128 Kbytes  
#define ADDR_FLASH_SECTOR_6     ((u32)0x08040000) 	//扇区6起始地址, 128 Kbytes  
#define ADDR_FLASH_SECTOR_7     ((u32)0x08060000) 	//扇区7起始地址, 128 Kbytes  
#define ADDR_FLASH_SECTOR_8     ((u32)0x08080000) 	//扇区8起始地址, 128 Kbytes  
#define ADDR_FLASH_SECTOR_9     ((u32)0x080A0000) 	//扇区9起始地址, 128 Kbytes  
#define ADDR_FLASH_SECTOR_10    ((u32)0x080C0000) 	//扇区10起始地址,128 Kbytes  
#define ADDR_FLASH_SECTOR_11    ((u32)0x080E0000) 	//扇区11起始地址,128 Kbytes

#define SIZE_FLASH_SECTOR_0     (16 * 1024) 	//扇区0起始地址, 16 Kbytes  
#define SIZE_FLASH_SECTOR_1     (16 * 1024) 	//扇区1起始地址, 16 Kbytes  
#define SIZE_FLASH_SECTOR_2     (16 * 1024) 	//扇区2起始地址, 16 Kbytes  
#define SIZE_FLASH_SECTOR_3     (16 * 1024) 	//扇区3起始地址, 16 Kbytes  
#define SIZE_FLASH_SECTOR_4     (64 * 1024) 	//扇区4起始地址, 64 Kbytes  
#define SIZE_FLASH_SECTOR_5     (128 * 1024) 	//扇区5起始地址, 128 Kbytes  
#define SIZE_FLASH_SECTOR_6     (128 * 1024) 	//扇区6起始地址, 128 Kbytes  
#define SIZE_FLASH_SECTOR_7     (128 * 1024) 	//扇区7起始地址, 128 Kbytes  
#define SIZE_FLASH_SECTOR_8     (128 * 1024) 	//扇区8起始地址, 128 Kbytes  
#define SIZE_FLASH_SECTOR_9     (128 * 1024) 	//扇区9起始地址, 128 Kbytes  
#define SIZE_FLASH_SECTOR_10    (128 * 1024) 	//扇区10起始地址,128 Kbytes  
#define SIZE_FLASH_SECTOR_11    (128 * 1024) 	//扇区11起始地址,128 Kbytes

#define FLASH_EARSE_SECTOR_START    3
#define FLASH_EARSE_SECTOR_END      11

//#define DEVICE_CODE 0x44543535 //DT55

typedef struct{
    uint32_t ulStartAddr;
    uint32_t ulSector;
    uint32_t ulSize;
}InternalFlash;

class CFlashInternal{

public:
    static uint32_t GetSector(uint32_t addr);
      
    static uint32_t GetSectorNumber(uint32_t addr);
  
    static uint32_t EraseFlash();

    static uint32_t WriteFlash(uint32_t &uFlashAddr  ,int iLength, uint32_t &iWritedLenth,  void *pData);

};
#endif 
