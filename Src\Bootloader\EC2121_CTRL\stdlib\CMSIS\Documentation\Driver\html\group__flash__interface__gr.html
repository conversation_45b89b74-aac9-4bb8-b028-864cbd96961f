<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Flash Interface</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__flash__interface__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Content</a> &#124;
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Flash Interface</div>  </div>
</div><!--header-->
<div class="contents">

<p>Driver API for Flash Device Interface (Driver_Flash.h)  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Content</h2></td></tr>
<tr class="memitem:group___flash__events"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___flash__events.html">Flash Events</a></td></tr>
<tr class="memdesc:group___flash__events"><td class="mdescLeft">&#160;</td><td class="mdescRight">The Flash driver generates call back events that are notified via the function <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a>. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r">ARM_FLASH_SECTOR</a></td></tr>
<tr class="memdesc:struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r"><td class="mdescLeft">&#160;</td><td class="mdescRight">Flash Sector information.  <a href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___f_l_a_s_h___i_n_f_o"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a></td></tr>
<tr class="memdesc:struct_a_r_m___f_l_a_s_h___i_n_f_o"><td class="mdescLeft">&#160;</td><td class="mdescRight">Flash information.  <a href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___f_l_a_s_h___i_n_f_o"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h">ARM_DRIVER_FLASH</a></td></tr>
<tr class="memdesc:struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access structure of the Flash Driver.  <a href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a></td></tr>
<tr class="memdesc:struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s"><td class="mdescLeft">&#160;</td><td class="mdescRight">Flash Driver Capabilities.  <a href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s">ARM_FLASH_STATUS</a></td></tr>
<tr class="memdesc:struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s"><td class="mdescLeft">&#160;</td><td class="mdescRight">Flash Status.  <a href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gabeb4ad43b1e6fa4ed956cd5c9371d327"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#gabeb4ad43b1e6fa4ed956cd5c9371d327">ARM_Flash_SignalEvent_t</a> )(uint32_t event)</td></tr>
<tr class="memdesc:gabeb4ad43b1e6fa4ed956cd5c9371d327"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a> : Signal Flash Event.  <a href="#gabeb4ad43b1e6fa4ed956cd5c9371d327">More...</a><br/></td></tr>
<tr class="separator:gabeb4ad43b1e6fa4ed956cd5c9371d327"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga1cfe24b2ffa571ee50ae544bd922b604"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ga1cfe24b2ffa571ee50ae544bd922b604">ARM_Flash_GetVersion</a> (void)</td></tr>
<tr class="memdesc:ga1cfe24b2ffa571ee50ae544bd922b604"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver version.  <a href="#ga1cfe24b2ffa571ee50ae544bd922b604">More...</a><br/></td></tr>
<tr class="separator:ga1cfe24b2ffa571ee50ae544bd922b604"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27c23c998032cd47cb47293c0185ee5d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a> (void)</td></tr>
<tr class="memdesc:ga27c23c998032cd47cb47293c0185ee5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver capabilities.  <a href="#ga27c23c998032cd47cb47293c0185ee5d">More...</a><br/></td></tr>
<tr class="separator:ga27c23c998032cd47cb47293c0185ee5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5b4bbe529d620d4ad4825588a4c4cf0"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#gaa5b4bbe529d620d4ad4825588a4c4cf0">ARM_Flash_Initialize</a> (<a class="el" href="group__flash__interface__gr.html#gabeb4ad43b1e6fa4ed956cd5c9371d327">ARM_Flash_SignalEvent_t</a> cb_event)</td></tr>
<tr class="memdesc:gaa5b4bbe529d620d4ad4825588a4c4cf0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize the Flash Interface.  <a href="#gaa5b4bbe529d620d4ad4825588a4c4cf0">More...</a><br/></td></tr>
<tr class="separator:gaa5b4bbe529d620d4ad4825588a4c4cf0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae23af293e9f8a67cdb19c7d0d562d415"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#gae23af293e9f8a67cdb19c7d0d562d415">ARM_Flash_Uninitialize</a> (void)</td></tr>
<tr class="memdesc:gae23af293e9f8a67cdb19c7d0d562d415"><td class="mdescLeft">&#160;</td><td class="mdescRight">De-initialize the Flash Interface.  <a href="#gae23af293e9f8a67cdb19c7d0d562d415">More...</a><br/></td></tr>
<tr class="separator:gae23af293e9f8a67cdb19c7d0d562d415"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa8baa4618ea33568f8b3752afb2ab5a2"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#gaa8baa4618ea33568f8b3752afb2ab5a2">ARM_Flash_PowerControl</a> (<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:gaa8baa4618ea33568f8b3752afb2ab5a2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control the Flash interface power.  <a href="#gaa8baa4618ea33568f8b3752afb2ab5a2">More...</a><br/></td></tr>
<tr class="separator:gaa8baa4618ea33568f8b3752afb2ab5a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga223138342383219896ed7e255faeb99a"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ga223138342383219896ed7e255faeb99a">ARM_Flash_ReadData</a> (uint32_t addr, void *data, uint32_t cnt)</td></tr>
<tr class="memdesc:ga223138342383219896ed7e255faeb99a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read data from Flash.  <a href="#ga223138342383219896ed7e255faeb99a">More...</a><br/></td></tr>
<tr class="separator:ga223138342383219896ed7e255faeb99a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga947f24ea4042093fdb5605a68ae74f9d"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ga947f24ea4042093fdb5605a68ae74f9d">ARM_Flash_ProgramData</a> (uint32_t addr, const void *data, uint32_t cnt)</td></tr>
<tr class="memdesc:ga947f24ea4042093fdb5605a68ae74f9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Program data to Flash.  <a href="#ga947f24ea4042093fdb5605a68ae74f9d">More...</a><br/></td></tr>
<tr class="separator:ga947f24ea4042093fdb5605a68ae74f9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b2b4fe5a7be579cf3644995a765ea20"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ga0b2b4fe5a7be579cf3644995a765ea20">ARM_Flash_EraseSector</a> (uint32_t addr)</td></tr>
<tr class="memdesc:ga0b2b4fe5a7be579cf3644995a765ea20"><td class="mdescLeft">&#160;</td><td class="mdescRight">Erase Flash Sector.  <a href="#ga0b2b4fe5a7be579cf3644995a765ea20">More...</a><br/></td></tr>
<tr class="separator:ga0b2b4fe5a7be579cf3644995a765ea20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6cbaebe069d31d56c70b1f8f847e2d55"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ga6cbaebe069d31d56c70b1f8f847e2d55">ARM_Flash_EraseChip</a> (void)</td></tr>
<tr class="memdesc:ga6cbaebe069d31d56c70b1f8f847e2d55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Erase complete Flash. Optional function for faster full chip erase.  <a href="#ga6cbaebe069d31d56c70b1f8f847e2d55">More...</a><br/></td></tr>
<tr class="separator:ga6cbaebe069d31d56c70b1f8f847e2d55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06885c0d4587d5a23f97614a8b849ef1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s">ARM_FLASH_STATUS</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> (void)</td></tr>
<tr class="memdesc:ga06885c0d4587d5a23f97614a8b849ef1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get Flash status.  <a href="#ga06885c0d4587d5a23f97614a8b849ef1">More...</a><br/></td></tr>
<tr class="separator:ga06885c0d4587d5a23f97614a8b849ef1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac047b7509356e888502e0424a9d189ae"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#gac047b7509356e888502e0424a9d189ae">ARM_Flash_GetInfo</a> (void)</td></tr>
<tr class="memdesc:gac047b7509356e888502e0424a9d189ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get Flash information.  <a href="#gac047b7509356e888502e0424a9d189ae">More...</a><br/></td></tr>
<tr class="separator:gac047b7509356e888502e0424a9d189ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga97b75555b5433b268add81f2e60f095a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a> (uint32_t event)</td></tr>
<tr class="memdesc:ga97b75555b5433b268add81f2e60f095a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signal Flash event.  <a href="#ga97b75555b5433b268add81f2e60f095a">More...</a><br/></td></tr>
<tr class="separator:ga97b75555b5433b268add81f2e60f095a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Driver API for Flash Device Interface (Driver_Flash.h) </p>
<p>NOR Flash is the preferred technology for embedded applications requiring a discrete non-volatile memory device. The low read latency characteristic of NOR Flash devices allow direct code execution and to store data in a single memory product. Wikipedia offers more information about the <a href="http://en.wikipedia.org/wiki/Flash_memory" target="_blank"><b>Flash Memories</b></a>, including NOR.</p>
<p><b>Flash Structure</b></p>
<p>&#160;</p>
<div class="image">
<img src="NOR_Schematics.png" alt="NOR_Schematics.png"/>
<div class="caption">
Simplified NOR Flash Schematic</div></div>
<p>&#160;</p>
<p><b>Flash API</b></p>
<p>The following header files define the Application Programming Interface (API) for the Flash interface:</p>
<ul>
<li><b>Driver_Flash.h</b> : Driver API for Flash Device Interface</li>
</ul>
<p><b>Driver Functions</b></p>
<p>The driver functions are published in the access struct as explained in <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a></p>
<ul>
<li><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h">ARM_DRIVER_FLASH</a> : access struct for Flash driver functions </li>
</ul>
<hr/><h2 class="groupheader">Data Structure Documentation</h2>
<a name="struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r" id="struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_FLASH_SECTOR</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Flash Sector information. </p>
<p>Specifies sector start and end address.</p>
<p><b>Element of</b>:</p>
<ul>
<li><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a> structure </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a204a3f4fa39b9c007f9784d3e4af4667"></a>uint32_t</td>
<td class="fieldname">
end</td>
<td class="fielddoc">
Sector End address (start+size-1) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a61eb63d26b2fa6c2971603ceccffb14b"></a>uint32_t</td>
<td class="fieldname">
start</td>
<td class="fielddoc">
Sector Start address. </td></tr>
</table>

</div>
</div>
<a name="struct_a_r_m___f_l_a_s_h___i_n_f_o" id="struct_a_r_m___f_l_a_s_h___i_n_f_o"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_FLASH_INFO</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Flash information. </p>
<p>Stores the characteristics of a Flash device. This includes sector layout, programming size and a default value for erased memory. This information can be obtained from the Flash device datasheet and is used by the middleware in order to properly interact with the Flash device.</p>
<p>Sector layout is described by specifying the <em>sector_info</em> which points to an array of sector information (start and end address) and by specifying the <em>sector_count</em> which defines the number of sectors. The element <em>sector_size</em> is not used in this case and needs to be <em>0</em>. Flash sectors need not to be aligned continuously. Gaps are allowed in the device memory space in order to reserve sectors for other usage (for example application code).</p>
<p>When the device has uniform sector size than the sector layout can be described by specifying the <em>sector_size</em> which defines the size of a single sector and by specifying the <em>sector_count</em> which defines the number of sectors. The element <em>sector_info</em> is not used in this case and needs to be <em>NULL</em>.</p>
<p>The smallest programmable unit within a sector is specified by the <em>program_unit</em>. It defines the granularity for programming data.</p>
<p>Optimal programming page size is specified by the <em>page_size</em> and defines the amount of data that should be programmed in one step to achieve maximum programming speed.</p>
<p>Contents of erased memory is specified by the <em>erased_value</em> and is typically <em>0xFF</em>. This value can be used before erasing a sector to check if the sector is blank and erase can be skipped. </p>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a85c3826bf20669d38e466dfd376994db"></a>uint8_t</td>
<td class="fieldname">
erased_value</td>
<td class="fielddoc">
Contents of erased memory (usually 0xFF) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a9dd3e47e968a8f6beb5d88c6d1b7ebe9"></a>uint32_t</td>
<td class="fieldname">
page_size</td>
<td class="fielddoc">
Optimal programming page size in bytes. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a483c41066757e2865bf3a27a2a627a54"></a>uint32_t</td>
<td class="fieldname">
program_unit</td>
<td class="fielddoc">
Smallest programmable unit in bytes. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a50947f9a42bbaa2d68d6e5079150d7bf"></a>uint32_t</td>
<td class="fieldname">
sector_count</td>
<td class="fielddoc">
Number of sectors. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a8dfb9d5160358e45293bba527762238d"></a><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r">ARM_FLASH_SECTOR</a> *</td>
<td class="fieldname">
sector_info</td>
<td class="fielddoc">
Sector layout information (NULL=Uniform sectors) </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a7d37def484362c6e97a2d75144080b1d"></a>uint32_t</td>
<td class="fieldname">
sector_size</td>
<td class="fielddoc">
Uniform sector size in bytes (0=sector_info used) </td></tr>
</table>

</div>
</div>
<a name="struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h" id="struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_DRIVER_FLASH</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Access structure of the Flash Driver. </p>
<p>The functions of the Flash driver are accessed by function pointers exposed by this structure. Refer to <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a> for overview information.</p>
<p>Each instance of a Flash interface provides such an access structure. The instance is identified by a postfix number in the symbol name of the access structure, for example:</p>
<ul>
<li><b>Driver_Flash0</b> is the name of the access struct of the first instance (no. 0).</li>
<li><b>Driver_Flash1</b> is the name of the access struct of the second instance (no. 1).</li>
</ul>
<p>A middleware configuration setting allows connecting the middleware to a specific driver instance <b>Driver_Flash<em>n</em></b>. The default is <span class="XML-Token">0</span>, which connects a middleware to the first instance of a driver. </p>
</div><table class="memberdecls">
<tr><td colspan="2"><h3>Data Fields</h3></td></tr>
<tr class="memitem:a8834b281da48583845c044a81566c1b3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#a8834b281da48583845c044a81566c1b3">GetVersion</a> )(void)</td></tr>
<tr class="memdesc:a8834b281da48583845c044a81566c1b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#ga1cfe24b2ffa571ee50ae544bd922b604">ARM_Flash_GetVersion</a> : Get driver version.  <a href="#a8834b281da48583845c044a81566c1b3">More...</a><br/></td></tr>
<tr class="separator:a8834b281da48583845c044a81566c1b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25076bd7274af5d3b0af6380ed1f0331"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#a25076bd7274af5d3b0af6380ed1f0331">GetCapabilities</a> )(void)</td></tr>
<tr class="memdesc:a25076bd7274af5d3b0af6380ed1f0331"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a> : Get driver capabilities.  <a href="#a25076bd7274af5d3b0af6380ed1f0331">More...</a><br/></td></tr>
<tr class="separator:a25076bd7274af5d3b0af6380ed1f0331"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d1eb2b5c3ee21ba5c92c37e89412567"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#a2d1eb2b5c3ee21ba5c92c37e89412567">Initialize</a> )(<a class="el" href="group__flash__interface__gr.html#gabeb4ad43b1e6fa4ed956cd5c9371d327">ARM_Flash_SignalEvent_t</a> cb_event)</td></tr>
<tr class="memdesc:a2d1eb2b5c3ee21ba5c92c37e89412567"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#gaa5b4bbe529d620d4ad4825588a4c4cf0">ARM_Flash_Initialize</a> : Initialize Flash Interface.  <a href="#a2d1eb2b5c3ee21ba5c92c37e89412567">More...</a><br/></td></tr>
<tr class="separator:a2d1eb2b5c3ee21ba5c92c37e89412567"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcf20681a1402869ecb5c6447fada17b"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#adcf20681a1402869ecb5c6447fada17b">Uninitialize</a> )(void)</td></tr>
<tr class="memdesc:adcf20681a1402869ecb5c6447fada17b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#gae23af293e9f8a67cdb19c7d0d562d415">ARM_Flash_Uninitialize</a> : De-initialize Flash Interface.  <a href="#adcf20681a1402869ecb5c6447fada17b">More...</a><br/></td></tr>
<tr class="separator:adcf20681a1402869ecb5c6447fada17b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba8f1c8019af95ffe19c32403e3240ef"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">PowerControl</a> )(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:aba8f1c8019af95ffe19c32403e3240ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#gaa8baa4618ea33568f8b3752afb2ab5a2">ARM_Flash_PowerControl</a> : Control Flash Interface Power.  <a href="#aba8f1c8019af95ffe19c32403e3240ef">More...</a><br/></td></tr>
<tr class="separator:aba8f1c8019af95ffe19c32403e3240ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adec45569a2f6f0d915a206f8f19107bd"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#adec45569a2f6f0d915a206f8f19107bd">ReadData</a> )(uint32_t addr, void *data, uint32_t cnt)</td></tr>
<tr class="memdesc:adec45569a2f6f0d915a206f8f19107bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#ga223138342383219896ed7e255faeb99a">ARM_Flash_ReadData</a> : Read data from Flash.  <a href="#adec45569a2f6f0d915a206f8f19107bd">More...</a><br/></td></tr>
<tr class="separator:adec45569a2f6f0d915a206f8f19107bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a429fc193f1ec62858219ab8749c563ae"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#a429fc193f1ec62858219ab8749c563ae">ProgramData</a> )(uint32_t addr, const void *data, uint32_t cnt)</td></tr>
<tr class="memdesc:a429fc193f1ec62858219ab8749c563ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#ga947f24ea4042093fdb5605a68ae74f9d">ARM_Flash_ProgramData</a> : Program data to Flash.  <a href="#a429fc193f1ec62858219ab8749c563ae">More...</a><br/></td></tr>
<tr class="separator:a429fc193f1ec62858219ab8749c563ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9d78f9fe07aabf12b23b95239818b55"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ad9d78f9fe07aabf12b23b95239818b55">EraseSector</a> )(uint32_t addr)</td></tr>
<tr class="memdesc:ad9d78f9fe07aabf12b23b95239818b55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#ga0b2b4fe5a7be579cf3644995a765ea20">ARM_Flash_EraseSector</a> : Erase Flash Sector.  <a href="#ad9d78f9fe07aabf12b23b95239818b55">More...</a><br/></td></tr>
<tr class="separator:ad9d78f9fe07aabf12b23b95239818b55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae873705c743d94572fb6500421e15760"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ae873705c743d94572fb6500421e15760">EraseChip</a> )(void)</td></tr>
<tr class="memdesc:ae873705c743d94572fb6500421e15760"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#ga6cbaebe069d31d56c70b1f8f847e2d55">ARM_Flash_EraseChip</a> : Erase complete Flash.  <a href="#ae873705c743d94572fb6500421e15760">More...</a><br/></td></tr>
<tr class="separator:ae873705c743d94572fb6500421e15760"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc1db6f33f777784f3c95efc816d4856"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s">ARM_FLASH_STATUS</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#afc1db6f33f777784f3c95efc816d4856">GetStatus</a> )(void)</td></tr>
<tr class="memdesc:afc1db6f33f777784f3c95efc816d4856"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> : Get Flash status.  <a href="#afc1db6f33f777784f3c95efc816d4856">More...</a><br/></td></tr>
<tr class="separator:afc1db6f33f777784f3c95efc816d4856"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae64d4ee61b7a7ee0b38a0ef2b61f1db2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a> *(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__flash__interface__gr.html#ae64d4ee61b7a7ee0b38a0ef2b61f1db2">GetInfo</a> )(void)</td></tr>
<tr class="memdesc:ae64d4ee61b7a7ee0b38a0ef2b61f1db2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__flash__interface__gr.html#gac047b7509356e888502e0424a9d189ae">ARM_Flash_GetInfo</a> : Get Flash information.  <a href="#ae64d4ee61b7a7ee0b38a0ef2b61f1db2">More...</a><br/></td></tr>
<tr class="separator:ae64d4ee61b7a7ee0b38a0ef2b61f1db2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h4 class="groupheader">Field Documentation</h4>
<a class="anchor" id="ae873705c743d94572fb6500421e15760"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* EraseChip)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#ga6cbaebe069d31d56c70b1f8f847e2d55">ARM_Flash_EraseChip</a> : Erase complete Flash. </p>

</div>
</div>
<a class="anchor" id="ad9d78f9fe07aabf12b23b95239818b55"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* EraseSector)(uint32_t addr)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#ga0b2b4fe5a7be579cf3644995a765ea20">ARM_Flash_EraseSector</a> : Erase Flash Sector. </p>

</div>
</div>
<a class="anchor" id="a25076bd7274af5d3b0af6380ed1f0331"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a>(* GetCapabilities)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a> : Get driver capabilities. </p>

</div>
</div>
<a class="anchor" id="ae64d4ee61b7a7ee0b38a0ef2b61f1db2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a>*(* GetInfo)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#gac047b7509356e888502e0424a9d189ae">ARM_Flash_GetInfo</a> : Get Flash information. </p>

</div>
</div>
<a class="anchor" id="afc1db6f33f777784f3c95efc816d4856"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s">ARM_FLASH_STATUS</a>(* GetStatus)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> : Get Flash status. </p>

</div>
</div>
<a class="anchor" id="a8834b281da48583845c044a81566c1b3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(* GetVersion)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#ga1cfe24b2ffa571ee50ae544bd922b604">ARM_Flash_GetVersion</a> : Get driver version. </p>

</div>
</div>
<a class="anchor" id="a2d1eb2b5c3ee21ba5c92c37e89412567"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Initialize)(<a class="el" href="group__flash__interface__gr.html#gabeb4ad43b1e6fa4ed956cd5c9371d327">ARM_Flash_SignalEvent_t</a> cb_event)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#gaa5b4bbe529d620d4ad4825588a4c4cf0">ARM_Flash_Initialize</a> : Initialize Flash Interface. </p>

</div>
</div>
<a class="anchor" id="aba8f1c8019af95ffe19c32403e3240ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* PowerControl)(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#gaa8baa4618ea33568f8b3752afb2ab5a2">ARM_Flash_PowerControl</a> : Control Flash Interface Power. </p>

</div>
</div>
<a class="anchor" id="a429fc193f1ec62858219ab8749c563ae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* ProgramData)(uint32_t addr, const void *data, uint32_t cnt)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#ga947f24ea4042093fdb5605a68ae74f9d">ARM_Flash_ProgramData</a> : Program data to Flash. </p>

</div>
</div>
<a class="anchor" id="adec45569a2f6f0d915a206f8f19107bd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* ReadData)(uint32_t addr, void *data, uint32_t cnt)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#ga223138342383219896ed7e255faeb99a">ARM_Flash_ReadData</a> : Read data from Flash. </p>

</div>
</div>
<a class="anchor" id="adcf20681a1402869ecb5c6447fada17b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Uninitialize)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#gae23af293e9f8a67cdb19c7d0d562d415">ARM_Flash_Uninitialize</a> : De-initialize Flash Interface. </p>

</div>
</div>

</div>
</div>
<a name="struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s" id="struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_FLASH_CAPABILITIES</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Flash Driver Capabilities. </p>
<p>A Flash driver can be implemented with different capabilities. The bitfield members of this struct encode the capabilities implemented by this driver.</p>
<p>The element <em>event_ready</em> indicates that the driver is able to generate the <a class="el" href="group___flash__events.html#gaf7a9c4ad125ee90df35907d861151e23">ARM_FLASH_EVENT_READY</a> event. In case that this event is not available it is possible to poll the driver status by calling the <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> and check the <em>busy</em> flag.</p>
<p>The element <em>data_width</em> specifies the data access size and also defines the data type (uint8_t, uint16_t or uint32_t) for the <em>data</em> parameter in <a class="el" href="group__flash__interface__gr.html#ga223138342383219896ed7e255faeb99a">ARM_Flash_ReadData</a> and <a class="el" href="group__flash__interface__gr.html#ga947f24ea4042093fdb5605a68ae74f9d">ARM_Flash_ProgramData</a> functions.</p>
<p>The element <em>erase_chip</em> specifies that the <a class="el" href="group__flash__interface__gr.html#ga6cbaebe069d31d56c70b1f8f847e2d55">ARM_Flash_EraseChip</a> function is supported. Typically full chip erase is much faster than erasing the whole device sector per sector.</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a04c173610dd0a545ecae308e342aafb0"></a>uint32_t</td>
<td class="fieldname">
data_width: 2</td>
<td class="fielddoc">
Data width: 0=8-bit, 1=16-bit, 2=32-bit. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="af5ec2b569c193fc5024c2739f46b328a"></a>uint32_t</td>
<td class="fieldname">
erase_chip: 1</td>
<td class="fielddoc">
Supports EraseChip operation. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="add296ba516c8fc17ba51e30f2a00f0a9"></a>uint32_t</td>
<td class="fieldname">
event_ready: 1</td>
<td class="fielddoc">
Signal Flash Ready event. </td></tr>
</table>

</div>
</div>
<a name="struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s" id="struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_FLASH_STATUS</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Flash Status. </p>
<p>Structure with information about the status of the Flash.</p>
<p>The flag <em>busy</em> indicates that the driver is busy executing read/program/erase operation.</p>
<p>The flag <em>error</em> flag is cleared on start of read/program/erase operation and is set at the end of the current operation in case of error.</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a50c88f3c1d787773e2ac1b59533f034a"></a>uint32_t</td>
<td class="fieldname">
busy: 1</td>
<td class="fielddoc">
Flash busy flag. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="aa8d183302fdfa4a6892f1d80300cdb32"></a>uint32_t</td>
<td class="fieldname">
error: 1</td>
<td class="fielddoc">
Read/Program/Erase error flag (cleared on start of next operation) </td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="gabeb4ad43b1e6fa4ed956cd5c9371d327"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ARM_Flash_SignalEvent_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a> : Signal Flash Event. </p>
<p>Provides the typedef for the callback function <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a>.</p>
<p><b>Parameter for:</b></p>
<ul>
<li><a class="el" href="group__flash__interface__gr.html#gaa5b4bbe529d620d4ad4825588a4c4cf0">ARM_Flash_Initialize</a> </li>
</ul>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga6cbaebe069d31d56c70b1f8f847e2d55"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_Flash_EraseChip </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Erase complete Flash. Optional function for faster full chip erase. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>This <b>optional</b> function erases the complete device. If the device does not support global erase or only a portion of the Flash memory space is used for storing files then this functions returns the error value <a class="el" href="group__execution__status.html#ga2efa59e480d82697795439220e6884e4">ARM_DRIVER_ERROR_UNSUPPORTED</a>. Function <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a> reports if the function <b>ARM_Flash_EraseChip</b> is supported.</p>
<p>The function is non-blocking and returns as soon as the driver has started the operation. When the operation is completed the <a class="el" href="group___flash__events.html#gaf7a9c4ad125ee90df35907d861151e23">ARM_FLASH_EVENT_READY</a> event is generated (if supported and reported by <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a>). In case of errors the <a class="el" href="group___flash__events.html#ga0dfea52761c0eed83e5d73e7a7f69962">ARM_FLASH_EVENT_ERROR</a> event is generated at the same time. Progress of the operation can also be monitored by calling the <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> function and checking the <em>busy</em> flag. </p>

</div>
</div>
<a class="anchor" id="ga0b2b4fe5a7be579cf3644995a765ea20"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_Flash_EraseSector </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>addr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Erase Flash Sector. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">addr</td><td>Sector address </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>This function erases a flash sector specified by the parameter <em>adr</em> (points to start of the sector).</p>
<p>The function is non-blocking and returns as soon as the driver has started the operation. When the operation is completed the <a class="el" href="group___flash__events.html#gaf7a9c4ad125ee90df35907d861151e23">ARM_FLASH_EVENT_READY</a> event is generated (if supported and reported by <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a>). In case of errors the <a class="el" href="group___flash__events.html#ga0dfea52761c0eed83e5d73e7a7f69962">ARM_FLASH_EVENT_ERROR</a> event is generated at the same time. Progress of the operation can also be monitored by calling the <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> function and checking the <em>busy</em> flag. </p>

</div>
</div>
<a class="anchor" id="ga27c23c998032cd47cb47293c0185ee5d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a> ARM_Flash_GetCapabilities </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver capabilities. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a></dd></dl>
<p>Retrieves information about capabilities in this driver implementation. The bitfield members of the struct <a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a> encode various capabilities, for example if a hardware is able to create signal events using the <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a> callback function.</p>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h" title="Access structure of the Flash Driver.">ARM_DRIVER_FLASH</a> Driver_Flash0;</div>
<div class="line"><a class="code" href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h" title="Access structure of the Flash Driver.">ARM_DRIVER_FLASH</a> *drv_info;</div>
<div class="line">  </div>
<div class="line"><span class="keywordtype">void</span> read_capabilities (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s" title="Flash Driver Capabilities.">ARM_FLASH_CAPABILITIES</a> drv_capabilities;</div>
<div class="line"> </div>
<div class="line">  drv_info = &amp;Driver_Flash0;  </div>
<div class="line">  drv_capabilities = drv_info-&gt;<a class="code" href="group__flash__interface__gr.html#a25076bd7274af5d3b0af6380ed1f0331" title="Pointer to ARM_Flash_GetCapabilities : Get driver capabilities.">GetCapabilities</a> ();</div>
<div class="line">  <span class="comment">// interrogate capabilities</span></div>
<div class="line"> </div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="gac047b7509356e888502e0424a9d189ae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a> * ARM_Flash_GetInfo </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get Flash information. </p>
<dl class="section return"><dt>Returns</dt><dd>Pointer to Flash information <a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a></dd></dl>
<p>Retrieves information about the Flash device. </p>

</div>
</div>
<a class="anchor" id="ga06885c0d4587d5a23f97614a8b849ef1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s">ARM_FLASH_STATUS</a> ARM_Flash_GetStatus </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get Flash status. </p>
<dl class="section return"><dt>Returns</dt><dd>Flash status <a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s">ARM_FLASH_STATUS</a></dd></dl>
<p>Retrieves current Flash interface status. </p>

</div>
</div>
<a class="anchor" id="ga1cfe24b2ffa571ee50ae544bd922b604"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a> ARM_Flash_GetVersion </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver version. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></dd></dl>
<p>Returns version information of the driver implementation in <a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></p>
<ul>
<li>API version is the version of the CMSIS-Driver specification used to implement this driver.</li>
<li>Driver version is source code version of the actual driver implementation.</li>
</ul>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h" title="Access structure of the Flash Driver.">ARM_DRIVER_FLASH</a> Driver_Flash0;</div>
<div class="line"><a class="code" href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h" title="Access structure of the Flash Driver.">ARM_DRIVER_FLASH</a> *drv_info;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> read_version (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n" title="Driver Version.">ARM_DRIVER_VERSION</a>  version;</div>
<div class="line"> </div>
<div class="line">  drv_info = &amp;Driver_Flash0;  </div>
<div class="line">  version = drv_info-&gt;<a class="code" href="group__flash__interface__gr.html#a8834b281da48583845c044a81566c1b3" title="Pointer to ARM_Flash_GetVersion : Get driver version.">GetVersion</a> ();</div>
<div class="line">  <span class="keywordflow">if</span> (version.<a class="code" href="group__common__drv__gr.html#ad180da20fbde1d3dafc074af87c19540" title="API version.">api</a> &lt; 0x10A)   {      <span class="comment">// requires at minimum API version 1.10 or higher</span></div>
<div class="line">    <span class="comment">// error handling</span></div>
<div class="line">    <span class="keywordflow">return</span>;</div>
<div class="line">  }</div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="gaa5b4bbe529d620d4ad4825588a4c4cf0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_Flash_Initialize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__flash__interface__gr.html#gabeb4ad43b1e6fa4ed956cd5c9371d327">ARM_Flash_SignalEvent_t</a>&#160;</td>
          <td class="paramname"><em>cb_event</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize the Flash Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">cb_event</td><td>Pointer to <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function initializes the Flash interface. It is called when the middleware component starts operation.</p>
<p>The function performs the following operations:</p>
<ul>
<li>Initializes the resources needed for the Flash interface.</li>
<li>Registers the <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a> callback function.</li>
</ul>
<p>The parameter <em>cb_event</em> is a pointer to the <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">ARM_Flash_SignalEvent</a> callback function; use a NULL pointer when no callback signals are required.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__flash__interface__gr.html">Flash Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="gaa8baa4618ea33568f8b3752afb2ab5a2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_Flash_PowerControl </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a>&#160;</td>
          <td class="paramname"><em>state</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control the Flash interface power. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">state</td><td>Power state </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Control Flash Interface Power. </p>

</div>
</div>
<a class="anchor" id="ga947f24ea4042093fdb5605a68ae74f9d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_Flash_ProgramData </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>cnt</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Program data to Flash. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">addr</td><td>Data address. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">data</td><td>Pointer to a buffer containing the data to be programmed to Flash. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cnt</td><td>Number of data items to program. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>number of data items programmed or <a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>This function programs data to the Flash device.</p>
<p>The parameter <em>addr</em> specifies the address to where to program data (needs to be aligned to <em>program_unit</em> specified in <a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a>). The parameter <em>data</em> specifies the pointer to a buffer containing data to be programmed. The data type is <em>uint8_t</em>, <em>uint16_t</em> or <em>uint32_t</em> and is specified by the <em>data_width</em> in <a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a>. The parameter <em>cnt</em> specifies the number of data items to program (data size needs to be a multiple of <em>program_unit</em>).</p>
<p>The function executes in the following ways:</p>
<ul>
<li>When the operation is non-blocking (typically) then the function only starts the operation and returns with zero number of data items programmed. When the operation is completed the <a class="el" href="group___flash__events.html#gaf7a9c4ad125ee90df35907d861151e23">ARM_FLASH_EVENT_READY</a> event is generated (if supported and reported by <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a>). In case of errors the <a class="el" href="group___flash__events.html#ga0dfea52761c0eed83e5d73e7a7f69962">ARM_FLASH_EVENT_ERROR</a> event is generated at the same time. Progress of the operation can also be monitored by calling the <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> function and checking the <em>busy</em> flag.</li>
<li>When the operation is blocking then the function returns after the data is programmed and returns the number of data items programmed. </li>
</ul>

</div>
</div>
<a class="anchor" id="ga223138342383219896ed7e255faeb99a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_Flash_ReadData </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>cnt</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Read data from Flash. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">addr</td><td>Data address. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">data</td><td>Pointer to a buffer storing the data read from Flash. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cnt</td><td>Number of data items to read. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>number of data items read or <a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>This function reads data from the Flash device.</p>
<p>The parameter <em>addr</em> specifies the address from where to read data (needs to be aligned to data type size). The parameter <em>data</em> specifies the pointer to a buffer storing the data read. The data type is <em>uint8_t</em>, <em>uint16_t</em> or <em>uint32_t</em> and is specified by the <em>data_width</em> in <a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a>. The parameter <em>cnt</em> specifies the number of data items to read.</p>
<p>The function executes in the following ways:</p>
<ul>
<li>When the operation is non-blocking (typical for SPI Flash) then the function only starts the operation and returns with zero number of data items read. When the operation is completed the <a class="el" href="group___flash__events.html#gaf7a9c4ad125ee90df35907d861151e23">ARM_FLASH_EVENT_READY</a> event is generated (if supported and reported by <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a>). In case of errors the <a class="el" href="group___flash__events.html#ga0dfea52761c0eed83e5d73e7a7f69962">ARM_FLASH_EVENT_ERROR</a> event is generated at the same time. Progress of the operation can also be monitored by calling the <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">ARM_Flash_GetStatus</a> function and checking the <em>busy</em> flag.</li>
<li>When the operation is blocking (typical for memory mapped Flash) then the function returns after the data is read and returns the number of data items read. </li>
</ul>

</div>
</div>
<a class="anchor" id="ga97b75555b5433b268add81f2e60f095a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void ARM_Flash_SignalEvent </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>event</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signal Flash event. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">event</td><td>Event notification mask </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p>The function <b>ARM_Flash_SignalEvent</b> notifies the application of the <a class="el" href="group___flash__events.html">Flash Events</a> and it is registered by the function <a class="el" href="group__flash__interface__gr.html#gaa5b4bbe529d620d4ad4825588a4c4cf0">ARM_Flash_Initialize</a>. The function is called automatically after read/program/erase operation completes. The function <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">ARM_Flash_GetCapabilities</a> returns information about the implemented optional events in a driver.</p>
<p>The argument <em>event</em> represents the notification mask of the events. Each event is coded in a separate bit and therefore it is possible to signal multiple events in the event call back function. The following call back notifications are generated:</p>
<table class="doxtable">
<tr>
<th>Bit </th><th align="left">Event </th><th align="left">Description</th></tr>
<tr>
<td>0 </td><td align="left"><a class="el" href="group___flash__events.html#gaf7a9c4ad125ee90df35907d861151e23">ARM_FLASH_EVENT_READY</a> </td><td align="left">Occurs after read/program/erase operation completes. </td></tr>
<tr>
<td>1 </td><td align="left"><a class="el" href="group___flash__events.html#ga0dfea52761c0eed83e5d73e7a7f69962">ARM_FLASH_EVENT_ERROR</a> </td><td align="left">Occurs together with <a class="el" href="group___flash__events.html#gaf7a9c4ad125ee90df35907d861151e23">ARM_FLASH_EVENT_READY</a> when operation completes with errors. </td></tr>
</table>

</div>
</div>
<a class="anchor" id="gae23af293e9f8a67cdb19c7d0d562d415"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_Flash_Uninitialize </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>De-initialize the Flash Interface. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__flash__interface__gr.html#gae23af293e9f8a67cdb19c7d0d562d415">ARM_Flash_Uninitialize</a> de-initializes the resources of Flash interface.</p>
<p>It is called when the middleware component stops operation and releases the software resources used by the interface. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
