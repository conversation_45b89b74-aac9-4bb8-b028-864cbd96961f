<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Bilinear Interpolation</title>
<title>CMSIS-DSP: Bilinear Interpolation</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___bilinear_interpolate.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Bilinear Interpolation</div>  </div>
<div class="ingroups"><a class="el" href="group__group_interpolation.html">Interpolation Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gab49a4c0f64854903d996d01ba38f711a"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___bilinear_interpolate.html#gab49a4c0f64854903d996d01ba38f711a">arm_bilinear_interp_f32</a> (const <a class="el" href="structarm__bilinear__interp__instance__f32.html">arm_bilinear_interp_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> X, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> Y)</td></tr>
<tr class="memdesc:gab49a4c0f64854903d996d01ba38f711a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Floating-point bilinear interpolation.  <a href="#gab49a4c0f64854903d996d01ba38f711a"></a><br/></td></tr>
<tr class="separator:gab49a4c0f64854903d996d01ba38f711a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga202a033c8a2ad3678b136f93153b6d13"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___bilinear_interpolate.html#ga202a033c8a2ad3678b136f93153b6d13">arm_bilinear_interp_q31</a> (<a class="el" href="structarm__bilinear__interp__instance__q31.html">arm_bilinear_interp_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> X, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> Y)</td></tr>
<tr class="memdesc:ga202a033c8a2ad3678b136f93153b6d13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q31 bilinear interpolation.  <a href="#ga202a033c8a2ad3678b136f93153b6d13"></a><br/></td></tr>
<tr class="separator:ga202a033c8a2ad3678b136f93153b6d13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa8dffbc2a01bb7accf231384498ec85e"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___bilinear_interpolate.html#gaa8dffbc2a01bb7accf231384498ec85e">arm_bilinear_interp_q15</a> (<a class="el" href="structarm__bilinear__interp__instance__q15.html">arm_bilinear_interp_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> X, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> Y)</td></tr>
<tr class="memdesc:gaa8dffbc2a01bb7accf231384498ec85e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q15 bilinear interpolation.  <a href="#gaa8dffbc2a01bb7accf231384498ec85e"></a><br/></td></tr>
<tr class="separator:gaa8dffbc2a01bb7accf231384498ec85e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade8db9706a3ae9ad03b2750a239d2ee6"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___bilinear_interpolate.html#gade8db9706a3ae9ad03b2750a239d2ee6">arm_bilinear_interp_q7</a> (<a class="el" href="structarm__bilinear__interp__instance__q7.html">arm_bilinear_interp_instance_q7</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> X, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> Y)</td></tr>
<tr class="memdesc:gade8db9706a3ae9ad03b2750a239d2ee6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q7 bilinear interpolation.  <a href="#gade8db9706a3ae9ad03b2750a239d2ee6"></a><br/></td></tr>
<tr class="separator:gade8db9706a3ae9ad03b2750a239d2ee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Bilinear interpolation is an extension of linear interpolation applied to a two dimensional grid. The underlying function <code>f(x, y)</code> is sampled on a regular grid and the interpolation process determines values between the grid points. Bilinear interpolation is equivalent to two step linear interpolation, first in the x-dimension and then in the y-dimension. Bilinear interpolation is often used in image processing to rescale images. The CMSIS DSP library provides bilinear interpolation functions for Q7, Q15, Q31, and floating-point data types.</p>
<p><b>Algorithm</b> </p>
<dl class="section user"><dt></dt><dd>The instance structure used by the bilinear interpolation functions describes a two dimensional data table. For floating-point, the instance structure is defined as: <pre>
  typedef struct
  {
    uint16_t numRows;
    uint16_t numCols;
    float32_t *pData;
} <a class="el" href="structarm__bilinear__interp__instance__f32.html" title="Instance structure for the floating-point bilinear interpolation function.">arm_bilinear_interp_instance_f32</a>;
</pre></dd></dl>
<dl class="section user"><dt></dt><dd>where <code>numRows</code> specifies the number of rows in the table; <code>numCols</code> specifies the number of columns in the table; and <code>pData</code> points to an array of size <code>numRows*numCols</code> values. The data table <code>pTable</code> is organized in row order and the supplied data values fall on integer indexes. That is, table element (x,y) is located at <code>pTable[x + y*numCols]</code> where x and y are integers.</dd></dl>
<dl class="section user"><dt></dt><dd>Let <code>(x, y)</code> specify the desired interpolation point. Then define: <pre>
    XF = floor(x)
    YF = floor(y)
</pre> </dd></dl>
<dl class="section user"><dt></dt><dd>The interpolated output point is computed as: <pre>
 f(x, y) = f(XF, YF) * (1-(x-XF)) * (1-(y-YF))
          + f(XF+1, YF) * (x-XF)*(1-(y-YF))
          + f(XF, YF+1) * (1-(x-XF))*(y-YF)
          + f(XF+1, YF+1) * (x-XF)*(y-YF)
</pre> Note that the coordinates (x, y) contain integer and fractional components. The integer components specify which portion of the table to use while the fractional components control the interpolation processor.</dd></dl>
<dl class="section user"><dt></dt><dd>if (x,y) are outside of the table boundary, Bilinear interpolation returns zero output. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gab49a4c0f64854903d996d01ba38f711a"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> arm_bilinear_interp_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__bilinear__interp__instance__f32.html">arm_bilinear_interp_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td>
          <td class="paramname"><em>X</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td>
          <td class="paramname"><em>Y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the interpolation structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">X</td><td>interpolation coordinate. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Y</td><td>interpolation coordinate. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>out interpolated value. </dd></dl>

<p>References <a class="el" href="structarm__bilinear__interp__instance__f32.html#aede17bebfb1f835b61d71dd813eab3f8">arm_bilinear_interp_instance_f32::numCols</a>, <a class="el" href="structarm__bilinear__interp__instance__f32.html#a34f2b17cc57b95011960df9718af6ed6">arm_bilinear_interp_instance_f32::numRows</a>, and <a class="el" href="structarm__bilinear__interp__instance__f32.html#afd1e764591c991c212d56c893efb5ea4">arm_bilinear_interp_instance_f32::pData</a>.</p>

</div>
</div>
<a class="anchor" id="gaa8dffbc2a01bb7accf231384498ec85e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> arm_bilinear_interp_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__bilinear__interp__instance__q15.html">arm_bilinear_interp_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>X</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>Y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the interpolation structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">X</td><td>interpolation coordinate in 12.20 format. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Y</td><td>interpolation coordinate in 12.20 format. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>out interpolated value. </dd></dl>

<p>References <a class="el" href="structarm__bilinear__interp__instance__q15.html#a7fa8772d01583374ff8ac18205a26a37">arm_bilinear_interp_instance_q15::numCols</a>, <a class="el" href="structarm__bilinear__interp__instance__q15.html#a2130ae30a804995a9f5d0e2189e08565">arm_bilinear_interp_instance_q15::numRows</a>, and <a class="el" href="structarm__bilinear__interp__instance__q15.html#a50d75b1316cee3e0dfad6dcc4c9a2954">arm_bilinear_interp_instance_q15::pData</a>.</p>

</div>
</div>
<a class="anchor" id="ga202a033c8a2ad3678b136f93153b6d13"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> arm_bilinear_interp_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__bilinear__interp__instance__q31.html">arm_bilinear_interp_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>X</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>Y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the interpolation structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">X</td><td>interpolation coordinate in 12.20 format. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Y</td><td>interpolation coordinate in 12.20 format. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>out interpolated value. </dd></dl>

<p>References <a class="el" href="structarm__bilinear__interp__instance__q31.html#a6c3eff4eb17ff1d43f170efb84713a2d">arm_bilinear_interp_instance_q31::numCols</a>, <a class="el" href="structarm__bilinear__interp__instance__q31.html#a2082e3eac56354d75291f03e96ce4aa5">arm_bilinear_interp_instance_q31::numRows</a>, and <a class="el" href="structarm__bilinear__interp__instance__q31.html#a843eae0c9db5f815e77e1aaf9afea358">arm_bilinear_interp_instance_q31::pData</a>.</p>

</div>
</div>
<a class="anchor" id="gade8db9706a3ae9ad03b2750a239d2ee6"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> arm_bilinear_interp_q7 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__bilinear__interp__instance__q7.html">arm_bilinear_interp_instance_q7</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>X</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>Y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the interpolation structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">X</td><td>interpolation coordinate in 12.20 format. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Y</td><td>interpolation coordinate in 12.20 format. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>out interpolated value. </dd></dl>

<p>References <a class="el" href="structarm__bilinear__interp__instance__q7.html#a860dd0d24380ea06cfbb348fb3b12c9a">arm_bilinear_interp_instance_q7::numCols</a>, <a class="el" href="structarm__bilinear__interp__instance__q7.html#ad5a8067cab5f9ea4688b11a623e16607">arm_bilinear_interp_instance_q7::numRows</a>, and <a class="el" href="structarm__bilinear__interp__instance__q7.html#af05194d691bbefb02c34bafb22ca9ef0">arm_bilinear_interp_instance_q7::pData</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
