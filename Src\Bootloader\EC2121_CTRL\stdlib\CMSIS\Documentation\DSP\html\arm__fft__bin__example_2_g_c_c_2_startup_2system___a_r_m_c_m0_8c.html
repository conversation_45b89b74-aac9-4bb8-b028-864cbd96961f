<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>system_ARMCM0.c File Reference</title>
<title>CMSIS-DSP: system_ARMCM0.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">arm_fft_bin_example/GCC/Startup/system_ARMCM0.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:abbd628d8a30e6695b3715ae72a693e56"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">__HSI</a></td></tr>
<tr class="separator:abbd628d8a30e6695b3715ae72a693e56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8687edecd98881631a879bd10528c7da"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">__XTAL</a></td></tr>
<tr class="separator:a8687edecd98881631a879bd10528c7da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16323f44d2b5b11ef3972f71339cbd39"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">__SYSTEM_CLOCK</a></td></tr>
<tr class="separator:a16323f44d2b5b11ef3972f71339cbd39"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ae0c36a9591fe6e9c45ecb21a794f0f0f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">SystemCoreClockUpdate</a> (void)</td></tr>
<tr class="separator:ae0c36a9591fe6e9c45ecb21a794f0f0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93f514700ccf00d08dbdcff7f1224eb2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">SystemInit</a> (void)</td></tr>
<tr class="memdesc:a93f514700ccf00d08dbdcff7f1224eb2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Setup the microcontroller system. Initialize the System.  <a href="#a93f514700ccf00d08dbdcff7f1224eb2"></a><br/></td></tr>
<tr class="separator:a93f514700ccf00d08dbdcff7f1224eb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:aa3cd3e43291e81e795d642b79b6088e6"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#aa3cd3e43291e81e795d642b79b6088e6">SystemCoreClock</a></td></tr>
<tr class="separator:aa3cd3e43291e81e795d642b79b6088e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="abbd628d8a30e6695b3715ae72a693e56"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __HSI</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a16323f44d2b5b11ef3972f71339cbd39"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __SYSTEM_CLOCK</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">SystemCoreClockUpdate()</a>, and <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">SystemInit()</a>.</p>

</div>
</div>
<a class="anchor" id="a8687edecd98881631a879bd10528c7da"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define __XTAL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ae0c36a9591fe6e9c45ecb21a794f0f0f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void SystemCoreClockUpdate </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>References <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">__SYSTEM_CLOCK</a>, and <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#aa3cd3e43291e81e795d642b79b6088e6">SystemCoreClock</a>.</p>

</div>
</div>
<a class="anchor" id="a93f514700ccf00d08dbdcff7f1224eb2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void SystemInit </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Initialize the system</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">none</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none </dd></dl>

<p>References <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">__SYSTEM_CLOCK</a>, and <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#aa3cd3e43291e81e795d642b79b6088e6">SystemCoreClock</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="aa3cd3e43291e81e795d642b79b6088e6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t SystemCoreClock</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>System Clock Frequency (Core Clock) </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_50f4d4f91ce5cd72cb6928b47e85a7f8.html">Examples</a></li><li class="navelem"><a class="el" href="dir_38d31328c42027cc5452e7496de7b88f.html">arm_fft_bin_example</a></li><li class="navelem"><a class="el" href="dir_38b9476ff75f3e6ddc67484ee999428d.html">GCC</a></li><li class="navelem"><a class="el" href="dir_4104b0ce39688295b07e7d8423237024.html">Startup</a></li><li class="navelem"><a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html">system_ARMCM0.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
