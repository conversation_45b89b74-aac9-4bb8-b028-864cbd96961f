<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Ethernet MAC Controls</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__eth__mac__ctrls.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">Ethernet MAC Controls<div class="ingroups"><a class="el" href="group__eth__mac__control.html">Ethernet MAC Control Codes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Configure and control the Ethernet MAC interface.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga7819c7a1aa7bbc13dc42d0fd7e75a23c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__ctrls.html#ga7819c7a1aa7bbc13dc42d0fd7e75a23c">ARM_ETH_MAC_CONFIGURE</a>&#160;&#160;&#160;(0x01)</td></tr>
<tr class="memdesc:ga7819c7a1aa7bbc13dc42d0fd7e75a23c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Configure MAC; arg = configuration.  <a href="#ga7819c7a1aa7bbc13dc42d0fd7e75a23c">More...</a><br/></td></tr>
<tr class="separator:ga7819c7a1aa7bbc13dc42d0fd7e75a23c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a98c8a7ee5ed4b1ffd250eecaeefe5c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__ctrls.html#ga3a98c8a7ee5ed4b1ffd250eecaeefe5c">ARM_ETH_MAC_CONTROL_TX</a>&#160;&#160;&#160;(0x02)</td></tr>
<tr class="memdesc:ga3a98c8a7ee5ed4b1ffd250eecaeefe5c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transmitter; arg: 0=disabled, 1=enabled.  <a href="#ga3a98c8a7ee5ed4b1ffd250eecaeefe5c">More...</a><br/></td></tr>
<tr class="separator:ga3a98c8a7ee5ed4b1ffd250eecaeefe5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae0964364b81b38b6e1fbf7196f3be869"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__ctrls.html#gae0964364b81b38b6e1fbf7196f3be869">ARM_ETH_MAC_CONTROL_RX</a>&#160;&#160;&#160;(0x03)</td></tr>
<tr class="memdesc:gae0964364b81b38b6e1fbf7196f3be869"><td class="mdescLeft">&#160;</td><td class="mdescRight">Receiver; arg: 0=disabled, 1=enabled.  <a href="#gae0964364b81b38b6e1fbf7196f3be869">More...</a><br/></td></tr>
<tr class="separator:gae0964364b81b38b6e1fbf7196f3be869"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga530812ef349a2e297f23de72e660fe27"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__ctrls.html#ga530812ef349a2e297f23de72e660fe27">ARM_ETH_MAC_FLUSH</a>&#160;&#160;&#160;(0x04)</td></tr>
<tr class="memdesc:ga530812ef349a2e297f23de72e660fe27"><td class="mdescLeft">&#160;</td><td class="mdescRight">Flush buffer; arg = ARM_ETH_MAC_FLUSH_...  <a href="#ga530812ef349a2e297f23de72e660fe27">More...</a><br/></td></tr>
<tr class="separator:ga530812ef349a2e297f23de72e660fe27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4afe66589216f566f529af52f9075fdf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__ctrls.html#ga4afe66589216f566f529af52f9075fdf">ARM_ETH_MAC_SLEEP</a>&#160;&#160;&#160;(0x05)</td></tr>
<tr class="memdesc:ga4afe66589216f566f529af52f9075fdf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sleep mode; arg: 1=enter and wait for Magic packet, 0=exit.  <a href="#ga4afe66589216f566f529af52f9075fdf">More...</a><br/></td></tr>
<tr class="separator:ga4afe66589216f566f529af52f9075fdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab332b58ba320e73864830dc42ad74181"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__ctrls.html#gab332b58ba320e73864830dc42ad74181">ARM_ETH_MAC_VLAN_FILTER</a>&#160;&#160;&#160;(0x06)</td></tr>
<tr class="memdesc:gab332b58ba320e73864830dc42ad74181"><td class="mdescLeft">&#160;</td><td class="mdescRight">VLAN Filter for received frames; arg15..0: VLAN Tag; arg16: optional ARM_ETH_MAC_VLAN_FILTER_ID_ONLY.  <a href="#gab332b58ba320e73864830dc42ad74181">More...</a><br/></td></tr>
<tr class="separator:gab332b58ba320e73864830dc42ad74181"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Configure and control the Ethernet MAC interface. </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="ga7819c7a1aa7bbc13dc42d0fd7e75a23c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_CONFIGURE&#160;&#160;&#160;(0x01)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Configure MAC; arg = configuration. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536" title="Control Ethernet Interface.">ARM_ETH_MAC_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae0964364b81b38b6e1fbf7196f3be869"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_CONTROL_RX&#160;&#160;&#160;(0x03)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Receiver; arg: 0=disabled, 1=enabled. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536" title="Control Ethernet Interface.">ARM_ETH_MAC_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3a98c8a7ee5ed4b1ffd250eecaeefe5c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_CONTROL_TX&#160;&#160;&#160;(0x02)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transmitter; arg: 0=disabled, 1=enabled. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536" title="Control Ethernet Interface.">ARM_ETH_MAC_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga530812ef349a2e297f23de72e660fe27"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_FLUSH&#160;&#160;&#160;(0x04)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Flush buffer; arg = ARM_ETH_MAC_FLUSH_... </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536" title="Control Ethernet Interface.">ARM_ETH_MAC_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4afe66589216f566f529af52f9075fdf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_SLEEP&#160;&#160;&#160;(0x05)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Sleep mode; arg: 1=enter and wait for Magic packet, 0=exit. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536" title="Control Ethernet Interface.">ARM_ETH_MAC_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab332b58ba320e73864830dc42ad74181"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_VLAN_FILTER&#160;&#160;&#160;(0x06)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>VLAN Filter for received frames; arg15..0: VLAN Tag; arg16: optional ARM_ETH_MAC_VLAN_FILTER_ID_ONLY. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536" title="Control Ethernet Interface.">ARM_ETH_MAC_Control</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
