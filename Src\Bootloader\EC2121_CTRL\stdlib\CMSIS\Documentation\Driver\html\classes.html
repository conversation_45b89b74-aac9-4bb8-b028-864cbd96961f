<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Data Structure Index</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="classes.html"><span>Data&#160;Structure&#160;Index</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('classes.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Data Structure Index</div>  </div>
</div><!--header-->
<div class="contents">
<div class="qindex"><a class="qindex" href="#letter_D">D</a>&#160;|&#160;<a class="qindex" href="#letter_E">E</a>&#160;|&#160;<a class="qindex" href="#letter_F">F</a>&#160;|&#160;<a class="qindex" href="#letter_I">I</a>&#160;|&#160;<a class="qindex" href="#letter_M">M</a>&#160;|&#160;<a class="qindex" href="#letter_N">N</a>&#160;|&#160;<a class="qindex" href="#letter_S">S</a>&#160;|&#160;<a class="qindex" href="#letter_U">U</a></div>
<table style="margin: 10px; white-space: nowrap;" align="center" width="95%" border="0" cellspacing="0" cellpadding="0">
<tr><td rowspan="2" valign="bottom"><a name="letter_D"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;D&#160;&#160;</div></td></tr></table>
</td><td valign="top"><a class="el" href="group__usbh__host__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_h">ARM_DRIVER_USBH</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o">ARM_FLASH_INFO</a>&#160;&#160;&#160;</td><td rowspan="2" valign="bottom"><a name="letter_N"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;N&#160;&#160;</div></td></tr></table>
</td><td valign="top"><a class="el" href="group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___m_o_d_e_m___s_t_a_t_u_s">ARM_USART_MODEM_STATUS</a>&#160;&#160;&#160;</td></tr>
<tr><td valign="top"><a class="el" href="group__usbh__hci__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_h___h_c_i">ARM_DRIVER_USBH_HCI</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r">ARM_FLASH_SECTOR</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___s_t_a_t_u_s">ARM_USART_STATUS</a>&#160;&#160;&#160;</td></tr>
<tr><td valign="top"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c">ARM_DRIVER_ETH_MAC</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s">ARM_FLASH_STATUS</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_NAND_CAPABILITIES</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__usbd__interface__gr.html#struct_a_r_m___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s">ARM_USBD_CAPABILITIES</a>&#160;&#160;&#160;</td></tr>
<tr><td valign="top"><a class="el" href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y">ARM_DRIVER_ETH_PHY</a>&#160;&#160;&#160;</td><td rowspan="2" valign="bottom"><a name="letter_E"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;E&#160;&#160;</div></td></tr></table>
</td><td rowspan="2" valign="bottom"><a name="letter_I"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;I&#160;&#160;</div></td></tr></table>
</td><td valign="top"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o">ARM_NAND_ECC_INFO</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__usbd__interface__gr.html#struct_a_r_m___u_s_b_d___s_t_a_t_e">ARM_USBD_STATE</a>&#160;&#160;&#160;</td></tr>
<tr><td valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h">ARM_DRIVER_FLASH</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s">ARM_NAND_STATUS</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__usbh__host__gr.html#struct_a_r_m___u_s_b_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_USBH_CAPABILITIES</a>&#160;&#160;&#160;</td></tr>
<tr><td valign="top"><a class="el" href="group__i2c__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___i2_c">ARM_DRIVER_I2C</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o">ARM_ETH_LINK_INFO</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__i2c__interface__gr.html#struct_a_r_m___i2_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_I2C_CAPABILITIES</a>&#160;&#160;&#160;</td><td rowspan="2" valign="bottom"><a name="letter_S"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;S&#160;&#160;</div></td></tr></table>
</td><td valign="top"><a class="el" href="group__usbh__hci__gr.html#struct_a_r_m___u_s_b_h___h_c_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_USBH_HCI_CAPABILITIES</a>&#160;&#160;&#160;</td></tr>
<tr><td valign="top"><a class="el" href="group__mci__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___m_c_i">ARM_DRIVER_MCI</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__i2c__interface__gr.html#struct_a_r_m___i2_c___s_t_a_t_u_s">ARM_I2C_STATUS</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__usbh__host__gr.html#struct_a_r_m___u_s_b_h___p_o_r_t___s_t_a_t_e">ARM_USBH_PORT_STATE</a>&#160;&#160;&#160;</td></tr>
<tr><td valign="top"><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d">ARM_DRIVER_NAND</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_ETH_MAC_CAPABILITIES</a>&#160;&#160;&#160;</td><td rowspan="2" valign="bottom"><a name="letter_M"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;M&#160;&#160;</div></td></tr></table>
</td><td valign="top"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_SPI_CAPABILITIES</a>&#160;&#160;&#160;</td><td></td></tr>
<tr><td valign="top"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i">ARM_DRIVER_SPI</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s">ARM_SPI_STATUS</a>&#160;&#160;&#160;</td><td></td></tr>
<tr><td valign="top"><a class="el" href="group__usart__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_a_r_t">ARM_DRIVER_USART</a>&#160;&#160;&#160;</td><td rowspan="2" valign="bottom"><a name="letter_F"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;F&#160;&#160;</div></td></tr></table>
</td><td valign="top"><a class="el" href="group__mci__interface__gr.html#struct_a_r_m___m_c_i___c_a_p_a_b_i_l_i_t_i_e_s">ARM_MCI_CAPABILITIES</a>&#160;&#160;&#160;</td><td rowspan="2" valign="bottom"><a name="letter_U"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;U&#160;&#160;</div></td></tr></table>
</td><td></td></tr>
<tr><td valign="top"><a class="el" href="group__usbd__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_d">ARM_DRIVER_USBD</a>&#160;&#160;&#160;</td><td valign="top"><a class="el" href="group__mci__interface__gr.html#struct_a_r_m___m_c_i___s_t_a_t_u_s">ARM_MCI_STATUS</a>&#160;&#160;&#160;</td><td></td></tr>
<tr><td></td><td valign="top"><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s">ARM_FLASH_CAPABILITIES</a>&#160;&#160;&#160;</td><td></td><td valign="top"><a class="el" href="group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___c_a_p_a_b_i_l_i_t_i_e_s">ARM_USART_CAPABILITIES</a>&#160;&#160;&#160;</td><td></td></tr>
<tr><td></td><td></td><td></td><td></td><td></td></tr>
</table>
<div class="qindex"><a class="qindex" href="#letter_D">D</a>&#160;|&#160;<a class="qindex" href="#letter_E">E</a>&#160;|&#160;<a class="qindex" href="#letter_F">F</a>&#160;|&#160;<a class="qindex" href="#letter_I">I</a>&#160;|&#160;<a class="qindex" href="#letter_M">M</a>&#160;|&#160;<a class="qindex" href="#letter_N">N</a>&#160;|&#160;<a class="qindex" href="#letter_S">S</a>&#160;|&#160;<a class="qindex" href="#letter_U">U</a></div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
