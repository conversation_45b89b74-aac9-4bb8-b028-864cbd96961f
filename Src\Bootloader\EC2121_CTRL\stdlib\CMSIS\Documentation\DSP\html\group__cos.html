<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Cosine</title>
<title>CMSIS-DSP: Cosine</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__cos.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Cosine</div>  </div>
<div class="ingroups"><a class="el" href="group__group_fast_math.html">Fast Math Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gace15287f9c64b9b4084d1c797d4c49d8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__cos.html#gace15287f9c64b9b4084d1c797d4c49d8">arm_cos_f32</a> (<a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> x)</td></tr>
<tr class="memdesc:gace15287f9c64b9b4084d1c797d4c49d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fast approximation to the trigonometric cosine function for floating-point data.  <a href="#gace15287f9c64b9b4084d1c797d4c49d8"></a><br/></td></tr>
<tr class="separator:gace15287f9c64b9b4084d1c797d4c49d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadfd60c24def501638c0d5db20f4c869b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__cos.html#gadfd60c24def501638c0d5db20f4c869b">arm_cos_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> x)</td></tr>
<tr class="memdesc:gadfd60c24def501638c0d5db20f4c869b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fast approximation to the trigonometric cosine function for Q15 data.  <a href="#gadfd60c24def501638c0d5db20f4c869b"></a><br/></td></tr>
<tr class="separator:gadfd60c24def501638c0d5db20f4c869b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad80f121949ef885a77d83ab36e002567"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__cos.html#gad80f121949ef885a77d83ab36e002567">arm_cos_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> x)</td></tr>
<tr class="memdesc:gad80f121949ef885a77d83ab36e002567"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fast approximation to the trigonometric cosine function for Q31 data.  <a href="#gad80f121949ef885a77d83ab36e002567"></a><br/></td></tr>
<tr class="separator:gad80f121949ef885a77d83ab36e002567"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Computes the trigonometric cosine function using a combination of table lookup and cubic interpolation. There are separate functions for Q15, Q31, and floating-point data types. The input to the floating-point version is in radians while the fixed-point Q15 and Q31 have a scaled input with the range [0 +0.9999] mapping to [0 2*pi). The fixed-point range is chosen so that a value of 2*pi wraps around to 0.</p>
<p>The implementation is based on table lookup using 256 values together with cubic interpolation. The steps used are:</p>
<ol type="1">
<li>Calculation of the nearest integer table index</li>
<li>Fetch the four table values a, b, c, and d</li>
<li>Compute the fractional portion (fract) of the table index.</li>
<li>Calculation of wa, wb, wc, wd</li>
<li>The final result equals <code>a*wa + b*wb + c*wc + d*wd</code></li>
</ol>
<p>where </p>
<pre>    
   a=Table[index-1];    
   b=Table[index+0];    
   c=Table[index+1];    
   d=Table[index+2];    
</pre><p> and </p>
<pre>    
   wa=-(1/6)*fract.^3 + (1/2)*fract.^2 - (1/3)*fract;    
   wb=(1/2)*fract.^3 - fract.^2 - (1/2)*fract + 1;    
   wc=-(1/2)*fract.^3+(1/2)*fract.^2+fract;    
   wd=(1/6)*fract.^3 - (1/6)*fract;    
</pre> <h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gace15287f9c64b9b4084d1c797d4c49d8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> arm_cos_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">x</td><td>input value in radians. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>cos(x). </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_sin_cos_example_f32_8c-example.html#a11">arm_sin_cos_example_f32.c</a>.</dd>
</dl>
<p>References <a class="el" href="arm__math_8h.html#afcb9147c96853bea484cfc2dde07463d">FAST_MATH_TABLE_SIZE</a>, and <a class="el" href="arm__common__tables_8c.html#a1dec82d596780f1a66ef4f76f137c1d9">sinTable_f32</a>.</p>

<p>Referenced by <a class="el" href="arm__sin__cos__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="gadfd60c24def501638c0d5db20f4c869b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> arm_cos_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">x</td><td>Scaled input value in radians. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>cos(x).</dd></dl>
<p>The Q15 input value is in the range [0 +0.9999] and is mapped to a radian value in the range [0 2*pi). </p>

<p>References <a class="el" href="arm__math_8h.html#a34716b73c631e65e8dd855e08384ecb2">FAST_MATH_Q15_SHIFT</a>, and <a class="el" href="arm__common__tables_8c.html#a9cbcfe313f61add745ebfeddb4fecd55">sinTable_q15</a>.</p>

</div>
</div>
<a class="anchor" id="gad80f121949ef885a77d83ab36e002567"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> arm_cos_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">x</td><td>Scaled input value in radians. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>cos(x).</dd></dl>
<p>The Q31 input value is in the range [0 +0.9999] and is mapped to a radian value in the range [0 2*pi). </p>

<p>References <a class="el" href="arm__math_8h.html#a4268f77b1811a0c7fc2532a0bf6108b0">FAST_MATH_Q31_SHIFT</a>, and <a class="el" href="arm__common__tables_8c.html#a8bfccee9e1c0042cf4a765f4b19d097d">sinTable_q31</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
