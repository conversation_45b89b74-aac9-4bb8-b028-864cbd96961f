<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_rfft_instance_f32 Struct Reference</title>
<title>CMSIS-DSP: arm_rfft_instance_f32 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('structarm__rfft__instance__f32.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">arm_rfft_instance_f32 Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Instance structure for the floating-point RFFT/RIFFT function.  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a4219d4669699e4efdcb150ed7a0d9a57"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__rfft__instance__f32.html#a4219d4669699e4efdcb150ed7a0d9a57">fftLenReal</a></td></tr>
<tr class="separator:a4219d4669699e4efdcb150ed7a0d9a57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a075076e07ebb8521d8e3b49a31db6c57"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__rfft__instance__f32.html#a075076e07ebb8521d8e3b49a31db6c57">fftLenBy2</a></td></tr>
<tr class="separator:a075076e07ebb8521d8e3b49a31db6c57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ee6d10a934ab4b666e0bb286c3d633f"><td class="memItemLeft" align="right" valign="top">uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__rfft__instance__f32.html#a5ee6d10a934ab4b666e0bb286c3d633f">ifftFlagR</a></td></tr>
<tr class="separator:a5ee6d10a934ab4b666e0bb286c3d633f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac342f3248157cbbd2f04a3c8ec9fc9eb"><td class="memItemLeft" align="right" valign="top">uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__rfft__instance__f32.html#ac342f3248157cbbd2f04a3c8ec9fc9eb">bitReverseFlagR</a></td></tr>
<tr class="separator:ac342f3248157cbbd2f04a3c8ec9fc9eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aede85350fb5ae6baa1b3e8bfa15b18d6"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__rfft__instance__f32.html#aede85350fb5ae6baa1b3e8bfa15b18d6">twidCoefRModifier</a></td></tr>
<tr class="separator:aede85350fb5ae6baa1b3e8bfa15b18d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a534cc7e6e9b3e3dd022fad611c762142"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__rfft__instance__f32.html#a534cc7e6e9b3e3dd022fad611c762142">pTwiddleAReal</a></td></tr>
<tr class="separator:a534cc7e6e9b3e3dd022fad611c762142"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23543ecfd027fea2477fe1eea23c3c4d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__rfft__instance__f32.html#a23543ecfd027fea2477fe1eea23c3c4d">pTwiddleBReal</a></td></tr>
<tr class="separator:a23543ecfd027fea2477fe1eea23c3c4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f47ba9f50c81e4445ae3827b981bc05"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__rfft__instance__f32.html#a9f47ba9f50c81e4445ae3827b981bc05">pCfft</a></td></tr>
<tr class="separator:a9f47ba9f50c81e4445ae3827b981bc05"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="ac342f3248157cbbd2f04a3c8ec9fc9eb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t arm_rfft_instance_f32::bitReverseFlagR</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>flag that enables (bitReverseFlagR=1) or disables (bitReverseFlagR=0) bit reversal of output. </p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32()</a>, and <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a075076e07ebb8521d8e3b49a31db6c57"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t arm_rfft_instance_f32::fftLenBy2</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>length of the complex FFT. </p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32()</a>, and <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a4219d4669699e4efdcb150ed7a0d9a57"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t arm_rfft_instance_f32::fftLenReal</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>length of the real FFT. </p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a5ee6d10a934ab4b666e0bb286c3d633f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t arm_rfft_instance_f32::ifftFlagR</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>flag that selects forward (ifftFlagR=0) or inverse (ifftFlagR=1) transform. </p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32()</a>, and <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a9f47ba9f50c81e4445ae3827b981bc05"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a>* arm_rfft_instance_f32::pCfft</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the complex FFT instance. </p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32()</a>, and <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a534cc7e6e9b3e3dd022fad611c762142"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>* arm_rfft_instance_f32::pTwiddleAReal</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the real twiddle factor table. </p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32()</a>, and <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="a23543ecfd027fea2477fe1eea23c3c4d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>* arm_rfft_instance_f32::pTwiddleBReal</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the imag twiddle factor table. </p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32()</a>, and <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="aede85350fb5ae6baa1b3e8bfa15b18d6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t arm_rfft_instance_f32::twidCoefRModifier</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. </p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32()</a>, and <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structarm__rfft__instance__f32.html">arm_rfft_instance_f32</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
