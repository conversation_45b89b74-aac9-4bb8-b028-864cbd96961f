<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Complex FFT Functions</title>
<title>CMSIS-DSP: Complex FFT Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___complex_f_f_t.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Complex FFT Functions</div>  </div>
<div class="ingroups"><a class="el" href="group__group_transforms.html">Transform Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gade0f9c4ff157b6b9c72a1eafd86ebf80"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> (const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *p1, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:gade0f9c4ff157b6b9c72a1eafd86ebf80"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point complex FFT.  <a href="#gade0f9c4ff157b6b9c72a1eafd86ebf80"></a><br/></td></tr>
<tr class="separator:gade0f9c4ff157b6b9c72a1eafd86ebf80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga68cdacd2267a2967955e40e6b7ec1229"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> (const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *p1, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:ga68cdacd2267a2967955e40e6b7ec1229"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q15 complex FFT.  <a href="#ga68cdacd2267a2967955e40e6b7ec1229"></a><br/></td></tr>
<tr class="separator:ga68cdacd2267a2967955e40e6b7ec1229"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a0008bd997ab6e2e299ef2fb272fb4b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> (const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *p1, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:ga5a0008bd997ab6e2e299ef2fb272fb4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the fixed-point complex FFT in Q31 format.  <a href="#ga5a0008bd997ab6e2e299ef2fb272fb4b"></a><br/></td></tr>
<tr class="separator:ga5a0008bd997ab6e2e299ef2fb272fb4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9fadd650b802f612ae558ddaab789a6d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga9fadd650b802f612ae558ddaab789a6d">arm_cfft_radix2_f32</a> (const <a class="el" href="structarm__cfft__radix2__instance__f32.html">arm_cfft_radix2_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc)</td></tr>
<tr class="memdesc:ga9fadd650b802f612ae558ddaab789a6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Radix-2 CFFT/CIFFT.  <a href="#ga9fadd650b802f612ae558ddaab789a6d"></a><br/></td></tr>
<tr class="separator:ga9fadd650b802f612ae558ddaab789a6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac9565e6bc7229577ecf5e090313cafd7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7">arm_cfft_radix2_init_f32</a> (<a class="el" href="structarm__cfft__radix2__instance__f32.html">arm_cfft_radix2_instance_f32</a> *S, uint16_t fftLen, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:gac9565e6bc7229577ecf5e090313cafd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point CFFT/CIFFT.  <a href="#gac9565e6bc7229577ecf5e090313cafd7"></a><br/></td></tr>
<tr class="separator:gac9565e6bc7229577ecf5e090313cafd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c5b2127b3c4ea2d03692127f8543858"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858">arm_cfft_radix2_init_q15</a> (<a class="el" href="structarm__cfft__radix2__instance__q15.html">arm_cfft_radix2_instance_q15</a> *S, uint16_t fftLen, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:ga5c5b2127b3c4ea2d03692127f8543858"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 CFFT/CIFFT.  <a href="#ga5c5b2127b3c4ea2d03692127f8543858"></a><br/></td></tr>
<tr class="separator:ga5c5b2127b3c4ea2d03692127f8543858"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabec9611e77382f31e152668bf6b4b638"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638">arm_cfft_radix2_init_q31</a> (<a class="el" href="structarm__cfft__radix2__instance__q31.html">arm_cfft_radix2_instance_q31</a> *S, uint16_t fftLen, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:gabec9611e77382f31e152668bf6b4b638"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 CFFT/CIFFT.  <a href="#gabec9611e77382f31e152668bf6b4b638"></a><br/></td></tr>
<tr class="separator:gabec9611e77382f31e152668bf6b4b638"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga55b424341dc3efd3fa0bcaaff4bdbf40"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga55b424341dc3efd3fa0bcaaff4bdbf40">arm_cfft_radix2_q15</a> (const <a class="el" href="structarm__cfft__radix2__instance__q15.html">arm_cfft_radix2_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc)</td></tr>
<tr class="memdesc:ga55b424341dc3efd3fa0bcaaff4bdbf40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the fixed-point CFFT/CIFFT.  <a href="#ga55b424341dc3efd3fa0bcaaff4bdbf40"></a><br/></td></tr>
<tr class="separator:ga55b424341dc3efd3fa0bcaaff4bdbf40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6321f703ec87a274aedaab33d3e766b4"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga6321f703ec87a274aedaab33d3e766b4">arm_cfft_radix2_q31</a> (const <a class="el" href="structarm__cfft__radix2__instance__q31.html">arm_cfft_radix2_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc)</td></tr>
<tr class="memdesc:ga6321f703ec87a274aedaab33d3e766b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the fixed-point CFFT/CIFFT.  <a href="#ga6321f703ec87a274aedaab33d3e766b4"></a><br/></td></tr>
<tr class="separator:ga6321f703ec87a274aedaab33d3e766b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga521f670cd9c571bc61aff9bec89f4c26"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26">arm_cfft_radix4_f32</a> (const <a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc)</td></tr>
<tr class="memdesc:ga521f670cd9c571bc61aff9bec89f4c26"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point Radix-4 CFFT/CIFFT.  <a href="#ga521f670cd9c571bc61aff9bec89f4c26"></a><br/></td></tr>
<tr class="separator:ga521f670cd9c571bc61aff9bec89f4c26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf336459f684f0b17bfae539ef1b1b78a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32</a> (<a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *S, uint16_t fftLen, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:gaf336459f684f0b17bfae539ef1b1b78a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point CFFT/CIFFT.  <a href="#gaf336459f684f0b17bfae539ef1b1b78a"></a><br/></td></tr>
<tr class="separator:gaf336459f684f0b17bfae539ef1b1b78a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0c2acfda3126c452e75b81669e8ad9ef"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef">arm_cfft_radix4_init_q15</a> (<a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a> *S, uint16_t fftLen, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:ga0c2acfda3126c452e75b81669e8ad9ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 CFFT/CIFFT.  <a href="#ga0c2acfda3126c452e75b81669e8ad9ef"></a><br/></td></tr>
<tr class="separator:ga0c2acfda3126c452e75b81669e8ad9ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad5caaafeec900c8ff72321c01bbd462c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c">arm_cfft_radix4_init_q31</a> (<a class="el" href="structarm__cfft__radix4__instance__q31.html">arm_cfft_radix4_instance_q31</a> *S, uint16_t fftLen, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:gad5caaafeec900c8ff72321c01bbd462c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 CFFT/CIFFT.  <a href="#gad5caaafeec900c8ff72321c01bbd462c"></a><br/></td></tr>
<tr class="separator:gad5caaafeec900c8ff72321c01bbd462c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d66cdac41b8bf6cefdb895456eee84a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga8d66cdac41b8bf6cefdb895456eee84a">arm_cfft_radix4_q15</a> (const <a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc)</td></tr>
<tr class="memdesc:ga8d66cdac41b8bf6cefdb895456eee84a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q15 CFFT/CIFFT.  <a href="#ga8d66cdac41b8bf6cefdb895456eee84a"></a><br/></td></tr>
<tr class="separator:ga8d66cdac41b8bf6cefdb895456eee84a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafde3ee1f58cf393b45a9073174fff548"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#gafde3ee1f58cf393b45a9073174fff548">arm_cfft_radix4_q31</a> (const <a class="el" href="structarm__cfft__radix4__instance__q31.html">arm_cfft_radix4_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc)</td></tr>
<tr class="memdesc:gafde3ee1f58cf393b45a9073174fff548"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q31 CFFT/CIFFT.  <a href="#gafde3ee1f58cf393b45a9073174fff548"></a><br/></td></tr>
<tr class="separator:gafde3ee1f58cf393b45a9073174fff548"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<dl class="section user"><dt></dt><dd>The Fast Fourier Transform (FFT) is an efficient algorithm for computing the Discrete Fourier Transform (DFT). The FFT can be orders of magnitude faster than the DFT, especially for long lengths. The algorithms described in this section operate on complex data. A separate set of functions is devoted to handling of real sequences. </dd></dl>
<dl class="section user"><dt></dt><dd>There are separate algorithms for handling floating-point, Q15, and Q31 data types. The algorithms available for each data type are described next. </dd></dl>
<dl class="section user"><dt></dt><dd>The FFT functions operate in-place. That is, the array holding the input data will also be used to hold the corresponding result. The input data is complex and contains <code>2*fftLen</code> interleaved values as shown below. <pre> {real[0], imag[0], real[1], imag[1],..} </pre> The FFT result will be contained in the same array and the frequency domain values will have the same interleaving.</dd></dl>
<dl class="section user"><dt>Floating-point</dt><dd>The floating-point complex FFT uses a mixed-radix algorithm. Multiple radix-8 stages are performed along with a single radix-2 or radix-4 stage, as needed. The algorithm supports lengths of [16, 32, 64, ..., 4096] and each length uses a different twiddle factor table. </dd></dl>
<dl class="section user"><dt></dt><dd>The function uses the standard FFT definition and output values may grow by a factor of <code>fftLen</code> when computing the forward transform. The inverse transform includes a scale of <code>1/fftLen</code> as part of the calculation and this matches the textbook definition of the inverse FFT. </dd></dl>
<dl class="section user"><dt></dt><dd>Pre-initialized data structures containing twiddle factors and bit reversal tables are provided and defined in <code><a class="el" href="arm__const__structs_8h.html">arm_const_structs.h</a></code>. Include this header in your function and then pass one of the constant structures as an argument to arm_cfft_f32. For example: </dd></dl>
<dl class="section user"><dt></dt><dd><code>arm_cfft_f32(arm_cfft_sR_f32_len64, pSrc, 1, 1)</code> </dd></dl>
<dl class="section user"><dt></dt><dd>computes a 64-point inverse complex FFT including bit reversal. The data structures are treated as constant data and not modified during the calculation. The same data structure can be reused for multiple transforms including mixing forward and inverse transforms. </dd></dl>
<dl class="section user"><dt></dt><dd>Earlier releases of the library provided separate radix-2 and radix-4 algorithms that operated on floating-point data. These functions are still provided but are deprecated. The older functions are slower and less general than the new functions. </dd></dl>
<dl class="section user"><dt></dt><dd>An example of initialization of the constants for the arm_cfft_f32 function follows: <div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keyword">static</span> <a class="code" href="structarm__cfft__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_instance_f32</a> *S;</div>
<div class="line">...</div>
<div class="line">  <span class="keywordflow">switch</span> (length) {</div>
<div class="line">    <span class="keywordflow">case</span> 16:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a27127e9d3deb59df12747233b1b9ea31">arm_cfft_sR_f32_len16</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 32:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a5fed2b5e0cc4cb5b8675f14daf226a25">arm_cfft_sR_f32_len32</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 64:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#af94d90db836f662321946154c76b5b80">arm_cfft_sR_f32_len64</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 128:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#ad283193397ba476465a330db9a955973">arm_cfft_sR_f32_len128</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 256:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#aeb2f0a0be605963264217cc10b7bd3b2">arm_cfft_sR_f32_len256</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 512:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a15f6e533f5cfeb014839303d8ed52e19">arm_cfft_sR_f32_len512</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 1024:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a05abc294a9159abbd6ffb4f188fe18b1">arm_cfft_sR_f32_len1024</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 2048:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a8d2fad347dcadc47377e1226231b9f62">arm_cfft_sR_f32_len2048</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 4096:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a01d2dbdb8193d43c2b7f003f9cb9a39d">arm_cfft_sR_f32_len4096</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">  }</div>
</div><!-- fragment --> </dd></dl>
<dl class="section user"><dt>Q15 and Q31</dt><dd>The floating-point complex FFT uses a mixed-radix algorithm. Multiple radix-4 stages are performed along with a single radix-2 stage, as needed. The algorithm supports lengths of [16, 32, 64, ..., 4096] and each length uses a different twiddle factor table. </dd></dl>
<dl class="section user"><dt></dt><dd>The function uses the standard FFT definition and output values may grow by a factor of <code>fftLen</code> when computing the forward transform. The inverse transform includes a scale of <code>1/fftLen</code> as part of the calculation and this matches the textbook definition of the inverse FFT. </dd></dl>
<dl class="section user"><dt></dt><dd>Pre-initialized data structures containing twiddle factors and bit reversal tables are provided and defined in <code><a class="el" href="arm__const__structs_8h.html">arm_const_structs.h</a></code>. Include this header in your function and then pass one of the constant structures as an argument to arm_cfft_q31. For example: </dd></dl>
<dl class="section user"><dt></dt><dd><code>arm_cfft_q31(arm_cfft_sR_q31_len64, pSrc, 1, 1)</code> </dd></dl>
<dl class="section user"><dt></dt><dd>computes a 64-point inverse complex FFT including bit reversal. The data structures are treated as constant data and not modified during the calculation. The same data structure can be reused for multiple transforms including mixing forward and inverse transforms. </dd></dl>
<dl class="section user"><dt></dt><dd>Earlier releases of the library provided separate radix-2 and radix-4 algorithms that operated on floating-point data. These functions are still provided but are deprecated. The older functions are slower and less general than the new functions. </dd></dl>
<dl class="section user"><dt></dt><dd>An example of initialization of the constants for the arm_cfft_q31 function follows: <div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keyword">static</span> <a class="code" href="structarm__cfft__instance__q31.html" title="Instance structure for the fixed-point CFFT/CIFFT function.">arm_cfft_instance_q31</a> *S;</div>
<div class="line">...</div>
<div class="line">  <span class="keywordflow">switch</span> (length) {</div>
<div class="line">    <span class="keywordflow">case</span> 16:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a1336431c4d2a88d32c42308cfe2defa1">arm_cfft_sR_q31_len16</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 32:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a4c083c013ef17920cf8f28dc6f139a39">arm_cfft_sR_q31_len32</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 64:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#ad11668a5662334e0bc6a2811c9cb1047">arm_cfft_sR_q31_len64</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 128:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a9a2fcdb54300f75ef1fafe02954e9a61">arm_cfft_sR_q31_len128</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 256:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a3f2de67938bd228918e40f60f18dd6b5">arm_cfft_sR_q31_len256</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 512:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#aa337272cf78aaf6075e7e19d0a097d6f">arm_cfft_sR_q31_len512</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 1024:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#ada9813a027999f3cff066c9f7b5df51b">arm_cfft_sR_q31_len1024</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 2048:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#a420622d75b277070784083ddd44b95fb">arm_cfft_sR_q31_len2048</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">    <span class="keywordflow">case</span> 4096:</div>
<div class="line">      S = &amp;<a class="code" href="arm__const__structs_8c.html#abfc9595f40a1c7aaba85e1328d824b1c">arm_cfft_sR_q31_len4096</a>;</div>
<div class="line">      <span class="keywordflow">break</span>;</div>
<div class="line">  }</div>
</div><!-- fragment --> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gade0f9c4ff157b6b9c72a1eafd86ebf80"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__instance__f32.html">arm_cfft_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>p1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point CFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*p1</td><td>points to the complex data buffer of size <code>2*fftLen</code>. Processing occurs in-place. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fft_bin_example_f32_8c-example.html#a11">arm_fft_bin_example_f32.c</a>.</dd>
</dl>
<p>References <a class="el" href="arm__cfft__f32_8c.html#ac8e7ebe1cb131a5b0f55d0464640591f">arm_bitreversal_32()</a>, <a class="el" href="arm__cfft__f32_8c.html#ae99e2b173033e9910058869bdf0619d9">arm_cfft_radix8by2_f32()</a>, <a class="el" href="arm__cfft__f32_8c.html#a4bb346f59bca06cebe0defc8e15b69a6">arm_cfft_radix8by4_f32()</a>, <a class="el" href="arm__cfft__f32_8c.html#a72350c6eaa1eef8796ab43c1497c6b9c">arm_radix8_butterfly_f32()</a>, <a class="el" href="structarm__cfft__instance__f32.html#a3ba329ed153d182746376208e773d648">arm_cfft_instance_f32::bitRevLength</a>, <a class="el" href="structarm__cfft__instance__f32.html#acd8f9e9540e3dd348212726e5d6aaa95">arm_cfft_instance_f32::fftLen</a>, <a class="el" href="structarm__cfft__instance__f32.html#a21ceaf59a1bb8440af57c28d2dd9bbab">arm_cfft_instance_f32::pBitRevTable</a>, and <a class="el" href="structarm__cfft__instance__f32.html#a59cc6f753f1498716e1444ac054c06de">arm_cfft_instance_f32::pTwiddle</a>.</p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga180d8b764d59cbb85d37a2d5f7cd9799">arm_rfft_fast_f32()</a>, and <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ga68cdacd2267a2967955e40e6b7ec1229"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__instance__q15.html">arm_cfft_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>p1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 CFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*p1</td><td>points to the complex data buffer of size <code>2*fftLen</code>. Processing occurs in-place. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__cfft__q15_8c.html#a773957c278f4d9e728711f27e8a6e278">arm_bitreversal_16()</a>, <a class="el" href="arm__cfft__q15_8c.html#abe669acc8db57d1fb9b1e2bba30f2224">arm_cfft_radix4by2_inverse_q15()</a>, <a class="el" href="arm__cfft__q15_8c.html#af1d4a751153857c173511e0c77ab4fa9">arm_cfft_radix4by2_q15()</a>, <a class="el" href="arm__cfft__q15_8c.html#a734ecff00f21a6a10e9ec437c8987eb1">arm_radix4_butterfly_inverse_q15()</a>, <a class="el" href="arm__cfft__q15_8c.html#abf1a2f9aa9f44ad5da1f0dbae8b54f2f">arm_radix4_butterfly_q15()</a>, <a class="el" href="structarm__cfft__instance__q15.html#a738907cf34bdbbaf724414ac2decbc3c">arm_cfft_instance_q15::bitRevLength</a>, <a class="el" href="structarm__cfft__instance__q15.html#a5f9e1d3a8c127ee323b5e6929aeb90df">arm_cfft_instance_q15::fftLen</a>, <a class="el" href="structarm__cfft__instance__q15.html#ac9160b80243b99a0b6e2f75ddb5cf0ae">arm_cfft_instance_q15::pBitRevTable</a>, and <a class="el" href="structarm__cfft__instance__q15.html#afdaf12ce4687cec021c5ae73d0987a3f">arm_cfft_instance_q15::pTwiddle</a>.</p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga00e615f5db21736ad5b27fb6146f3fc5">arm_rfft_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ga5a0008bd997ab6e2e299ef2fb272fb4b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>p1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the fixed-point CFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*p1</td><td>points to the complex data buffer of size <code>2*fftLen</code>. Processing occurs in-place. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__cfft__f32_8c.html#ac8e7ebe1cb131a5b0f55d0464640591f">arm_bitreversal_32()</a>, <a class="el" href="arm__cfft__q31_8c.html#a3f3ae10bc2057cc1360abfa25f224c8c">arm_cfft_radix4by2_inverse_q31()</a>, <a class="el" href="arm__cfft__q31_8c.html#af6df8bf714c30d44e6b871ea87d22b30">arm_cfft_radix4by2_q31()</a>, <a class="el" href="arm__cfft__q31_8c.html#ac9c7c553114c1201a3a987a11b8a6d01">arm_radix4_butterfly_inverse_q31()</a>, <a class="el" href="arm__cfft__q31_8c.html#ac12f1e7f159d5741358cdc36830a0395">arm_radix4_butterfly_q31()</a>, <a class="el" href="structarm__cfft__instance__q31.html#a2250fa6b8fe73292c5418c50c0549f87">arm_cfft_instance_q31::bitRevLength</a>, <a class="el" href="structarm__cfft__instance__q31.html#a4406f23e8fd0bff8d555225612e2a2a8">arm_cfft_instance_q31::fftLen</a>, <a class="el" href="structarm__cfft__instance__q31.html#a8a464461649f023325ced1e10470f5d0">arm_cfft_instance_q31::pBitRevTable</a>, and <a class="el" href="structarm__cfft__instance__q31.html#af751114feb91de3ace8600e91bdd0872">arm_cfft_instance_q31::pTwiddle</a>.</p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gabaeab5646aeea9844e6d42ca8c73fe3a">arm_rfft_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ga9fadd650b802f612ae558ddaab789a6d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_radix2_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__radix2__instance__f32.html">arm_cfft_radix2_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000001">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> and will be removed in the future. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point Radix-2 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pSrc</td><td>points to the complex data buffer of size <code>2*fftLen</code>. Processing occurs in-place. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__bitreversal_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b">arm_bitreversal_f32()</a>, <a class="el" href="arm__cfft__radix2__f32_8c.html#a04631e102b5209af3402b225b1abe868">arm_radix2_butterfly_f32()</a>, <a class="el" href="arm__cfft__radix2__f32_8c.html#abda34af152e515a95ac38470ac053b77">arm_radix2_butterfly_inverse_f32()</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#af713b4ac5256a19bc965c89fe3005fa3">arm_cfft_radix2_instance_f32::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#ac1688dafa5177f6b1505abbfd0cf8b21">arm_cfft_radix2_instance_f32::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a2f915a1c29635c1623086aaaa726be8f">arm_cfft_radix2_instance_f32::fftLen</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a8dbe98d2c924e35e0a3fed2fe948176f">arm_cfft_radix2_instance_f32::ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a1d3d289d47443e597d88a40effd14b8f">arm_cfft_radix2_instance_f32::onebyfftLen</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a92b8fa0a151cd800436094903a5ca0a4">arm_cfft_radix2_instance_f32::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#adb0c9d47dbfbd90a6f6ed0a05313a974">arm_cfft_radix2_instance_f32::pTwiddle</a>, and <a class="el" href="structarm__cfft__radix2__instance__f32.html#a411f75b6ed01690293f4f5988030ea42">arm_cfft_radix2_instance_f32::twidCoefModifier</a>.</p>

</div>
</div>
<a class="anchor" id="gac9565e6bc7229577ecf5e090313cafd7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_cfft_radix2_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix2__instance__f32.html">arm_cfft_radix2_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000002">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> and will be removed in the future. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the floating-point CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLen</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlag</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlag for calculation of CIFFT otherwise CFFT is calculated </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLen</code> Specifies length of CFFT/CIFFT process. Supported FFT Lengths are 16, 64, 256, 1024. </dd></dl>
<dl class="section user"><dt></dt><dd>This Function also initializes Twiddle factor table pointer and Bit reversal table pointer. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">armBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#af713b4ac5256a19bc965c89fe3005fa3">arm_cfft_radix2_instance_f32::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#ac1688dafa5177f6b1505abbfd0cf8b21">arm_cfft_radix2_instance_f32::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a2f915a1c29635c1623086aaaa726be8f">arm_cfft_radix2_instance_f32::fftLen</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a8dbe98d2c924e35e0a3fed2fe948176f">arm_cfft_radix2_instance_f32::ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a1d3d289d47443e597d88a40effd14b8f">arm_cfft_radix2_instance_f32::onebyfftLen</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a92b8fa0a151cd800436094903a5ca0a4">arm_cfft_radix2_instance_f32::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#adb0c9d47dbfbd90a6f6ed0a05313a974">arm_cfft_radix2_instance_f32::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="structarm__cfft__radix2__instance__f32.html#a411f75b6ed01690293f4f5988030ea42">arm_cfft_radix2_instance_f32::twidCoefModifier</a>, and <a class="el" href="arm__common__tables_8h.html#a9bf8c85e4c91b9b55818b3d650d2c761">twiddleCoef</a>.</p>

</div>
</div>
<a class="anchor" id="ga5c5b2127b3c4ea2d03692127f8543858"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_cfft_radix2_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix2__instance__q15.html">arm_cfft_radix2_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000003">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> and will be removed </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q15 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLen</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlag</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlag for calculation of CIFFT otherwise CFFT is calculated </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLen</code> Specifies length of CFFT/CIFFT process. Supported FFT Lengths are 16, 64, 256, 1024. </dd></dl>
<dl class="section user"><dt></dt><dd>This Function also initializes Twiddle factor table pointer and Bit reversal table pointer. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">armBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#af8300c1f60caa21e6b44b9240ab5af19">arm_cfft_radix2_instance_q15::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#a8722720c542cabd41df83fe88ef4f4cb">arm_cfft_radix2_instance_q15::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#a874085647351dcf3f0de39d2b1d49744">arm_cfft_radix2_instance_q15::fftLen</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#ab5c073286bdd2f6e2bf783ced36bf1de">arm_cfft_radix2_instance_q15::ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#ab88afeff6493be3c8b5e4530efa82d51">arm_cfft_radix2_instance_q15::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#a3809dd15e7cbf1a054c728cfbbb0cc5a">arm_cfft_radix2_instance_q15::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#a6f2ab87fb4c568656e1f92f687b5c850">arm_cfft_radix2_instance_q15::twidCoefModifier</a>, and <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9b409d6995eab17805b1d1881d4bc652">twiddleCoef_4096_q15</a>.</p>

</div>
</div>
<a class="anchor" id="gabec9611e77382f31e152668bf6b4b638"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_cfft_radix2_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix2__instance__q31.html">arm_cfft_radix2_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000004">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> and will be removed </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q31 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLen</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlag</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlag for calculation of CIFFT otherwise CFFT is calculated </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLen</code> Specifies length of CFFT/CIFFT process. Supported FFT Lengths are 16, 64, 256, 1024. </dd></dl>
<dl class="section user"><dt></dt><dd>This Function also initializes Twiddle factor table pointer and Bit reversal table pointer. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">armBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a6239b8d268285334e88c008c07d68616">arm_cfft_radix2_instance_q31::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a9d17a87263953fe3559a007512c9f3a4">arm_cfft_radix2_instance_q31::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a960199f1373a192366878ef279eab00f">arm_cfft_radix2_instance_q31::fftLen</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a2607378ce64be16698bb8a3b1af8d3c8">arm_cfft_radix2_instance_q31::ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#ada8e5264f4b22ff4c621817978994674">arm_cfft_radix2_instance_q31::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a1d5bbe9a991e133f81652a77a7985d23">arm_cfft_radix2_instance_q31::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#ae63ca9193322cd477970c1d2086407d1">arm_cfft_radix2_instance_q31::twidCoefModifier</a>, and <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga67c0890317deab3391e276f22c1fc400">twiddleCoef_4096_q31</a>.</p>

</div>
</div>
<a class="anchor" id="ga55b424341dc3efd3fa0bcaaff4bdbf40"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_radix2_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__radix2__instance__q15.html">arm_cfft_radix2_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000005">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> and will be removed </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the fixed-point CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pSrc</td><td>points to the complex data buffer of size <code>2*fftLen</code>. Processing occurs in-place. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__bitreversal_8c.html#a12a07b49948c354172ae07358309a4a5">arm_bitreversal_q15()</a>, <a class="el" href="arm__cfft__radix2__q15_8c.html#a91ff93fa10757b3872680fec4835c412">arm_radix2_butterfly_inverse_q15()</a>, <a class="el" href="arm__cfft__radix2__q15_8c.html#a521780ba2fd3450cbf02784e38859699">arm_radix2_butterfly_q15()</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#a8722720c542cabd41df83fe88ef4f4cb">arm_cfft_radix2_instance_q15::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#a874085647351dcf3f0de39d2b1d49744">arm_cfft_radix2_instance_q15::fftLen</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#ab5c073286bdd2f6e2bf783ced36bf1de">arm_cfft_radix2_instance_q15::ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#ab88afeff6493be3c8b5e4530efa82d51">arm_cfft_radix2_instance_q15::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__q15.html#a3809dd15e7cbf1a054c728cfbbb0cc5a">arm_cfft_radix2_instance_q15::pTwiddle</a>, and <a class="el" href="structarm__cfft__radix2__instance__q15.html#a6f2ab87fb4c568656e1f92f687b5c850">arm_cfft_radix2_instance_q15::twidCoefModifier</a>.</p>

</div>
</div>
<a class="anchor" id="ga6321f703ec87a274aedaab33d3e766b4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_radix2_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__radix2__instance__q31.html">arm_cfft_radix2_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000006">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> and will be removed </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the fixed-point CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pSrc</td><td>points to the complex data buffer of size <code>2*fftLen</code>. Processing occurs in-place. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__bitreversal_8c.html#a27618705158b5c42db5fb0a381f8efc1">arm_bitreversal_q31()</a>, <a class="el" href="arm__cfft__radix2__q31_8c.html#a4d665f67080455b95cafec04009fd52d">arm_radix2_butterfly_inverse_q31()</a>, <a class="el" href="arm__cfft__radix2__q31_8c.html#a740f4fe69e6148d22fc99f374d304e7e">arm_radix2_butterfly_q31()</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a9d17a87263953fe3559a007512c9f3a4">arm_cfft_radix2_instance_q31::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a960199f1373a192366878ef279eab00f">arm_cfft_radix2_instance_q31::fftLen</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a2607378ce64be16698bb8a3b1af8d3c8">arm_cfft_radix2_instance_q31::ifftFlag</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#ada8e5264f4b22ff4c621817978994674">arm_cfft_radix2_instance_q31::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix2__instance__q31.html#a1d5bbe9a991e133f81652a77a7985d23">arm_cfft_radix2_instance_q31::pTwiddle</a>, and <a class="el" href="structarm__cfft__radix2__instance__q31.html#ae63ca9193322cd477970c1d2086407d1">arm_cfft_radix2_instance_q31::twidCoefModifier</a>.</p>

</div>
</div>
<a class="anchor" id="ga521f670cd9c571bc61aff9bec89f4c26"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_radix4_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000007">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> and will be removed in the future. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point Radix-4 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pSrc</td><td>points to the complex data buffer of size <code>2*fftLen</code>. Processing occurs in-place. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_convolution_example_f32_8c-example.html#a17">arm_convolution_example_f32.c</a>.</dd>
</dl>
<p>References <a class="el" href="arm__bitreversal_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b">arm_bitreversal_f32()</a>, <a class="el" href="group__group_transforms.html#gae239ddf995d1607115f9e84d5c069b9c">arm_radix4_butterfly_f32()</a>, <a class="el" href="arm__cfft__radix4__f32_8c.html#a2a78df6e4bbf080624f2b6349224ec93">arm_radix4_butterfly_inverse_f32()</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#ac10927a1620195a88649ce63dab66120">arm_cfft_radix4_instance_f32::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#acc8cb18a8b901b8321ab9d86491e41a3">arm_cfft_radix4_instance_f32::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a7e6a6d290ce158ce9a15a45e364b021a">arm_cfft_radix4_instance_f32::fftLen</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a25d1da64dd6487c291f04d226f9acc66">arm_cfft_radix4_instance_f32::ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#ab9eed39e40b8d7c16381fbccf84467cd">arm_cfft_radix4_instance_f32::onebyfftLen</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a8da0d2ca69749fde8cbb95caeac6fe6a">arm_cfft_radix4_instance_f32::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a14860c7544911702ca1fa0bf78204ef3">arm_cfft_radix4_instance_f32::pTwiddle</a>, and <a class="el" href="structarm__cfft__radix4__instance__f32.html#abe31ea2157dfa233e389cdfd3b9993ee">arm_cfft_radix4_instance_f32::twidCoefModifier</a>.</p>

<p>Referenced by <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="gaf336459f684f0b17bfae539ef1b1b78a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_cfft_radix4_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000008">Deprecated:</a></b></dt><dd>Do not use this function. It has been superceded by <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32</a> and will be removed in the future. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the floating-point CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLen</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlag</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlag for calculation of CIFFT otherwise CFFT is calculated </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLen</code> Specifies length of CFFT/CIFFT process. Supported FFT Lengths are 16, 64, 256, 1024. </dd></dl>
<dl class="section user"><dt></dt><dd>This Function also initializes Twiddle factor table pointer and Bit reversal table pointer. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_convolution_example_f32_8c-example.html#a16">arm_convolution_example_f32.c</a>.</dd>
</dl>
<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">armBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#ac10927a1620195a88649ce63dab66120">arm_cfft_radix4_instance_f32::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#acc8cb18a8b901b8321ab9d86491e41a3">arm_cfft_radix4_instance_f32::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a7e6a6d290ce158ce9a15a45e364b021a">arm_cfft_radix4_instance_f32::fftLen</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a25d1da64dd6487c291f04d226f9acc66">arm_cfft_radix4_instance_f32::ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#ab9eed39e40b8d7c16381fbccf84467cd">arm_cfft_radix4_instance_f32::onebyfftLen</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a8da0d2ca69749fde8cbb95caeac6fe6a">arm_cfft_radix4_instance_f32::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a14860c7544911702ca1fa0bf78204ef3">arm_cfft_radix4_instance_f32::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#abe31ea2157dfa233e389cdfd3b9993ee">arm_cfft_radix4_instance_f32::twidCoefModifier</a>, and <a class="el" href="arm__common__tables_8h.html#a9bf8c85e4c91b9b55818b3d650d2c761">twiddleCoef</a>.</p>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ga0c2acfda3126c452e75b81669e8ad9ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_cfft_radix4_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000009">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> and will be removed </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q15 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLen</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlag</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlag for calculation of CIFFT otherwise CFFT is calculated </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLen</code> Specifies length of CFFT/CIFFT process. Supported FFT Lengths are 16, 64, 256, 1024. </dd></dl>
<dl class="section user"><dt></dt><dd>This Function also initializes Twiddle factor table pointer and Bit reversal table pointer. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">armBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a101e3f7b0bd6b5b14cd5214f23df4133">arm_cfft_radix4_instance_q15::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a6b010e5f02d1130c621e3d2e26b95df1">arm_cfft_radix4_instance_q15::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a5fc543e7d84ca8cb7cf6648970f21ca6">arm_cfft_radix4_instance_q15::fftLen</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a2ecff6ea735cb4d22e922d0fd5736655">arm_cfft_radix4_instance_q15::ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a4acf704ae0cf30b53bf0fbfae8e34a59">arm_cfft_radix4_instance_q15::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a29dd693537e45421a36891f8439e1fba">arm_cfft_radix4_instance_q15::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#af32fdc78bcc27ca385f9b76a0a1f71c3">arm_cfft_radix4_instance_q15::twidCoefModifier</a>, and <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9b409d6995eab17805b1d1881d4bc652">twiddleCoef_4096_q15</a>.</p>

</div>
</div>
<a class="anchor" id="gad5caaafeec900c8ff72321c01bbd462c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_cfft_radix4_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix4__instance__q31.html">arm_cfft_radix4_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000010">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> and will be removed </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q31 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLen</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlag</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlag for calculation of CIFFT otherwise CFFT is calculated </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLen</code> Specifies length of CFFT/CIFFT process. Supported FFT Lengths are 16, 64, 256, 1024. </dd></dl>
<dl class="section user"><dt></dt><dd>This Function also initializes Twiddle factor table pointer and Bit reversal table pointer. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">armBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a5a7c4f4c7b3fb655cbb2bc11ef160a2a">arm_cfft_radix4_instance_q31::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a94d2fead4efa4d5eaae142bbe30b0e15">arm_cfft_radix4_instance_q31::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#ab413d2a5d3f45fa187d93813bf3bf81b">arm_cfft_radix4_instance_q31::fftLen</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#adc0a62ba669ad2282ecbe43d5d96abab">arm_cfft_radix4_instance_q31::ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a33a3bc774c97373261699463c05dfe54">arm_cfft_radix4_instance_q31::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a561c22dee4cbdcfa0fd5f15106ecc306">arm_cfft_radix4_instance_q31::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a8cf8187b8232815cf17ee82bf572ecf9">arm_cfft_radix4_instance_q31::twidCoefModifier</a>, and <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga67c0890317deab3391e276f22c1fc400">twiddleCoef_4096_q31</a>.</p>

</div>
</div>
<a class="anchor" id="ga8d66cdac41b8bf6cefdb895456eee84a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_radix4_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000011">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15</a> and will be removed </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pSrc</td><td>points to the complex data buffer. Processing occurs in-place. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Input and output formats: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>Internally input is downscaled by 2 for every stage to avoid saturations inside CFFT/CIFFT process. Hence the output format is different for different FFT sizes. The input and output formats for different FFT sizes and number of bits to upscale are mentioned in the tables below for CFFT and CIFFT: </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="CFFTQ15.gif" alt="CFFTQ15.gif"/>
<div class="caption">
Input and Output Formats for Q15 CFFT</div></div>
 <div class="image">
<img src="CIFFTQ15.gif" alt="CIFFTQ15.gif"/>
<div class="caption">
Input and Output Formats for Q15 CIFFT</div></div>
 </dd></dl>

<p>References <a class="el" href="arm__bitreversal_8c.html#a12a07b49948c354172ae07358309a4a5">arm_bitreversal_q15()</a>, <a class="el" href="arm__cfft__q15_8c.html#a734ecff00f21a6a10e9ec437c8987eb1">arm_radix4_butterfly_inverse_q15()</a>, <a class="el" href="arm__cfft__q15_8c.html#abf1a2f9aa9f44ad5da1f0dbae8b54f2f">arm_radix4_butterfly_q15()</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a101e3f7b0bd6b5b14cd5214f23df4133">arm_cfft_radix4_instance_q15::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a6b010e5f02d1130c621e3d2e26b95df1">arm_cfft_radix4_instance_q15::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a5fc543e7d84ca8cb7cf6648970f21ca6">arm_cfft_radix4_instance_q15::fftLen</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a2ecff6ea735cb4d22e922d0fd5736655">arm_cfft_radix4_instance_q15::ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a4acf704ae0cf30b53bf0fbfae8e34a59">arm_cfft_radix4_instance_q15::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__q15.html#a29dd693537e45421a36891f8439e1fba">arm_cfft_radix4_instance_q15::pTwiddle</a>, and <a class="el" href="structarm__cfft__radix4__instance__q15.html#af32fdc78bcc27ca385f9b76a0a1f71c3">arm_cfft_radix4_instance_q15::twidCoefModifier</a>.</p>

</div>
</div>
<a class="anchor" id="gafde3ee1f58cf393b45a9073174fff548"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_radix4_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__cfft__radix4__instance__q31.html">arm_cfft_radix4_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000012">Deprecated:</a></b></dt><dd>Do not use this function. It has been superseded by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> and will be removed </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pSrc</td><td>points to the complex data buffer of size <code>2*fftLen</code>. Processing occurs in-place. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Input and output formats: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>Internally input is downscaled by 2 for every stage to avoid saturations inside CFFT/CIFFT process. Hence the output format is different for different FFT sizes. The input and output formats for different FFT sizes and number of bits to upscale are mentioned in the tables below for CFFT and CIFFT: </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="CFFTQ31.gif" alt="CFFTQ31.gif"/>
<div class="caption">
Input and Output Formats for Q31 CFFT</div></div>
 <div class="image">
<img src="CIFFTQ31.gif" alt="CIFFTQ31.gif"/>
<div class="caption">
Input and Output Formats for Q31 CIFFT</div></div>
 </dd></dl>

<p>References <a class="el" href="arm__bitreversal_8c.html#a27618705158b5c42db5fb0a381f8efc1">arm_bitreversal_q31()</a>, <a class="el" href="arm__cfft__q31_8c.html#ac9c7c553114c1201a3a987a11b8a6d01">arm_radix4_butterfly_inverse_q31()</a>, <a class="el" href="arm__cfft__q31_8c.html#ac12f1e7f159d5741358cdc36830a0395">arm_radix4_butterfly_q31()</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a5a7c4f4c7b3fb655cbb2bc11ef160a2a">arm_cfft_radix4_instance_q31::bitReverseFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a94d2fead4efa4d5eaae142bbe30b0e15">arm_cfft_radix4_instance_q31::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#ab413d2a5d3f45fa187d93813bf3bf81b">arm_cfft_radix4_instance_q31::fftLen</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#adc0a62ba669ad2282ecbe43d5d96abab">arm_cfft_radix4_instance_q31::ifftFlag</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a33a3bc774c97373261699463c05dfe54">arm_cfft_radix4_instance_q31::pBitRevTable</a>, <a class="el" href="structarm__cfft__radix4__instance__q31.html#a561c22dee4cbdcfa0fd5f15106ecc306">arm_cfft_radix4_instance_q31::pTwiddle</a>, and <a class="el" href="structarm__cfft__radix4__instance__q31.html#a8cf8187b8232815cf17ee82bf572ecf9">arm_cfft_radix4_instance_q31::twidCoefModifier</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
