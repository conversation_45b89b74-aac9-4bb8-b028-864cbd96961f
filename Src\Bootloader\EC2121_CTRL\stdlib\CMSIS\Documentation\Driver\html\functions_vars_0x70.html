<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="classes.html"><span>Data&#160;Structure&#160;Index</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li class="current"><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions_vars_0x70.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_p"></a>- p -</h3><ul>
<li>page_count
: <a class="el" href="group__nand__interface__gr.html#aa993bc236650aa405b01d00b7ca72904">ARM_NAND_ECC_INFO</a>
</li>
<li>page_layout
: <a class="el" href="group__nand__interface__gr.html#a5952ba4313bda7833fefd358f5aff979">ARM_NAND_ECC_INFO</a>
</li>
<li>page_size
: <a class="el" href="group__flash__interface__gr.html#a9dd3e47e968a8f6beb5d88c6d1b7ebe9">ARM_FLASH_INFO</a>
, <a class="el" href="group__nand__interface__gr.html#a9dd3e47e968a8f6beb5d88c6d1b7ebe9">ARM_NAND_ECC_INFO</a>
</li>
<li>PHY_Read
: <a class="el" href="group__eth__mac__interface__gr.html#a0f2ddb734e4242077275761400b26e35">ARM_DRIVER_ETH_MAC</a>
</li>
<li>PHY_Write
: <a class="el" href="group__eth__mac__interface__gr.html#ac3efe9bdc31c3b1d7fd8eb82bbfb4c13">ARM_DRIVER_ETH_MAC</a>
</li>
<li>PipeCreate
: <a class="el" href="group__usbh__host__gr.html#a7ce5ca579a8c535434187ad05f596fbd">ARM_DRIVER_USBH</a>
</li>
<li>PipeDelete
: <a class="el" href="group__usbh__host__gr.html#ab2f8047e89786bb7a459fb9c6c3f03d5">ARM_DRIVER_USBH</a>
</li>
<li>PipeModify
: <a class="el" href="group__usbh__host__gr.html#a3efae6fe31a53f7ecd765ba6db99992e">ARM_DRIVER_USBH</a>
</li>
<li>PipeReset
: <a class="el" href="group__usbh__host__gr.html#afe91e3e22bc401546d033cb9554550b7">ARM_DRIVER_USBH</a>
</li>
<li>PipeTransfer
: <a class="el" href="group__usbh__host__gr.html#a495b069fadf5ba5b069bfdec6cda8b88">ARM_DRIVER_USBH</a>
</li>
<li>PipeTransferAbort
: <a class="el" href="group__usbh__host__gr.html#ab82fb8b02ff81156098b8210c0344f5e">ARM_DRIVER_USBH</a>
</li>
<li>PipeTransferGetResult
: <a class="el" href="group__usbh__host__gr.html#a18369bada042ff5557ff919056636a62">ARM_DRIVER_USBH</a>
</li>
<li>port_mask
: <a class="el" href="group__usbh__host__gr.html#ac37c09b54483c2a1e41fa8a976721fc4">ARM_USBH_CAPABILITIES</a>
, <a class="el" href="group__usbh__hci__gr.html#ac37c09b54483c2a1e41fa8a976721fc4">ARM_USBH_HCI_CAPABILITIES</a>
</li>
<li>PortGetState
: <a class="el" href="group__usbh__host__gr.html#a84c391c0db065fa27b672eef6002905b">ARM_DRIVER_USBH</a>
</li>
<li>PortReset
: <a class="el" href="group__usbh__host__gr.html#a95125e80b07640860a7e16f510eca506">ARM_DRIVER_USBH</a>
</li>
<li>PortResume
: <a class="el" href="group__usbh__host__gr.html#a79126109256c1ccef901f22bc36ddc1d">ARM_DRIVER_USBH</a>
</li>
<li>PortSuspend
: <a class="el" href="group__usbh__host__gr.html#ab8be30d2d44a6447c8c085439ef117fb">ARM_DRIVER_USBH</a>
</li>
<li>PortVbusOnOff
: <a class="el" href="group__usbh__host__gr.html#ab859fb9f73a60ffa1ce71ed961d4744f">ARM_DRIVER_USBH</a>
, <a class="el" href="group__usbh__hci__gr.html#ab859fb9f73a60ffa1ce71ed961d4744f">ARM_DRIVER_USBH_HCI</a>
</li>
<li>PowerControl
: <a class="el" href="group__nand__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_NAND</a>
, <a class="el" href="group__usbh__host__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_USBH</a>
, <a class="el" href="group__usbd__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_USBD</a>
, <a class="el" href="group__i2c__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_I2C</a>
, <a class="el" href="group__spi__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_SPI</a>
, <a class="el" href="group__usbh__hci__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_USBH_HCI</a>
, <a class="el" href="group__flash__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_FLASH</a>
, <a class="el" href="group__usart__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_USART</a>
, <a class="el" href="group__eth__phy__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_ETH_PHY</a>
, <a class="el" href="group__mci__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_MCI</a>
, <a class="el" href="group__eth__mac__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">ARM_DRIVER_ETH_MAC</a>
</li>
<li>precision_timer
: <a class="el" href="group__eth__mac__interface__gr.html#a881a863974d32f95d7829f768ac47aa2">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>program_unit
: <a class="el" href="group__flash__interface__gr.html#a483c41066757e2865bf3a27a2a627a54">ARM_FLASH_INFO</a>
</li>
<li>ProgramData
: <a class="el" href="group__flash__interface__gr.html#a429fc193f1ec62858219ab8749c563ae">ARM_DRIVER_FLASH</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
