<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li class="current"><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_defs.html#index__"><span>_</span></a></li>
      <li><a href="globals_defs_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_defs_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_defs_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_defs_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_defs_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_defs_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_defs_0x73.html#index_s"><span>s</span></a></li>
      <li class="current"><a href="globals_defs_0x75.html#index_u"><span>u</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_defs_0x75.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_u"></a>- u -</h3><ul>
<li>ARM_USART_ABORT_RECEIVE
: <a class="el" href="group__usart__misc__control.html#ga3f57bcedf610dc844e6cc3a230dba5f7">Driver_USART.h</a>
</li>
<li>ARM_USART_ABORT_SEND
: <a class="el" href="group__usart__misc__control.html#ga54e88b32bc7368ff9c44613eae735c44">Driver_USART.h</a>
</li>
<li>ARM_USART_ABORT_TRANSFER
: <a class="el" href="group__usart__misc__control.html#ga83d0ef402feb342f9939f0e4ffe26182">Driver_USART.h</a>
</li>
<li>ARM_USART_API_VERSION
: <a class="el" href="_driver___u_s_a_r_t_8h.html#ab37a12fd0981e09c42ea42684a5dfbab">Driver_USART.h</a>
</li>
<li>ARM_USART_CONTROL_BREAK
: <a class="el" href="group__usart__misc__control.html#gab194a6f916e5b25e0262534c0cce54dc">Driver_USART.h</a>
</li>
<li>ARM_USART_CONTROL_Msk
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a253d29333d1a40d0401a02f9675a90fd">Driver_USART.h</a>
</li>
<li>ARM_USART_CONTROL_Pos
: <a class="el" href="_driver___u_s_a_r_t_8h.html#ab654e36e71012c28b91273e96827e1b8">Driver_USART.h</a>
</li>
<li>ARM_USART_CONTROL_RX
: <a class="el" href="group__usart__misc__control.html#gad52c08553ae203d4f7741404589b8169">Driver_USART.h</a>
</li>
<li>ARM_USART_CONTROL_SMART_CARD_NACK
: <a class="el" href="group__usart__misc__control.html#ga4bb5374e7db308b6ff48aa13aa9c4b8a">Driver_USART.h</a>
</li>
<li>ARM_USART_CONTROL_TX
: <a class="el" href="group__usart__misc__control.html#gad96ea1a80c97f968fbc0ae4c20e7fa6a">Driver_USART.h</a>
</li>
<li>ARM_USART_CPHA0
: <a class="el" href="group__usart__clock__phase.html#ga5eb27c2294b7d14a20d0c7e2ef0a47b4">Driver_USART.h</a>
</li>
<li>ARM_USART_CPHA1
: <a class="el" href="group__usart__clock__phase.html#ga4b9f16371870476739a198c52dba6862">Driver_USART.h</a>
</li>
<li>ARM_USART_CPHA_Msk
: <a class="el" href="_driver___u_s_a_r_t_8h.html#afba3e5931503b5a820472c4610252d72">Driver_USART.h</a>
</li>
<li>ARM_USART_CPHA_Pos
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a01ec7322a6a62197e82e948b1a8a41fa">Driver_USART.h</a>
</li>
<li>ARM_USART_CPOL0
: <a class="el" href="group__usart__clock__polarity.html#ga472d459abb99f1caaff94fa0cdd2ad27">Driver_USART.h</a>
</li>
<li>ARM_USART_CPOL1
: <a class="el" href="group__usart__clock__polarity.html#ga9e5541d8937a9d92e42aeb273138592a">Driver_USART.h</a>
</li>
<li>ARM_USART_CPOL_Msk
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a2424397076d0479ab6b83e557be35db2">Driver_USART.h</a>
</li>
<li>ARM_USART_CPOL_Pos
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a76148e4ea9d9e8a798e904e1d65d5dfc">Driver_USART.h</a>
</li>
<li>ARM_USART_DATA_BITS_5
: <a class="el" href="group__usart__data__bits.html#ga981ff25b4ff806f743d1af4575b87339">Driver_USART.h</a>
</li>
<li>ARM_USART_DATA_BITS_6
: <a class="el" href="group__usart__data__bits.html#ga92ba3d6cea5cd5c0b661667539a9e43c">Driver_USART.h</a>
</li>
<li>ARM_USART_DATA_BITS_7
: <a class="el" href="group__usart__data__bits.html#gad86a2d971ce521c6f6eda28d4f8786a4">Driver_USART.h</a>
</li>
<li>ARM_USART_DATA_BITS_8
: <a class="el" href="group__usart__data__bits.html#gadc5e8d17b5c69cd7f9135b849c2a4586">Driver_USART.h</a>
</li>
<li>ARM_USART_DATA_BITS_9
: <a class="el" href="group__usart__data__bits.html#gae238a08198dc7ac6178ae0a2a95a2764">Driver_USART.h</a>
</li>
<li>ARM_USART_DATA_BITS_Msk
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a84581b0925c149db3ca28d2656107656">Driver_USART.h</a>
</li>
<li>ARM_USART_DATA_BITS_Pos
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a08696262ebd491edf1e7865ebe93a81f">Driver_USART.h</a>
</li>
<li>ARM_USART_ERROR_BAUDRATE
: <a class="el" href="group__usart__execution__status.html#gab57c4e8d4cb3a4b73751a002f5ec4586">Driver_USART.h</a>
</li>
<li>ARM_USART_ERROR_CPHA
: <a class="el" href="group__usart__execution__status.html#gade1af23c4ed5409dacd99ab76dc2ff8b">Driver_USART.h</a>
</li>
<li>ARM_USART_ERROR_CPOL
: <a class="el" href="group__usart__execution__status.html#ga2a1cd0a1e1bce9b545b0d7854a6fd6d6">Driver_USART.h</a>
</li>
<li>ARM_USART_ERROR_DATA_BITS
: <a class="el" href="group__usart__execution__status.html#gaade95ddec6882e96c086dfe8e0ba9a4c">Driver_USART.h</a>
</li>
<li>ARM_USART_ERROR_FLOW_CONTROL
: <a class="el" href="group__usart__execution__status.html#gaf8fea8d43ff72c76434d8b5e9eebd890">Driver_USART.h</a>
</li>
<li>ARM_USART_ERROR_MODE
: <a class="el" href="group__usart__execution__status.html#gaa98f35611ec5bd7034f21cb47199322b">Driver_USART.h</a>
</li>
<li>ARM_USART_ERROR_PARITY
: <a class="el" href="group__usart__execution__status.html#gaefabd886c586a45f4f7346c1f04392d0">Driver_USART.h</a>
</li>
<li>ARM_USART_ERROR_STOP_BITS
: <a class="el" href="group__usart__execution__status.html#ga1d699654fbbed3ca41c5ea10aac8f859">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_CTS
: <a class="el" href="group___u_s_a_r_t__events.html#ga4cd807ca131bdcb1a7eb4f223fa70476">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_DCD
: <a class="el" href="group___u_s_a_r_t__events.html#ga1628b951feba1c851f424ce89da409a4">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_DSR
: <a class="el" href="group___u_s_a_r_t__events.html#ga5afef591c2e8dd9bc4332b7bc8d96309">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_RECEIVE_COMPLETE
: <a class="el" href="group___u_s_a_r_t__events.html#ga08b165fd8525e44e3ce42ed6183cd30a">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_RI
: <a class="el" href="group___u_s_a_r_t__events.html#gac17fe5723d4c5923656dadd9d1302154">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_RX_BREAK
: <a class="el" href="group___u_s_a_r_t__events.html#gaa1d19e48faf2bdc2a976de448928288e">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_RX_FRAMING_ERROR
: <a class="el" href="group___u_s_a_r_t__events.html#ga2d97495c650220fbfe9d6977d0953127">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_RX_OVERFLOW
: <a class="el" href="group___u_s_a_r_t__events.html#ga43a0869daf83abb3fea96926a97047ad">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_RX_PARITY_ERROR
: <a class="el" href="group___u_s_a_r_t__events.html#gadb4fec2530fc5ae3ad2b056741883451">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_RX_TIMEOUT
: <a class="el" href="group___u_s_a_r_t__events.html#ga66ee2256571450a3fc3c530344ea9bd7">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_SEND_COMPLETE
: <a class="el" href="group___u_s_a_r_t__events.html#gaae1c626192b16ccace93f3546e7884bf">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_TRANSFER_COMPLETE
: <a class="el" href="group___u_s_a_r_t__events.html#ga0599793e6aa531d56ff9f81ff12605d7">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_TX_COMPLETE
: <a class="el" href="group___u_s_a_r_t__events.html#ga12872a3b04343f97d9535b5b0d37286d">Driver_USART.h</a>
</li>
<li>ARM_USART_EVENT_TX_UNDERFLOW
: <a class="el" href="group___u_s_a_r_t__events.html#gae57b9977bd338bf8bef86978843fa443">Driver_USART.h</a>
</li>
<li>ARM_USART_FLOW_CONTROL_CTS
: <a class="el" href="group__usart__flow__control.html#gaa7b38ebff1ce0f5c3e4479d22e66715f">Driver_USART.h</a>
</li>
<li>ARM_USART_FLOW_CONTROL_Msk
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a0e80cb6a6f47c164fb1fe5fe8eab43f4">Driver_USART.h</a>
</li>
<li>ARM_USART_FLOW_CONTROL_NONE
: <a class="el" href="group__usart__flow__control.html#gad04aa3fe4ea4b7363aee4bdca2ed3764">Driver_USART.h</a>
</li>
<li>ARM_USART_FLOW_CONTROL_Pos
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a2e09a6b54db30327511241fdf422c4c9">Driver_USART.h</a>
</li>
<li>ARM_USART_FLOW_CONTROL_RTS
: <a class="el" href="group__usart__flow__control.html#ga80c8a78e8868165cfcc543105bfd9621">Driver_USART.h</a>
</li>
<li>ARM_USART_FLOW_CONTROL_RTS_CTS
: <a class="el" href="group__usart__flow__control.html#gab16151b5c376b41586faf033f4a42d02">Driver_USART.h</a>
</li>
<li>ARM_USART_MODE_ASYNCHRONOUS
: <a class="el" href="group__usart__mode__control.html#gad85039731478c924d3b418ec00768388">Driver_USART.h</a>
</li>
<li>ARM_USART_MODE_IRDA
: <a class="el" href="group__usart__mode__control.html#ga458f4f60d1d772cfd7567ae424d9aad9">Driver_USART.h</a>
</li>
<li>ARM_USART_MODE_SINGLE_WIRE
: <a class="el" href="group__usart__mode__control.html#ga4132136971d4f93f2e6a87c6775a9bb0">Driver_USART.h</a>
</li>
<li>ARM_USART_MODE_SMART_CARD
: <a class="el" href="group__usart__mode__control.html#gade65a1c27d9097d9ef0e86c02b55cecd">Driver_USART.h</a>
</li>
<li>ARM_USART_MODE_SYNCHRONOUS_MASTER
: <a class="el" href="group__usart__mode__control.html#ga7d3e9e0e838a3f15f8661983b9ac4573">Driver_USART.h</a>
</li>
<li>ARM_USART_MODE_SYNCHRONOUS_SLAVE
: <a class="el" href="group__usart__mode__control.html#gae78778475f3fab09a080c2279afc69fa">Driver_USART.h</a>
</li>
<li>ARM_USART_PARITY_EVEN
: <a class="el" href="group__usart__parity__bit.html#gabc35e8dd2cbebb730abf36959e87a207">Driver_USART.h</a>
</li>
<li>ARM_USART_PARITY_Msk
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a434c48980c65129c01aa5bc1c8e22898">Driver_USART.h</a>
</li>
<li>ARM_USART_PARITY_NONE
: <a class="el" href="group__usart__parity__bit.html#ga141a64650f99a1f642c3b3b6ced0eb8d">Driver_USART.h</a>
</li>
<li>ARM_USART_PARITY_ODD
: <a class="el" href="group__usart__parity__bit.html#ga02f30181eedd3b04d650dd507bf40d6d">Driver_USART.h</a>
</li>
<li>ARM_USART_PARITY_Pos
: <a class="el" href="_driver___u_s_a_r_t_8h.html#a2ce50af2e58db12c25a5791080aca258">Driver_USART.h</a>
</li>
<li>ARM_USART_SET_DEFAULT_TX_VALUE
: <a class="el" href="group__usart__misc__control.html#gacd6f060afd55ffa1422567c31ebad950">Driver_USART.h</a>
</li>
<li>ARM_USART_SET_IRDA_PULSE
: <a class="el" href="group__usart__misc__control.html#gab8565d1f26382e832327e4553d18eb02">Driver_USART.h</a>
</li>
<li>ARM_USART_SET_SMART_CARD_CLOCK
: <a class="el" href="group__usart__misc__control.html#ga79698a2bd564c1f5bb1829ea422e9d3d">Driver_USART.h</a>
</li>
<li>ARM_USART_SET_SMART_CARD_GUARD_TIME
: <a class="el" href="group__usart__misc__control.html#ga169be809adc186c131bb8b1618005b28">Driver_USART.h</a>
</li>
<li>ARM_USART_STOP_BITS_0_5
: <a class="el" href="group__usart__stop__bits.html#ga47f43cb83d9955a4c90d918acaaa44ba">Driver_USART.h</a>
</li>
<li>ARM_USART_STOP_BITS_1
: <a class="el" href="group__usart__stop__bits.html#ga45f51a51e654b4753a538ed33f0d7d78">Driver_USART.h</a>
</li>
<li>ARM_USART_STOP_BITS_1_5
: <a class="el" href="group__usart__stop__bits.html#gafc1d0f2c95a76ef4c5152792a619f136">Driver_USART.h</a>
</li>
<li>ARM_USART_STOP_BITS_2
: <a class="el" href="group__usart__stop__bits.html#ga17f034b5f0d0328dc636b403d1954795">Driver_USART.h</a>
</li>
<li>ARM_USART_STOP_BITS_Msk
: <a class="el" href="_driver___u_s_a_r_t_8h.html#aff72dd7b794cf2be5b5edca180be7a40">Driver_USART.h</a>
</li>
<li>ARM_USART_STOP_BITS_Pos
: <a class="el" href="_driver___u_s_a_r_t_8h.html#ac73d045a0058006dbdc64a6d43772217">Driver_USART.h</a>
</li>
<li>ARM_USB_ENDPOINT_BULK
: <a class="el" href="group___u_s_b__endpoint__type.html#gac80fcc73aada5562e35e4bf2c21b7b2d">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_CONTROL
: <a class="el" href="group___u_s_b__endpoint__type.html#gaf8df4a353e829cf41a9f712e1b3c93a1">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_DIRECTION_MASK
: <a class="el" href="_driver___u_s_b_8h.html#afc3be8e98be7a242c81cd677996f21d8">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_INTERRUPT
: <a class="el" href="group___u_s_b__endpoint__type.html#ga9375cd3a2735e7d5c8c359a1cdbc7d95">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_ISOCHRONOUS
: <a class="el" href="group___u_s_b__endpoint__type.html#gabb5913e9d1434240588ec43722d3eb16">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_MAX_PACKET_SIZE_MASK
: <a class="el" href="_driver___u_s_b_8h.html#acdacc3b2d7854566a90f0fe265a96f9d">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_MICROFRAME_TRANSACTIONS_1
: <a class="el" href="_driver___u_s_b_8h.html#aff3c2adf06d5dc8ccb3622b5860b380c">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_MICROFRAME_TRANSACTIONS_2
: <a class="el" href="_driver___u_s_b_8h.html#afd061484f2de3a75e89b126f6ed21226">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_MICROFRAME_TRANSACTIONS_3
: <a class="el" href="_driver___u_s_b_8h.html#a7c23bf8680f3feaf444d289df3603c38">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_MICROFRAME_TRANSACTIONS_MASK
: <a class="el" href="_driver___u_s_b_8h.html#a72544ba674dc6d32c9caffcf9083fdfd">Driver_USB.h</a>
</li>
<li>ARM_USB_ENDPOINT_NUMBER_MASK
: <a class="el" href="_driver___u_s_b_8h.html#a6ef3e2cf16ee3b1356947a17e5d42a6c">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_ACK
: <a class="el" href="_driver___u_s_b_8h.html#a27505b779f79c82f3d7e63f7841b9c98">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_DATA0
: <a class="el" href="_driver___u_s_b_8h.html#a047d50d8c7ffa89fb75608cce2756ac6">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_DATA1
: <a class="el" href="_driver___u_s_b_8h.html#ab46f4fe90edf21e8e88f21a07876d88d">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_DATA2
: <a class="el" href="_driver___u_s_b_8h.html#af3462fb5987bcda04963c7fd02461ced">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_ERR
: <a class="el" href="_driver___u_s_b_8h.html#a56e57da4c1665866bf48a4bff4fa36b7">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_IN
: <a class="el" href="_driver___u_s_b_8h.html#adb2459ddd000755c1ea52183bb8c55bb">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_MDATA
: <a class="el" href="_driver___u_s_b_8h.html#ad78b3806a0963b041135a76eaf2fcd73">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_NAK
: <a class="el" href="_driver___u_s_b_8h.html#a3a0de1078536cc459dd21d9b87b952f5">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_NYET
: <a class="el" href="_driver___u_s_b_8h.html#ab4f0fb1af1b2257eef132a684a58b62b">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_OUT
: <a class="el" href="_driver___u_s_b_8h.html#a828674d5225de2aadf7fb764a22e74f3">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_PING
: <a class="el" href="_driver___u_s_b_8h.html#a2249b4907c4e9fa63a9d5685a6525ae6">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_PRE
: <a class="el" href="_driver___u_s_b_8h.html#aa044bae2705c08f38d6cc40bc32d0323">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_RESERVED
: <a class="el" href="_driver___u_s_b_8h.html#a8a8e917e48e97820b08e36b3da87f678">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_SETUP
: <a class="el" href="_driver___u_s_b_8h.html#a08d69b2a1e3d5ef4ef1a36f591ea0df6">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_SOF
: <a class="el" href="_driver___u_s_b_8h.html#ab8945ae385d3e7d0da9ba9ff461e09c1">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_SPLIT
: <a class="el" href="_driver___u_s_b_8h.html#a136c540358ff3ec0007a7fee6ddcf308">Driver_USB.h</a>
</li>
<li>ARM_USB_PID_STALL
: <a class="el" href="_driver___u_s_b_8h.html#ab7f3d6c2acab8b6b607ff7c063c11444">Driver_USB.h</a>
</li>
<li>ARM_USB_PIN_DM
: <a class="el" href="_driver___u_s_b_8h.html#a5a5348d08c911233e4e9d81d1c42a367">Driver_USB.h</a>
</li>
<li>ARM_USB_PIN_DP
: <a class="el" href="_driver___u_s_b_8h.html#a7f42a0ff2d6e9187428a0126cc140c5b">Driver_USB.h</a>
</li>
<li>ARM_USB_PIN_ID
: <a class="el" href="_driver___u_s_b_8h.html#a03a103d754d9751485b748cd144cbfd7">Driver_USB.h</a>
</li>
<li>ARM_USB_PIN_OC
: <a class="el" href="_driver___u_s_b_8h.html#a03ae07428733660d5e187fec1aede075">Driver_USB.h</a>
</li>
<li>ARM_USB_PIN_VBUS
: <a class="el" href="_driver___u_s_b_8h.html#a390491e93356426de470ae41302dd547">Driver_USB.h</a>
</li>
<li>ARM_USB_ROLE_DEVICE
: <a class="el" href="_driver___u_s_b_8h.html#ab67eb404d64392b8910d7fdaef01aa0d">Driver_USB.h</a>
</li>
<li>ARM_USB_ROLE_HOST
: <a class="el" href="_driver___u_s_b_8h.html#ab21de5df4cadf9bc4381c8c1066205d8">Driver_USB.h</a>
</li>
<li>ARM_USB_ROLE_NONE
: <a class="el" href="_driver___u_s_b_8h.html#a2cb4a6a3dc939c3b13021be266f23e76">Driver_USB.h</a>
</li>
<li>ARM_USB_SPEED_FULL
: <a class="el" href="group___u_s_b__speed.html#ga0d1b465db654b651dcf588c8b59899d5">Driver_USB.h</a>
</li>
<li>ARM_USB_SPEED_HIGH
: <a class="el" href="group___u_s_b__speed.html#ga13fa1e1934021f744dba837776205c89">Driver_USB.h</a>
</li>
<li>ARM_USB_SPEED_LOW
: <a class="el" href="group___u_s_b__speed.html#gae44fe8958474cd90f2288ea27752df27">Driver_USB.h</a>
</li>
<li>ARM_USBD_API_VERSION
: <a class="el" href="_driver___u_s_b_d_8h.html#a71df406694e557f19cae5e43ff1960ba">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_HIGH_SPEED
: <a class="el" href="group___u_s_b_d__dev__events.html#ga689d1e031013d0e66aeef4243490d843">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_IN
: <a class="el" href="group___u_s_b_d__ep__events.html#ga375d3d8f363a056ff607c5ab3b92a864">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_OUT
: <a class="el" href="group___u_s_b_d__ep__events.html#ga35f7340508acb5fe7a5f43bbcac1887a">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_RESET
: <a class="el" href="group___u_s_b_d__dev__events.html#ga489e1b88f7b0361494ca3a8dc73c227a">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_RESUME
: <a class="el" href="group___u_s_b_d__dev__events.html#ga5b1c9884b237ba7778f79761e5db9f45">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_SETUP
: <a class="el" href="group___u_s_b_d__ep__events.html#gaa0814f6880f4c0ac302ac9ebc8170739">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_SUSPEND
: <a class="el" href="group___u_s_b_d__dev__events.html#ga74dc7c0ba71baf285400d5a555224653">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_VBUS_OFF
: <a class="el" href="group___u_s_b_d__dev__events.html#ga6810c08a6e6a46ba443899e5ba9c3aec">Driver_USBD.h</a>
</li>
<li>ARM_USBD_EVENT_VBUS_ON
: <a class="el" href="group___u_s_b_d__dev__events.html#ga32546413cfe55154351f74fb56de1045">Driver_USBD.h</a>
</li>
<li>ARM_USBH_API_VERSION
: <a class="el" href="_driver___u_s_b_h_8h.html#a032e1e5caca3235109e2d2d6bf2e34bb">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EP_HANDLE
: <a class="el" href="_driver___u_s_b_h_8h.html#ab2c57d8ea726b331b891167aa4a201c9">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_BUS_ERROR
: <a class="el" href="group___u_s_b_h__pipe__events.html#ga7bd871b1e5c059bee398c32429370724">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_CONNECT
: <a class="el" href="group___u_s_b_h__port__events.html#ga71bfd8b8cd41b8aa6303d5d3a91597e6">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_DISCONNECT
: <a class="el" href="group___u_s_b_h__port__events.html#gaba67919f64e9a08ba1264363b2710d20">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_HANDSHAKE_ERR
: <a class="el" href="group___u_s_b_h__pipe__events.html#gac7cc573f879fbab678dc7d1347c68614">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_HANDSHAKE_MDATA
: <a class="el" href="group___u_s_b_h__pipe__events.html#ga681ce0983f8c77c41f3cc5df1af8d010">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_HANDSHAKE_NAK
: <a class="el" href="group___u_s_b_h__pipe__events.html#ga3895b82193855d9a6f0b7e8a9b65e2c0">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_HANDSHAKE_NYET
: <a class="el" href="group___u_s_b_h__pipe__events.html#ga62ae214576c923ce737a16098e9836e5">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_HANDSHAKE_STALL
: <a class="el" href="group___u_s_b_h__pipe__events.html#ga4fdc44fc78f342576dd11ad7cb84b4b8">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_OVERCURRENT
: <a class="el" href="group___u_s_b_h__port__events.html#ga0955fdc2aedd2c5aa2be6cd782b3f2a8">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_REMOTE_WAKEUP
: <a class="el" href="group___u_s_b_h__port__events.html#ga2b61e9df3c63fd78fc08f79280a7066e">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_RESET
: <a class="el" href="group___u_s_b_h__port__events.html#ga70ae1e0a7872556d302a7f7840843c4a">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_RESUME
: <a class="el" href="group___u_s_b_h__port__events.html#ga42f62bdf6dd639f9f3dffc6c127456e3">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_SUSPEND
: <a class="el" href="group___u_s_b_h__port__events.html#gae1f91db7d31bcebbf60a23fb04cf7eb5">Driver_USBH.h</a>
</li>
<li>ARM_USBH_EVENT_TRANSFER_COMPLETE
: <a class="el" href="group___u_s_b_h__pipe__events.html#gab161955b1ab0b7928befe446ef78634b">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_CSPLIT
: <a class="el" href="group___u_s_b_h__packets.html#gadbfbbf7b4709f3ee4c3610da8402cfec">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_DATA0
: <a class="el" href="group___u_s_b_h__packets.html#ga40075aa1d3eff6d4b94dfe28d7745873">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_DATA1
: <a class="el" href="group___u_s_b_h__packets.html#ga34014ff212b26e3ee8c8670a180846e2">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_DATA_Msk
: <a class="el" href="_driver___u_s_b_h_8h.html#a979e5c7ad6bae9b5eb42eb9ee1b6a843">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_DATA_Pos
: <a class="el" href="_driver___u_s_b_h_8h.html#a2b28435952abaeea6fd29480f10e56eb">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_IN
: <a class="el" href="group___u_s_b_h__packets.html#ga08d60ec20c091b5e7e252d137268cb76">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_OUT
: <a class="el" href="group___u_s_b_h__packets.html#ga409b2ae6503e738eb86e35652f9ebf8d">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_PING
: <a class="el" href="group___u_s_b_h__packets.html#ga2eeab58cebb4556214c021ff02c36b16">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_PRE
: <a class="el" href="group___u_s_b_h__packets.html#ga6dd82c7b96bc1339d725a6133a32a62f">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_SETUP
: <a class="el" href="group___u_s_b_h__packets.html#gafb0bcfee8abd4ada7f789aec2993048a">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_SPLIT_Msk
: <a class="el" href="_driver___u_s_b_h_8h.html#ad0c886d7d97a8ad2c343eab0552a09db">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_SPLIT_Pos
: <a class="el" href="_driver___u_s_b_h_8h.html#a41e5706fcf6e028c5c86751b37a27dd6">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_SSPLIT
: <a class="el" href="group___u_s_b_h__packets.html#gaf47930d994c53fc1772caed129aee921">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_SSPLIT_E
: <a class="el" href="group___u_s_b_h__packets.html#gaf99ee84befc6522fef56b21df870df72">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_SSPLIT_S
: <a class="el" href="group___u_s_b_h__packets.html#ga3b8fa0d3aa083718b4f5d60e92394b47">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_SSPLIT_S_E
: <a class="el" href="group___u_s_b_h__packets.html#ga8d2b46fbc04d871abe0661f8acd18a94">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_TOKEN_Msk
: <a class="el" href="_driver___u_s_b_h_8h.html#a366b3541934b74772eba60e6332923ad">Driver_USBH.h</a>
</li>
<li>ARM_USBH_PACKET_TOKEN_Pos
: <a class="el" href="_driver___u_s_b_h_8h.html#a8139a9c76ea4a062795130196d3b6ed9">Driver_USBH.h</a>
</li>
<li>ARM_USBH_SignalEndpointEvent_t
: <a class="el" href="_driver___u_s_b_h_8h.html#ab66601b4c31f638479d7ab6efa515dcc">Driver_USBH.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
