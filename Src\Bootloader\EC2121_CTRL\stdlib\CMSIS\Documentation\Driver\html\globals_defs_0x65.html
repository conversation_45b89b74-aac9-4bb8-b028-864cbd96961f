<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li class="current"><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_defs.html#index__"><span>_</span></a></li>
      <li><a href="globals_defs_0x64.html#index_d"><span>d</span></a></li>
      <li class="current"><a href="globals_defs_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_defs_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_defs_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_defs_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_defs_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_defs_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_defs_0x75.html#index_u"><span>u</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_defs_0x65.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_e"></a>- e -</h3><ul>
<li>ARM_ETH_DUPLEX_FULL
: <a class="el" href="_driver___e_t_h_8h.html#a7848c83cd1fd6b2645c17919c2990354">Driver_ETH.h</a>
</li>
<li>ARM_ETH_DUPLEX_HALF
: <a class="el" href="_driver___e_t_h_8h.html#acb15afc2bfe61c56049b7279d6eae8fe">Driver_ETH.h</a>
</li>
<li>ARM_ETH_INTERFACE_MII
: <a class="el" href="_driver___e_t_h_8h.html#a468c848ddf75d7925130171af1ec2ac7">Driver_ETH.h</a>
</li>
<li>ARM_ETH_INTERFACE_RMII
: <a class="el" href="_driver___e_t_h_8h.html#ac0361b34fbec9c19840ad0349e4c388b">Driver_ETH.h</a>
</li>
<li>ARM_ETH_INTERFACE_SMII
: <a class="el" href="_driver___e_t_h_8h.html#a24047d142be48bbc241e8d6eacb5cf7a">Driver_ETH.h</a>
</li>
<li>ARM_ETH_MAC_ADDRESS_ALL
: <a class="el" href="group__eth__mac__configuration__ctrls.html#gab29ab9e295807f4c59ddd1c4642086d1">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_ADDRESS_BROADCAST
: <a class="el" href="group__eth__mac__configuration__ctrls.html#ga43792feab641c3c87eafb943351ab0f4">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_ADDRESS_MULTICAST
: <a class="el" href="group__eth__mac__configuration__ctrls.html#ga1d3ff8c63362b385548fe91730f20588">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_API_VERSION
: <a class="el" href="_driver___e_t_h___m_a_c_8h.html#a89ade7fd7f91b1b6e21883a8f823a8cc">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_CHECKSUM_OFFLOAD_RX
: <a class="el" href="group__eth__mac__configuration__ctrls.html#ga281dfed993b5666ed999709b9f28578f">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_CHECKSUM_OFFLOAD_TX
: <a class="el" href="group__eth__mac__configuration__ctrls.html#ga7272d2c55aeeeadbb95c591cbf6c1a2e">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_CONFIGURE
: <a class="el" href="group__eth__mac__ctrls.html#ga7819c7a1aa7bbc13dc42d0fd7e75a23c">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_CONTROL_RX
: <a class="el" href="group__eth__mac__ctrls.html#gae0964364b81b38b6e1fbf7196f3be869">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_CONTROL_TX
: <a class="el" href="group__eth__mac__ctrls.html#ga3a98c8a7ee5ed4b1ffd250eecaeefe5c">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_DUPLEX_FULL
: <a class="el" href="group__eth__mac__configuration__ctrls.html#gad5a7d4b5b8a31825eff1504e3828d8f6">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_DUPLEX_HALF
: <a class="el" href="group__eth__mac__configuration__ctrls.html#gadb0fe2c5a1e21b0656d39c788ae22f36">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_DUPLEX_Msk
: <a class="el" href="_driver___e_t_h___m_a_c_8h.html#a9249afa842bb7e1088a7729542f91e22">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_DUPLEX_Pos
: <a class="el" href="_driver___e_t_h___m_a_c_8h.html#a245688f6265e8d017435dc6d2c233b87">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_EVENT_RX_FRAME
: <a class="el" href="group___e_t_h___m_a_c__events.html#ga76943471a4a3e9e8c1ff9fe83e43bd47">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_EVENT_TIMER_ALARM
: <a class="el" href="group___e_t_h___m_a_c__events.html#ga4afc71ecac964f195e27be4acdbe7c61">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_EVENT_TX_FRAME
: <a class="el" href="group___e_t_h___m_a_c__events.html#ga0c0328ff7cf886d5fdb53bb84ec03c1b">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_EVENT_WAKEUP
: <a class="el" href="group___e_t_h___m_a_c__events.html#ga1f3bdb219afa8f2a121b58cc84f5761c">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_FLUSH
: <a class="el" href="group__eth__mac__ctrls.html#ga530812ef349a2e297f23de72e660fe27">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_FLUSH_RX
: <a class="el" href="group__eth__mac__flush__flag__ctrls.html#gac18950811038319960756f063e1ef6d4">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_FLUSH_TX
: <a class="el" href="group__eth__mac__flush__flag__ctrls.html#ga2d10ff33f4f4927820c6a17a2262b120">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_LOOPBACK
: <a class="el" href="group__eth__mac__configuration__ctrls.html#gab32765f35c35b672ee476278fe24a24e">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_SLEEP
: <a class="el" href="group__eth__mac__ctrls.html#ga4afe66589216f566f529af52f9075fdf">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_SPEED_100M
: <a class="el" href="group__eth__mac__configuration__ctrls.html#ga29160c83a7b0952c64053d86789c6490">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_SPEED_10M
: <a class="el" href="group__eth__mac__configuration__ctrls.html#ga8c5b40d018ecfad05fe2546ba717c1d4">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_SPEED_1G
: <a class="el" href="group__eth__mac__configuration__ctrls.html#ga8acefed744d8397a1777b9fd0e6230d2">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_SPEED_Msk
: <a class="el" href="_driver___e_t_h___m_a_c_8h.html#a7f3cf200085c3387a5572102af2ee2da">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_SPEED_Pos
: <a class="el" href="_driver___e_t_h___m_a_c_8h.html#ad7fd5c5f4d4f39a56466c2d34cb699ef">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TIMER_ADJUST_CLOCK
: <a class="el" href="group__eth__mac__time__control.html#ga85cb862eba0934e958a8552022588db7">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TIMER_DEC_TIME
: <a class="el" href="group__eth__mac__time__control.html#gaca9f1c4259d0342e9717a362de1ccf41">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TIMER_GET_TIME
: <a class="el" href="group__eth__mac__time__control.html#gad9a439b9727c032a7d851df2a7a622c2">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TIMER_INC_TIME
: <a class="el" href="group__eth__mac__time__control.html#ga3c57b3150717fb1a8cbbbac6a9b7ff69">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TIMER_SET_ALARM
: <a class="el" href="group__eth__mac__time__control.html#ga04c2469ba027b020bc6b5baf3b51cf74">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TIMER_SET_TIME
: <a class="el" href="group__eth__mac__time__control.html#ga5e867a003c06046d7944bcb5723e6049">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TX_FRAME_EVENT
: <a class="el" href="group__eth__mac__frame__transmit__ctrls.html#ga91a923680ea0dad758b8950a3fbd237e">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TX_FRAME_FRAGMENT
: <a class="el" href="group__eth__mac__frame__transmit__ctrls.html#gab7bd6dea5bb57240291db71e95c99d9c">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_TX_FRAME_TIMESTAMP
: <a class="el" href="group__eth__mac__frame__transmit__ctrls.html#gade137f65dd345ae40e93c77d495f9b54">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_VLAN_FILTER
: <a class="el" href="group__eth__mac__ctrls.html#gab332b58ba320e73864830dc42ad74181">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_MAC_VLAN_FILTER_ID_ONLY
: <a class="el" href="group__eth__mac__vlan__filter__ctrls.html#ga2511c9e4c22a2b351ce2e454be1c9427">Driver_ETH_MAC.h</a>
</li>
<li>ARM_ETH_PHY_API_VERSION
: <a class="el" href="_driver___e_t_h___p_h_y_8h.html#abb996b4afe22e0600aff5623076d0505">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_AUTO_NEGOTIATE
: <a class="el" href="group__eth__phy__mode__ctrls.html#ga6a8c54f8fed3e5f68bd04eb715d10ab9">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_DUPLEX_FULL
: <a class="el" href="group__eth__phy__mode__ctrls.html#ga5d06a94867c89cd311b6e279669321e3">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_DUPLEX_HALF
: <a class="el" href="group__eth__phy__mode__ctrls.html#gace797b3cd143be22f47c3ef61b20e14d">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_DUPLEX_Msk
: <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a832fd4424dc9aaafac8034457396c259">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_DUPLEX_Pos
: <a class="el" href="_driver___e_t_h___p_h_y_8h.html#aebafadc356d8e58407db156a5dac743f">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_ISOLATE
: <a class="el" href="group__eth__phy__mode__ctrls.html#ga8d68719e07c7af449b57c5df802376c8">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_LOOPBACK
: <a class="el" href="group__eth__phy__mode__ctrls.html#ga5f7e46cda8ab3c774fe7ce0a8a1ba3ec">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_SPEED_100M
: <a class="el" href="group__eth__phy__mode__ctrls.html#gad1e8b2c8c210fa36949db9a34a993657">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_SPEED_10M
: <a class="el" href="group__eth__phy__mode__ctrls.html#gabc7acc4ebe828c3d0825400e14ad20f0">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_SPEED_1G
: <a class="el" href="group__eth__phy__mode__ctrls.html#ga046605398ceae99a176e6f82432ae710">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_SPEED_Msk
: <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a8802cb0c4caed22c70d92f656e0811f5">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_PHY_SPEED_Pos
: <a class="el" href="_driver___e_t_h___p_h_y_8h.html#a13700fab82aa60a3357614faa0619e97">Driver_ETH_PHY.h</a>
</li>
<li>ARM_ETH_SPEED_100M
: <a class="el" href="_driver___e_t_h_8h.html#a3bddfc4cf5645f8568d9cb6621fd606a">Driver_ETH.h</a>
</li>
<li>ARM_ETH_SPEED_10M
: <a class="el" href="_driver___e_t_h_8h.html#a1f834c4c785d7f69b1eaca011ee298ec">Driver_ETH.h</a>
</li>
<li>ARM_ETH_SPEED_1G
: <a class="el" href="_driver___e_t_h_8h.html#a218f470079b7c04de6776d467a53772a">Driver_ETH.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
