<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Ethernet MAC Frame Transmit Flags</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__eth__mac__frame__transmit__ctrls.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">Ethernet MAC Frame Transmit Flags<div class="ingroups"><a class="el" href="group__eth__mac__interface__gr.html">Ethernet MAC Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Specify frame transmit flags.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gab7bd6dea5bb57240291db71e95c99d9c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__frame__transmit__ctrls.html#gab7bd6dea5bb57240291db71e95c99d9c">ARM_ETH_MAC_TX_FRAME_FRAGMENT</a>&#160;&#160;&#160;(1UL &lt;&lt; 0)</td></tr>
<tr class="memdesc:gab7bd6dea5bb57240291db71e95c99d9c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicate frame fragment.  <a href="#gab7bd6dea5bb57240291db71e95c99d9c">More...</a><br/></td></tr>
<tr class="separator:gab7bd6dea5bb57240291db71e95c99d9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga91a923680ea0dad758b8950a3fbd237e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__frame__transmit__ctrls.html#ga91a923680ea0dad758b8950a3fbd237e">ARM_ETH_MAC_TX_FRAME_EVENT</a>&#160;&#160;&#160;(1UL &lt;&lt; 1)</td></tr>
<tr class="memdesc:ga91a923680ea0dad758b8950a3fbd237e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate event when frame is transmitted.  <a href="#ga91a923680ea0dad758b8950a3fbd237e">More...</a><br/></td></tr>
<tr class="separator:ga91a923680ea0dad758b8950a3fbd237e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade137f65dd345ae40e93c77d495f9b54"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__frame__transmit__ctrls.html#gade137f65dd345ae40e93c77d495f9b54">ARM_ETH_MAC_TX_FRAME_TIMESTAMP</a>&#160;&#160;&#160;(1UL &lt;&lt; 2)</td></tr>
<tr class="memdesc:gade137f65dd345ae40e93c77d495f9b54"><td class="mdescLeft">&#160;</td><td class="mdescRight">Capture frame time stamp.  <a href="#gade137f65dd345ae40e93c77d495f9b54">More...</a><br/></td></tr>
<tr class="separator:gade137f65dd345ae40e93c77d495f9b54"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Specify frame transmit flags. </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="ga91a923680ea0dad758b8950a3fbd237e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TX_FRAME_EVENT&#160;&#160;&#160;(1UL &lt;&lt; 1)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Generate event when frame is transmitted. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3" title="Send Ethernet frame.">ARM_ETH_MAC_SendFrame</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab7bd6dea5bb57240291db71e95c99d9c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TX_FRAME_FRAGMENT&#160;&#160;&#160;(1UL &lt;&lt; 0)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Indicate frame fragment. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3" title="Send Ethernet frame.">ARM_ETH_MAC_SendFrame</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gade137f65dd345ae40e93c77d495f9b54"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TX_FRAME_TIMESTAMP&#160;&#160;&#160;(1UL &lt;&lt; 2)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Capture frame time stamp. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3" title="Send Ethernet frame.">ARM_ETH_MAC_SendFrame</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
