<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_func.html#index_a"><span>a</span></a></li>
      <li><a href="globals_func_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_func_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_func_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_func_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_func_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_func_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_func_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_func_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_func_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_func_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_func_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_func_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_func_0x72.html#index_r"><span>r</span></a></li>
      <li class="current"><a href="globals_func_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_func_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_func_0x76.html#index_v"><span>v</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_func_0x73.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_s"></a>- s -</h3><ul>
<li>arm_scale_f32()
: <a class="el" href="group__scale.html#ga3487af88b112f682ee90589cd419e123">arm_scale_f32.c</a>
, <a class="el" href="group__scale.html#ga3487af88b112f682ee90589cd419e123">arm_math.h</a>
</li>
<li>arm_scale_q15()
: <a class="el" href="group__scale.html#gafaac0e1927daffeb68a42719b53ea780">arm_math.h</a>
, <a class="el" href="group__scale.html#gafaac0e1927daffeb68a42719b53ea780">arm_scale_q15.c</a>
</li>
<li>arm_scale_q31()
: <a class="el" href="group__scale.html#ga83e36cd82bf51ce35406a199e477d47c">arm_scale_q31.c</a>
, <a class="el" href="group__scale.html#ga83e36cd82bf51ce35406a199e477d47c">arm_math.h</a>
</li>
<li>arm_scale_q7()
: <a class="el" href="group__scale.html#gabc9fd3d37904c58df56492b351d21fb0">arm_math.h</a>
, <a class="el" href="group__scale.html#gabc9fd3d37904c58df56492b351d21fb0">arm_scale_q7.c</a>
</li>
<li>arm_shift_q15()
: <a class="el" href="group__shift.html#gaa1757e53279780107acc92cf100adb61">arm_shift_q15.c</a>
, <a class="el" href="group__shift.html#gaa1757e53279780107acc92cf100adb61">arm_math.h</a>
</li>
<li>arm_shift_q31()
: <a class="el" href="group__shift.html#ga387dd8b7b87377378280978f16cdb13d">arm_shift_q31.c</a>
, <a class="el" href="group__shift.html#ga387dd8b7b87377378280978f16cdb13d">arm_math.h</a>
</li>
<li>arm_shift_q7()
: <a class="el" href="group__shift.html#ga47295d08a685f7de700a48dafb4db6fb">arm_shift_q7.c</a>
, <a class="el" href="group__shift.html#ga47295d08a685f7de700a48dafb4db6fb">arm_math.h</a>
</li>
<li>arm_sin_cos_f32()
: <a class="el" href="group___sin_cos.html#ga4420d45c37d58c310ef9ae1b5fe58020">arm_math.h</a>
, <a class="el" href="group___sin_cos.html#ga4420d45c37d58c310ef9ae1b5fe58020">arm_sin_cos_f32.c</a>
</li>
<li>arm_sin_cos_q31()
: <a class="el" href="group___sin_cos.html#gae9e4ddebff9d4eb5d0a093e28e0bc504">arm_sin_cos_q31.c</a>
, <a class="el" href="group___sin_cos.html#gae9e4ddebff9d4eb5d0a093e28e0bc504">arm_math.h</a>
</li>
<li>arm_sin_f32()
: <a class="el" href="group__sin.html#gae164899c4a3fc0e946dc5d55555fe541">arm_sin_f32.c</a>
, <a class="el" href="group__sin.html#gae164899c4a3fc0e946dc5d55555fe541">arm_math.h</a>
</li>
<li>arm_sin_q15()
: <a class="el" href="group__sin.html#ga1fc6d6640be6cfa688a8bea0a48397ee">arm_sin_q15.c</a>
, <a class="el" href="group__sin.html#ga1fc6d6640be6cfa688a8bea0a48397ee">arm_math.h</a>
</li>
<li>arm_sin_q31()
: <a class="el" href="group__sin.html#ga57aade7d8892585992cdc6375bd82f9c">arm_sin_q31.c</a>
, <a class="el" href="group__sin.html#ga57aade7d8892585992cdc6375bd82f9c">arm_math.h</a>
</li>
<li>arm_snr_f32()
: <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab">arm_graphic_equalizer_example/ARM/math_helper.c</a>
</li>
<li>arm_split_rfft_f32()
: <a class="el" href="group__group_transforms.html#ga6cfdb6bdc66b13732ef2351caf98fdbb">arm_rfft_f32.c</a>
</li>
<li>arm_split_rfft_q15()
: <a class="el" href="arm__rfft__q15_8c.html#a7c2a21793586f9a69c42140665550e09">arm_rfft_q15.c</a>
</li>
<li>arm_split_rfft_q31()
: <a class="el" href="arm__rfft__q31_8c.html#a520e1c358d44fcd2724cb19d46eb5dfa">arm_rfft_q31.c</a>
</li>
<li>arm_split_rifft_f32()
: <a class="el" href="arm__rfft__f32_8c.html#a585bef78c103d150a116241a4feb6442">arm_rfft_f32.c</a>
</li>
<li>arm_split_rifft_q15()
: <a class="el" href="arm__rfft__q15_8c.html#aa72a531dd15a53570dddaf01b62158f4">arm_rfft_q15.c</a>
</li>
<li>arm_split_rifft_q31()
: <a class="el" href="arm__rfft__q31_8c.html#acc62dd39a59091c4d6a80d4e55adeb13">arm_rfft_q31.c</a>
</li>
<li>arm_sqrt_f32()
: <a class="el" href="group___s_q_r_t.html#ga56a40d1cf842b0b45267df6761975da0">arm_math.h</a>
</li>
<li>arm_sqrt_q15()
: <a class="el" href="group___s_q_r_t.html#ga5abe5ca724f3e15849662b03752c1238">arm_sqrt_q15.c</a>
</li>
<li>arm_sqrt_q31()
: <a class="el" href="group___s_q_r_t.html#ga119e25831e141d734d7ef10636670058">arm_sqrt_q31.c</a>
</li>
<li>arm_std_f32()
: <a class="el" href="group___s_t_d.html#ga4969b5b5f3d001377bc401a3ee99dfc2">arm_std_f32.c</a>
, <a class="el" href="group___s_t_d.html#ga4969b5b5f3d001377bc401a3ee99dfc2">arm_math.h</a>
</li>
<li>arm_std_q15()
: <a class="el" href="group___s_t_d.html#gaf9d27afa9928ff28a63cd98ea9218a72">arm_std_q15.c</a>
, <a class="el" href="group___s_t_d.html#gaf9d27afa9928ff28a63cd98ea9218a72">arm_math.h</a>
</li>
<li>arm_std_q31()
: <a class="el" href="group___s_t_d.html#ga39495e74f96116178be085c9dc7742f5">arm_std_q31.c</a>
, <a class="el" href="group___s_t_d.html#ga39495e74f96116178be085c9dc7742f5">arm_math.h</a>
</li>
<li>arm_sub_f32()
: <a class="el" href="group___basic_sub.html#ga7f975a472de286331134227c08aad826">arm_sub_f32.c</a>
, <a class="el" href="group___basic_sub.html#ga7f975a472de286331134227c08aad826">arm_math.h</a>
</li>
<li>arm_sub_q15()
: <a class="el" href="group___basic_sub.html#ga997a8ee93088d15bda23c325d455b588">arm_sub_q15.c</a>
, <a class="el" href="group___basic_sub.html#ga997a8ee93088d15bda23c325d455b588">arm_math.h</a>
</li>
<li>arm_sub_q31()
: <a class="el" href="group___basic_sub.html#ga28aa6908d092752144413e21933dc878">arm_sub_q31.c</a>
, <a class="el" href="group___basic_sub.html#ga28aa6908d092752144413e21933dc878">arm_math.h</a>
</li>
<li>arm_sub_q7()
: <a class="el" href="group___basic_sub.html#gab09941de7dfeb247e5c29b406a435fcc">arm_sub_q7.c</a>
, <a class="el" href="group___basic_sub.html#gab09941de7dfeb247e5c29b406a435fcc">arm_math.h</a>
</li>
<li>stage_rfft_f32()
: <a class="el" href="arm__rfft__fast__f32_8c.html#a47157c5a53c8aac5e80fda31acf1f9cc">arm_rfft_fast_f32.c</a>
</li>
<li>SystemCoreClockUpdate()
: <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_class_marks_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_class_marks_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_sin_cos_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_class_marks_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_class_marks_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_class_marks_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_signal_converge_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_class_marks_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_convolution_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_convolution_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_matrix_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_convolution_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_convolution_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_convolution_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_convolution_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_convolution_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_dotproduct_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_dotproduct_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_dotproduct_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_dotproduct_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_dotproduct_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fir_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_dotproduct_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_dotproduct_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fft_bin_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fft_bin_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fft_bin_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fft_bin_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fft_bin_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fft_bin_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fft_bin_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fir_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fir_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_fir_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_linear_interp_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_linear_interp_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_linear_interp_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_linear_interp_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_matrix_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_matrix_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_matrix_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_signal_converge_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_signal_converge_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_signal_converge_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_sin_cos_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_sin_cos_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_sin_cos_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_variance_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_variance_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_variance_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_variance_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">arm_class_marks_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
</li>
<li>SystemInit()
: <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_signal_converge_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_class_marks_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_convolution_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fir_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_class_marks_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_matrix_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fir_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fft_bin_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_class_marks_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_convolution_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_signal_converge_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_dotproduct_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_convolution_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_dotproduct_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_linear_interp_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_dotproduct_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_dotproduct_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_matrix_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_dotproduct_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_sin_cos_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fft_bin_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_class_marks_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_sin_cos_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_convolution_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fir_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_class_marks_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_class_marks_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fft_bin_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_linear_interp_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_variance_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_convolution_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fir_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_linear_interp_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_dotproduct_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_sin_cos_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_convolution_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_variance_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fft_bin_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_dotproduct_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_signal_converge_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fft_bin_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_sin_cos_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_matrix_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_linear_interp_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_convolution_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fft_bin_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_class_marks_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_fft_bin_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_variance_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_matrix_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_variance_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a93f514700ccf00d08dbdcff7f1224eb2">arm_signal_converge_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
