<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li class="current"><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_vars.html#index_a"><span>a</span></a></li>
      <li><a href="globals_vars_0x62.html#index_b"><span>b</span></a></li>
      <li class="current"><a href="globals_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="globals_vars_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="globals_vars_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_vars_0x63.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_c"></a>- c -</h3><ul>
<li>arm_cfft_sR_f32_len1024
: <a class="el" href="arm__const__structs_8c.html#a05abc294a9159abbd6ffb4f188fe18b1">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a05abc294a9159abbd6ffb4f188fe18b1">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_f32_len128
: <a class="el" href="arm__const__structs_8h.html#ad283193397ba476465a330db9a955973">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#ad283193397ba476465a330db9a955973">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_f32_len16
: <a class="el" href="arm__const__structs_8c.html#a27127e9d3deb59df12747233b1b9ea31">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a27127e9d3deb59df12747233b1b9ea31">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_f32_len2048
: <a class="el" href="arm__const__structs_8h.html#a8d2fad347dcadc47377e1226231b9f62">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#a8d2fad347dcadc47377e1226231b9f62">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_f32_len256
: <a class="el" href="arm__const__structs_8c.html#aeb2f0a0be605963264217cc10b7bd3b2">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#aeb2f0a0be605963264217cc10b7bd3b2">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_f32_len32
: <a class="el" href="arm__const__structs_8c.html#a5fed2b5e0cc4cb5b8675f14daf226a25">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a5fed2b5e0cc4cb5b8675f14daf226a25">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_f32_len4096
: <a class="el" href="arm__const__structs_8c.html#a01d2dbdb8193d43c2b7f003f9cb9a39d">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a01d2dbdb8193d43c2b7f003f9cb9a39d">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_f32_len512
: <a class="el" href="arm__const__structs_8h.html#a15f6e533f5cfeb014839303d8ed52e19">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#a15f6e533f5cfeb014839303d8ed52e19">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_f32_len64
: <a class="el" href="arm__const__structs_8c.html#af94d90db836f662321946154c76b5b80">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#af94d90db836f662321946154c76b5b80">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q15_len1024
: <a class="el" href="arm__const__structs_8c.html#ad343fb2e4cba826f092f9d72c4adc831">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#ad343fb2e4cba826f092f9d72c4adc831">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q15_len128
: <a class="el" href="arm__const__structs_8c.html#a736a97efd37c6386dab8db730904f69b">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a736a97efd37c6386dab8db730904f69b">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q15_len16
: <a class="el" href="arm__const__structs_8c.html#a7ed661717c58b18f3e557daa72f2b91b">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a7ed661717c58b18f3e557daa72f2b91b">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q15_len2048
: <a class="el" href="arm__const__structs_8c.html#a92c94dc79c66ec66c95f793aedb964b9">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a92c94dc79c66ec66c95f793aedb964b9">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q15_len256
: <a class="el" href="arm__const__structs_8c.html#ad80be0db1ea40c66b079404c48d2dcf4">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#ad80be0db1ea40c66b079404c48d2dcf4">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q15_len32
: <a class="el" href="arm__const__structs_8c.html#a8d5426a822a6017235b5e10119606a90">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a8d5426a822a6017235b5e10119606a90">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q15_len4096
: <a class="el" href="arm__const__structs_8h.html#ab57c118edaa3260f7f16686152845b18">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#ab57c118edaa3260f7f16686152845b18">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_q15_len512
: <a class="el" href="arm__const__structs_8c.html#a273b91ec86bb2bd8ac14e69252d487fb">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a273b91ec86bb2bd8ac14e69252d487fb">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q15_len64
: <a class="el" href="arm__const__structs_8c.html#a95c216e7dcfd59a8d40ef55ac223a749">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a95c216e7dcfd59a8d40ef55ac223a749">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q31_len1024
: <a class="el" href="arm__const__structs_8c.html#ada9813a027999f3cff066c9f7b5df51b">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#ada9813a027999f3cff066c9f7b5df51b">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q31_len128
: <a class="el" href="arm__const__structs_8h.html#a9a2fcdb54300f75ef1fafe02954e9a61">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#a9a2fcdb54300f75ef1fafe02954e9a61">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_q31_len16
: <a class="el" href="arm__const__structs_8c.html#a1336431c4d2a88d32c42308cfe2defa1">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a1336431c4d2a88d32c42308cfe2defa1">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q31_len2048
: <a class="el" href="arm__const__structs_8h.html#a420622d75b277070784083ddd44b95fb">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#a420622d75b277070784083ddd44b95fb">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_q31_len256
: <a class="el" href="arm__const__structs_8c.html#a3f2de67938bd228918e40f60f18dd6b5">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#a3f2de67938bd228918e40f60f18dd6b5">arm_const_structs.h</a>
</li>
<li>arm_cfft_sR_q31_len32
: <a class="el" href="arm__const__structs_8h.html#a4c083c013ef17920cf8f28dc6f139a39">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#a4c083c013ef17920cf8f28dc6f139a39">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_q31_len4096
: <a class="el" href="arm__const__structs_8h.html#abfc9595f40a1c7aaba85e1328d824b1c">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#abfc9595f40a1c7aaba85e1328d824b1c">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_q31_len512
: <a class="el" href="arm__const__structs_8h.html#aa337272cf78aaf6075e7e19d0a097d6f">arm_const_structs.h</a>
, <a class="el" href="arm__const__structs_8c.html#aa337272cf78aaf6075e7e19d0a097d6f">arm_const_structs.c</a>
</li>
<li>arm_cfft_sR_q31_len64
: <a class="el" href="arm__const__structs_8c.html#ad11668a5662334e0bc6a2811c9cb1047">arm_const_structs.c</a>
, <a class="el" href="arm__const__structs_8h.html#ad11668a5662334e0bc6a2811c9cb1047">arm_const_structs.h</a>
</li>
<li>coeffTable
: <a class="el" href="arm__graphic__equalizer__example__q31_8c.html#a024c59772b9603698b898721c1e8204e">arm_graphic_equalizer_example_q31.c</a>
</li>
<li>cos_factors_128
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga16248ed86161ef97538011b49f13e8b7">arm_dct4_init_f32.c</a>
</li>
<li>cos_factors_2048
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga1ba5306e0bc44730b40ab34cced45fd6">arm_dct4_init_f32.c</a>
</li>
<li>cos_factors_512
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga49fd288352ca5bb43f5cec52273b0d80">arm_dct4_init_f32.c</a>
</li>
<li>cos_factors_8192
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gac12484542bc6aaecc754c855457411de">arm_dct4_init_f32.c</a>
</li>
<li>cos_factorsQ15_128
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga1477edd21c7b08b0b59a564f6c24d6c5">arm_dct4_init_q15.c</a>
</li>
<li>cos_factorsQ15_2048
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaeee5df7c1be2374441868ecbbc6c7e5d">arm_dct4_init_q15.c</a>
</li>
<li>cos_factorsQ15_512
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gac056c3d026058eab3ba650828ff5642f">arm_dct4_init_q15.c</a>
</li>
<li>cos_factorsQ15_8192
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga988ff0563cc9df7848c9348871ac6c07">arm_dct4_init_q15.c</a>
</li>
<li>cos_factorsQ31_128
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gabb8ee2004a3520fd08388db637d43875">arm_dct4_init_q31.c</a>
</li>
<li>cos_factorsQ31_2048
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaa15fc3fb058482defda371113cd12e74">arm_dct4_init_q31.c</a>
</li>
<li>cos_factorsQ31_512
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga3559569e603cb918911074be88523d0e">arm_dct4_init_q31.c</a>
</li>
<li>cos_factorsQ31_8192
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaf687c4bbdbc700a3ad5d807d28de63e4">arm_dct4_init_q31.c</a>
</li>
<li>cosOutput
: <a class="el" href="arm__sin__cos__example__f32_8c.html#a85b1050fcef4347d69f35a9aee798f8a">arm_sin_cos_example_f32.c</a>
</li>
<li>cosSquareOutput
: <a class="el" href="arm__sin__cos__example__f32_8c.html#a2cb185794dcb587d158f346ab049cc4e">arm_sin_cos_example_f32.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
