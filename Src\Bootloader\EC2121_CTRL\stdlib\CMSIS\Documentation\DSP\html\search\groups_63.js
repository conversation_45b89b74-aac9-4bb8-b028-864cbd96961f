var searchData=
[
  ['complex_20fft_20tables',['Complex FFT Tables',['../group___c_f_f_t___c_i_f_f_t.html',1,'']]],
  ['class_20marks_20example',['Class Marks Example',['../group___class_marks.html',1,'']]],
  ['complex_20conjugate',['Complex Conjugate',['../group__cmplx__conj.html',1,'']]],
  ['complex_20dot_20product',['Complex Dot Product',['../group__cmplx__dot__prod.html',1,'']]],
  ['complex_20magnitude',['Complex Magnitude',['../group__cmplx__mag.html',1,'']]],
  ['complex_20magnitude_20squared',['Complex Magnitude Squared',['../group__cmplx__mag__squared.html',1,'']]],
  ['complex_2dby_2dcomplex_20multiplication',['Complex-by-Complex Multiplication',['../group___cmplx_by_cmplx_mult.html',1,'']]],
  ['complex_2dby_2dreal_20multiplication',['Complex-by-Real Multiplication',['../group___cmplx_by_real_mult.html',1,'']]],
  ['complex_20matrix_20multiplication',['Complex Matrix Multiplication',['../group___cmplx_matrix_mult.html',1,'']]],
  ['complex_20fft_20functions',['Complex FFT Functions',['../group___complex_f_f_t.html',1,'']]],
  ['convolution',['Convolution',['../group___conv.html',1,'']]],
  ['convolution_20example',['Convolution Example',['../group___convolution_example.html',1,'']]],
  ['correlation',['Correlation',['../group___corr.html',1,'']]],
  ['cosine',['Cosine',['../group__cos.html',1,'']]],
  ['convert_2032_2dbit_20floating_20point_20value',['Convert 32-bit floating point value',['../group__float__to__x.html',1,'']]],
  ['complex_20math_20functions',['Complex Math Functions',['../group__group_cmplx_math.html',1,'']]],
  ['controller_20functions',['Controller Functions',['../group__group_controller.html',1,'']]],
  ['convert_2016_2dbit_20integer_20value',['Convert 16-bit Integer value',['../group__q15__to__x.html',1,'']]],
  ['convert_2032_2dbit_20integer_20value',['Convert 32-bit Integer value',['../group__q31__to__x.html',1,'']]],
  ['convert_208_2dbit_20integer_20value',['Convert 8-bit Integer value',['../group__q7__to__x.html',1,'']]]
];
