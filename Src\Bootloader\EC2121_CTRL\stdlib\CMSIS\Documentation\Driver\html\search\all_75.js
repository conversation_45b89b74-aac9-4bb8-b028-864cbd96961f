var searchData=
[
  ['uhs_5fddr50',['uhs_ddr50',['../group__mci__interface__gr.html#a1ee73c19020d5f1bedf7c013d0e5f730',1,'ARM_MCI_CAPABILITIES']]],
  ['uhs_5fdriver_5ftype_5fa',['uhs_driver_type_a',['../group__mci__interface__gr.html#afe5de4fdc6657aa19fa87577a8d460e5',1,'ARM_MCI_CAPABILITIES']]],
  ['uhs_5fdriver_5ftype_5fc',['uhs_driver_type_c',['../group__mci__interface__gr.html#a3c3df9641e7216dd20d3bc395dc4948f',1,'ARM_MCI_CAPABILITIES']]],
  ['uhs_5fdriver_5ftype_5fd',['uhs_driver_type_d',['../group__mci__interface__gr.html#a639bebbcb9a3a743f4f232fec82e2bfc',1,'ARM_MCI_CAPABILITIES']]],
  ['uhs_5fsdr104',['uhs_sdr104',['../group__mci__interface__gr.html#ae07ceef1800252495a79f225142740e7',1,'ARM_MCI_CAPABILITIES']]],
  ['uhs_5fsdr50',['uhs_sdr50',['../group__mci__interface__gr.html#a5c3dcb2f8aa6f65408d9a6741abb7b3e',1,'ARM_MCI_CAPABILITIES']]],
  ['uhs_5fsignaling',['uhs_signaling',['../group__mci__interface__gr.html#a084188480d589cdc8d3e164b9f41bea9',1,'ARM_MCI_CAPABILITIES']]],
  ['uhs_5ftuning',['uhs_tuning',['../group__mci__interface__gr.html#a617bf7fb73b49a20398b90098ecc3ec0',1,'ARM_MCI_CAPABILITIES']]],
  ['uninitialize',['Uninitialize',['../group__eth__mac__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_ETH_MAC::Uninitialize()'],['../group__eth__phy__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_ETH_PHY::Uninitialize()'],['../group__i2c__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_I2C::Uninitialize()'],['../group__mci__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_MCI::Uninitialize()'],['../group__nand__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_NAND::Uninitialize()'],['../group__flash__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_FLASH::Uninitialize()'],['../group__spi__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_SPI::Uninitialize()'],['../group__usart__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_USART::Uninitialize()'],['../group__usbd__interface__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_USBD::Uninitialize()'],['../group__usbh__host__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_USBH::Uninitialize()'],['../group__usbh__hci__gr.html#adcf20681a1402869ecb5c6447fada17b',1,'ARM_DRIVER_USBH_HCI::Uninitialize()']]],
  ['usart_20clock_20phase',['USART Clock Phase',['../group__usart__clock__phase.html',1,'']]],
  ['usart_20clock_20polarity',['USART Clock Polarity',['../group__usart__clock__polarity.html',1,'']]],
  ['usart_20control_20codes',['USART Control Codes',['../group___u_s_a_r_t__control.html',1,'']]],
  ['usart_20data_20bits',['USART Data Bits',['../group__usart__data__bits.html',1,'']]],
  ['usart_20events',['USART Events',['../group___u_s_a_r_t__events.html',1,'']]],
  ['usart_20flow_20control',['USART Flow Control',['../group__usart__flow__control.html',1,'']]],
  ['usart_20interface',['USART Interface',['../group__usart__interface__gr.html',1,'']]],
  ['usart_20miscellaneous_20control',['USART Miscellaneous Control',['../group__usart__misc__control.html',1,'']]],
  ['usart_20mode_20control',['USART Mode Control',['../group__usart__mode__control.html',1,'']]],
  ['usart_20parity_20bit',['USART Parity Bit',['../group__usart__parity__bit.html',1,'']]],
  ['usart_20stop_20bits',['USART Stop Bits',['../group__usart__stop__bits.html',1,'']]],
  ['usb_20endpoint_20type',['USB Endpoint Type',['../group___u_s_b__endpoint__type.html',1,'']]],
  ['usb_20interface',['USB Interface',['../group__usb__interface__gr.html',1,'']]],
  ['usb_20speed',['USB Speed',['../group___u_s_b__speed.html',1,'']]],
  ['usbd_20device_20events',['USBD Device Events',['../group___u_s_b_d__dev__events.html',1,'']]],
  ['usbd_20endpoint_20events',['USBD Endpoint Events',['../group___u_s_b_d__ep__events.html',1,'']]],
  ['usb_20device_20interface',['USB Device Interface',['../group__usbd__interface__gr.html',1,'']]],
  ['usb_20ohci_2fehci',['USB OHCI/EHCI',['../group__usbh__hci__gr.html',1,'']]],
  ['usb_20host',['USB Host',['../group__usbh__host__gr.html',1,'']]],
  ['usb_20host_20interface',['USB Host Interface',['../group__usbh__interface__gr.html',1,'']]],
  ['usbh_20packet_20information',['USBH Packet Information',['../group___u_s_b_h__packets.html',1,'']]],
  ['usbh_20pipe_20events',['USBH Pipe Events',['../group___u_s_b_h__pipe__events.html',1,'']]],
  ['usbh_20port_20events',['USBH Port Events',['../group___u_s_b_h__port__events.html',1,'']]]
];
