#include "DebugConsole.h"
#include "CTimer.h"
#include "CUart.h"
#include "CRccClock.h"

#define MAX_PRINTF_NUM_TONGBU      128

CUart* pPrintfUart = NULL;

static uint32_t CInitFlag = 0;
void* pRxBuffer = NULL;

uint32_t bufferFullCuont = 0;

char rt_log_buf[MAX_PRINTF_NUM_TONGBU] = {0};

void SendData(uint8_t * pData, uint32_t length)
{
    for(uint32_t i = 0; i < length; i++)
    {
        while(!USART_GetFlagStatus(USART1, USART_FLAG_TXE));
        USART_SendData(USART1, pData[i]);
    }
}

//打印到缓存，时间很短不用考虑打印时延
void kprintf(const char *fmt, ...)
{
    va_list args;
    if(!CInitFlag)
    {
        CInitFlag = 1;
        DebugUartConfig();
    }
    va_start(args, fmt);
    vsnprintf(rt_log_buf, MAX_PRINTF_NUM_TONGBU - 1, fmt, args);
    SendData((uint8_t*)rt_log_buf,strlen(rt_log_buf));
    va_end(args);
}

void DebugUartConfig()
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;

    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE); //使能GPIOA时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE); //使能USART1时钟
    //串口1对应引脚复用映射
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource9, GPIO_AF_USART1); //GPIOA9复用为USART1
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource10, GPIO_AF_USART1); //GPIOA10复用为USART1
    //USART1端口配置
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9 | GPIO_Pin_10; //GPIOA9与GPIOA10
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;//复用功能
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;   //速度50MHz
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; //推挽复用输出
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP; //上拉
    GPIO_Init(GPIOA, &GPIO_InitStructure); //初始化PA9，PA10
    //USART1 初始化设置
    USART_InitStructure.USART_BaudRate = 921600;//波特率设置
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;//字长为8位数据格式
    USART_InitStructure.USART_StopBits = USART_StopBits_1;//一个停止位
    USART_InitStructure.USART_Parity = USART_Parity_No;//无奇偶校验位
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;//无硬件数据流控制
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; //收发模式
    USART_Init(USART1, &USART_InitStructure); //初始化串口1
    USART_Cmd(USART1, ENABLE);  //使能串口1
    USART_ClearFlag(USART1, USART_FLAG_TC);
}


