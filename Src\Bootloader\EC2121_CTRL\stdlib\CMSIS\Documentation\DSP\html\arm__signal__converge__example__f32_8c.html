<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_signal_converge_example_f32.c File Reference</title>
<title>CMSIS-DSP: arm_signal_converge_example_f32.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__signal__converge__example__f32_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">arm_signal_converge_example_f32.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:abc004a7fade488e72310fd96c0a101dc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a></td></tr>
<tr class="separator:abc004a7fade488e72310fd96c0a101dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1d8ddb4f9a957eef3ad13d44de4d804"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a></td></tr>
<tr class="separator:ac1d8ddb4f9a957eef3ad13d44de4d804"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afcf795f5a96fd55561abe69f56224630"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a></td></tr>
<tr class="separator:afcf795f5a96fd55561abe69f56224630"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d3c6a4484dcaac72fbfe5100c39b9b6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a6d3c6a4484dcaac72fbfe5100c39b9b6">DELTA_ERROR</a></td></tr>
<tr class="separator:a6d3c6a4484dcaac72fbfe5100c39b9b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9156349d99957ded15d8aa3aa11723de"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a9156349d99957ded15d8aa3aa11723de">DELTA_COEFF</a></td></tr>
<tr class="separator:a9156349d99957ded15d8aa3aa11723de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09bc9e6a44f0291cfcf578f2efcddfab"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a09bc9e6a44f0291cfcf578f2efcddfab">MU</a></td></tr>
<tr class="separator:a09bc9e6a44f0291cfcf578f2efcddfab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b6b859e1e3f6021a360390be287ca2c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a4b6b859e1e3f6021a360390be287ca2c">NUMFRAMES</a></td></tr>
<tr class="separator:a4b6b859e1e3f6021a360390be287ca2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ac786d43cbc17bb09738447034ff8e22e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#ac786d43cbc17bb09738447034ff8e22e">test_signal_converge_example</a> (void)</td></tr>
<tr class="separator:ac786d43cbc17bb09738447034ff8e22e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f521e839d4fad24a4f12a18dfeae5d4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a8f521e839d4fad24a4f12a18dfeae5d4">test_signal_converge</a> (<a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *<a class="el" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a>, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="separator:a8f521e839d4fad24a4f12a18dfeae5d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd2975c4763ec935771e6f63bfe7758b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#afd2975c4763ec935771e6f63bfe7758b">getinput</a> (<a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *input, uint32_t fr_cnt, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="separator:afd2975c4763ec935771e6f63bfe7758b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52d2cba30e6946c95578be946ac12a65"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main</a> (void)</td></tr>
<tr class="separator:a52d2cba30e6946c95578be946ac12a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a358ec4e79689e6d3787b89fe78bdb772"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a358ec4e79689e6d3787b89fe78bdb772">firStateF32</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>+<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td></tr>
<tr class="separator:a358ec4e79689e6d3787b89fe78bdb772"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a652d3507a776117b4860b3e18f2d2d64"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structarm__fir__instance__f32.html">arm_fir_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a652d3507a776117b4860b3e18f2d2d64">LPF_instance</a></td></tr>
<tr class="separator:a652d3507a776117b4860b3e18f2d2d64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a706980f6f654d199c61e08e7814bd0a1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a706980f6f654d199c61e08e7814bd0a1">lmsStateF32</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>+<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td></tr>
<tr class="separator:a706980f6f654d199c61e08e7814bd0a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a276e8a27484cf9389dabf047e76992ed"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a276e8a27484cf9389dabf047e76992ed">errOutput</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td></tr>
<tr class="separator:a276e8a27484cf9389dabf047e76992ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519f9b4db839245f3bf2075ff4c17605"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structarm__lms__norm__instance__f32.html">arm_lms_norm_instance_f32</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a519f9b4db839245f3bf2075ff4c17605">lmsNorm_instance</a></td></tr>
<tr class="separator:a519f9b4db839245f3bf2075ff4c17605"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac98609c83ad8ed2b05c4fd82d2ba59b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#aac98609c83ad8ed2b05c4fd82d2ba59b">testInput_f32</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td></tr>
<tr class="separator:aac98609c83ad8ed2b05c4fd82d2ba59b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad7c60c30c5af397bb75e603f250f9d3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a> [32]</td></tr>
<tr class="separator:aad7c60c30c5af397bb75e603f250f9d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aede8780f021b7f5c33df0c5ee2183ee6"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#aede8780f021b7f5c33df0c5ee2183ee6">FIRCoeff_f32</a> [32]</td></tr>
<tr class="separator:aede8780f021b7f5c33df0c5ee2183ee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16e759789fbc05f878863f009066c8ea"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td></tr>
<tr class="separator:a16e759789fbc05f878863f009066c8ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e370163c81ae2b72cc655a6b79e4c6a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td></tr>
<tr class="separator:a4e370163c81ae2b72cc655a6b79e4c6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e2cceadf6ec7f0aa0f698a680fa3a4b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td></tr>
<tr class="separator:a7e2cceadf6ec7f0aa0f698a680fa3a4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6bcc00ea126543ab33d6174549eacda"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td></tr>
<tr class="separator:ae6bcc00ea126543ab33d6174549eacda"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="afcf795f5a96fd55561abe69f56224630"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define BLOCKSIZE</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a9156349d99957ded15d8aa3aa11723de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define DELTA_COEFF</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a37">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a6d3c6a4484dcaac72fbfe5100c39b9b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define DELTA_ERROR</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a34">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a09bc9e6a44f0291cfcf578f2efcddfab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MU</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a24">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a4b6b859e1e3f6021a360390be287ca2c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NUMFRAMES</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a26">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ac1d8ddb4f9a957eef3ad13d44de4d804"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NUMTAPS</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a1">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="abc004a7fade488e72310fd96c0a101dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define TEST_LENGTH_SAMPLES</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="afd2975c4763ec935771e6f63bfe7758b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void getinput </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>input</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fr_cnt</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a14">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
</div>
</div>
<a class="anchor" id="a52d2cba30e6946c95578be946ac12a65"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t main </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>References <a class="el" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02">arm_abs_f32()</a>, <a class="el" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3">arm_copy_f32()</a>, <a class="el" href="group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a">arm_fir_f32()</a>, <a class="el" href="group___f_i_r.html#ga98d13def6427e29522829f945d0967db">arm_fir_init_f32()</a>, <a class="el" href="group___l_m_s___n_o_r_m.html#ga2418c929087c6eba719758eaae3f3300">arm_lms_norm_f32()</a>, <a class="el" href="group___l_m_s___n_o_r_m.html#gac7ccbaea863882056eee815456464670">arm_lms_norm_init_f32()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>, <a class="el" href="group___min.html#gaf62b1673740fc516ea64daf777b7d74a">arm_min_f32()</a>, <a class="el" href="group__scale.html#ga3487af88b112f682ee90589cd419e123">arm_scale_f32()</a>, <a class="el" href="group___basic_sub.html#ga7f975a472de286331134227c08aad826">arm_sub_f32()</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#a9156349d99957ded15d8aa3aa11723de">DELTA_COEFF</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#a6d3c6a4484dcaac72fbfe5100c39b9b6">DELTA_ERROR</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#ae6bcc00ea126543ab33d6174549eacda">err_signal</a>, <a class="el" href="arm__signal__converge__data_8c.html#aede8780f021b7f5c33df0c5ee2183ee6">FIRCoeff_f32</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#a358ec4e79689e6d3787b89fe78bdb772">firStateF32</a>, <a class="el" href="arm__signal__converge__data_8c.html#aad7c60c30c5af397bb75e603f250f9d3">lmsNormCoeff_f32</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#a706980f6f654d199c61e08e7814bd0a1">lmsStateF32</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#a09bc9e6a44f0291cfcf578f2efcddfab">MU</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#a4b6b859e1e3f6021a360390be287ca2c">NUMFRAMES</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a>, <a class="el" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>, and <a class="el" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>.</p>

</div>
</div>
<a class="anchor" id="a8f521e839d4fad24a4f12a18dfeae5d4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> test_signal_converge </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>err_signal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a11">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
</div>
</div>
<a class="anchor" id="ac786d43cbc17bb09738447034ff8e22e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> test_signal_converge_example </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a10">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="ae6bcc00ea126543ab33d6174549eacda"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> err_signal[<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a12">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a276e8a27484cf9389dabf047e76992ed"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> errOutput[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a6">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
</div>
</div>
<a class="anchor" id="aede8780f021b7f5c33df0c5ee2183ee6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> FIRCoeff_f32[32]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a358ec4e79689e6d3787b89fe78bdb772"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> firStateF32[<a class="el" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>+<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fir_example_f32_8c-example.html#a4">arm_fir_example_f32.c</a>, and <a class="el" href="arm_signal_converge_example_f32_8c-example.html#a0">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a519f9b4db839245f3bf2075ff4c17605"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structarm__lms__norm__instance__f32.html">arm_lms_norm_instance_f32</a> lmsNorm_instance</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a9">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
</div>
</div>
<a class="anchor" id="aad7c60c30c5af397bb75e603f250f9d3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> lmsNormCoeff_f32[32]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a706980f6f654d199c61e08e7814bd0a1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> lmsStateF32[<a class="el" href="arm__signal__converge__example__f32_8c.html#ac1d8ddb4f9a957eef3ad13d44de4d804">NUMTAPS</a>+<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a5">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a652d3507a776117b4860b3e18f2d2d64"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structarm__fir__instance__f32.html">arm_fir_instance_f32</a> LPF_instance</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a4">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
</div>
</div>
<a class="anchor" id="aac98609c83ad8ed2b05c4fd82d2ba59b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testInput_f32[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="arm__graphic__equalizer__example__q31_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a16e759789fbc05f878863f009066c8ea"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> wire1[<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a18">arm_signal_converge_example_f32.c</a>, and <a class="el" href="arm_variance_example_f32_8c-example.html#a0">arm_variance_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a4e370163c81ae2b72cc655a6b79e4c6a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> wire2[<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a19">arm_signal_converge_example_f32.c</a>, and <a class="el" href="arm_variance_example_f32_8c-example.html#a2">arm_variance_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a7e2cceadf6ec7f0aa0f698a680fa3a4b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> wire3[<a class="el" href="arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a20">arm_signal_converge_example_f32.c</a>, and <a class="el" href="arm_variance_example_f32_8c-example.html#a3">arm_variance_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_50f4d4f91ce5cd72cb6928b47e85a7f8.html">Examples</a></li><li class="navelem"><a class="el" href="dir_e850fff378e36258e2a085808e9d898c.html">arm_signal_converge_example</a></li><li class="navelem"><a class="el" href="dir_9bbc0b803a378696e320e7975798d136.html">ARM</a></li><li class="navelem"><a class="el" href="arm__signal__converge__example__f32_8c.html">arm_signal_converge_example_f32.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
