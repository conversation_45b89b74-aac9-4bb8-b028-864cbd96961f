#ifndef __CADC_H__
#define __CADC_H__
#include "stdarg.h"

#include"stm32f4xx_gpio.h"
#include"stm32f4xx_adc.h"

#include "stdio.h"
#include "Globel.h"

#define ADC1_CHANEL_COUNT  5//7	//ADC1采样通道数
#define ADC1_CALC_COUNT  10		//ADC1采样计算平均数

#define ADC3_CHANEL_COUNT  11//12	//ADC采样通道数
#define ADC3_CALC_COUNT  10		//ADC采样计算平均数


class CAdc
{
public:
    enum ADC_USER //上层应用通过这个参数获取ad电压，AD读取接口很频繁，这里需要直接映射，不用查找的方式
    {
        
        ADC_TEMP_L1, //
        ADC_TEMP_L2,
        ADC_TEMP_L3,
        ADC_TEMP_N,
        
        ADC_L1_VIN,
        ADC_L2_VIN,
        ADC_L3_VIN, //
        
        ADC_CP1,
        ADC_CP2, 
        
        ADC_TEMP_RLY1,
        ADC_TEMP_RLY2,
        
        ADC_TEMP_PWR_0,
        ADC_TEMP_PWR_1,
        
        ADC_PE_DET,
        ADC_VER_xx,
        ADC_PP_STATU,
        
        

        ADC_NOT_USE
    };

    typedef struct
    {
        uint8_t       ADCx_Channel;
        uint8_t       ADCx_Rank;
        uint8_t       ADCx_SampleTime;//
        GPIO_TypeDef* ADCx_PORT;
        uint16_t      ADCx_PIN;
        uint32_t      ADCx_RccClock;//管脚时钟
        uint8_t       ADCx_RCC_Periph;//时钟类型
    } ADC_CHANNEL_MAP;
    typedef struct
    {
        ADC_TypeDef *       pADCx;//地址
        uint32_t            ADC_RccClock;//时钟
        uint8_t             ADC_RCC_Periph;//时钟类型
        uint8_t             ADC_ChannelNum;//使用的通道数量
        ADC_CHANNEL_MAP*    pADC_CHANNEL_PARA;
        uint32_t            DMA_Channel;
        uint32_t            DMA_rccClock;//时钟
        uint8_t             DMA_RCC_Periph;//时钟类型
        IRQn_Type           DMA_IRQn;//中断号
        DMA_Stream_TypeDef* DMA_Stream;
        uint32_t            DMA_MemoryBaseAddr;
        uint32_t            DMA_BufferSize;
    } ADC_MANAGER;
    


public:
    CAdc();

    ~CAdc();


    //uint16_t GetV(ADC_USER adUser);


private:
    //void UartHWInit();


public:
    //uint32_t BaseID;
    

private:
    static uint8_t m_objCount;
    static ADC_MANAGER     m_AdcManager[];
    static ADC_CHANNEL_MAP m_ADC1_ChannelMap[];
    static ADC_CHANNEL_MAP m_ADC3_ChannelMap[];
    static volatile uint16_t m_ADC3_Value[ADC3_CALC_COUNT][ADC3_CHANEL_COUNT];
    static volatile uint16_t m_ADC1_Value[ADC1_CALC_COUNT][ADC1_CHANEL_COUNT];

};


#endif

