<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>PID Motor Control</title>
<title>CMSIS-DSP: PID Motor Control</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___p_i_d.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">PID Motor Control</div>  </div>
<div class="ingroups"><a class="el" href="group__group_controller.html">Controller Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gae31536b19b82b93ed184fb1ab73cfcb3"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#gae31536b19b82b93ed184fb1ab73cfcb3">arm_pid_init_f32</a> (<a class="el" href="structarm__pid__instance__f32.html">arm_pid_instance_f32</a> *S, int32_t resetStateFlag)</td></tr>
<tr class="memdesc:gae31536b19b82b93ed184fb1ab73cfcb3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point PID Control.  <a href="#gae31536b19b82b93ed184fb1ab73cfcb3"></a><br/></td></tr>
<tr class="separator:gae31536b19b82b93ed184fb1ab73cfcb3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2cb1e3d3ebb167348fdabec74653d5c3"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#ga2cb1e3d3ebb167348fdabec74653d5c3">arm_pid_init_q15</a> (<a class="el" href="structarm__pid__instance__q15.html">arm_pid_instance_q15</a> *S, int32_t resetStateFlag)</td></tr>
<tr class="memdesc:ga2cb1e3d3ebb167348fdabec74653d5c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 PID Control.  <a href="#ga2cb1e3d3ebb167348fdabec74653d5c3"></a><br/></td></tr>
<tr class="separator:ga2cb1e3d3ebb167348fdabec74653d5c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad9d88485234fa9460b1ce9e64989ac86"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#gad9d88485234fa9460b1ce9e64989ac86">arm_pid_init_q31</a> (<a class="el" href="structarm__pid__instance__q31.html">arm_pid_instance_q31</a> *S, int32_t resetStateFlag)</td></tr>
<tr class="memdesc:gad9d88485234fa9460b1ce9e64989ac86"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 PID Control.  <a href="#gad9d88485234fa9460b1ce9e64989ac86"></a><br/></td></tr>
<tr class="separator:gad9d88485234fa9460b1ce9e64989ac86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ec860bcb6f8ca31205bf0f1b51ab723"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#ga9ec860bcb6f8ca31205bf0f1b51ab723">arm_pid_reset_f32</a> (<a class="el" href="structarm__pid__instance__f32.html">arm_pid_instance_f32</a> *S)</td></tr>
<tr class="memdesc:ga9ec860bcb6f8ca31205bf0f1b51ab723"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reset function for the floating-point PID Control.  <a href="#ga9ec860bcb6f8ca31205bf0f1b51ab723"></a><br/></td></tr>
<tr class="separator:ga9ec860bcb6f8ca31205bf0f1b51ab723"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga408566dacb4fa6e0458b2c75672e525f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#ga408566dacb4fa6e0458b2c75672e525f">arm_pid_reset_q15</a> (<a class="el" href="structarm__pid__instance__q15.html">arm_pid_instance_q15</a> *S)</td></tr>
<tr class="memdesc:ga408566dacb4fa6e0458b2c75672e525f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reset function for the Q15 PID Control.  <a href="#ga408566dacb4fa6e0458b2c75672e525f"></a><br/></td></tr>
<tr class="separator:ga408566dacb4fa6e0458b2c75672e525f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeecbacd3fb37c608ec25474d3a0dffa9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#gaeecbacd3fb37c608ec25474d3a0dffa9">arm_pid_reset_q31</a> (<a class="el" href="structarm__pid__instance__q31.html">arm_pid_instance_q31</a> *S)</td></tr>
<tr class="memdesc:gaeecbacd3fb37c608ec25474d3a0dffa9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reset function for the Q31 PID Control.  <a href="#gaeecbacd3fb37c608ec25474d3a0dffa9"></a><br/></td></tr>
<tr class="separator:gaeecbacd3fb37c608ec25474d3a0dffa9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac5c79ed46abf2d72b8cf41fa6c708bda"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#gac5c79ed46abf2d72b8cf41fa6c708bda">arm_pid_f32</a> (<a class="el" href="structarm__pid__instance__f32.html">arm_pid_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> in)</td></tr>
<tr class="memdesc:gac5c79ed46abf2d72b8cf41fa6c708bda"><td class="mdescLeft">&#160;</td><td class="mdescRight">Process function for the floating-point PID Control.  <a href="#gac5c79ed46abf2d72b8cf41fa6c708bda"></a><br/></td></tr>
<tr class="separator:gac5c79ed46abf2d72b8cf41fa6c708bda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5f6f941e7ae981728dd3a662f8f4ecd7"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#ga5f6f941e7ae981728dd3a662f8f4ecd7">arm_pid_q31</a> (<a class="el" href="structarm__pid__instance__q31.html">arm_pid_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> in)</td></tr>
<tr class="memdesc:ga5f6f941e7ae981728dd3a662f8f4ecd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Process function for the Q31 PID Control.  <a href="#ga5f6f941e7ae981728dd3a662f8f4ecd7"></a><br/></td></tr>
<tr class="separator:ga5f6f941e7ae981728dd3a662f8f4ecd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga084f646bbb20d55f225c3efafcf7fc1f"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___p_i_d.html#ga084f646bbb20d55f225c3efafcf7fc1f">arm_pid_q15</a> (<a class="el" href="structarm__pid__instance__q15.html">arm_pid_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> in)</td></tr>
<tr class="memdesc:ga084f646bbb20d55f225c3efafcf7fc1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Process function for the Q15 PID Control.  <a href="#ga084f646bbb20d55f225c3efafcf7fc1f"></a><br/></td></tr>
<tr class="separator:ga084f646bbb20d55f225c3efafcf7fc1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>A Proportional Integral Derivative (PID) controller is a generic feedback control loop mechanism widely used in industrial control systems. A PID controller is the most commonly used type of feedback controller.</p>
<p>This set of functions implements (PID) controllers for Q15, Q31, and floating-point data types. The functions operate on a single sample of data and each call to the function returns a single processed value. <code>S</code> points to an instance of the PID control data structure. <code>in</code> is the input sample value. The functions return the output value.</p>
<dl class="section user"><dt>Algorithm:</dt><dd><pre>
   y[n] = y[n-1] + A0 * x[n] + A1 * x[n-1] + A2 * x[n-2]
   A0 = Kp + Ki + Kd
   A1 = (-Kp ) - (2 * Kd )
   A2 = Kd  </pre></dd></dl>
<dl class="section user"><dt></dt><dd>where <code>Kp</code> is proportional constant, <code>Ki</code> is Integral constant and <code>Kd</code> is Derivative constant</dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="PID.gif" alt="PID.gif"/>
<div class="caption">
Proportional Integral Derivative Controller</div></div>
 </dd></dl>
<dl class="section user"><dt></dt><dd>The PID controller calculates an "error" value as the difference between the measured output and the reference input. The controller attempts to minimize the error by adjusting the process control inputs. The proportional value determines the reaction to the current error, the integral value determines the reaction based on the sum of recent errors, and the derivative value determines the reaction based on the rate at which the error has been changing.</dd></dl>
<dl class="section user"><dt>Instance Structure</dt><dd>The Gains A0, A1, A2 and state variables for a PID controller are stored together in an instance data structure. A separate instance structure must be defined for each PID Controller. There are separate instance structure declarations for each of the 3 supported data types.</dd></dl>
<dl class="section user"><dt>Reset Functions</dt><dd>There is also an associated reset function for each data type which clears the state array.</dd></dl>
<dl class="section user"><dt>Initialization Functions</dt><dd>There is also an associated initialization function for each data type. The initialization function performs the following operations:<ul>
<li>Initializes the Gains A0, A1, A2 from Kp,Ki, Kd gains.</li>
<li>Zeros out the values in the state buffer.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Instance structure cannot be placed into a const data section and it is recommended to use the initialization function.</dd></dl>
<dl class="section user"><dt>Fixed-Point Behavior</dt><dd>Care must be taken when using the fixed-point versions of the PID Controller functions. In particular, the overflow and saturation behavior of the accumulator used in each function must be considered. Refer to the function specific documentation below for usage guidelines. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gac5c79ed46abf2d72b8cf41fa6c708bda"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> arm_pid_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__f32.html">arm_pid_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td>
          <td class="paramname"><em>in</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>is an instance of the floating-point PID Control structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">in</td><td>input sample to process </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>out processed output sample. </dd></dl>

<p>References <a class="el" href="structarm__pid__instance__f32.html#ad7b0bed64915d0a25a3409fa2dc45556">arm_pid_instance_f32::A0</a>, <a class="el" href="structarm__pid__instance__f32.html#a7def89571c50f7137a213326a396e560">arm_pid_instance_f32::A1</a>, <a class="el" href="structarm__pid__instance__f32.html#a155acf642ba2f521869f19d694cd7fa0">arm_pid_instance_f32::A2</a>, and <a class="el" href="structarm__pid__instance__f32.html#afd394e1e52fb1d526aa472c83b8f2464">arm_pid_instance_f32::state</a>.</p>

</div>
</div>
<a class="anchor" id="gae31536b19b82b93ed184fb1ab73cfcb3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_pid_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__f32.html">arm_pid_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>resetStateFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the PID structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">resetStateFlag</td><td>flag to reset the state. 0 = no change in state &amp; 1 = reset the state. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The <code>resetStateFlag</code> specifies whether to set state to zero or not. <br/>
 The function computes the structure fields: <code>A0</code>, <code>A1</code> <code>A2</code> using the proportional gain( <code>Kp</code>), integral gain( <code>Ki</code>) and derivative gain( <code>Kd</code>) also sets the state variables to all zeros. </dd></dl>

<p>References <a class="el" href="structarm__pid__instance__f32.html#ad7b0bed64915d0a25a3409fa2dc45556">arm_pid_instance_f32::A0</a>, <a class="el" href="structarm__pid__instance__f32.html#a7def89571c50f7137a213326a396e560">arm_pid_instance_f32::A1</a>, <a class="el" href="structarm__pid__instance__f32.html#a155acf642ba2f521869f19d694cd7fa0">arm_pid_instance_f32::A2</a>, <a class="el" href="structarm__pid__instance__f32.html#ad5b68fbf84d16188ae4747ff91f6f088">arm_pid_instance_f32::Kd</a>, <a class="el" href="structarm__pid__instance__f32.html#ac0feffde05fe391eeab3bf78e953830a">arm_pid_instance_f32::Ki</a>, <a class="el" href="structarm__pid__instance__f32.html#aa9b9aa9e413c6cec376a9dddc9f01ebe">arm_pid_instance_f32::Kp</a>, and <a class="el" href="structarm__pid__instance__f32.html#afd394e1e52fb1d526aa472c83b8f2464">arm_pid_instance_f32::state</a>.</p>

</div>
</div>
<a class="anchor" id="ga2cb1e3d3ebb167348fdabec74653d5c3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_pid_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__q15.html">arm_pid_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>resetStateFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q15 PID structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">resetStateFlag</td><td>flag to reset the state. 0 = no change in state 1 = reset the state. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The <code>resetStateFlag</code> specifies whether to set state to zero or not. <br/>
 The function computes the structure fields: <code>A0</code>, <code>A1</code> <code>A2</code> using the proportional gain( <code>Kp</code>), integral gain( <code>Ki</code>) and derivative gain( <code>Kd</code>) also sets the state variables to all zeros. </dd></dl>

<p>References <a class="el" href="structarm__pid__instance__q15.html#ad77f3a2823c7f96de42c92a3fbf3246b">arm_pid_instance_q15::A0</a>, <a class="el" href="structarm__pid__instance__q15.html#a1b8412c517071962a9acfdc6778906ec">arm_pid_instance_q15::A1</a>, <a class="el" href="structarm__pid__instance__q15.html#af5d4b53091f19eff7536636b7cc43111">arm_pid_instance_q15::Kd</a>, <a class="el" href="structarm__pid__instance__q15.html#a0dcc19d5c8f7bc401acea9e8318cd777">arm_pid_instance_q15::Ki</a>, <a class="el" href="structarm__pid__instance__q15.html#ad228aae24a1b6d855c93a8b9bbc1c4f1">arm_pid_instance_q15::Kp</a>, and <a class="el" href="structarm__pid__instance__q15.html#a4a3f0a878b5b6b055e3478a2f244cd30">arm_pid_instance_q15::state</a>.</p>

</div>
</div>
<a class="anchor" id="gad9d88485234fa9460b1ce9e64989ac86"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_pid_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__q31.html">arm_pid_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>resetStateFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q31 PID structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">resetStateFlag</td><td>flag to reset the state. 0 = no change in state 1 = reset the state. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The <code>resetStateFlag</code> specifies whether to set state to zero or not. <br/>
 The function computes the structure fields: <code>A0</code>, <code>A1</code> <code>A2</code> using the proportional gain( <code>Kp</code>), integral gain( <code>Ki</code>) and derivative gain( <code>Kd</code>) also sets the state variables to all zeros. </dd></dl>

<p>References <a class="el" href="structarm__pid__instance__q31.html#aa5332635ce9c7078cdb4c1ecf442eadd">arm_pid_instance_q31::A0</a>, <a class="el" href="structarm__pid__instance__q31.html#a2f7492bd6fb92fae5e2de7fbbec39b0e">arm_pid_instance_q31::A1</a>, <a class="el" href="structarm__pid__instance__q31.html#a3e34537c53af4f9ad7bfffa4dff27c82">arm_pid_instance_q31::A2</a>, <a class="el" href="arm__math_8h.html#ad7373e53d3c2e1adfeafc8c2e9720b5c">clip_q63_to_q31()</a>, <a class="el" href="structarm__pid__instance__q31.html#aab4ff371d14441df501f1169f71cbd17">arm_pid_instance_q31::Kd</a>, <a class="el" href="structarm__pid__instance__q31.html#aa861d69fd398f29aa0b4b455a823ed72">arm_pid_instance_q31::Ki</a>, <a class="el" href="structarm__pid__instance__q31.html#ac2410bf7f856d58dc1d773d4983cac8e">arm_pid_instance_q31::Kp</a>, and <a class="el" href="structarm__pid__instance__q31.html#a228e4a64da6014844a0a671a1fa391d4">arm_pid_instance_q31::state</a>.</p>

</div>
</div>
<a class="anchor" id="ga084f646bbb20d55f225c3efafcf7fc1f"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> arm_pid_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__q15.html">arm_pid_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td>
          <td class="paramname"><em>in</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q15 PID Control structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">in</td><td>input sample to process </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>out processed output sample.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 64-bit internal accumulator. Both Gains and state variables are represented in 1.15 format and multiplications yield a 2.30 result. The 2.30 intermediate results are accumulated in a 64-bit accumulator in 34.30 format. There is no risk of internal overflow with this approach and the full precision of intermediate multiplications is preserved. After all additions have been performed, the accumulator is truncated to 34.15 format by discarding low 15 bits. Lastly, the accumulator is saturated to yield a result in 1.15 format. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a1185d670d798aaf52eec13f0403f3407">__SIMD32_CONST</a>, <a class="el" href="structarm__pid__instance__q15.html#ad77f3a2823c7f96de42c92a3fbf3246b">arm_pid_instance_q15::A0</a>, <a class="el" href="structarm__pid__instance__q15.html#a1b8412c517071962a9acfdc6778906ec">arm_pid_instance_q15::A1</a>, and <a class="el" href="structarm__pid__instance__q15.html#a4a3f0a878b5b6b055e3478a2f244cd30">arm_pid_instance_q15::state</a>.</p>

</div>
</div>
<a class="anchor" id="ga5f6f941e7ae981728dd3a662f8f4ecd7"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> arm_pid_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__q31.html">arm_pid_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>in</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q31 PID Control structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">in</td><td>input sample to process </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>out processed output sample.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using an internal 64-bit accumulator. The accumulator has a 2.62 format and maintains full precision of the intermediate multiplication results but provides only a single guard bit. Thus, if the accumulator result overflows it wraps around rather than clip. In order to avoid overflows completely the input signal must be scaled down by 2 bits as there are four additions. After all multiply-accumulates are performed, the 2.62 accumulator is truncated to 1.32 format and then saturated to 1.31 format. </dd></dl>

<p>References <a class="el" href="structarm__pid__instance__q31.html#aa5332635ce9c7078cdb4c1ecf442eadd">arm_pid_instance_q31::A0</a>, <a class="el" href="structarm__pid__instance__q31.html#a2f7492bd6fb92fae5e2de7fbbec39b0e">arm_pid_instance_q31::A1</a>, <a class="el" href="structarm__pid__instance__q31.html#a3e34537c53af4f9ad7bfffa4dff27c82">arm_pid_instance_q31::A2</a>, and <a class="el" href="structarm__pid__instance__q31.html#a228e4a64da6014844a0a671a1fa391d4">arm_pid_instance_q31::state</a>.</p>

</div>
</div>
<a class="anchor" id="ga9ec860bcb6f8ca31205bf0f1b51ab723"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_pid_reset_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__f32.html">arm_pid_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>Instance pointer of PID control data structure. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl class="section user"><dt>Description: </dt><dd>The function resets the state buffer to zeros. </dd></dl>

<p>References <a class="el" href="structarm__pid__instance__f32.html#afd394e1e52fb1d526aa472c83b8f2464">arm_pid_instance_f32::state</a>.</p>

</div>
</div>
<a class="anchor" id="ga408566dacb4fa6e0458b2c75672e525f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_pid_reset_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__q15.html">arm_pid_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>Instance pointer of PID control data structure. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl class="section user"><dt>Description: </dt><dd>The function resets the state buffer to zeros. </dd></dl>

<p>References <a class="el" href="structarm__pid__instance__q15.html#a4a3f0a878b5b6b055e3478a2f244cd30">arm_pid_instance_q15::state</a>.</p>

</div>
</div>
<a class="anchor" id="gaeecbacd3fb37c608ec25474d3a0dffa9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_pid_reset_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__pid__instance__q31.html">arm_pid_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>Instance pointer of PID control data structure. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl class="section user"><dt>Description: </dt><dd>The function resets the state buffer to zeros. </dd></dl>

<p>References <a class="el" href="structarm__pid__instance__q31.html#a228e4a64da6014844a0a671a1fa391d4">arm_pid_instance_q31::state</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
