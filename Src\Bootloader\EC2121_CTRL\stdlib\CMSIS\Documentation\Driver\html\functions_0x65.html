<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="classes.html"><span>Data&#160;Structure&#160;Index</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li class="current"><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions_0x65.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a class="anchor" id="index_e"></a>- e -</h3><ul>
<li>ecc_error
: <a class="el" href="group__nand__interface__gr.html#a7707d2200a3bf8f49b148ffc8ded7636">ARM_NAND_STATUS</a>
</li>
<li>ecc_offset
: <a class="el" href="group__nand__interface__gr.html#a22d6a1813a47a7044f7acb478f8e9eb8">ARM_NAND_ECC_INFO</a>
</li>
<li>ecc_size
: <a class="el" href="group__nand__interface__gr.html#a22365f6a2af1171a1c3629c8ae5fe001">ARM_NAND_ECC_INFO</a>
</li>
<li>end
: <a class="el" href="group__flash__interface__gr.html#a204a3f4fa39b9c007f9784d3e4af4667">ARM_FLASH_SECTOR</a>
</li>
<li>EndpointConfigure
: <a class="el" href="group__usbd__interface__gr.html#a9fcafd15149d35022b05cf3c396e714e">ARM_DRIVER_USBD</a>
</li>
<li>EndpointStall
: <a class="el" href="group__usbd__interface__gr.html#a6fbcf63ac9f962787cddc8f11a44dccb">ARM_DRIVER_USBD</a>
</li>
<li>EndpointTransfer
: <a class="el" href="group__usbd__interface__gr.html#a0cc21434bc57e696fabf0207925fe5ff">ARM_DRIVER_USBD</a>
</li>
<li>EndpointTransferAbort
: <a class="el" href="group__usbd__interface__gr.html#a4e36fd46291f71e4a748264e2f6ae431">ARM_DRIVER_USBD</a>
</li>
<li>EndpointTransferGetResult
: <a class="el" href="group__usbd__interface__gr.html#a217e38c26bbcdecbad8c984753b2597a">ARM_DRIVER_USBD</a>
</li>
<li>EndpointUnconfigure
: <a class="el" href="group__usbd__interface__gr.html#ad45578fffbd046231f69aa058d29bba5">ARM_DRIVER_USBD</a>
</li>
<li>erase_chip
: <a class="el" href="group__flash__interface__gr.html#af5ec2b569c193fc5024c2739f46b328a">ARM_FLASH_CAPABILITIES</a>
</li>
<li>EraseChip
: <a class="el" href="group__flash__interface__gr.html#ae873705c743d94572fb6500421e15760">ARM_DRIVER_FLASH</a>
</li>
<li>erased_value
: <a class="el" href="group__flash__interface__gr.html#a85c3826bf20669d38e466dfd376994db">ARM_FLASH_INFO</a>
</li>
<li>EraseSector
: <a class="el" href="group__flash__interface__gr.html#ad9d78f9fe07aabf12b23b95239818b55">ARM_DRIVER_FLASH</a>
</li>
<li>error
: <a class="el" href="group__flash__interface__gr.html#aa8d183302fdfa4a6892f1d80300cdb32">ARM_FLASH_STATUS</a>
</li>
<li>event_connect
: <a class="el" href="group__usbh__host__gr.html#ae76b779cb9fdf447b20c8b6beed2d534">ARM_USBH_CAPABILITIES</a>
</li>
<li>event_cts
: <a class="el" href="group__usart__interface__gr.html#a4ebe5ddec8d99a63843f2a3c70ac85f9">ARM_USART_CAPABILITIES</a>
</li>
<li>event_dcd
: <a class="el" href="group__usart__interface__gr.html#a7c1dd043d0db9738d6b5fa8d89211446">ARM_USART_CAPABILITIES</a>
</li>
<li>event_device_ready
: <a class="el" href="group__nand__interface__gr.html#a5f347e9b63764bbb657f52dc20682128">ARM_NAND_CAPABILITIES</a>
</li>
<li>event_disconnect
: <a class="el" href="group__usbh__host__gr.html#ab83941051cac8e19807b887354dc42fc">ARM_USBH_CAPABILITIES</a>
</li>
<li>event_dsr
: <a class="el" href="group__usart__interface__gr.html#aefdb61f16498d650b5a7f5f9b62779df">ARM_USART_CAPABILITIES</a>
</li>
<li>event_mode_fault
: <a class="el" href="group__spi__interface__gr.html#a309619714f0c4febaa497ebdb9b7e3ca">ARM_SPI_CAPABILITIES</a>
</li>
<li>event_overcurrent
: <a class="el" href="group__usbh__host__gr.html#acd3087b3a4a7691595dd75568c12d696">ARM_USBH_CAPABILITIES</a>
</li>
<li>event_ready
: <a class="el" href="group__flash__interface__gr.html#add296ba516c8fc17ba51e30f2a00f0a9">ARM_FLASH_CAPABILITIES</a>
</li>
<li>event_ri
: <a class="el" href="group__usart__interface__gr.html#ab55f90aec5f909ff3a75bf36e61312ea">ARM_USART_CAPABILITIES</a>
</li>
<li>event_rx_frame
: <a class="el" href="group__eth__mac__interface__gr.html#a8c8f1ac2bf053a9bac98c476646a6018">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>event_rx_timeout
: <a class="el" href="group__usart__interface__gr.html#afe469796cfca4ea61bd6181afb4916be">ARM_USART_CAPABILITIES</a>
</li>
<li>event_tx_complete
: <a class="el" href="group__usart__interface__gr.html#a0190aabe8d8f59176be8d693f8874fb3">ARM_USART_CAPABILITIES</a>
</li>
<li>event_tx_frame
: <a class="el" href="group__eth__mac__interface__gr.html#a1b4af3590d59ea4f8e845b4239a4e445">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>event_vbus_off
: <a class="el" href="group__usbd__interface__gr.html#a72d905bc20735bafda40d73c91829709">ARM_USBD_CAPABILITIES</a>
</li>
<li>event_vbus_on
: <a class="el" href="group__usbd__interface__gr.html#a53f95b9ecb7c84197947e7542501c7d3">ARM_USBD_CAPABILITIES</a>
</li>
<li>event_wakeup
: <a class="el" href="group__eth__mac__interface__gr.html#a7536d9b9818b20b6974a712e0449439b">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>ExecuteSequence
: <a class="el" href="group__nand__interface__gr.html#af0dd5e96fbcc5c15bb183363f8541af8">ARM_DRIVER_NAND</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
