<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__"><span>_</span></a></li>
      <li><a href="globals_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_0x69.html#index_i"><span>i</span></a></li>
      <li class="current"><a href="globals_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_0x75.html#index_u"><span>u</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_0x6d.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index_m"></a>- m -</h3><ul>
<li>ARM_MCI_AbortTransfer()
: <a class="el" href="group__mci__interface__gr.html#ga78fd8cd818542a03df45abb117fa916e">Driver_MCI.c</a>
</li>
<li>ARM_MCI_API_VERSION
: <a class="el" href="_driver___m_c_i_8h.html#ac1863acc4889811dc5f45ec0e059d892">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BOOT_ACK
: <a class="el" href="group__mci__send__command__flags__ctrls.html#ga8c55bc0a310630d49810802ccd1bb10d">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BOOT_ALTERNATIVE
: <a class="el" href="group__mci__send__command__flags__ctrls.html#ga30bd304652d4f870ee7ce61c266a9348">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BOOT_OPERATION
: <a class="el" href="group__mci__send__command__flags__ctrls.html#gae04254f51dfd9838583206cae0a5f8f7">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_CMD_MODE
: <a class="el" href="group__mci__mode__ctrls.html#ga8b7571e37520c07d8ef4f697f3886715">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_CMD_OPEN_DRAIN
: <a class="el" href="group__mci__cmd__line__ctrls.html#gaadf8667985731964d57d1ed672e90fd3">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_CMD_PUSH_PULL
: <a class="el" href="group__mci__cmd__line__ctrls.html#gaaed404312d9bc073e3489779a911c7dc">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_DATA_WIDTH
: <a class="el" href="group__mci__mode__ctrls.html#ga876d964d0eeacdb16e93f7558a544587">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_DATA_WIDTH_1
: <a class="el" href="group__mci__bus__data__width__ctrls.html#gaa09a00d810a4dfd1d1824311ee290585">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_DATA_WIDTH_4
: <a class="el" href="group__mci__bus__data__width__ctrls.html#gaa28150d8c3789e8cf1bcda318f74a28c">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_DATA_WIDTH_4_DDR
: <a class="el" href="group__mci__bus__data__width__ctrls.html#gaccb174bd131f8fd8cd9a56439a8ebb60">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_DATA_WIDTH_8
: <a class="el" href="group__mci__bus__data__width__ctrls.html#ga3bb99a2d98ba9fb8c5bc97fa2b8ef469">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_DATA_WIDTH_8_DDR
: <a class="el" href="group__mci__bus__data__width__ctrls.html#ga7b31f81ae703229095fe9efcfbe80b47">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_DEFAULT_SPEED
: <a class="el" href="group__mci__bus__speed__ctrls.html#ga601fa8b27ab2e5f6d90c93d54c8f412d">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_HIGH_SPEED
: <a class="el" href="group__mci__bus__speed__ctrls.html#gaabda746ac7d6b4497358ff655a8ea6be">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_SPEED
: <a class="el" href="group__mci__mode__ctrls.html#ga7f982d2e5aec768307d35a83c65fb3ef">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_SPEED_MODE
: <a class="el" href="group__mci__mode__ctrls.html#gaf7ede525eabc618fbbb9f7a294c8ed96">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_UHS_DDR50
: <a class="el" href="group__mci__bus__speed__ctrls.html#ga04da920a5cac99eab9784527057f1b9c">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_UHS_SDR104
: <a class="el" href="group__mci__bus__speed__ctrls.html#gad2bab563e7bbb4bcf6bdabe6a13dadf4">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_UHS_SDR12
: <a class="el" href="group__mci__bus__speed__ctrls.html#ga0473c44a7b65044b3c6a8e7012009a4a">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_UHS_SDR25
: <a class="el" href="group__mci__bus__speed__ctrls.html#gae32422631052307b3c4d269b25415907">Driver_MCI.h</a>
</li>
<li>ARM_MCI_BUS_UHS_SDR50
: <a class="el" href="group__mci__bus__speed__ctrls.html#ga9f326c02391d965918ae619b912b81e7">Driver_MCI.h</a>
</li>
<li>ARM_MCI_CARD_INITIALIZE
: <a class="el" href="group__mci__send__command__flags__ctrls.html#ga81606bd94ce782e2c3764b913f929f60">Driver_MCI.h</a>
</li>
<li>ARM_MCI_CardPower()
: <a class="el" href="group__mci__interface__gr.html#gab161f80e0eda2815f3e0ebbba1314ff0">Driver_MCI.c</a>
</li>
<li>ARM_MCI_CCS
: <a class="el" href="group__mci__send__command__flags__ctrls.html#gab82c472e4ca3fca12ae3291e25997f00">Driver_MCI.h</a>
</li>
<li>ARM_MCI_CCSD
: <a class="el" href="group__mci__send__command__flags__ctrls.html#gab9df5169b37621764f8bb0f93db5281a">Driver_MCI.h</a>
</li>
<li>ARM_MCI_Control()
: <a class="el" href="group__mci__interface__gr.html#gaec0506a2aa4ae75cf6bc02528f36fe30">Driver_MCI.c</a>
</li>
<li>ARM_MCI_CONTROL_CLOCK_IDLE
: <a class="el" href="group__mci__mode__ctrls.html#ga889473fbfbdcb89aab4d53cc8a13f615">Driver_MCI.h</a>
</li>
<li>ARM_MCI_CONTROL_READ_WAIT
: <a class="el" href="group__mci__mode__ctrls.html#gaaa10c5aa7a8108aa59c3734b3eec2e3a">Driver_MCI.h</a>
</li>
<li>ARM_MCI_CONTROL_RESET
: <a class="el" href="group__mci__mode__ctrls.html#ga21e403e8c3fa8cc75431a513813f0a16">Driver_MCI.h</a>
</li>
<li>ARM_MCI_CSS_TIMEOUT
: <a class="el" href="group__mci__mode__ctrls.html#gae97b1a819a5d326b1f1009b0d6d48b5a">Driver_MCI.h</a>
</li>
<li>ARM_MCI_DATA_TIMEOUT
: <a class="el" href="group__mci__mode__ctrls.html#ga09a58821e42595f0c2e55f8cc2d32ceb">Driver_MCI.h</a>
</li>
<li>ARM_MCI_DRIVER_STRENGTH
: <a class="el" href="group__mci__mode__ctrls.html#ga78068f519139f2ae7b09e0608070aaf6">Driver_MCI.h</a>
</li>
<li>ARM_MCI_DRIVER_TYPE_A
: <a class="el" href="group__mci__driver__strength__ctrls.html#ga64eb1c4847711a262f084c361b60a912">Driver_MCI.h</a>
</li>
<li>ARM_MCI_DRIVER_TYPE_B
: <a class="el" href="group__mci__driver__strength__ctrls.html#ga078d3c3bc7c9335b92e6445a0abafc46">Driver_MCI.h</a>
</li>
<li>ARM_MCI_DRIVER_TYPE_C
: <a class="el" href="group__mci__driver__strength__ctrls.html#ga3da11696d1fcd3930eb7e70fe097d747">Driver_MCI.h</a>
</li>
<li>ARM_MCI_DRIVER_TYPE_D
: <a class="el" href="group__mci__driver__strength__ctrls.html#ga8185f82b1d8857a3f0eb461d664f2b3d">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_CARD_INSERTED
: <a class="el" href="group__mci__event__gr.html#gae2cf8ef238c092e94e96a01602f3a23c">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_CARD_REMOVED
: <a class="el" href="group__mci__event__gr.html#ga92ba748f9324ec13898f10456f17c8cc">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_CCS
: <a class="el" href="group__mci__event__gr.html#ga8161f3960ddf2a3cdc3c4c83148c6099">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_CCS_TIMEOUT
: <a class="el" href="group__mci__event__gr.html#gafa8cbcd597a05c64901eeb777cc0b74f">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_COMMAND_COMPLETE
: <a class="el" href="group__mci__event__gr.html#gae69356c75d55103d93ef91ac1bc02b49">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_COMMAND_ERROR
: <a class="el" href="group__mci__event__gr.html#ga373aeb3eca0e4c6d159312488a130442">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_COMMAND_TIMEOUT
: <a class="el" href="group__mci__event__gr.html#gab79b3ab4dcd03c38df1e173fa903d822">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_SDIO_INTERRUPT
: <a class="el" href="group__mci__event__gr.html#ga75a050fdfe04e6816e96c938d6a6c197">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_TRANSFER_COMPLETE
: <a class="el" href="group__mci__event__gr.html#gabc3c468dedaed890683360f2c5c65bea">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_TRANSFER_ERROR
: <a class="el" href="group__mci__event__gr.html#ga5d2cee5ba6d0e40ad505983155706c29">Driver_MCI.h</a>
</li>
<li>ARM_MCI_EVENT_TRANSFER_TIMEOUT
: <a class="el" href="group__mci__event__gr.html#ga614e7c7226adbaa8ec4165bf8b87ef27">Driver_MCI.h</a>
</li>
<li>ARM_MCI_GetCapabilities()
: <a class="el" href="group__mci__interface__gr.html#ga7e5a78b6e6409189833a0b72a0a3c48a">Driver_MCI.c</a>
</li>
<li>ARM_MCI_GetStatus()
: <a class="el" href="group__mci__interface__gr.html#ga8d61aa42ce78d1864fa928c1f273cbd9">Driver_MCI.c</a>
</li>
<li>ARM_MCI_GetVersion()
: <a class="el" href="group__mci__interface__gr.html#ga3418183015dbf3025b94eebaedb00ab1">Driver_MCI.c</a>
</li>
<li>ARM_MCI_Initialize()
: <a class="el" href="group__mci__interface__gr.html#ga6f34d4ab362e596ddaf23aac093268cf">Driver_MCI.c</a>
</li>
<li>ARM_MCI_INTERRUPT_COMMAND
: <a class="el" href="group__mci__send__command__flags__ctrls.html#gab2bfeedf1dc2df1872ebbcc559a7385a">Driver_MCI.h</a>
</li>
<li>ARM_MCI_INTERRUPT_RESPONSE
: <a class="el" href="group__mci__send__command__flags__ctrls.html#gabc31b6b26988998c84c92a9a698fd5dc">Driver_MCI.h</a>
</li>
<li>ARM_MCI_MONITOR_SDIO_INTERRUPT
: <a class="el" href="group__mci__mode__ctrls.html#ga19fd7d3b74ac48ca74a2e138b3ee9963">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VCCQ_1V2
: <a class="el" href="group__mci__card__power__ctrls.html#ga5304b3b6cadc5113f841c3d526ab5db6">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VCCQ_1V8
: <a class="el" href="group__mci__card__power__ctrls.html#gaaf389e0a139d5808dff8ebb6897d4c7b">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VCCQ_3V3
: <a class="el" href="group__mci__card__power__ctrls.html#ga91a7201173cc092eaf7f0bcee028871e">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VCCQ_Msk
: <a class="el" href="_driver___m_c_i_8h.html#aef11db3d5da54f0e0cf2e346f08622e2">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VCCQ_OFF
: <a class="el" href="group__mci__card__power__ctrls.html#ga6929c98a6bc8d898939a95111509220b">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VCCQ_Pos
: <a class="el" href="_driver___m_c_i_8h.html#ae2de0ecc2d5d73b134c18b80bedb23a7">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VDD_1V8
: <a class="el" href="group__mci__card__power__ctrls.html#ga2ce8e379c3691da3b51c9c97d61770ef">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VDD_3V3
: <a class="el" href="group__mci__card__power__ctrls.html#ga565ecd36c8d0379fb4172da577cc540d">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VDD_Msk
: <a class="el" href="_driver___m_c_i_8h.html#a58913d82ea4abd60dee50cec024de4a0">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VDD_OFF
: <a class="el" href="group__mci__card__power__ctrls.html#ga288fbd80b384739a80e8f3ec31205cf5">Driver_MCI.h</a>
</li>
<li>ARM_MCI_POWER_VDD_Pos
: <a class="el" href="_driver___m_c_i_8h.html#a8de1e84f879b741ed1e13a0aac93a246">Driver_MCI.h</a>
</li>
<li>ARM_MCI_PowerControl()
: <a class="el" href="group__mci__interface__gr.html#ga19752749d04ed22dc91c4294645e0244">Driver_MCI.c</a>
</li>
<li>ARM_MCI_ReadCD()
: <a class="el" href="group__mci__interface__gr.html#ga012fca8f1ce5366fce14b708c771c635">Driver_MCI.c</a>
</li>
<li>ARM_MCI_ReadWP()
: <a class="el" href="group__mci__interface__gr.html#ga3d70286918405ac81fa795c7d09dc6fd">Driver_MCI.c</a>
</li>
<li>ARM_MCI_RESPONSE_CRC
: <a class="el" href="group__mci__send__command__flags__ctrls.html#ga6ab3f4c1a2bf0fdb81fbcf7a5698f2de">Driver_MCI.h</a>
</li>
<li>ARM_MCI_RESPONSE_INDEX
: <a class="el" href="group__mci__send__command__flags__ctrls.html#ga497abf878c6e12f54cc7ddb92da76c4a">Driver_MCI.h</a>
</li>
<li>ARM_MCI_RESPONSE_LONG
: <a class="el" href="group__mci__send__command__flags__ctrls.html#gac49c7b39a7c51bd2193e048835bec2fb">Driver_MCI.h</a>
</li>
<li>ARM_MCI_RESPONSE_Msk
: <a class="el" href="_driver___m_c_i_8h.html#af12ae94785114eb31b1e077d52eda3fd">Driver_MCI.h</a>
</li>
<li>ARM_MCI_RESPONSE_NONE
: <a class="el" href="group__mci__send__command__flags__ctrls.html#ga70934cef80884e8c75fb4eebf8452118">Driver_MCI.h</a>
</li>
<li>ARM_MCI_RESPONSE_Pos
: <a class="el" href="_driver___m_c_i_8h.html#a125bb871e85f4646ff0fa8be1ab7c8c9">Driver_MCI.h</a>
</li>
<li>ARM_MCI_RESPONSE_SHORT
: <a class="el" href="group__mci__send__command__flags__ctrls.html#gaa5ddf1cf772b234e3c247039effd0e7b">Driver_MCI.h</a>
</li>
<li>ARM_MCI_RESPONSE_SHORT_BUSY
: <a class="el" href="group__mci__send__command__flags__ctrls.html#gaa1d541b0edf32ec05e68d623c727ef9c">Driver_MCI.h</a>
</li>
<li>ARM_MCI_RESUME_TRANSFER
: <a class="el" href="group__mci__mode__ctrls.html#gac4907319499072fddf61f2f83b0dd966">Driver_MCI.h</a>
</li>
<li>ARM_MCI_SendCommand()
: <a class="el" href="group__mci__interface__gr.html#ga5a431da89feabc2b4bc0c27943dff6f2">Driver_MCI.c</a>
</li>
<li>ARM_MCI_SetupTransfer()
: <a class="el" href="group__mci__interface__gr.html#gaaec681bcd8e6811c5743e33ee0f35ed1">Driver_MCI.c</a>
</li>
<li>ARM_MCI_SignalEvent()
: <a class="el" href="group__mci__interface__gr.html#gaac2dbd1c1a98436938c5d0d6248cb700">Driver_MCI.c</a>
</li>
<li>ARM_MCI_SignalEvent_t
: <a class="el" href="group__mci__interface__gr.html#ga0d14651f6788c1ffd81544602565faf1">Driver_MCI.h</a>
</li>
<li>ARM_MCI_SUSPEND_TRANSFER
: <a class="el" href="group__mci__mode__ctrls.html#ga04cf174b0ef13240f26225bf8e45a4a0">Driver_MCI.h</a>
</li>
<li>ARM_MCI_TRANSFER_BLOCK
: <a class="el" href="group__mci__transfer__ctrls.html#ga4ced782e7c0c70d5f0edbddd1e48323b">Driver_MCI.h</a>
</li>
<li>ARM_MCI_TRANSFER_DATA
: <a class="el" href="group__mci__send__command__flags__ctrls.html#ga8aa566f69aa74ed416213df6ca3267bd">Driver_MCI.h</a>
</li>
<li>ARM_MCI_TRANSFER_READ
: <a class="el" href="group__mci__transfer__ctrls.html#gaa6f3be235a9dce5c66be8fe64f399846">Driver_MCI.h</a>
</li>
<li>ARM_MCI_TRANSFER_STREAM
: <a class="el" href="group__mci__transfer__ctrls.html#gac7db0cffd571e60758627d889ba7a432">Driver_MCI.h</a>
</li>
<li>ARM_MCI_TRANSFER_WRITE
: <a class="el" href="group__mci__transfer__ctrls.html#gaddc60aab15f75993a99f98f71ddbd50c">Driver_MCI.h</a>
</li>
<li>ARM_MCI_UHS_TUNING_OPERATION
: <a class="el" href="group__mci__mode__ctrls.html#ga98853f60bdc085aeeccd66f7bdf22d3d">Driver_MCI.h</a>
</li>
<li>ARM_MCI_UHS_TUNING_RESULT
: <a class="el" href="group__mci__mode__ctrls.html#gabd3af448e26da5657c1c5a03330476b7">Driver_MCI.h</a>
</li>
<li>ARM_MCI_Uninitialize()
: <a class="el" href="group__mci__interface__gr.html#gaef8183e77797e74997551d03646d42c2">Driver_MCI.c</a>
</li>
<li>ARM_MCI_WAIT_BUSY
: <a class="el" href="group__mci__send__command__flags__ctrls.html#ga68e879799bb27a1b13baf57ed19d719d">Driver_MCI.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
