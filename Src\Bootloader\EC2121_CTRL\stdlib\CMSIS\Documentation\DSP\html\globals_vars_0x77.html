<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li class="current"><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_vars.html#index_a"><span>a</span></a></li>
      <li><a href="globals_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_vars_0x76.html#index_v"><span>v</span></a></li>
      <li class="current"><a href="globals_vars_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="globals_vars_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_vars_0x77.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_w"></a>- w -</h3><ul>
<li>Weights_128
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gad00f29d896d64d6da7afbbb9d3e182a4">arm_dct4_init_f32.c</a>
</li>
<li>Weights_2048
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gac3a2a00b3106dfcb5e0a582f50c65692">arm_dct4_init_f32.c</a>
</li>
<li>Weights_512
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaeb67b0be5b3c2139d660e02cedeed908">arm_dct4_init_f32.c</a>
</li>
<li>Weights_8192
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga45a8ec91e5da91790566105bc7e6f0c2">arm_dct4_init_f32.c</a>
</li>
<li>WeightsQ15_128
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaa4ff5e6f062efb1d1ec8c6c2207c3727">arm_dct4_init_q15.c</a>
</li>
<li>WeightsQ15_2048
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga2235ec700d0d6925d9733f48541d46f5">arm_dct4_init_q15.c</a>
</li>
<li>WeightsQ15_512
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#gadc8ee250fc217d6cb5c84dd7c1eb6d31">arm_dct4_init_q15.c</a>
</li>
<li>WeightsQ15_8192
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga4fdc60621eb306984a82ce8b2d645bb7">arm_dct4_init_q15.c</a>
</li>
<li>WeightsQ31_128
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga02d7024538a87214296b01d83ba36b02">arm_dct4_init_q31.c</a>
</li>
<li>WeightsQ31_2048
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga725b65c25a02b3cad329e18bb832f65e">arm_dct4_init_q31.c</a>
</li>
<li>WeightsQ31_512
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga31a8217a96f7d3171921e98398f31596">arm_dct4_init_q31.c</a>
</li>
<li>WeightsQ31_8192
: <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga16bf6bbe5c4c9b35f88253cf7bdcc435">arm_dct4_init_q31.c</a>
</li>
<li>wire1
: <a class="el" href="arm__variance__example__f32_8c.html#acc43b372d92d5027b9f9cac782c8b3c7">arm_variance_example_f32.c</a>
, <a class="el" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">arm_signal_converge_example_f32.c</a>
</li>
<li>wire2
: <a class="el" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">arm_signal_converge_example_f32.c</a>
, <a class="el" href="arm__variance__example__f32_8c.html#a41a9afab5be5ccd2e6f618b83102f0d1">arm_variance_example_f32.c</a>
</li>
<li>wire3
: <a class="el" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">arm_signal_converge_example_f32.c</a>
, <a class="el" href="arm__variance__example__f32_8c.html#af61f43ad332a2322e43ced590b6d9768">arm_variance_example_f32.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
