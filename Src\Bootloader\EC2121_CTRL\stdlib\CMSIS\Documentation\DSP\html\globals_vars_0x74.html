<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li class="current"><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_vars.html#index_a"><span>a</span></a></li>
      <li><a href="globals_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_vars_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_vars_0x73.html#index_s"><span>s</span></a></li>
      <li class="current"><a href="globals_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="globals_vars_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="globals_vars_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_vars_0x74.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_t"></a>- t -</h3><ul>
<li>testIndex
: <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a4a391651dbb95db35ccba70fc4f9e049">ARM/arm_fft_bin_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a4a391651dbb95db35ccba70fc4f9e049">GCC/arm_fft_bin_example_f32.c</a>
</li>
<li>testInput_f32
: <a class="el" href="arm__graphic__equalizer__example__q31_8c.html#aa4699dc6ee05353c83e0be4e69f6ad05">arm_graphic_equalizer_example_q31.c</a>
, <a class="el" href="arm__variance__example__f32_8c.html#a9170ec9e681e2b519fe9c46a30455841">arm_variance_example_f32.c</a>
, <a class="el" href="arm__signal__converge__data_8c.html#a898fab3d597516192bb221fb658315cc">arm_signal_converge_data.c</a>
, <a class="el" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">arm_graphic_equalizer_data.c</a>
, <a class="el" href="arm__signal__converge__example__f32_8c.html#aac98609c83ad8ed2b05c4fd82d2ba59b">arm_signal_converge_example_f32.c</a>
, <a class="el" href="arm__sin__cos__example__f32_8c.html#a8b3b7113988efd5bf11a247d07ae39a1">arm_sin_cos_example_f32.c</a>
</li>
<li>testInput_f32_10khz
: <a class="el" href="_a_r_m_2arm__fft__bin__data_8c.html#a8a12a8ec4b866be84f4b7d3daf6a1242">ARM/arm_fft_bin_data.c</a>
, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a3d8ecb82590486ceebccc76263963b16">ARM/arm_fft_bin_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__fft__bin__data_8c.html#a8a12a8ec4b866be84f4b7d3daf6a1242">GCC/arm_fft_bin_data.c</a>
, <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a3d8ecb82590486ceebccc76263963b16">GCC/arm_fft_bin_example_f32.c</a>
</li>
<li>testInput_f32_1kHz_15kHz
: <a class="el" href="arm__fir__data_8c.html#a143154a165358f0016714cb7f1c83970">arm_fir_data.c</a>
, <a class="el" href="arm__fir__example__f32_8c.html#a35d190391c204b677e2839d76ede6e8b">arm_fir_example_f32.c</a>
</li>
<li>testInputA_f32
: <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a7db2f016e1afcb524a2fdc3c5a3cb640">GCC/arm_convolution_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7db2f016e1afcb524a2fdc3c5a3cb640">ARM/arm_convolution_example_f32.c</a>
</li>
<li>testInputB_f32
: <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#acb22287e7e096b677e352dfd363ba60d">ARM/arm_convolution_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#acb22287e7e096b677e352dfd363ba60d">GCC/arm_convolution_example_f32.c</a>
</li>
<li>testInputSin_f32
: <a class="el" href="arm__linear__interp__example__f32_8c.html#a4be0c4d25e63ce04b8cc8ad070805287">arm_linear_interp_example_f32.c</a>
</li>
<li>testLinIntOutput
: <a class="el" href="arm__linear__interp__example__f32_8c.html#a8ca7d0ad6e04efed464bcaacedacf925">arm_linear_interp_example_f32.c</a>
</li>
<li>testMarks_f32
: <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a0153222efa82b7f1a0ea3835921bf921">ARM/arm_class_marks_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a0153222efa82b7f1a0ea3835921bf921">GCC/arm_class_marks_example_f32.c</a>
</li>
<li>testOutput
: <a class="el" href="arm__sin__cos__example__f32_8c.html#a324833b61eae796082e07d078a67c34f">arm_sin_cos_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">ARM/arm_class_marks_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">GCC/arm_class_marks_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a324833b61eae796082e07d078a67c34f">ARM/arm_dotproduct_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__dotproduct__example__f32_8c.html#a324833b61eae796082e07d078a67c34f">GCC/arm_dotproduct_example_f32.c</a>
, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#aaaf907bde12051bc8bb5d48b7d315a35">ARM/arm_fft_bin_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#aaaf907bde12051bc8bb5d48b7d315a35">GCC/arm_fft_bin_example_f32.c</a>
, <a class="el" href="arm__fir__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">arm_fir_example_f32.c</a>
, <a class="el" href="arm__graphic__equalizer__example__q31_8c.html#a3293e83a154108a1d398bb042e293894">arm_graphic_equalizer_example_q31.c</a>
, <a class="el" href="arm__linear__interp__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">arm_linear_interp_example_f32.c</a>
</li>
<li>testRefOutput_f32
: <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">ARM/arm_convolution_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">GCC/arm_convolution_example_f32.c</a>
, <a class="el" href="arm__graphic__equalizer__data_8c.html#a32c13cab7708773e0f86a9677e259c64">arm_graphic_equalizer_data.c</a>
, <a class="el" href="arm__graphic__equalizer__example__q31_8c.html#a9cbbafa975d67bc8ee8ea3260fdd5638">arm_graphic_equalizer_example_q31.c</a>
, <a class="el" href="arm__sin__cos__example__f32_8c.html#a8bcb5c1f75ed5f1737e435f0de1850a3">arm_sin_cos_example_f32.c</a>
</li>
<li>testRefSinOutput32_f32
: <a class="el" href="arm__linear__interp__example__f32_8c.html#a5a33218d422603f3e5267b6984bdddd1">arm_linear_interp_example_f32.c</a>
</li>
<li>testUnity_f32
: <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a993b9b2a1faf43b319c1c6d58b26e7a1">ARM/arm_class_marks_example_f32.c</a>
, <a class="el" href="_g_c_c_2arm__class__marks__example__f32_8c.html#a993b9b2a1faf43b319c1c6d58b26e7a1">GCC/arm_class_marks_example_f32.c</a>
</li>
<li>twiddleCoef_1024
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga27c056eb130a4333d1cc5dd43ec738b1">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga27c056eb130a4333d1cc5dd43ec738b1">arm_common_tables.h</a>
</li>
<li>twiddleCoef_1024_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8a0ec95d866fe96b740e77d6e1356b59">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8a0ec95d866fe96b740e77d6e1356b59">arm_common_tables.h</a>
</li>
<li>twiddleCoef_1024_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga514443c44b62b8b3d240afefebcda310">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga514443c44b62b8b3d240afefebcda310">arm_common_tables.h</a>
</li>
<li>twiddleCoef_128
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga948433536dafaac1381decfccf4e2d9c">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga948433536dafaac1381decfccf4e2d9c">arm_common_tables.h</a>
</li>
<li>twiddleCoef_128_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gabfdd1c5cd2b3f96da5fe5f07c707a8e5">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gabfdd1c5cd2b3f96da5fe5f07c707a8e5">arm_common_tables.h</a>
</li>
<li>twiddleCoef_128_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafecf9ed9873415d9f5f17f37b30c7250">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafecf9ed9873415d9f5f17f37b30c7250">arm_common_tables.h</a>
</li>
<li>twiddleCoef_16
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae75e243ec61706427314270f222e0c8e">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae75e243ec61706427314270f222e0c8e">arm_common_tables.h</a>
</li>
<li>twiddleCoef_16_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8e4e2e05f4a3112184c96cb3308d6c39">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8e4e2e05f4a3112184c96cb3308d6c39">arm_common_tables.h</a>
</li>
<li>twiddleCoef_16_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaef4697e1ba348c4ac9358f2b9e279e93">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaef4697e1ba348c4ac9358f2b9e279e93">arm_common_tables.h</a>
</li>
<li>twiddleCoef_2048
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga23e7f30421a7905b21c2015429779633">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga23e7f30421a7905b21c2015429779633">arm_common_tables.h</a>
</li>
<li>twiddleCoef_2048_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gadd16ce08ffd1048c385e0534a3b19cbb">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gadd16ce08ffd1048c385e0534a3b19cbb">arm_common_tables.h</a>
</li>
<li>twiddleCoef_2048_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9c5767de9f5a409fd0c2027e6ac67179">arm_common_tables.h</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9c5767de9f5a409fd0c2027e6ac67179">arm_common_tables.c</a>
</li>
<li>twiddleCoef_256
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafe813758a03a798e972359a092315be4">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafe813758a03a798e972359a092315be4">arm_common_tables.h</a>
</li>
<li>twiddleCoef_256_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6099ae5262a0a3a8d9ce1e6da02f0c2e">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6099ae5262a0a3a8d9ce1e6da02f0c2e">arm_common_tables.h</a>
</li>
<li>twiddleCoef_256_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaef1ea005053b715b851cf5f908168ede">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaef1ea005053b715b851cf5f908168ede">arm_common_tables.h</a>
</li>
<li>twiddleCoef_32
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga78a72c85d88185de98050c930cfc76e3">arm_common_tables.h</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga78a72c85d88185de98050c930cfc76e3">arm_common_tables.c</a>
</li>
<li>twiddleCoef_32_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gac194a4fe04a19051ae1811f69c6e5df2">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gac194a4fe04a19051ae1811f69c6e5df2">arm_common_tables.h</a>
</li>
<li>twiddleCoef_32_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8ba78d5e6ef4bdc58e8f0044e0664a0a">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8ba78d5e6ef4bdc58e8f0044e0664a0a">arm_common_tables.h</a>
</li>
<li>twiddleCoef_4096
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae0182d1dd3b2f21aad4e38a815a0bd40">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae0182d1dd3b2f21aad4e38a815a0bd40">arm_common_tables.h</a>
</li>
<li>twiddleCoef_4096_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9b409d6995eab17805b1d1881d4bc652">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9b409d6995eab17805b1d1881d4bc652">arm_common_tables.h</a>
</li>
<li>twiddleCoef_4096_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga67c0890317deab3391e276f22c1fc400">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga67c0890317deab3391e276f22c1fc400">arm_common_tables.h</a>
</li>
<li>twiddleCoef_512
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gad8830f0c068ab2cc19f2f87d220fa148">arm_common_tables.h</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gad8830f0c068ab2cc19f2f87d220fa148">arm_common_tables.c</a>
</li>
<li>twiddleCoef_512_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6152621af210f847128c6f38958fa385">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6152621af210f847128c6f38958fa385">arm_common_tables.h</a>
</li>
<li>twiddleCoef_512_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga416c61b2f08542a39111e06b0378bebe">arm_common_tables.h</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga416c61b2f08542a39111e06b0378bebe">arm_common_tables.c</a>
</li>
<li>twiddleCoef_64
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga4f3c6d98c7e66393b4ef3ac63746e43d">arm_common_tables.h</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga4f3c6d98c7e66393b4ef3ac63746e43d">arm_common_tables.c</a>
</li>
<li>twiddleCoef_64_q15
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaa0cc411e0b3c82078e85cfdf1b84290f">arm_common_tables.c</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaa0cc411e0b3c82078e85cfdf1b84290f">arm_common_tables.h</a>
</li>
<li>twiddleCoef_64_q31
: <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6e0a7e941a25a0d74b2e6590307de47e">arm_common_tables.h</a>
, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6e0a7e941a25a0d74b2e6590307de47e">arm_common_tables.c</a>
</li>
<li>twiddleCoef_rfft_1024
: <a class="el" href="arm__common__tables_8c.html#aa7d8d3aa9898d557385748a13c959a4c">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#aa7d8d3aa9898d557385748a13c959a4c">arm_common_tables.h</a>
</li>
<li>twiddleCoef_rfft_128
: <a class="el" href="arm__common__tables_8c.html#af089dd2fe1a543d40a3325982bf45e7c">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#af089dd2fe1a543d40a3325982bf45e7c">arm_common_tables.h</a>
</li>
<li>twiddleCoef_rfft_2048
: <a class="el" href="arm__common__tables_8h.html#a749a5995ebd433a163f7adc474dabcaa">arm_common_tables.h</a>
, <a class="el" href="arm__common__tables_8c.html#a749a5995ebd433a163f7adc474dabcaa">arm_common_tables.c</a>
</li>
<li>twiddleCoef_rfft_256
: <a class="el" href="arm__common__tables_8c.html#a5c5c161dd469d8e6806664956dae31f9">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a5c5c161dd469d8e6806664956dae31f9">arm_common_tables.h</a>
</li>
<li>twiddleCoef_rfft_32
: <a class="el" href="arm__common__tables_8h.html#a5992afe8574289cd71921651b80bd57d">arm_common_tables.h</a>
, <a class="el" href="arm__common__tables_8c.html#a5992afe8574289cd71921651b80bd57d">arm_common_tables.c</a>
</li>
<li>twiddleCoef_rfft_4096
: <a class="el" href="arm__common__tables_8c.html#a8013d68dd2476c86b77173bb98b87b29">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a8013d68dd2476c86b77173bb98b87b29">arm_common_tables.h</a>
</li>
<li>twiddleCoef_rfft_512
: <a class="el" href="arm__common__tables_8c.html#a94bd2fc98798f87003fef5cd0c04d1f5">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a94bd2fc98798f87003fef5cd0c04d1f5">arm_common_tables.h</a>
</li>
<li>twiddleCoef_rfft_64
: <a class="el" href="arm__common__tables_8c.html#a2759d8789e1e6ae2ba7fb8d7f5e9c2ab">arm_common_tables.c</a>
, <a class="el" href="arm__common__tables_8h.html#a2759d8789e1e6ae2ba7fb8d7f5e9c2ab">arm_common_tables.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
