<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_dct4_init_q31.c File Reference</title>
<title>CMSIS-DSP: arm_dct4_init_q31.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__dct4__init__q31_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">arm_dct4_init_q31.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga631bb59c7c97c814ff7147ecba6a716a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31</a> (<a class="el" href="structarm__dct4__instance__q31.html">arm_dct4_instance_q31</a> *S, <a class="el" href="structarm__rfft__instance__q31.html">arm_rfft_instance_q31</a> *S_RFFT, <a class="el" href="structarm__cfft__radix4__instance__q31.html">arm_cfft_radix4_instance_q31</a> *S_CFFT, uint16_t N, uint16_t Nby2, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> normalize)</td></tr>
<tr class="memdesc:ga631bb59c7c97c814ff7147ecba6a716a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 DCT4/IDCT4.  <a href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a"></a><br/></td></tr>
<tr class="separator:ga631bb59c7c97c814ff7147ecba6a716a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ga02d7024538a87214296b01d83ba36b02"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga02d7024538a87214296b01d83ba36b02">WeightsQ31_128</a> [256]</td></tr>
<tr class="separator:ga02d7024538a87214296b01d83ba36b02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31a8217a96f7d3171921e98398f31596"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga31a8217a96f7d3171921e98398f31596">WeightsQ31_512</a> [1024]</td></tr>
<tr class="separator:ga31a8217a96f7d3171921e98398f31596"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga725b65c25a02b3cad329e18bb832f65e"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga725b65c25a02b3cad329e18bb832f65e">WeightsQ31_2048</a> [4096]</td></tr>
<tr class="separator:ga725b65c25a02b3cad329e18bb832f65e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16bf6bbe5c4c9b35f88253cf7bdcc435"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga16bf6bbe5c4c9b35f88253cf7bdcc435">WeightsQ31_8192</a> [16384]</td></tr>
<tr class="separator:ga16bf6bbe5c4c9b35f88253cf7bdcc435"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabb8ee2004a3520fd08388db637d43875"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gabb8ee2004a3520fd08388db637d43875">cos_factorsQ31_128</a> [128]</td></tr>
<tr class="separator:gabb8ee2004a3520fd08388db637d43875"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3559569e603cb918911074be88523d0e"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga3559569e603cb918911074be88523d0e">cos_factorsQ31_512</a> [512]</td></tr>
<tr class="separator:ga3559569e603cb918911074be88523d0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa15fc3fb058482defda371113cd12e74"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaa15fc3fb058482defda371113cd12e74">cos_factorsQ31_2048</a> [2048]</td></tr>
<tr class="separator:gaa15fc3fb058482defda371113cd12e74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf687c4bbdbc700a3ad5d807d28de63e4"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaf687c4bbdbc700a3ad5d807d28de63e4">cos_factorsQ31_8192</a> [8192]</td></tr>
<tr class="separator:gaf687c4bbdbc700a3ad5d807d28de63e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_7e8aa87db1ad6b3d9b1f25792e7c5208.html">Source</a></li><li class="navelem"><a class="el" href="dir_9c857f0e41082f634e50072d001e0d4f.html">TransformFunctions</a></li><li class="navelem"><a class="el" href="arm__dct4__init__q31_8c.html">arm_dct4_init_q31.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
