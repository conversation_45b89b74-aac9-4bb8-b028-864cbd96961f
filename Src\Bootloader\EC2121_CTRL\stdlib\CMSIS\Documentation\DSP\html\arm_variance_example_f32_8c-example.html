<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_variance_example_f32.c</title>
<title>CMSIS-DSP: arm_variance_example_f32.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_variance_example_f32_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_variance_example_f32.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* $Date:         17. January 2013</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.0</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment">* Title:         arm_variance_example_f32.c</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Description:   Example code demonstrating variance calculation of input sequence.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment">* -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &lt;math.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Defines each of the tests performed</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="preprocessor">#define MAX_BLOCKSIZE   32</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define DELTA           (0.000001f)</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Declare I/O buffers</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a>[<a name="a1"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>];</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a2"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>[<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>];</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a3"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>[<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Test input data for Floating point Variance example for 32-blockSize</span></div>
<div class="line"><span class="comment">* Generated by the MATLAB randn() function</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a4"></a><a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>[32] =</div>
<div class="line">{</div>
<div class="line">  -0.432564811528221,  -1.665584378238097,   0.125332306474831,   0.287676420358549,</div>
<div class="line">  -1.146471350681464,   1.190915465642999,   1.189164201652103,  -0.037633276593318,</div>
<div class="line">   0.327292361408654,   0.174639142820925,  -0.186708577681439,   0.725790548293303,</div>
<div class="line">  -0.588316543014189,   2.183185818197101,  -0.136395883086596,   0.113931313520810,</div>
<div class="line">   1.066768211359189,   0.059281460523605,  -0.095648405483669,  -0.832349463650022,</div>
<div class="line">   0.294410816392640,  -1.336181857937804,   0.714324551818952,   1.623562064446271,</div>
<div class="line">  -0.691775701702287,   0.857996672828263,   1.254001421602532,  -1.593729576447477,</div>
<div class="line">  -1.440964431901020,   0.571147623658178,  -0.399885577715363,   0.689997375464345</div>
<div class="line"></div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Declare Global variables</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line">uint32_t <a name="a5"></a><a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a> = 32;</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  <a name="a6"></a><a class="code" href="arm__variance__example__f32_8c.html#a26cbbfe2a7905669e8440159465050df">refVarianceOut</a> = 0.903941793931839;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Variance calculation test</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">int32_t <a name="a7"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a8"></a><a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>;</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a9"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681">mean</a>, oneByBlockSize;</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> variance;</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> diff;</div>
<div class="line"></div>
<div class="line">  status = <a name="a10"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>;</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Calculation of mean value of input */</span></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* x&#39; = 1/blockSize * (x(0)* 1 + x(1) * 1 + ... + x(n-1) * 1) */</span></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Fill wire1 buffer with 1.0 value */</span></div>
<div class="line">  <a name="a11"></a><a class="code" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94" title="Fills a constant value into a floating-point vector.">arm_fill_f32</a>(1.0,  <a class="code" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a>, <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Calculate the dot product of wire1 and wire2 */</span></div>
<div class="line">  <span class="comment">/* (x(0)* 1 + x(1) * 1 + ...+ x(n-1) * 1) */</span></div>
<div class="line">  <a name="a12"></a><a class="code" href="group__dot__prod.html#ga55418d4362f6ba84c327f9b4f089a8c3" title="Dot product of floating-point vectors.">arm_dot_prod_f32</a>(<a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#a16e759789fbc05f878863f009066c8ea">wire1</a>, <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, &amp;mean);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Calculation of 1/blockSize */</span></div>
<div class="line">  oneByBlockSize = 1.0 / (<a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* 1/blockSize * (x(0)* 1 + x(1) * 1 + ... + x(n-1) * 1)  */</span></div>
<div class="line">  <a name="a13"></a><a class="code" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32" title="Floating-point vector multiplication.">arm_mult_f32</a>(&amp;mean, &amp;oneByBlockSize, &amp;mean, 1);</div>
<div class="line"></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Calculation of variance value of input */</span></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* (1/blockSize) * (x(0) - x&#39;) * (x(0) - x&#39;) + (x(1) - x&#39;) * (x(1) - x&#39;) + ... + (x(n-1) - x&#39;) * (x(n-1) - x&#39;) */</span></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Fill wire2 with mean value x&#39; */</span></div>
<div class="line">  <a class="code" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94" title="Fills a constant value into a floating-point vector.">arm_fill_f32</a>(mean,  <a class="code" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>, <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* wire3 contains (x-x&#39;) */</span></div>
<div class="line">  <a name="a14"></a><a class="code" href="group___basic_sub.html#ga7f975a472de286331134227c08aad826" title="Floating-point vector subtraction.">arm_sub_f32</a>(<a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>, <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* wire2 contains (x-x&#39;) */</span></div>
<div class="line">  <a name="a15"></a><a class="code" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3" title="Copies the elements of a floating-point vector.">arm_copy_f32</a>(<a class="code" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>, <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* (x(0) - x&#39;) * (x(0) - x&#39;) + (x(1) - x&#39;) * (x(1) - x&#39;) + ... + (x(n-1) - x&#39;) * (x(n-1) - x&#39;) */</span></div>
<div class="line">  <a class="code" href="group__dot__prod.html#ga55418d4362f6ba84c327f9b4f089a8c3" title="Dot product of floating-point vectors.">arm_dot_prod_f32</a>(<a class="code" href="arm__signal__converge__example__f32_8c.html#a4e370163c81ae2b72cc655a6b79e4c6a">wire2</a>, <a class="code" href="arm__signal__converge__example__f32_8c.html#a7e2cceadf6ec7f0aa0f698a680fa3a4b">wire3</a>, <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, &amp;variance);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* Calculation of 1/blockSize */</span></div>
<div class="line">  oneByBlockSize = 1.0 / (<a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a> - 1);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Calculation of variance */</span></div>
<div class="line">  <a class="code" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32" title="Floating-point vector multiplication.">arm_mult_f32</a>(&amp;variance, &amp;oneByBlockSize, &amp;variance, 1);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* absolute value of difference between ref and test */</span></div>
<div class="line">  diff = fabsf(<a class="code" href="arm__variance__example__f32_8c.html#a26cbbfe2a7905669e8440159465050df">refVarianceOut</a> - variance);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Comparison of variance value with reference */</span></div>
<div class="line">  <span class="keywordflow">if</span>(diff &gt; <a name="a16"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a3fd2b1bcd7ddcf506237987ad780f495">DELTA</a>)</div>
<div class="line">  {</div>
<div class="line">    status = <a name="a17"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>;</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span>( status != <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>)</div>
<div class="line">  {</div>
<div class="line">    <span class="keywordflow">while</span>(1);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
<div class="line"></div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
