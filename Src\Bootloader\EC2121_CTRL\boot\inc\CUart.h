#ifndef __CUART_H__
#define __CUART_H__
#include "stdarg.h"
#include "stm32f4xx_usart.h"
#include "stm32f4xx_gpio.h"
#include "stdio.h"
#include "Globel.h"
#include "CircularQueue.h"


//typedef void (*CALLBACKFNC_UART_RX)(uint16_t recvNum);
//typedef void (*CALLBACKFNC_UART_TX)(void);
//使用计数信号量能最快速的切换到接收，但是由于计数次数最多20，在需要缓存多包的情况下，会溢出
//不使用计数信号量，接收中断后要经过最多1ms（接收线程优先级足够高）才能响应接收，赵命华，20210429
#define Use_Semaphore_Count     0 //是否使用计数信号量来触发接收

class CUart
{
public:
    /*
    enum tagUART_MODULE
    {
        UART_PRINTF, //
        UART_4G, //
        UART_WIFI_BT, //蓝牙和wifi供一个串口
        UART_LIN,
        UART_NFC,
        UART_485,
        UART_NOT_USE
    };*/
    typedef struct
    {
        uint32_t            DMA_Channel;
        uint32_t            DMA_rccClock;//时钟
        uint8_t             RCC_Periph;//时钟类型
        IRQn_Type           DMA_TX_IRQn;//中断号,需要用到发送数据得DMA完成中断
        DMA_Stream_TypeDef* DMA_Stream_TX;
        IRQn_Type           DMA_RX_IRQn;
        DMA_Stream_TypeDef* DMA_Stream_RX;
        uint32_t            DMA_RX_TCIF;
        uint32_t            DMA_RX_HTIF;
        //
    } UART_DMA_MAP;
    typedef struct
    {
        GPIO_TypeDef* UARTx_TX_PORT;
        uint16_t      UARTx_TX_PIN;
        uint32_t      UARTx_TX_rccClock;//管脚时钟
        uint8_t       UARTx_TX_RCC_Periph;//时钟类型
        uint8_t       UARTx_TX_PinSource;//引脚复用源
        uint8_t       UARTx_TX_PinAF;//引脚复用功能
        GPIO_TypeDef* UARTx_RX_PORT;
        uint16_t      UARTx_RX_PIN;
        uint32_t      UARTx_RX_rccClock;//管脚时钟
        uint8_t       UARTx_RX_RCC_Periph;//时钟类型
        uint8_t       UARTx_RX_PinSource;//引脚复用源
        uint8_t       UARTx_RX_PinAF;//引脚复用功能
    } UART_PIN_MAP;
    typedef struct
    {
        USART_TypeDef *     pUARTx;//地址
        uint32_t            rccClock;//时钟
        uint8_t             RCC_Periph;//时钟类型
        IRQn_Type           UartIRQn;//中断号
        UART_DMA_MAP*       pDMA_PARA;
        UART_PIN_MAP*       pPIN_PARA;
        uint8_t             AllocState;
        CUart*              pUartObj;//串口对象的指针
        char*               Module;//该串口硬件分配对象,改用字符的形式，和应用层解耦,驱动层也方便打印，赵命华20210505
    } UART_MANAGER;
    /*
    typedef struct
    {
        uint16_t               dataLen;//buffer中有效数据长度
        uint8_t*               pdataBuffer;
    } UART_BUFFER;
    */
    typedef struct _UART_PARA
    {
        uint16_t          MaxRxLen;//接收数据的最大长度，应用调用recv接口接收到的数据不会超过这个大小，赵命华，20210427
        uint16_t          NumOfBuffer;//实际使用缓存约等于：MaxRxLen*NumOfBuffer
        USART_InitTypeDef uartHal;//串口波特率等参数
        char*             uartModule;//串口拥有者，比如4g、wifi等
    } UART_PARA;
    enum tagUART
    {
        UART_NUM1 = 0, //这里对应cUart.pp文件中的m_uartManager资源表格，不能随意改动
                       //为了方便中断直接找到对应的uart（不用一个一个取比较查找）,赵命华，20210405
        UART_NUM2, //
        UART_NUM3, //
        UART_NUM4,
        UART_NUM5,
        UART_NUM6//
        //UART_NUM7,
        //UART_NUM8,
        //UART_NUM9,
        //UART_NUM10
    };
    


public:
    ////////////////////////////////////////////////////////////////////////////////本段是串口驱动对外的接口
    CUart(UART_PARA userPara);

    ~CUart();

    bool UsartSend(uint8_t* pTxBuff, uint16_t Size,uint32_t timeOutMs=100);

    bool UsartSendFromISR(uint8_t* pTxBuff, uint16_t Size);

    uint16_t UsartRecv(uint8_t* pRxBuff, uint16_t Size);

    uint32_t UsartGetLostCount();//读取数据丢失长度（因回调处理不及时，导致循环队列queue满）

    void UsartClearLostCount();

    void UsartRecvLockFree();//由于Recv为阻塞式接收，析构时需要先调用本接口确保退出Recv函数

    static void UartDefaultSet(USART_InitTypeDef* USART_Struct,uint32_t BaudRate);//设置结构体波特率，并将其他参数设为默认

    ////////////////////////////////////////////////////////////////////////////////////



    ///////////////////////////////////////////////////////////////////////////////////本段函数用于中断调用

    bool UsartPushToRecvQueue();

    void UsartRecvFinish(uint16_t recvNum);

    void UartSetSendFinish();

    void UsartDmaRecvHalf();

    void UsartDmaRecvFinish();

    void UsartStartRecv();

    void UsartStopRecv();

    /////////////////////////////////////////////////////////////////////////////////

private:
    void UsartInit();

    void UsartSendDmaInit(uint8_t* pTxBuff, uint16_t Size);

    void UsartSendDmaDeInit();

public:
    //uint32_t BaseID;
    static UART_MANAGER m_uartManager[];//这几个成员虽然是public，但是仅用于中断中调用，其他地方不得使用
    UART_PARA m_uartPara;
    uint16_t m_curRecvNum;

private:
    static UART_DMA_MAP m_uartDmaMap[];
    static UART_PIN_MAP m_uartPinMap[];

    UART_MANAGER* m_pCurUartCtl;
    
    uint8_t  m_IsUartSending;

    uint8_t  m_SendBreakflag;

    uint8_t  m_UartRecvLockState;

    uint8_t* m_pRxBuffer;
    uint8_t* m_pTempBuffer;
    
    uint8_t  m_IntCount;
    uint32_t m_LostDataCount;
    
    CircularQueue* m_pRecvQueue;//循环队列，先申请空间，用于中断函数调用的接口

    //xQueueHandle   m_pSendQueue;//指针队列，用于将要发送的数据申请对应的内存后存放地址，不能用于中断


#if Use_Semaphore_Count
    SemaphoreHandle_t m_UartRecvLock;
#else
    uint8_t m_RecvLockFreeFlag;
#endif
    //SemaphoreHandle_t m_UartSendBufferLock;


};


#endif

