<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_fir_sparse_instance_q31 Struct Reference</title>
<title>CMSIS-DSP: arm_fir_sparse_instance_q31 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('structarm__fir__sparse__instance__q31.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">arm_fir_sparse_instance_q31 Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Instance structure for the Q31 sparse FIR filter.  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a07b6c01e58ec6dde384719130d36b0dc"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__fir__sparse__instance__q31.html#a07b6c01e58ec6dde384719130d36b0dc">numTaps</a></td></tr>
<tr class="separator:a07b6c01e58ec6dde384719130d36b0dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a557ed9d477e76e4ad2019344f19f568a"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__fir__sparse__instance__q31.html#a557ed9d477e76e4ad2019344f19f568a">stateIndex</a></td></tr>
<tr class="separator:a557ed9d477e76e4ad2019344f19f568a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a830be89daa5a393b225048889aa045d1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__fir__sparse__instance__q31.html#a830be89daa5a393b225048889aa045d1">pState</a></td></tr>
<tr class="separator:a830be89daa5a393b225048889aa045d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a093d6227f0d1597982cd083fb126f4e0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__fir__sparse__instance__q31.html#a093d6227f0d1597982cd083fb126f4e0">pCoeffs</a></td></tr>
<tr class="separator:a093d6227f0d1597982cd083fb126f4e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afdd3a1dc72132c854dc379154b68b674"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__fir__sparse__instance__q31.html#afdd3a1dc72132c854dc379154b68b674">maxDelay</a></td></tr>
<tr class="separator:afdd3a1dc72132c854dc379154b68b674"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab87ae457adec8f727afefaa2599fc983"><td class="memItemLeft" align="right" valign="top">int32_t *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__fir__sparse__instance__q31.html#ab87ae457adec8f727afefaa2599fc983">pTapDelay</a></td></tr>
<tr class="separator:ab87ae457adec8f727afefaa2599fc983"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="afdd3a1dc72132c854dc379154b68b674"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t arm_fir_sparse_instance_q31::maxDelay</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>maximum offset specified by the pTapDelay array. </p>

<p>Referenced by <a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_fir_sparse_init_q31()</a>, and <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a07b6c01e58ec6dde384719130d36b0dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t arm_fir_sparse_instance_q31::numTaps</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>number of coefficients in the filter. </p>

<p>Referenced by <a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_fir_sparse_init_q31()</a>, and <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a093d6227f0d1597982cd083fb126f4e0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>* arm_fir_sparse_instance_q31::pCoeffs</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the coefficient array. The array is of length numTaps. </p>

<p>Referenced by <a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_fir_sparse_init_q31()</a>, and <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a830be89daa5a393b225048889aa045d1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>* arm_fir_sparse_instance_q31::pState</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the state buffer array. The array is of length maxDelay+blockSize-1. </p>

<p>Referenced by <a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_fir_sparse_init_q31()</a>, and <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ab87ae457adec8f727afefaa2599fc983"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t* arm_fir_sparse_instance_q31::pTapDelay</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the array of delay values. The array is of length numTaps. </p>

<p>Referenced by <a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_fir_sparse_init_q31()</a>, and <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a557ed9d477e76e4ad2019344f19f568a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t arm_fir_sparse_instance_q31::stateIndex</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>state buffer index. Points to the oldest sample in the state buffer. </p>

<p>Referenced by <a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_fir_sparse_init_q31()</a>, and <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structarm__fir__sparse__instance__q31.html">arm_fir_sparse_instance_q31</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
