<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li class="current"><a href="globals.html#index__"><span>_</span></a></li>
      <li><a href="globals_0x61.html#index_a"><span>a</span></a></li>
      <li><a href="globals_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="globals_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="globals_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="globals_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index__"></a>- _ -</h3><ul>
<li>__CMSIS_GENERIC
: <a class="el" href="arm__math_8h.html#a87c3b351c33a90de11a2f23e67867a8a">arm_math.h</a>
</li>
<li>__HSI
: <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_class_marks_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_class_marks_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_class_marks_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_dotproduct_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fir_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fir_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_dotproduct_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_convolution_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_dotproduct_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_dotproduct_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_linear_interp_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_linear_interp_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_class_marks_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_convolution_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_dotproduct_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_linear_interp_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_linear_interp_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_dotproduct_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_matrix_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_matrix_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_convolution_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_dotproduct_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_matrix_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_matrix_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fft_bin_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_signal_converge_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_signal_converge_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_class_marks_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_class_marks_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_convolution_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fft_bin_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_signal_converge_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_signal_converge_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fft_bin_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_sin_cos_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_sin_cos_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_convolution_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fft_bin_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_sin_cos_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_sin_cos_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fft_bin_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_variance_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_variance_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_class_marks_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_convolution_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fft_bin_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_variance_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_variance_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fft_bin_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_convolution_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fir_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#abbd628d8a30e6695b3715ae72a693e56">arm_fir_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
</li>
<li>__PACKq7
: <a class="el" href="arm__math_8h.html#a3ebff224ad44c217fde9f530342e2960">arm_math.h</a>
</li>
<li>__SIMD32
: <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">arm_math.h</a>
</li>
<li>__SIMD32_CONST
: <a class="el" href="arm__math_8h.html#a1185d670d798aaf52eec13f0403f3407">arm_math.h</a>
</li>
<li>__SIMD64
: <a class="el" href="arm__math_8h.html#ad1b053da364f9fd82ca1a381df7590b6">arm_math.h</a>
</li>
<li>__SYSTEM_CLOCK
: <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_class_marks_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_class_marks_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_class_marks_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_sin_cos_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_class_marks_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_class_marks_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_convolution_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_signal_converge_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_convolution_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_convolution_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_convolution_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_matrix_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_convolution_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_convolution_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_convolution_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_dotproduct_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_dotproduct_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_dotproduct_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_dotproduct_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_dotproduct_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_dotproduct_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_dotproduct_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fir_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fft_bin_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fft_bin_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fft_bin_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fft_bin_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fft_bin_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fft_bin_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fft_bin_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fir_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fir_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_fir_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_linear_interp_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_linear_interp_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_linear_interp_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_linear_interp_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_matrix_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_matrix_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_matrix_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_signal_converge_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_signal_converge_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_signal_converge_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_sin_cos_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_sin_cos_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_sin_cos_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_variance_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_variance_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_variance_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_variance_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_class_marks_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a16323f44d2b5b11ef3972f71339cbd39">arm_class_marks_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
</li>
<li>__XTAL
: <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_variance_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_matrix_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_class_marks_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_matrix_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_sin_cos_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_sin_cos_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_convolution_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_signal_converge_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_signal_converge_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_convolution_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_dotproduct_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_linear_interp_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_linear_interp_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_fir_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_fir_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_fir_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_fft_bin_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_fft_bin_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_fft_bin_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_dotproduct_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_matrix_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_dotproduct_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_convolution_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_fft_bin_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_fft_bin_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_class_marks_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_class_marks_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_convolution_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_variance_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_variance_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_signal_converge_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_signal_converge_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_dotproduct_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_fft_bin_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_linear_interp_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_fir_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_convolution_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_dotproduct_example/GCC/Startup/system_ARMCM0.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_convolution_example/GCC/Startup/system_ARMCM4.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_dotproduct_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_class_marks_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_class_marks_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_sin_cos_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_dotproduct_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a8687edecd98881631a879bd10528c7da">arm_matrix_example/ARM/RTE/Device/ARMCM0/system_ARMCM0.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_graphic_equalizer_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_fft_bin_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_convolution_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_class_marks_example/GCC/Startup/system_ARMCM3.c</a>
, <a class="el" href="arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html#a8687edecd98881631a879bd10528c7da">arm_variance_example/ARM/RTE/Device/ARMCM3/system_ARMCM3.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html#a8687edecd98881631a879bd10528c7da">arm_linear_interp_example/ARM/RTE/Device/ARMCM7_SP/system_ARMCM7.c</a>
, <a class="el" href="arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_sin_cos_example/ARM/RTE/Device/ARMCM4_FP/system_ARMCM4.c</a>
, <a class="el" href="arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html#a8687edecd98881631a879bd10528c7da">arm_class_marks_example/GCC/Startup/system_ARMCM4.c</a>
</li>
<li>_SIMD32_OFFSET
: <a class="el" href="arm__math_8h.html#af0d54ec57b936994a34f073d0049ea3f">arm_math.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
