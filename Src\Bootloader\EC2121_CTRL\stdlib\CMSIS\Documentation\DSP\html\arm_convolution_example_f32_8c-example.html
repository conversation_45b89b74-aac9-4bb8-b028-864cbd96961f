<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_convolution_example_f32.c</title>
<title>CMSIS-DSP: arm_convolution_example_f32.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_convolution_example_f32_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_convolution_example_f32.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* $Date:         17. January 2013</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.0</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment">* Title:         arm_convolution_example_f32.c</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Description:   Example code demonstrating Convolution of two input signals using fft.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment">* -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;math_helper.h&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Defines each of the tests performed</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="preprocessor">#define MAX_BLOCKSIZE   128</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define DELTA           (0.000001f)</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define SNR_THRESHOLD   90</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Declare I/O buffers</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">Ak</a>[<a name="a1"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>];        <span class="comment">/* Input A */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a2"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3">Bk</a>[<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>];        <span class="comment">/* Input B */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a3"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098">AxB</a>[<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a> * 2];   <span class="comment">/* Output */</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Test input data for Floating point Convolution example for 32-blockSize</span></div>
<div class="line"><span class="comment">* Generated by the MATLAB randn() function</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a4"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7db2f016e1afcb524a2fdc3c5a3cb640">testInputA_f32</a>[64] =</div>
<div class="line">{</div>
<div class="line">  -0.808920,   1.357369,   1.180861,  -0.504544,   1.762637,  -0.703285,</div>
<div class="line">   1.696966,   0.620571,  -0.151093,  -0.100235,  -0.872382,  -0.403579,</div>
<div class="line">  -0.860749,  -0.382648,  -1.052338,   0.128113,  -0.646269,   1.093377,</div>
<div class="line">  -2.209198,   0.471706,   0.408901,   1.266242,   0.598252,   1.176827,</div>
<div class="line">  -0.203421,   0.213596,  -0.851964,  -0.466958,   0.021841,  -0.698938,</div>
<div class="line">  -0.604107,   0.461778,  -0.318219,   0.942520,   0.577585,   0.417619,</div>
<div class="line">   0.614665,   0.563679,  -1.295073,  -0.764437,   0.952194,  -0.859222,</div>
<div class="line">  -0.618554,  -2.268542,  -1.210592,   1.655853,  -2.627219,  -0.994249,</div>
<div class="line">  -1.374704,   0.343799,   0.025619,   1.227481,  -0.708031,   0.069355,</div>
<div class="line">  -1.845228,  -1.570886,   1.010668,  -1.802084,   1.630088,   1.286090,</div>
<div class="line">  -0.161050,  -0.940794,   0.367961,   0.291907</div>
<div class="line"></div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a5"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#acb22287e7e096b677e352dfd363ba60d">testInputB_f32</a>[64] =</div>
<div class="line">{</div>
<div class="line">   0.933724,   0.046881,   1.316470,   0.438345,   0.332682,   2.094885,</div>
<div class="line">   0.512081,   0.035546,   0.050894,  -2.320371,   0.168711,  -1.830493,</div>
<div class="line">  -0.444834,  -1.003242,  -0.531494,  -1.365600,  -0.155420,  -0.757692,</div>
<div class="line">  -0.431880,  -0.380021,   0.096243,  -0.695835,   0.558850,  -1.648962,</div>
<div class="line">   0.020369,  -0.363630,   0.887146,   0.845503,  -0.252864,  -0.330397,</div>
<div class="line">   1.269131,  -1.109295,  -1.027876,   0.135940,   0.116721,  -0.293399,</div>
<div class="line">  -1.349799,   0.166078,  -0.802201,   0.369367,  -0.964568,  -2.266011,</div>
<div class="line">   0.465178,   0.651222,  -0.325426,   0.320245,  -0.784178,  -0.579456,</div>
<div class="line">   0.093374,   0.604778,  -0.048225,   0.376297,  -0.394412,   0.578182,</div>
<div class="line">  -1.218141,  -1.387326,   0.692462,  -0.631297,   0.153137,  -0.638952,</div>
<div class="line">  0.635474,   -0.970468,   1.334057,  -0.111370</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><span class="keyword">const</span> <span class="keywordtype">float</span> <a name="a6"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">testRefOutput_f32</a>[127] =</div>
<div class="line">{</div>
<div class="line">   -0.818943,    1.229484,  -0.533664,    1.016604,   0.341875,  -1.963656,</div>
<div class="line">    5.171476,    3.478033,   7.616361,    6.648384,   0.479069,   1.792012,</div>
<div class="line">   -1.295591,   -7.447818,   0.315830,  -10.657445,  -2.483469,  -6.524236,</div>
<div class="line">   -7.380591,   -3.739005,  -8.388957,    0.184147,  -1.554888,   3.786508,</div>
<div class="line">   -1.684421,    5.400610,  -1.578126,    7.403361,   8.315999,   2.080267,</div>
<div class="line">   11.077776,    2.749673,   7.138962,    2.748762,   0.660363,   0.981552,</div>
<div class="line">    1.442275,    0.552721,  -2.576892,    4.703989,   0.989156,   8.759344,</div>
<div class="line">   -0.564825,   -3.994680,   0.954710,   -5.014144,   6.592329,   1.599488,</div>
<div class="line">  -13.979146,   -0.391891,  -4.453369,   -2.311242,  -2.948764,   1.761415,</div>
<div class="line">   -0.138322,   10.433007,  -2.309103,    4.297153,   8.535523,   3.209462,</div>
<div class="line">    8.695819,    5.569919,   2.514304,    5.582029,   2.060199,   0.642280,</div>
<div class="line">    7.024616,    1.686615,  -6.481756,    1.343084,  -3.526451,   1.099073,</div>
<div class="line">   -2.965764,   -0.173723,  -4.111484,    6.528384,  -6.965658,   1.726291,</div>
<div class="line">    1.535172,   11.023435,   2.338401,   -4.690188,   1.298210,   3.943885,</div>
<div class="line">    8.407885,    5.168365,   0.684131,    1.559181,   1.859998,   2.852417,</div>
<div class="line">    8.574070,   -6.369078,   6.023458,   11.837963,  -6.027632,   4.469678,</div>
<div class="line">   -6.799093,   -2.674048,   6.250367,   -6.809971,  -3.459360,   9.112410,</div>
<div class="line">   -2.711621,   -1.336678,   1.564249,   -1.564297,  -1.296760,   8.904013,</div>
<div class="line">   -3.230109,    6.878013,  -7.819823,    3.369909,  -1.657410,  -2.007358,</div>
<div class="line">   -4.112825,    1.370685,  -3.420525,   -6.276605,   3.244873,  -3.352638,</div>
<div class="line">    1.545372,    0.902211,   0.197489,   -1.408732,   0.523390,   0.348440, 0</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Declare Global variables</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line">uint32_t <a name="a7"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a> = 64;   <span class="comment">/* Length of Input A */</span></div>
<div class="line">uint32_t <a name="a8"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a> = 64;   <span class="comment">/* Length of Input B */</span></div>
<div class="line">uint32_t <a name="a9"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a9c49c44c8bc5c432d220d33a26b4b589">outLen</a>;         <span class="comment">/* Length of convolution output */</span></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a10"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a>;           <span class="comment">/* output SNR */</span></div>
<div class="line"></div>
<div class="line">int32_t <a name="a11"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a12"></a><a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>;                           <span class="comment">/* Status of the example */</span></div>
<div class="line">  <a name="_a13"></a><a class="code" href="structarm__cfft__radix4__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_radix4_instance_f32</a> cfft_instance;  <span class="comment">/* CFFT Structure instance */</span></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* CFFT Structure instance pointer */</span></div>
<div class="line">  <a class="code" href="structarm__cfft__radix4__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_radix4_instance_f32</a> *cfft_instance_ptr =</div>
<div class="line">      (<a class="code" href="structarm__cfft__radix4__instance__f32.html" title="Instance structure for the floating-point CFFT/CIFFT function.">arm_cfft_radix4_instance_f32</a>*) &amp;cfft_instance;</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* output length of convolution */</span></div>
<div class="line">  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a9c49c44c8bc5c432d220d33a26b4b589">outLen</a> = <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a> + <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#aea71286f498978c5ed3775609b974fc8">srcBLen</a> - 1;</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialise the fft input buffers with all zeros */</span></div>
<div class="line">  <a name="a14"></a><a class="code" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94" title="Fills a constant value into a floating-point vector.">arm_fill_f32</a>(0.0,  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">Ak</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>);</div>
<div class="line">  <a class="code" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94" title="Fills a constant value into a floating-point vector.">arm_fill_f32</a>(0.0,  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3">Bk</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Copy the input values to the fft input buffers */</span></div>
<div class="line">  <a name="a15"></a><a class="code" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3" title="Copies the elements of a floating-point vector.">arm_copy_f32</a>(<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7db2f016e1afcb524a2fdc3c5a3cb640">testInputA_f32</a>,  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">Ak</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>/2);</div>
<div class="line">  <a class="code" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3" title="Copies the elements of a floating-point vector.">arm_copy_f32</a>(<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#acb22287e7e096b677e352dfd363ba60d">testInputB_f32</a>,  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3">Bk</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>/2);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialize the CFFT function to compute 64 point fft */</span></div>
<div class="line">  status = <a name="a16"></a><a class="code" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a" title="Initialization function for the floating-point CFFT/CIFFT.">arm_cfft_radix4_init_f32</a>(cfft_instance_ptr, 64, 0, 1);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Transform input a[n] from time domain to frequency domain A[k] */</span></div>
<div class="line">  <a name="a17"></a><a class="code" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26" title="Processing function for the floating-point Radix-4 CFFT/CIFFT.">arm_cfft_radix4_f32</a>(cfft_instance_ptr, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">Ak</a>);</div>
<div class="line">  <span class="comment">/* Transform input b[n] from time domain to frequency domain B[k] */</span></div>
<div class="line">  <a class="code" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26" title="Processing function for the floating-point Radix-4 CFFT/CIFFT.">arm_cfft_radix4_f32</a>(cfft_instance_ptr, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3">Bk</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Complex Multiplication of the two input buffers in frequency domain */</span></div>
<div class="line">  <a name="a18"></a><a class="code" href="group___cmplx_by_cmplx_mult.html#ga14b47080054a1ba1250a86805be1ff6b" title="Floating-point complex-by-complex multiplication.">arm_cmplx_mult_cmplx_f32</a>(<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705">Ak</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3">Bk</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098">AxB</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>/2);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialize the CIFFT function to compute 64 point ifft */</span></div>
<div class="line">  status = <a class="code" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a" title="Initialization function for the floating-point CFFT/CIFFT.">arm_cfft_radix4_init_f32</a>(cfft_instance_ptr, 64, 1, 1);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Transform the multiplication output from frequency domain to time domain,</span></div>
<div class="line"><span class="comment">     that gives the convolved output  */</span></div>
<div class="line">  <a class="code" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26" title="Processing function for the floating-point Radix-4 CFFT/CIFFT.">arm_cfft_radix4_f32</a>(cfft_instance_ptr, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098">AxB</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* SNR Calculation */</span></div>
<div class="line">  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a> = <a name="a19"></a><a class="code" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab" title="Caluclation of SNR.">arm_snr_f32</a>((<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">testRefOutput_f32</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098">AxB</a>, <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#ace48ed566e2cd6a680f0681192e6af28">srcALen</a> + srcBLen - 1);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Compare the SNR with threshold to test whether the</span></div>
<div class="line"><span class="comment">     computed output is matched with the reference output values. */</span></div>
<div class="line">  <span class="keywordflow">if</span>( <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a> &gt; <a name="a20"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af08ec3fef897d77c6817638bf0e0c5c6">SNR_THRESHOLD</a>)</div>
<div class="line">  {</div>
<div class="line">    status = <a name="a21"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>;</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span>( status != <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>)</div>
<div class="line">  {</div>
<div class="line">    <span class="keywordflow">while</span>(1);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
<div class="line"></div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
