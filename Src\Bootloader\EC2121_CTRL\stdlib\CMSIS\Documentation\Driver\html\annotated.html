<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Data Structures</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="classes.html"><span>Data&#160;Structure&#160;Index</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('annotated.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Data Structures</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here are the data structures with brief descriptions:</div><div class="directory">
<table class="directory">
<tr id="row_0_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c" target="_self">ARM_DRIVER_ETH_MAC</a></td><td class="desc">Access structure of the Ethernet MAC Driver</td></tr>
<tr id="row_1_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__eth__phy__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___p_h_y" target="_self">ARM_DRIVER_ETH_PHY</a></td><td class="desc">Access structure of the Ethernet PHY Driver</td></tr>
<tr id="row_2_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___f_l_a_s_h" target="_self">ARM_DRIVER_FLASH</a></td><td class="desc">Access structure of the Flash Driver</td></tr>
<tr id="row_3_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__i2c__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___i2_c" target="_self">ARM_DRIVER_I2C</a></td><td class="desc">Access structure of the I2C Driver</td></tr>
<tr id="row_4_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__mci__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___m_c_i" target="_self">ARM_DRIVER_MCI</a></td><td class="desc">Access structure of the MCI Driver</td></tr>
<tr id="row_5_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___n_a_n_d" target="_self">ARM_DRIVER_NAND</a></td><td class="desc">Access structure of the NAND Driver</td></tr>
<tr id="row_6_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___s_p_i" target="_self">ARM_DRIVER_SPI</a></td><td class="desc">Access structure of the SPI Driver</td></tr>
<tr id="row_7_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usart__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_a_r_t" target="_self">ARM_DRIVER_USART</a></td><td class="desc">Access structure of the USART Driver</td></tr>
<tr id="row_8_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usbd__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_d" target="_self">ARM_DRIVER_USBD</a></td><td class="desc">Access structure of the USB Device Driver</td></tr>
<tr id="row_9_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usbh__host__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_h" target="_self">ARM_DRIVER_USBH</a></td><td class="desc">Access structure of USB Host Driver</td></tr>
<tr id="row_10_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usbh__hci__gr.html#struct_a_r_m___d_r_i_v_e_r___u_s_b_h___h_c_i" target="_self">ARM_DRIVER_USBH_HCI</a></td><td class="desc">Access structure of USB Host HCI (OHCI/EHCI) Driver</td></tr>
<tr id="row_11_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n" target="_self">ARM_DRIVER_VERSION</a></td><td class="desc">Driver Version</td></tr>
<tr id="row_12_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___l_i_n_k___i_n_f_o" target="_self">ARM_ETH_LINK_INFO</a></td><td class="desc">Ethernet link information</td></tr>
<tr id="row_13_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r" target="_self">ARM_ETH_MAC_ADDR</a></td><td class="desc">Ethernet MAC Address</td></tr>
<tr id="row_14_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_ETH_MAC_CAPABILITIES</a></td><td class="desc">Ethernet MAC Capabilities</td></tr>
<tr id="row_15_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e" target="_self">ARM_ETH_MAC_TIME</a></td><td class="desc">Ethernet MAC Time</td></tr>
<tr id="row_16_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_FLASH_CAPABILITIES</a></td><td class="desc">Flash Driver Capabilities</td></tr>
<tr id="row_17_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___i_n_f_o" target="_self">ARM_FLASH_INFO</a></td><td class="desc">Flash information</td></tr>
<tr id="row_18_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_e_c_t_o_r" target="_self">ARM_FLASH_SECTOR</a></td><td class="desc">Flash Sector information</td></tr>
<tr id="row_19_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__flash__interface__gr.html#struct_a_r_m___f_l_a_s_h___s_t_a_t_u_s" target="_self">ARM_FLASH_STATUS</a></td><td class="desc">Flash Status</td></tr>
<tr id="row_20_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__i2c__interface__gr.html#struct_a_r_m___i2_c___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_I2C_CAPABILITIES</a></td><td class="desc">I2C Driver Capabilities</td></tr>
<tr id="row_21_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__i2c__interface__gr.html#struct_a_r_m___i2_c___s_t_a_t_u_s" target="_self">ARM_I2C_STATUS</a></td><td class="desc">I2C Status</td></tr>
<tr id="row_22_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__mci__interface__gr.html#struct_a_r_m___m_c_i___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_MCI_CAPABILITIES</a></td><td class="desc">MCI Driver Capabilities</td></tr>
<tr id="row_23_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__mci__interface__gr.html#struct_a_r_m___m_c_i___s_t_a_t_u_s" target="_self">ARM_MCI_STATUS</a></td><td class="desc">MCI Status</td></tr>
<tr id="row_24_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_NAND_CAPABILITIES</a></td><td class="desc">NAND Driver Capabilities</td></tr>
<tr id="row_25_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___e_c_c___i_n_f_o" target="_self">ARM_NAND_ECC_INFO</a></td><td class="desc">NAND ECC (Error Correction Code) Information</td></tr>
<tr id="row_26_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__nand__interface__gr.html#struct_a_r_m___n_a_n_d___s_t_a_t_u_s" target="_self">ARM_NAND_STATUS</a></td><td class="desc">NAND Status</td></tr>
<tr id="row_27_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_SPI_CAPABILITIES</a></td><td class="desc">SPI Driver Capabilities</td></tr>
<tr id="row_28_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__spi__interface__gr.html#struct_a_r_m___s_p_i___s_t_a_t_u_s" target="_self">ARM_SPI_STATUS</a></td><td class="desc">SPI Status</td></tr>
<tr id="row_29_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_USART_CAPABILITIES</a></td><td class="desc">USART Device Driver Capabilities</td></tr>
<tr id="row_30_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___m_o_d_e_m___s_t_a_t_u_s" target="_self">ARM_USART_MODEM_STATUS</a></td><td class="desc">USART Modem Status</td></tr>
<tr id="row_31_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usart__interface__gr.html#struct_a_r_m___u_s_a_r_t___s_t_a_t_u_s" target="_self">ARM_USART_STATUS</a></td><td class="desc">USART Status</td></tr>
<tr id="row_32_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usbd__interface__gr.html#struct_a_r_m___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_USBD_CAPABILITIES</a></td><td class="desc">USB Device Driver Capabilities</td></tr>
<tr id="row_33_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usbd__interface__gr.html#struct_a_r_m___u_s_b_d___s_t_a_t_e" target="_self">ARM_USBD_STATE</a></td><td class="desc">USB Device State</td></tr>
<tr id="row_34_" class="even"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usbh__host__gr.html#struct_a_r_m___u_s_b_h___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_USBH_CAPABILITIES</a></td><td class="desc">USB Host Driver Capabilities</td></tr>
<tr id="row_35_"><td class="entry"><img src="ftv2node.png" alt="o" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usbh__hci__gr.html#struct_a_r_m___u_s_b_h___h_c_i___c_a_p_a_b_i_l_i_t_i_e_s" target="_self">ARM_USBH_HCI_CAPABILITIES</a></td><td class="desc">USB Host HCI (OHCI/EHCI) Driver Capabilities</td></tr>
<tr id="row_36_" class="even"><td class="entry"><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><img src="ftv2cl.png" alt="C" width="24" height="22" /><a class="el" href="group__usbh__host__gr.html#struct_a_r_m___u_s_b_h___p_o_r_t___s_t_a_t_e" target="_self">ARM_USBH_PORT_STATE</a></td><td class="desc">USB Host Port State</td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
