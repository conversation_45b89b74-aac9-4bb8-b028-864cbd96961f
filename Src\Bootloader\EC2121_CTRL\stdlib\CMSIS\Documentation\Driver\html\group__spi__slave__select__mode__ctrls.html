<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: SPI Slave Select Mode</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__spi__slave__select__mode__ctrls.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">SPI Slave Select Mode<div class="ingroups"><a class="el" href="group___s_p_i__control.html">SPI Control Codes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Specifies SPI slave select mode.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gae19343adc7bd71408b51733171f99dc7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__slave__select__mode__ctrls.html#gae19343adc7bd71408b51733171f99dc7">ARM_SPI_SS_MASTER_UNUSED</a>&#160;&#160;&#160;(0UL &lt;&lt; ARM_SPI_SS_MASTER_MODE_Pos)</td></tr>
<tr class="memdesc:gae19343adc7bd71408b51733171f99dc7"><td class="mdescLeft">&#160;</td><td class="mdescRight">SPI Slave Select when Master: Not used (default)  <a href="#gae19343adc7bd71408b51733171f99dc7">More...</a><br/></td></tr>
<tr class="separator:gae19343adc7bd71408b51733171f99dc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab5e319aa3f9d4d8c9ed92f0fe865f624"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__slave__select__mode__ctrls.html#gab5e319aa3f9d4d8c9ed92f0fe865f624">ARM_SPI_SS_MASTER_SW</a>&#160;&#160;&#160;(1UL &lt;&lt; ARM_SPI_SS_MASTER_MODE_Pos)</td></tr>
<tr class="memdesc:gab5e319aa3f9d4d8c9ed92f0fe865f624"><td class="mdescLeft">&#160;</td><td class="mdescRight">SPI Slave Select when Master: Software controlled.  <a href="#gab5e319aa3f9d4d8c9ed92f0fe865f624">More...</a><br/></td></tr>
<tr class="separator:gab5e319aa3f9d4d8c9ed92f0fe865f624"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07762709a40dc90aca85553f500c8761"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__slave__select__mode__ctrls.html#ga07762709a40dc90aca85553f500c8761">ARM_SPI_SS_MASTER_HW_OUTPUT</a>&#160;&#160;&#160;(2UL &lt;&lt; ARM_SPI_SS_MASTER_MODE_Pos)</td></tr>
<tr class="memdesc:ga07762709a40dc90aca85553f500c8761"><td class="mdescLeft">&#160;</td><td class="mdescRight">SPI Slave Select when Master: Hardware controlled Output.  <a href="#ga07762709a40dc90aca85553f500c8761">More...</a><br/></td></tr>
<tr class="separator:ga07762709a40dc90aca85553f500c8761"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8561bd0cc25ab2bb02b138c1c6a586cd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__slave__select__mode__ctrls.html#ga8561bd0cc25ab2bb02b138c1c6a586cd">ARM_SPI_SS_MASTER_HW_INPUT</a>&#160;&#160;&#160;(3UL &lt;&lt; ARM_SPI_SS_MASTER_MODE_Pos)</td></tr>
<tr class="memdesc:ga8561bd0cc25ab2bb02b138c1c6a586cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">SPI Slave Select when Master: Hardware monitored Input.  <a href="#ga8561bd0cc25ab2bb02b138c1c6a586cd">More...</a><br/></td></tr>
<tr class="separator:ga8561bd0cc25ab2bb02b138c1c6a586cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2bd0d1f3ade2dc0cc48cc0593336ad70"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__slave__select__mode__ctrls.html#ga2bd0d1f3ade2dc0cc48cc0593336ad70">ARM_SPI_SS_SLAVE_HW</a>&#160;&#160;&#160;(0UL &lt;&lt; ARM_SPI_SS_SLAVE_MODE_Pos)</td></tr>
<tr class="memdesc:ga2bd0d1f3ade2dc0cc48cc0593336ad70"><td class="mdescLeft">&#160;</td><td class="mdescRight">SPI Slave Select when Slave: Hardware monitored (default)  <a href="#ga2bd0d1f3ade2dc0cc48cc0593336ad70">More...</a><br/></td></tr>
<tr class="separator:ga2bd0d1f3ade2dc0cc48cc0593336ad70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad371f6ba0d12a57bdcc3217c351abfb0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__slave__select__mode__ctrls.html#gad371f6ba0d12a57bdcc3217c351abfb0">ARM_SPI_SS_SLAVE_SW</a>&#160;&#160;&#160;(1UL &lt;&lt; ARM_SPI_SS_SLAVE_MODE_Pos)</td></tr>
<tr class="memdesc:gad371f6ba0d12a57bdcc3217c351abfb0"><td class="mdescLeft">&#160;</td><td class="mdescRight">SPI Slave Select when Slave: Software controlled.  <a href="#gad371f6ba0d12a57bdcc3217c351abfb0">More...</a><br/></td></tr>
<tr class="separator:gad371f6ba0d12a57bdcc3217c351abfb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Specifies SPI slave select mode. </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="ga8561bd0cc25ab2bb02b138c1c6a586cd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_SS_MASTER_HW_INPUT&#160;&#160;&#160;(3UL &lt;&lt; ARM_SPI_SS_MASTER_MODE_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>SPI Slave Select when Master: Hardware monitored Input. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga07762709a40dc90aca85553f500c8761"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_SS_MASTER_HW_OUTPUT&#160;&#160;&#160;(2UL &lt;&lt; ARM_SPI_SS_MASTER_MODE_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>SPI Slave Select when Master: Hardware controlled Output. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab5e319aa3f9d4d8c9ed92f0fe865f624"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_SS_MASTER_SW&#160;&#160;&#160;(1UL &lt;&lt; ARM_SPI_SS_MASTER_MODE_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>SPI Slave Select when Master: Software controlled. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae19343adc7bd71408b51733171f99dc7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_SS_MASTER_UNUSED&#160;&#160;&#160;(0UL &lt;&lt; ARM_SPI_SS_MASTER_MODE_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>SPI Slave Select when Master: Not used (default) </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2bd0d1f3ade2dc0cc48cc0593336ad70"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_SS_SLAVE_HW&#160;&#160;&#160;(0UL &lt;&lt; ARM_SPI_SS_SLAVE_MODE_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>SPI Slave Select when Slave: Hardware monitored (default) </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad371f6ba0d12a57bdcc3217c351abfb0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_SS_SLAVE_SW&#160;&#160;&#160;(1UL &lt;&lt; ARM_SPI_SS_SLAVE_MODE_Pos)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>SPI Slave Select when Slave: Software controlled. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a" title="Control SPI Interface.">ARM_SPI_Control</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
