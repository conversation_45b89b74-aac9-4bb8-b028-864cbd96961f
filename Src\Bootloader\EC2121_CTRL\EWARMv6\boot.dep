<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>1339340499</fileChecksum>
  <configuration>
    <name>WallBoxAC</name>
    <outputs>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash.c</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spi.c</file>
      <file>$TOOLKIT_DIR$\lib\dlpp7M_tl_ne.a</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c</file>
      <file>$PROJ_DIR$\..\boot\src\ErrorCode.cpp</file>
      <file>$PROJ_DIR$\..\boot\src\MAIN.CPP</file>
      <file>$PROJ_DIR$\VERSION.H</file>
      <file>$PROJ_DIR$\..\boot\src\CTIMER.CPP</file>
      <file>$PROJ_DIR$\..\boot\src\DebugConsole.cpp</file>
      <file>$PROJ_DIR$\..\boot\src\FlashInternal.cpp</file>
      <file>$PROJ_DIR$\..\boot\src\UpdateApp.cpp</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\misc.c</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Device\ST\STM32F4xx\Source\Templates\iar\startup_stm32f40xx.s</file>
      <file>$PROJ_DIR$\..\boot\src\InterruptUser.cpp</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c</file>
      <file>$PROJ_DIR$\..\boot\src\CRccClock.cpp</file>
      <file>$PROJ_DIR$\..\boot\src\FlashExternal.cpp</file>
      <file>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</file>
      <file>$PROJ_DIR$\..\boot\src\Data_CRC_Table.cpp</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_spi.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\ErrorCode.pbi</file>
      <file>$PROJ_DIR$\..\boot\inc\GLOBEL.H</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h</file>
      <file>$PROJ_DIR$\..\boot\inc\FlashInternal.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h</file>
      <file>$PROJ_DIR$\..\boot\inc\CRccClock.h</file>
      <file>$PROJ_DIR$\..\boot\inc\UpdateApp.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_flash.__cstat.et</file>
      <file>$TOOLKIT_DIR$\inc\c\cstdio</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_usart.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\List\CTimer.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\system_stm32f4xx.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\DebugConsole.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_spi.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_usart.s</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h</file>
      <file>$TOOLKIT_DIR$\lib\rt7M_tl.a</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h</file>
      <file>$TOOLKIT_DIR$\inc\c\xencoding_limits.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\main.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\Exe\WallBoxAC_BOOT.bin</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\CTIMER.pbi</file>
      <file>$TOOLKIT_DIR$\inc\c\DLib_Defaults.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_gpio.s</file>
      <file>$PROJ_DIR$\WallBoxAC\List\UpdateApp.lst</file>
      <file>$TOOLKIT_DIR$\inc\c\stdint.h</file>
      <file>$PROJ_DIR$\..\boot\inc\FlashExternal.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_dma.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_dma.pbi</file>
      <file>$TOOLKIT_DIR$\inc\c\ycheck.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_rcc.pbi</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Include\core_cmSimd.h</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx_conf.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_dma.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\InterruptUser.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\List\misc.lst</file>
      <file>$TOOLKIT_DIR$\lib\m7M_tls.a</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_gpio.__cstat.et</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\FlashInternal.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\Exe\WallBoxAC_BOOT.out</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_dma.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_tim.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\FlashExternal.pbi</file>
      <file>$TOOLKIT_DIR$\inc\c\intrinsics.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\CRccClock.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\UpdateApp.o</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_flash.pbi</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\DebugConsole.s</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_flash.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\ErrorCode.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_adc.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_tim.__cstat.et</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\misc.s</file>
      <file>$TOOLKIT_DIR$\lib\shb_l.a</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_rcc.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\FlashExternal.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_dma.lst</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Include\core_cm4.h</file>
      <file>$TOOLKIT_DIR$\lib\dl7M_tln.a</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Include\core_cmInstr.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_usart.lst</file>
      <file>$PROJ_DIR$\..\boot\inc\CTIMER.H</file>
      <file>$TOOLKIT_DIR$\inc\c\DLib_Product.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\CRccClock.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\FlashExternal.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_tim.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_usart.o</file>
      <file>$PROJ_DIR$\..\stdlib\CMSIS\Include\core_cmFunc.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_adc.__cstat.et</file>
      <file>$TOOLKIT_DIR$\inc\c\string.h</file>
      <file>$PROJ_DIR$\..\boot\inc\ErrorCode.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_spi.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\List\WallBoxAC_BOOT.map</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_usart.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\CTimer.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\List\CRccClock.s</file>
      <file>$PROJ_DIR$\..\boot\inc\InterruptUser.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_gpio.pbi</file>
      <file>$TOOLKIT_DIR$\inc\c\DLib_Threads.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_gpio.o</file>
      <file>$TOOLKIT_DIR$\inc\c\ystdio.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h</file>
      <file>$PROJ_DIR$\..\boot\inc\DebugConsole.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_tim.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\system_stm32f4xx.pbi</file>
      <file>$PROJ_DIR$\..\boot\inc\CircularQueue.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\FlashExternal.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_gpio.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_rcc.__cstat.et</file>
      <file>$TOOLKIT_DIR$\inc\c\yvals.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\misc.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\UpdateApp.pbi</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_spi.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\CRccClock.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\List\CRccClock.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\List\system_stm32f4xx.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\List\InterruptUser.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\FlashInternal.o</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_adc.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\InterruptUser.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\DebugConsole.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\FlashInternal.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\system_stm32f4xx.o</file>
      <file>$PROJ_DIR$\WallBoxAC\List\ErrorCode.s</file>
      <file>$PROJ_DIR$\WallBoxAC\List\InterruptUser.s</file>
      <file>$PROJ_DIR$\WallBoxAC\List\main.s</file>
      <file>$PROJ_DIR$\WallBoxAC\List\system_stm32f4xx.s</file>
      <file>$PROJ_DIR$\WallBoxAC\List\main.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_tim.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\misc.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\List\UpdateApp.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\UpdateApp.__cstat.et</file>
      <file>$PROJ_DIR$\WallBoxAC\List\ErrorCode.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\DebugConsole.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_flash.lst</file>
      <file>$PROJ_DIR$\..\boot\inc\CUART.H</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_flash.o</file>
      <file>$PROJ_DIR$\AutoChargerACBoot.icf</file>
      <file>$TOOLKIT_DIR$\inc\c\cmsis_iar.h</file>
      <file>$TOOLKIT_DIR$\inc\c\stdio.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\misc.h</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_adc.o</file>
      <file>$PROJ_DIR$\WallBoxAC\List\DebugConsole.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\MAIN.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\ErrorCode.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\InterruptUser.o</file>
      <file>$PROJ_DIR$\WallBoxAC\List\FlashInternal.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\List\CTimer.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\List\stm32f4xx_rcc.lst</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\MAIN.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\CTIMER.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\misc.o</file>
      <file>$TOOLKIT_DIR$\inc\c\DLib_Product_string.h</file>
      <file>$PROJ_DIR$\WallBoxAC\List\FlashExternal.s</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\boot.pbd</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_rcc.o</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_spi.o</file>
      <file>$PROJ_DIR$\WallBoxAC\List\FlashInternal.s</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\stm32f4xx_adc.pbi</file>
      <file>$PROJ_DIR$\WallBoxAC\Obj\startup_stm32f40xx.o</file>
      <file>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h</file>
      <file>$TOOLKIT_DIR$\inc\c\ysizet.h</file>
      <file>$PROJ_DIR$\WallBoxAC\Exe\ctrl_boot.bin</file>
    </outputs>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 81</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 158 160</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 34</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 27 69 58 132 97 54 75 165 31 95 121 88 112 30 62 107 102 36 186 91 164 68 135 28 61 98 60 51 47 22 101 162 80 46 82 179 115 124 45 79 78 43 108</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 28 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 36 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 59</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 92 173 181</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 131</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 27 30 88 95 121 132 112 54 58 75 165 97 31 69 62 107 102 36 186 91 164 68 135 61 98 60 51 47 22 101 162 80 46 28 82 179 115 124 45 79 78 43 108</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 186 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spi.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 113</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 24 136 182</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 41</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 58 82 27 43 47 101 45 60 46 115 186 61 98 51 22 162 80 28 179 124 79 78 108 95 54 62 107 132 121 75 165 102 88 36 97 112 91 31 164 30 68 69 135</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 124 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 91 45 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 120</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 52 130 122</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 67</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 27 28 162 78 61 51 22 124 98 80 179 79 108 186 95 54 60 58 47 101 46 82 115 45 43 36 62 107 132 121 75 165 102 88 97 112 91 31 164 30 68 69 135</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 36 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 116</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 42 99 106</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 37</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 121 112 132 95 88 30 54 58 75 165 97 31 69 27 186 62 107 102 36 91 164 68 135 61 98 60 51 47 22 101 162 80 46 28 82 179 115 124 45 79 78 43 108</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 31 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 124 91 45 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\ErrorCode.cpp</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 25</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 147 156 169</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 85</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 111 26 110 58 132 51 22 101 47 121 187 177 163 123</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\MAIN.CPP</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 168</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 149 151 174</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 48</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 163 58 132 51 22 101 47 121 187 123 110 177 100 26 27 95 54 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135 55 29 33 125 8</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\CTIMER.CPP</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 50</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 38 172 175</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 117</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 100 26 110 58 132 51 22 101 47 121 187 177 163 123 27 95 54 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135 119 125 32</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\DebugConsole.cpp</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 157</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 83 167 144</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 40</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 125 31 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 124 91 45 79 164 30 78 68 43 69 108 135 163 187 123 100 26 110 177 159 128 32</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\FlashInternal.cpp</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 70</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 183 171 141</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 145</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 29 26 110 58 132 51 22 101 47 121 187 177 163 123 8 111 35 28 27 95 54 98 162 75 107 60 61 62 165 80 102 46 88 36 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\UpdateApp.cpp</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 134</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 154 53 77</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 155</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 33 26 110 58 132 51 22 101 47 121 187 177 163 123 55 54 36 27 95 98 162 75 107 60 61 62 165 80 102 46 88 28 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135 29 111 125</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\misc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 153</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 89 65 176</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 133</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 95 102 27 107 164 62 186 135 54 36 91 68 61 132 58 121 75 165 88 97 112 31 30 69 98 60 51 47 22 101 162 80 46 28 82 179 115 124 45 79 78 43 108</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 164 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 124 91 45 31 79 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 184</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 142 86 166</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 109</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 27 31 75 54 165 132 58 97 69 95 121 88 112 30 186 62 107 102 36 91 164 68 135 61 98 60 51 47 22 101 162 80 46 28 82 179 115 124 45 79 78 43 108</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 165 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 80 102 46 88 28 36 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 57</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 56 94 63</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 72</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 27 43 58 82 47 101 45 60 46 115 186 61 98 51 22 162 80 28 179 124 79 78 108 95 54 62 107 132 121 75 165 102 88 36 97 112 91 31 164 30 68 69 135</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 46 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 88 28 36 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\CMSIS\Device\ST\STM32F4xx\Source\Templates\iar\startup_stm32f40xx.s</name>
      <outputs>
        <tool>
          <name>AARM</name>
          <file> 185</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\InterruptUser.cpp</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 64</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 148 140 170</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 143</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 100 26 110 58 132 51 22 101 47 121 187 177 163 123 27 95 54 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135 119 125</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 127</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 150 139 146</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 39</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 30 95 88 27 121 132 112 54 58 75 165 97 31 69 61 62 107 102 36 186 91 164 68 135 98 60 51 47 22 101 162 80 46 28 82 179 115 124 45 79 78 43 108</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\stdlib\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 105</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 126 152 73</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 87</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 30 88 27 95 121 132 112 54 58 75 165 97 31 69 186 62 107 102 36 91 164 68 135 45 61 98 60 51 47 22 101 162 80 46 28 82 179 115 124 79 78 43 108</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 45 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 186 115 112 124 91 31 79 164 30 78 68 43 69 108 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\CRccClock.cpp</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 103</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 118 138 76</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 137</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 32 186 27 95 54 58 132 51 22 101 47 121 98 162 75 107 60 61 62 165 80 102 46 88 28 36 82 97 179 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135 163 187 123 26 110 177</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\boot\src\FlashExternal.cpp</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 74</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 178 129 104</file>
        </tool>
        <tool>
          <name>__cstat</name>
          <file> 93</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 55 163 58 132 51 22 101 47 121 187 123 54 36 27 95 98 162 75 107 60 61 62 165 80 102 46 88 28 82 97 179 186 115 112 124 91 45 31 79 164 30 78 68 43 69 108 135 100 26 110 177 125</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>ILINK</name>
          <file> 114 71</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\WallBoxAC\Exe\WallBoxAC_BOOT.out</name>
      <outputs>
        <tool>
          <name>ILINK</name>
          <file> 114</file>
        </tool>
        <tool>
          <name>OBJCOPY</name>
          <file> 49 188</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ILINK</name>
          <file> 161 76 175 144 169 104 141 170 174 176 185 166 63 160 122 181 182 73 106 146 77 90 44 3 66 96</file>
        </tool>
      </inputs>
    </file>
    <forcedrebuild>
      <name>[REBUILD_ALL]</name>
    </forcedrebuild>
  </configuration>
</project>


