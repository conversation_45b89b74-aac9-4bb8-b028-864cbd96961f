<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__"><span>_</span></a></li>
      <li><a href="globals_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_0x65.html#index_e"><span>e</span></a></li>
      <li class="current"><a href="globals_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_0x75.html#index_u"><span>u</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_0x66.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index_f"></a>- f -</h3><ul>
<li>ARM_FLASH_API_VERSION
: <a class="el" href="_driver___flash_8h.html#a16e58dce04f3f65d5183c973c9189fa4">Driver_Flash.h</a>
</li>
<li>ARM_Flash_EraseChip()
: <a class="el" href="group__flash__interface__gr.html#ga6cbaebe069d31d56c70b1f8f847e2d55">Driver_Flash.c</a>
</li>
<li>ARM_Flash_EraseSector()
: <a class="el" href="group__flash__interface__gr.html#ga0b2b4fe5a7be579cf3644995a765ea20">Driver_Flash.c</a>
</li>
<li>ARM_FLASH_EVENT_ERROR
: <a class="el" href="group___flash__events.html#ga0dfea52761c0eed83e5d73e7a7f69962">Driver_Flash.h</a>
</li>
<li>ARM_FLASH_EVENT_READY
: <a class="el" href="group___flash__events.html#gaf7a9c4ad125ee90df35907d861151e23">Driver_Flash.h</a>
</li>
<li>ARM_Flash_GetCapabilities()
: <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">Driver_Flash.c</a>
</li>
<li>ARM_Flash_GetInfo()
: <a class="el" href="group__flash__interface__gr.html#gac047b7509356e888502e0424a9d189ae">Driver_Flash.c</a>
</li>
<li>ARM_Flash_GetStatus()
: <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">Driver_Flash.c</a>
</li>
<li>ARM_Flash_GetVersion()
: <a class="el" href="group__flash__interface__gr.html#ga1cfe24b2ffa571ee50ae544bd922b604">Driver_Flash.c</a>
</li>
<li>ARM_Flash_Initialize()
: <a class="el" href="group__flash__interface__gr.html#gaa5b4bbe529d620d4ad4825588a4c4cf0">Driver_Flash.c</a>
</li>
<li>ARM_Flash_PowerControl()
: <a class="el" href="group__flash__interface__gr.html#gaa8baa4618ea33568f8b3752afb2ab5a2">Driver_Flash.c</a>
</li>
<li>ARM_Flash_ProgramData()
: <a class="el" href="group__flash__interface__gr.html#ga947f24ea4042093fdb5605a68ae74f9d">Driver_Flash.c</a>
</li>
<li>ARM_Flash_ReadData()
: <a class="el" href="group__flash__interface__gr.html#ga223138342383219896ed7e255faeb99a">Driver_Flash.c</a>
</li>
<li>ARM_FLASH_SECTOR_INFO
: <a class="el" href="_driver___flash_8h.html#aaeb2830d80f8aca0eaf72e3718537a60">Driver_Flash.h</a>
</li>
<li>ARM_Flash_SignalEvent()
: <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">Driver_Flash.c</a>
</li>
<li>ARM_Flash_SignalEvent_t
: <a class="el" href="group__flash__interface__gr.html#gabeb4ad43b1e6fa4ed956cd5c9371d327">Driver_Flash.h</a>
</li>
<li>ARM_Flash_Uninitialize()
: <a class="el" href="group__flash__interface__gr.html#gae23af293e9f8a67cdb19c7d0d562d415">Driver_Flash.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
