<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Convolution Example</title>
<title>CMSIS-DSP: Convolution Example</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___convolution_example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Convolution Example</div>  </div>
<div class="ingroups"><a class="el" href="group__group_examples.html">Examples</a></div></div><!--header-->
<div class="contents">
<dl class="section user"><dt>Description:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>Demonstrates the convolution theorem with the use of the Complex FFT, Complex-by-Complex Multiplication, and Support Functions.</dd></dl>
<dl class="section user"><dt>Algorithm:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The convolution theorem states that convolution in the time domain corresponds to multiplication in the frequency domain. Therefore, the Fourier transform of the convoution of two signals is equal to the product of their individual Fourier transforms. The Fourier transform of a signal can be evaluated efficiently using the Fast Fourier Transform (FFT). </dd></dl>
<dl class="section user"><dt></dt><dd>Two input signals, <code>a[n]</code> and <code>b[n]</code>, with lengths <code>n1</code> and <code>n2</code> respectively, are zero padded so that their lengths become <code>N</code>, which is greater than or equal to <code>(n1+n2-1)</code> and is a power of 4 as FFT implementation is radix-4. The convolution of <code>a[n]</code> and <code>b[n]</code> is obtained by taking the FFT of the input signals, multiplying the Fourier transforms of the two signals, and taking the inverse FFT of the multiplied result. </dd></dl>
<dl class="section user"><dt></dt><dd>This is denoted by the following equations: <pre> A[k] = FFT(a[n],N)
B[k] = FFT(b[n],N)
conv(a[n], b[n]) = IFFT(A[k] * B[k], N)</pre> where <code>A[k]</code> and <code>B[k]</code> are the N-point FFTs of the signals <code>a[n]</code> and <code>b[n]</code> respectively. The length of the convolved signal is <code>(n1+n2-1)</code>.</dd></dl>
<dl class="section user"><dt>Block Diagram:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="Convolution.gif" alt="Convolution.gif"/>
</div>
</dd></dl>
<dl class="section user"><dt>Variables Description:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd><ul>
<li><code>testInputA_f32</code> points to the first input sequence </li>
<li><code>srcALen</code> length of the first input sequence </li>
<li><code>testInputB_f32</code> points to the second input sequence </li>
<li><code>srcBLen</code> length of the second input sequence </li>
<li><code>outLen</code> length of convolution output sequence, <code>(srcALen + srcBLen - 1)</code> </li>
<li><code>AxB</code> points to the output array where the product of individual FFTs of inputs is stored.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>CMSIS DSP Software Library Functions Used:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd><ul>
<li><a class="el" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94" title="Fills a constant value into a floating-point vector.">arm_fill_f32()</a></li>
<li><a class="el" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3" title="Copies the elements of a floating-point vector.">arm_copy_f32()</a></li>
<li><a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a" title="Initialization function for the floating-point CFFT/CIFFT.">arm_cfft_radix4_init_f32()</a></li>
<li><a class="el" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26" title="Processing function for the floating-point Radix-4 CFFT/CIFFT.">arm_cfft_radix4_f32()</a></li>
<li><a class="el" href="group___cmplx_by_cmplx_mult.html#ga14b47080054a1ba1250a86805be1ff6b" title="Floating-point complex-by-complex multiplication.">arm_cmplx_mult_cmplx_f32()</a></li>
</ul>
</dd></dl>
<p><b> Refer </b> <a class="el" href="arm_convolution_example_f32_8c-example.html">arm_convolution_example_f32.c</a> </p>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
