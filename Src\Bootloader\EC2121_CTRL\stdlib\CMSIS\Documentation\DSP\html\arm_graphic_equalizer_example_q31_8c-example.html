<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_graphic_equalizer_example_q31.c</title>
<title>CMSIS-DSP: arm_graphic_equalizer_example_q31.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_graphic_equalizer_example_q31_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_graphic_equalizer_example_q31.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* $Date:         17. January 2013</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.0</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment">* Title:         arm_graphic_equalizer_example_q31.c</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Description:   Example showing an audio graphic equalizer constructed</span></div>
<div class="line"><span class="comment">*                out of Biquad filters.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment"> * -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;math_helper.h&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* Length of the overall data in the test */</span></div>
<div class="line"><span class="preprocessor">#define TESTLENGTH 320</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* Block size for the underlying processing */</span></div>
<div class="line"><span class="preprocessor">#define BLOCKSIZE 32</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* Total number of blocks to run */</span></div>
<div class="line"><span class="preprocessor">#define NUMBLOCKS (TESTLENGTH/BLOCKSIZE)</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* Number of 2nd order Biquad stages per filter */</span></div>
<div class="line"><span class="preprocessor">#define NUMSTAGES 2</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor">#define SNR_THRESHOLD_F32  98</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* -------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * External Declarations for Input and Output buffers</span></div>
<div class="line"><span class="comment"> * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>[<a name="a1"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a4f4a95eaace4e7b4e2f5243ed24f6b28">TESTLENGTH</a>];</div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a2"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>[<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a4f4a95eaace4e7b4e2f5243ed24f6b28">TESTLENGTH</a>];</div>
<div class="line"></div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a3"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">testRefOutput_f32</a>[<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a4f4a95eaace4e7b4e2f5243ed24f6b28">TESTLENGTH</a>];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** Q31 state buffers for Band1, Band2, Band3, Band4, Band5</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#a5aea1cb12fc02d9d44c8abf217eaa5c6" title="64-bit fractional data type in 1.63 format.">q63_t</a> <a name="a4"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a8e7062fa3f8b5ed9849566d16270f4ec">biquadStateBand1Q31</a>[4 * 2];</div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#a5aea1cb12fc02d9d44c8abf217eaa5c6" title="64-bit fractional data type in 1.63 format.">q63_t</a> <a name="a5"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a4a82090b15ebd0a45048d94f16131782">biquadStateBand2Q31</a>[4 * 2];</div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> <a name="a6"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#ad487a54340631b764952fccf599adc8a">biquadStateBand3Q31</a>[4 * 2];</div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> <a name="a7"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a122ed5f6d8665139fbe9424a073b3474">biquadStateBand4Q31</a>[4 * 2];</div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> <a name="a8"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#af88658a8c5f87eeea26da30305921b59">biquadStateBand5Q31</a>[4 * 2];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** Q31 input and output buffers</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> <a name="a9"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a79521a4d6a9adb144c4d999ae713413c">inputQ31</a>[<a name="a10"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>];</div>
<div class="line"><a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> <a name="a11"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>[<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** Entire coefficient table.  There are 10 coefficients per 4th order Biquad</span></div>
<div class="line"><span class="comment">** cascade filter.  The first 10 coefficients correspond to the -9 dB gain</span></div>
<div class="line"><span class="comment">** setting of band 1; the next 10 coefficient correspond to the -8 dB gain</span></div>
<div class="line"><span class="comment">** setting of band 1; and so on.  There are 10*19=190 coefficients in total</span></div>
<div class="line"><span class="comment">** for band 1 (gains = -9, -8, -7, ..., 9).  After this come the 190 coefficients</span></div>
<div class="line"><span class="comment">** for band 2.</span></div>
<div class="line"><span class="comment">**</span></div>
<div class="line"><span class="comment">** The coefficients are in Q29 format and require a postShift of 2.</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> <a name="a12"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a024c59772b9603698b898721c1e8204e">coeffTable</a>[950] = {</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Band 1, -9 dB gain */</span></div>
<div class="line">  535576962, -1071153923, 535576962, 1073741824, -536870912, 535576962, -1063501998, 527979313, 1060865294, -524146981,</div>
<div class="line">  <span class="comment">/* Band 1, -8 dB gain */</span></div>
<div class="line">  535723226, -1071446451, 535723226, 1073741824, -536870912, 535723226, -1063568947, 527903217, 1061230578, -524503778,</div>
<div class="line">  535868593, -1071737186, 535868593, 1073741824, -536870912, 535868593, -1063627467, 527819780, 1061585502, -524850686,</div>
<div class="line">  536013181, -1072026363, 536013181, 1073741824, -536870912, 536013181, -1063677598, 527728935, 1061930361, -525187972,</div>
<div class="line">  536157109, -1072314217, 536157109, 1073741824, -536870912, 536157109, -1063719372, 527630607, 1062265438, -525515897,</div>
<div class="line">  536300492, -1072600983, 536300492, 1073741824, -536870912, 536300492, -1063752815, 527524720, 1062591011, -525834716,</div>
<div class="line">  536443447, -1072886894, 536443447, 1073741824, -536870912, 536443447, -1063777945, 527411186, 1062907350, -526144676,</div>
<div class="line">  536586091, -1073172183, 536586091, 1073741824, -536870912, 536586091, -1063794775, 527289917, 1063214717, -526446017,</div>
<div class="line">  536728541, -1073457082, 536728541, 1073741824, -536870912, 536728541, -1063803308, 527160815, 1063513366, -526738975,</div>
<div class="line">  536870912, -1073741824, 536870912, 1073741824, -536870912, 536870912, -1063803543, 527023777, 1063803543, -527023777,</div>
<div class="line">  537013321, -1074026642, 537013321, 1073741824, -536870912, 537013321, -1063795470, 526878696, 1064085490, -527300648,</div>
<div class="line">  537155884, -1074311768, 537155884, 1073741824, -536870912, 537155884, -1063779073, 526725455, 1064359439, -527569803,</div>
<div class="line">  537298718, -1074597435, 537298718, 1073741824, -536870912, 537298718, -1063754328, 526563934, 1064625617, -527831454,</div>
<div class="line">  537441939, -1074883878, 537441939, 1073741824, -536870912, 537441939, -1063721205, 526394005, 1064884245, -528085806,</div>
<div class="line">  537585666, -1075171331, 537585666, 1073741824, -536870912, 537585666, -1063679666, 526215534, 1065135536, -528333059,</div>
<div class="line">  537730015, -1075460030, 537730015, 1073741824, -536870912, 537730015, -1063629666, 526028380, 1065379699, -528573409,</div>
<div class="line">  537875106, -1075750212, 537875106, 1073741824, -536870912, 537875106, -1063571152, 525832396, 1065616936, -528807045,</div>
<div class="line">  538021057, -1076042114, 538021057, 1073741824, -536870912, 538021057, -1063504065, 525627429, 1065847444, -529034151,</div>
<div class="line">  538167989, -1076335977, 538167989, 1073741824, -536870912, 538167989, -1063428338, 525413317, 1066071412, -529254907,</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Band 2, -9 dB gain */</span></div>
<div class="line">  531784976, -1055497692, 523873415, 1066213307, -529420241, 531784976, -1040357886, 509828014, 1028908252, -494627367,</div>
<div class="line">  <span class="comment">/* Band 2, -8 dB gain */</span></div>
<div class="line">  532357636, -1056601982, 524400080, 1066115844, -529326645, 532357636, -1040623406, 509562600, 1030462237, -496062122,</div>
<div class="line">  532927392, -1057707729, 524931110, 1066024274, -529239070, 532927392, -1040848253, 509262081, 1031969246, -497457090,</div>
<div class="line">  533494678, -1058816094, 525467240, 1065939047, -529157961, 533494678, -1041032161, 508925950, 1033429976, -498812573,</div>
<div class="line">  534059929, -1059928204, 526009170, 1065860582, -529083734, 534059929, -1041174868, 508553717, 1034845124, -500128887,</div>
<div class="line">  534623580, -1061045148, 526557561, 1065789260, -529016764, 534623580, -1041276126, 508144920, 1036215393, -501406373,</div>
<div class="line">  535186068, -1062167969, 527113032, 1065725420, -528957385, 535186068, -1041335703, 507699125, 1037541500, -502645399,</div>
<div class="line">  535747827, -1063297666, 527676151, 1065669351, -528905879, 535747827, -1041353386, 507215934, 1038824183, -503846368,</div>
<div class="line">  536309295, -1064435183, 528247436, 1065621289, -528862476, 536309295, -1041328990, 506694984, 1040064203, -505009724,</div>
<div class="line">  536870912, -1065581413, 528827349, 1065581413, -528827349, 536870912, -1041262354, 506135953, 1041262354, -506135953,</div>
<div class="line">  537433117, -1066737194, 529416295, 1065549847, -528800610, 537433117, -1041153346, 505538564, 1042419457, -507225588,</div>
<div class="line">  537996352, -1067903307, 530014622, 1065526651, -528782316, 537996352, -1041001864, 504902578, 1043536370, -508279208,</div>
<div class="line">  538561061, -1069080480, 530622620, 1065511830, -528772462, 538561061, -1040807833, 504227800, 1044613981, -509297437,</div>
<div class="line">  539127690, -1070269387, 531240527, 1065505333, -528770987, 539127690, -1040571205, 503514074, 1045653211, -510280946,</div>
<div class="line">  539696690, -1071470656, 531868525, 1065507054, -528777778, 539696690, -1040291951, 502761277, 1046655011, -511230450,</div>
<div class="line">  540268512, -1072684867, 532506750, 1065516837, -528792672, 540268512, -1039970063, 501969320, 1047620358, -512146700,</div>
<div class="line">  540843613, -1073912567, 533155297, 1065534483, -528815459, 540843613, -1039605542, 501138139, 1048550251, -513030484,</div>
<div class="line">  541422451, -1075154268, 533814224, 1065559750, -528845892, 541422451, -1039198394, 500267687, 1049445708, -513882621,</div>
<div class="line">  542005489, -1076410460, 534483561, 1065592362, -528883686, 542005489, -1038748624, 499357932, 1050307760, -514703956,</div>
<div class="line">  518903861, -1001986830, 486725277, 1037235801, -502367695, 518903861, -945834422, 446371043, 902366163, -400700571,</div>
<div class="line">  520899989, -1005630916, 488289126, 1036926846, -502147311, 520899989, -946490935, 445581846, 907921945, -404936158,</div>
<div class="line">  522893209, -1009290002, 489869792, 1036650484, -501961419, 522893209, -947006359, 444685310, 913306106, -409075225,</div>
<div class="line">  524884763, -1012968199, 491470256, 1036407567, -501810737, 524884763, -947377809, 443679533, 918521018, -413116221,</div>
<div class="line">  526875910, -1016669649, 493093518, 1036198712, -501695739, 526875910, -947602324, 442562672, 923569247, -417057897,</div>
<div class="line">  528867927, -1020398503, 494742575, 1036024293, -501616651, 528867927, -947676875, 441332970, 928453558, -420899319,</div>
<div class="line">  530862111, -1024158905, 496420407, 1035884447, -501573457, 530862111, -947598385, 439988777, 933176909, -424639872,</div>
<div class="line">  532859778, -1027954970, 498129955, 1035779077, -501565907, 532859778, -947363742, 438528571, 937742446, -428279254,</div>
<div class="line">  534862260, -1031790763, 499874098, 1035707863, -501593525, 534862260, -946969823, 436950987, 942153486, -431817474,</div>
<div class="line">  536870912, -1035670279, 501655630, 1035670279, -501655630, 536870912, -946413508, 435254839, 946413508, -435254839,</div>
<div class="line">  538887107, -1039597419, 503477238, 1035665609, -501751354, 538887107, -945691703, 433439146, 950526127, -438591937,</div>
<div class="line">  540912240, -1043575967, 505341475, 1035692963, -501879659, 540912240, -944801359, 431503152, 954495080, -441829621,</div>
<div class="line">  542947726, -1047609569, 507250741, 1035751307, -502039364, 542947726, -943739490, 429446349, 958324201, -444968987,</div>
<div class="line">  544995000, -1051701717, 509207261, 1035839473, -502229165, 544995000, -942503190, 427268492, 962017400, -448011351,</div>
<div class="line">  547055523, -1055855728, 511213065, 1035956193, -502447657, 547055523, -941089647, 424969617, 965578640, -450958226,</div>
<div class="line">  549130774, -1060074734, 513269973, 1036100110, -502693359, 549130774, -939496155, 422550049, 969011913, -453811298,</div>
<div class="line">  551222259, -1064361672, 515379585, 1036269804, -502964731, 551222259, -937720119, 420010407, 972321228, -456572401,</div>
<div class="line">  553331507, -1068719280, 517543273, 1036463810, -503260192, 553331507, -935759057, 417351601, 975510582, -459243495,</div>
<div class="line">  555460072, -1073150100, 519762181, 1036680633, -503578144, 555460072, -933610600, 414574832, 978583948, -461826644,</div>
<div class="line">  494084017, -851422604, 404056273, 930151631, -423619864, 494084017, -673714108, 339502486, 561843007, -265801750,</div>
<div class="line">  498713542, -859177141, 406587077, 929211656, -423786402, 498713542, -673274906, 338185129, 573719128, -272222942,</div>
<div class="line">  503369016, -867012190, 409148384, 928362985, -424054784, 503369016, -672533059, 336693984, 585290277, -278599028,</div>
<div class="line">  508052536, -874935599, 411746438, 927604291, -424422151, 508052536, -671478538, 335026905, 596558312, -284920289,</div>
<div class="line">  512766286, -882955583, 414387826, 926933782, -424885216, 512766286, -670100998, 333182045, 607525792, -291177811,</div>
<div class="line">  517512534, -891080712, 417079474, 926349262, -425440318, 517512534, -668389789, 331157902, 618195914, -297363485,</div>
<div class="line">  522293635, -899319903, 419828635, 925848177, -426083491, 522293635, -666333963, 328953368, 628572440, -303470012,</div>
<div class="line">  527112032, -907682405, 422642886, 925427679, -426810526, 527112032, -663922286, 326567785, 638659631, -309490882,</div>
<div class="line">  531970251, -916177781, 425530105, 925084675, -427617023, 531970251, -661143261, 324000998, 648462180, -315420352,</div>
<div class="line">  536870912, -924815881, 428498454, 924815881, -428498454, 536870912, -657985147, 321253420, 657985147, -321253420,</div>
<div class="line">  541816719, -933606817, 431556352, 924617870, -429450209, 541816719, -654435997, 318326093, 667233900, -326985786,</div>
<div class="line">  546810467, -942560921, 434712438, 924487114, -430467639, 546810467, -650483688, 315220754, 676214053, -332613816,</div>
<div class="line">  551855042, -951688708, 437975532, 924420027, -431546101, 551855042, -646115970, 311939896, 684931422, -338134495,</div>
<div class="line">  556953421, -961000826, 441354588, 924413001, -432680993, 556953421, -641320513, 308486839, 693391970, -343545389,</div>
<div class="line">  562108672, -970508005, 444858642, 924462435, -433867780, 562108672, -636084967, 304865786, 701601770, -348844597,</div>
<div class="line">  567323959, -980220994, 448496743, 924564764, -435102022, 567323959, -630397020, 301081886, 709566963, -354030710,</div>
<div class="line">  572602539, -990150500, 452277894, 924716482, -436379394, 572602539, -624244471, 297141281, 717293726, -359102767,</div>
<div class="line">  577947763, -1000307125, 456210977, 924914158, -437695705, 577947763, -617615296, 293051155, 724788245, -364060214,</div>
<div class="line">  583363084, -1010701292, 460304674, 925154455, -439046908, 583363084, -610497723, 288819761, 732056685, -368902865,</div>
<div class="line">  387379495, -506912469, 196933274, 840112184, -347208270, 387379495, 506912469, 196933274, -840112184, -347208270,</div>
<div class="line">  401658082, -532275898, 207149427, 833765363, -343175316, 401658082, 532275898, 207149427, -833765363, -343175316,</div>
<div class="line">  416472483, -558722695, 217902617, 827270154, -339107319, 416472483, 558722695, 217902617, -827270154, -339107319,</div>
<div class="line">  431841949, -586290861, 229212798, 820624988, -335007540, 431841949, 586290861, 229212798, -820624988, -335007540,</div>
<div class="line">  447786335, -615019650, 241100489, 813828443, -330879528, 447786335, 615019650, 241100489, -813828443, -330879528,</div>
<div class="line">  464326111, -644949597, 253586805, 806879270, -326727141, 464326111, 644949597, 253586805, -806879270, -326727141,</div>
<div class="line">  481482377, -676122557, 266693475, 799776409, -322554559, 481482377, 676122557, 266693475, -799776409, -322554559,</div>
<div class="line">  499276882, -708581728, 280442865, 792519013, -318366296, 499276882, 708581728, 280442865, -792519013, -318366296,</div>
<div class="line">  517732032, -742371685, 294857996, 785106465, -314167221, 517732032, 742371685, 294857996, -785106465, -314167221,</div>
<div class="line">  536870912, -777538408, 309962566, 777538408, -309962566, 536870912, 777538408, 309962566, -777538408, -309962566,</div>
<div class="line">  556717294, -814129313, 325780968, 769814766, -305757943, 556717294, 814129313, 325780968, -769814766, -305757943,</div>
<div class="line">  577295658, -852193284, 342338310, 761935777, -301559360, 577295658, 852193284, 342338310, -761935777, -301559360,</div>
<div class="line">  598631206, -891780698, 359660433, 753902014, -297373230, 598631206, 891780698, 359660433, -753902014, -297373230,</div>
<div class="line">  620749877, -932943463, 377773927, 745714425, -293206383, 620749877, 932943463, 377773927, -745714425, -293206383,</div>
<div class="line">  643678365, -975735041, 396706151, 737374355, -289066077, 643678365, 975735041, 396706151, -737374355, -289066077,</div>
<div class="line">  667444134, -1020210487, 416485252, 728883588, -284960004, 667444134, 1020210487, 416485252, -728883588, -284960004,</div>
<div class="line">  692075438, -1066426476, 437140179, 720244375, -280896294, 692075438, 1066426476, 437140179, -720244375, -280896294,</div>
<div class="line">  717601336, -1114441339, 458700704, 711459472, -276883515, 717601336, 1114441339, 458700704, -711459472, -276883515,</div>
<div class="line">  744051710, -1164315096, 481197437, 702532174, -272930673, 744051710, 1164315096, 481197437, -702532174, -272930673</div>
<div class="line"></div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** Desired gains, in dB, per band</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keywordtype">int</span> <a name="a13"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a963aee85bb41a50fc943ac9048d123ab">gainDB</a>[5] = {0, -3, 6, 4, -6};</div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a14"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a>;</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Graphic equalizer Example</span></div>
<div class="line"><span class="comment"> * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">int32_t <a name="a15"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  *inputF32, *outputF32;</div>
<div class="line">  <a name="_a16"></a><a class="code" href="structarm__biquad__cas__df1__32x64__ins__q31.html" title="Instance structure for the high precision Q31 Biquad cascade filter.">arm_biquad_cas_df1_32x64_ins_q31</a> S1;</div>
<div class="line">  <a class="code" href="structarm__biquad__cas__df1__32x64__ins__q31.html" title="Instance structure for the high precision Q31 Biquad cascade filter.">arm_biquad_cas_df1_32x64_ins_q31</a> S2;</div>
<div class="line">  <a name="_a17"></a><a class="code" href="structarm__biquad__casd__df1__inst__q31.html" title="Instance structure for the Q31 Biquad cascade filter.">arm_biquad_casd_df1_inst_q31</a> S3;</div>
<div class="line">  <a class="code" href="structarm__biquad__casd__df1__inst__q31.html" title="Instance structure for the Q31 Biquad cascade filter.">arm_biquad_casd_df1_inst_q31</a> S4;</div>
<div class="line">  <a class="code" href="structarm__biquad__casd__df1__inst__q31.html" title="Instance structure for the Q31 Biquad cascade filter.">arm_biquad_casd_df1_inst_q31</a> S5;</div>
<div class="line">  <span class="keywordtype">int</span> i;</div>
<div class="line">  int32_t <a name="a18"></a><a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>;</div>
<div class="line"></div>
<div class="line">  inputF32 = &amp;<a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>[0];</div>
<div class="line">  outputF32 = &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>[0];</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialize the state and coefficient buffers for all Biquad sections */</span></div>
<div class="line"></div>
<div class="line">  <a name="a19"></a><a class="code" href="group___biquad_cascade_d_f1__32x64.html#ga44900cecb8083afcaabf905ffcd656bb">arm_biquad_cas_df1_32x64_init_q31</a>(&amp;S1, <a name="a20"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a23f8e430b510dfdb3ebe53bffca0d864">NUMSTAGES</a>,</div>
<div class="line">            (<a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> *) &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a024c59772b9603698b898721c1e8204e">coeffTable</a>[190*0 + 10*(<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a963aee85bb41a50fc943ac9048d123ab">gainDB</a>[0] + 9)],</div>
<div class="line">            &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a8e7062fa3f8b5ed9849566d16270f4ec">biquadStateBand1Q31</a>[0], 2);</div>
<div class="line"></div>
<div class="line">  <a class="code" href="group___biquad_cascade_d_f1__32x64.html#ga44900cecb8083afcaabf905ffcd656bb">arm_biquad_cas_df1_32x64_init_q31</a>(&amp;S2, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a23f8e430b510dfdb3ebe53bffca0d864">NUMSTAGES</a>,</div>
<div class="line">            (<a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> *) &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a024c59772b9603698b898721c1e8204e">coeffTable</a>[190*1 + 10*(<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a963aee85bb41a50fc943ac9048d123ab">gainDB</a>[1] + 9)],</div>
<div class="line">            &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a4a82090b15ebd0a45048d94f16131782">biquadStateBand2Q31</a>[0], 2);</div>
<div class="line"></div>
<div class="line">  <a name="a21"></a><a class="code" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b" title="Initialization function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_init_q31</a>(&amp;S3, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a23f8e430b510dfdb3ebe53bffca0d864">NUMSTAGES</a>,</div>
<div class="line">          (<a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> *) &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a024c59772b9603698b898721c1e8204e">coeffTable</a>[190*2 + 10*(<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a963aee85bb41a50fc943ac9048d123ab">gainDB</a>[2] + 9)],</div>
<div class="line">          &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#ad487a54340631b764952fccf599adc8a">biquadStateBand3Q31</a>[0], 2);</div>
<div class="line"></div>
<div class="line">  <a class="code" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b" title="Initialization function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_init_q31</a>(&amp;S4, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a23f8e430b510dfdb3ebe53bffca0d864">NUMSTAGES</a>,</div>
<div class="line">          (<a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> *) &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a024c59772b9603698b898721c1e8204e">coeffTable</a>[190*3 + 10*(<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a963aee85bb41a50fc943ac9048d123ab">gainDB</a>[3] + 9)],</div>
<div class="line">          &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a122ed5f6d8665139fbe9424a073b3474">biquadStateBand4Q31</a>[0], 2);</div>
<div class="line"></div>
<div class="line">  <a class="code" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b" title="Initialization function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_init_q31</a>(&amp;S5, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a23f8e430b510dfdb3ebe53bffca0d864">NUMSTAGES</a>,</div>
<div class="line">          (<a class="code" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0" title="32-bit fractional data type in 1.31 format.">q31_t</a> *) &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a024c59772b9603698b898721c1e8204e">coeffTable</a>[190*4 + 10*(<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a963aee85bb41a50fc943ac9048d123ab">gainDB</a>[4] + 9)],</div>
<div class="line">          &amp;<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#af88658a8c5f87eeea26da30305921b59">biquadStateBand5Q31</a>[0], 2);</div>
<div class="line"></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Call the process functions and needs to change filter coefficients</span></div>
<div class="line"><span class="comment">     for varying the gain of each band */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">for</span>(i=0; i &lt; <a name="a22"></a><a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a814e34126e1b8150f4de047e1a9e7030">NUMBLOCKS</a>; i++)</div>
<div class="line">  {</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">    ** Convert block of input data from float to Q31</span></div>
<div class="line"><span class="comment">    ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">    <a name="a23"></a><a class="code" href="group__float__to__x.html#ga177704107f94564e9abe4daaa36f4554" title="Converts the elements of the floating-point vector to Q31 vector.">arm_float_to_q31</a>(inputF32 + (i*<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>), <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a79521a4d6a9adb144c4d999ae713413c">inputQ31</a>, BLOCKSIZE);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">    ** Scale down by 1/8.  This provides additional headroom so that the</span></div>
<div class="line"><span class="comment">    ** graphic EQ can apply gain.</span></div>
<div class="line"><span class="comment">    ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">    <a name="a24"></a><a class="code" href="group__scale.html#ga83e36cd82bf51ce35406a199e477d47c" title="Multiplies a Q31 vector by a scalar.">arm_scale_q31</a>(<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a79521a4d6a9adb144c4d999ae713413c">inputQ31</a>, 0x7FFFFFFF, -3, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a79521a4d6a9adb144c4d999ae713413c">inputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">    ** Call the Q31 Biquad Cascade DF1 32x64 process function for band1, band2</span></div>
<div class="line"><span class="comment">    ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">    <a name="a25"></a><a class="code" href="group___biquad_cascade_d_f1__32x64.html#ga953a83e69685de6575cff37feb358a93">arm_biquad_cas_df1_32x64_q31</a>(&amp;S1, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a79521a4d6a9adb144c4d999ae713413c">inputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line">    <a class="code" href="group___biquad_cascade_d_f1__32x64.html#ga953a83e69685de6575cff37feb358a93">arm_biquad_cas_df1_32x64_q31</a>(&amp;S2, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">    ** Call the Q31 Biquad Cascade DF1 process function for band3, band4, band5</span></div>
<div class="line"><span class="comment">    ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">    <a name="a26"></a><a class="code" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f" title="Processing function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_q31</a>(&amp;S3, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line">    <a class="code" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f" title="Processing function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_q31</a>(&amp;S4, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line">    <a class="code" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f" title="Processing function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_q31</a>(&amp;S5, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">    ** Convert Q31 result back to float</span></div>
<div class="line"><span class="comment">    ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">    <a name="a27"></a><a class="code" href="group__q31__to__x.html#gacf407b007a37da18e99dabd9023c56b4" title="Converts the elements of the Q31 vector to floating-point vector.">arm_q31_to_float</a>(<a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a9862488450f2547b07aee8035d6b4d8a">outputQ31</a>, outputF32 + (i * <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>), BLOCKSIZE);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">    ** Scale back up</span></div>
<div class="line"><span class="comment">    ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">    <a name="a28"></a><a class="code" href="group__scale.html#ga3487af88b112f682ee90589cd419e123" title="Multiplies a floating-point vector by a scalar.">arm_scale_f32</a>(outputF32 + (i * <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630">BLOCKSIZE</a>), 8.0f, outputF32 + (i * BLOCKSIZE), BLOCKSIZE);</div>
<div class="line">  };</div>
<div class="line"></div>
<div class="line">  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a> = <a name="a29"></a><a class="code" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab" title="Caluclation of SNR.">arm_snr_f32</a>(<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">testRefOutput_f32</a>, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, <a class="code" href="arm__graphic__equalizer__example__q31_8c.html#a4f4a95eaace4e7b4e2f5243ed24f6b28">TESTLENGTH</a>);</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span> (<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a> &lt; <a name="a30"></a><a class="code" href="arm__fir__example__f32_8c.html#af7d1dd4deffa8e7ed6429e5dd0fe1812">SNR_THRESHOLD_F32</a>)</div>
<div class="line">  {</div>
<div class="line">    status = <a name="a31"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>;</div>
<div class="line">  }</div>
<div class="line">  <span class="keywordflow">else</span></div>
<div class="line">  {</div>
<div class="line">    status = <a name="a32"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>;</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Loop here if the signal does not match the reference output.</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span>( status != <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>)</div>
<div class="line">  {</div>
<div class="line">    <span class="keywordflow">while</span>(1);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
<div class="line"></div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
