<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="classes.html"><span>Data&#160;Structure&#160;Index</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li class="current"><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions_vars_0x73.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_s"></a>- s -</h3><ul>
<li>sdio_interrupt
: <a class="el" href="group__mci__interface__gr.html#a61e2a440b27d7d22c866ad4427f4b825">ARM_MCI_STATUS</a>
, <a class="el" href="group__mci__interface__gr.html#a61e2a440b27d7d22c866ad4427f4b825">ARM_MCI_CAPABILITIES</a>
</li>
<li>sdr_timing_mode
: <a class="el" href="group__nand__interface__gr.html#a21036f2047273d90c0af0e97031df5a9">ARM_NAND_CAPABILITIES</a>
</li>
<li>sec
: <a class="el" href="group__eth__mac__interface__gr.html#aaf5f5a3fa5d596a9136b4331f2b54bfc">ARM_ETH_MAC_TIME</a>
</li>
<li>sector_count
: <a class="el" href="group__flash__interface__gr.html#a50947f9a42bbaa2d68d6e5079150d7bf">ARM_FLASH_INFO</a>
</li>
<li>sector_info
: <a class="el" href="group__flash__interface__gr.html#a8dfb9d5160358e45293bba527762238d">ARM_FLASH_INFO</a>
</li>
<li>sector_size
: <a class="el" href="group__flash__interface__gr.html#a7d37def484362c6e97a2d75144080b1d">ARM_FLASH_INFO</a>
</li>
<li>Send
: <a class="el" href="group__spi__interface__gr.html#a44eedddf4428cf4b98883b6c27d31922">ARM_DRIVER_SPI</a>
, <a class="el" href="group__usart__interface__gr.html#a44eedddf4428cf4b98883b6c27d31922">ARM_DRIVER_USART</a>
</li>
<li>SendAddress
: <a class="el" href="group__nand__interface__gr.html#a5a43001ef1ca6c6d73f03e366bf41cb5">ARM_DRIVER_NAND</a>
</li>
<li>SendCommand
: <a class="el" href="group__mci__interface__gr.html#affefb5c1d352082933c2fb0620b37212">ARM_DRIVER_MCI</a>
, <a class="el" href="group__nand__interface__gr.html#a3dbd36d86718980665ce2e3a1ba672ca">ARM_DRIVER_NAND</a>
</li>
<li>SendFrame
: <a class="el" href="group__eth__mac__interface__gr.html#ac095aea379f23e30a0e51b1f3518ad37">ARM_DRIVER_ETH_MAC</a>
</li>
<li>sequence_operation
: <a class="el" href="group__nand__interface__gr.html#afa4b798731b1154878c26dda3f090acf">ARM_NAND_CAPABILITIES</a>
</li>
<li>SetAddressFilter
: <a class="el" href="group__eth__mac__interface__gr.html#a45b879a6df608f582d1866daff715798">ARM_DRIVER_ETH_MAC</a>
</li>
<li>SetInterface
: <a class="el" href="group__eth__phy__interface__gr.html#a7dfc7cf346c80e7fdb2fe4cea2c61161">ARM_DRIVER_ETH_PHY</a>
</li>
<li>SetMacAddress
: <a class="el" href="group__eth__mac__interface__gr.html#ac640f929dc4d5bde3e4282c75b25c00d">ARM_DRIVER_ETH_MAC</a>
</li>
<li>SetMode
: <a class="el" href="group__eth__phy__interface__gr.html#ae6686344f4d6afa0881d1e545c898a3d">ARM_DRIVER_ETH_PHY</a>
</li>
<li>SetModemControl
: <a class="el" href="group__usart__interface__gr.html#af6703d4078818df27ab9f8a7a8ad7b7b">ARM_DRIVER_USART</a>
</li>
<li>SetupTransfer
: <a class="el" href="group__mci__interface__gr.html#adc63bab660e8304d78faa1ac429e792b">ARM_DRIVER_MCI</a>
</li>
<li>simplex
: <a class="el" href="group__spi__interface__gr.html#af244e2c2facf6414e3886495ee6b40bc">ARM_SPI_CAPABILITIES</a>
</li>
<li>single_wire
: <a class="el" href="group__usart__interface__gr.html#ad1928b61021dd9ff689a3ccf9b8966a8">ARM_USART_CAPABILITIES</a>
</li>
<li>SlaveReceive
: <a class="el" href="group__i2c__interface__gr.html#a12d2689d6e93985e64b9561a8e4e917b">ARM_DRIVER_I2C</a>
</li>
<li>SlaveTransmit
: <a class="el" href="group__i2c__interface__gr.html#ae9e3b81b352d4564fd2337fdf0e5488c">ARM_DRIVER_I2C</a>
</li>
<li>smart_card
: <a class="el" href="group__usart__interface__gr.html#aa78e1ee1726d1db2cfa83fd7b5acc8bd">ARM_USART_CAPABILITIES</a>
</li>
<li>smart_card_clock
: <a class="el" href="group__usart__interface__gr.html#a7b3c14ea1b5e9ba0a37ebc05fcfd51a6">ARM_USART_CAPABILITIES</a>
</li>
<li>speed
: <a class="el" href="group__eth__interface__gr.html#a220859a8b5da0232739a11cbe7f79fc5">ARM_ETH_LINK_INFO</a>
, <a class="el" href="group__usbh__host__gr.html#a220859a8b5da0232739a11cbe7f79fc5">ARM_USBH_PORT_STATE</a>
, <a class="el" href="group__usbd__interface__gr.html#a220859a8b5da0232739a11cbe7f79fc5">ARM_USBD_STATE</a>
</li>
<li>start
: <a class="el" href="group__flash__interface__gr.html#a61eb63d26b2fa6c2971603ceccffb14b">ARM_FLASH_SECTOR</a>
</li>
<li>suspend_resume
: <a class="el" href="group__mci__interface__gr.html#abb03f0187e4658f417b5a24cac33eed9">ARM_MCI_CAPABILITIES</a>
</li>
<li>synchronous_master
: <a class="el" href="group__usart__interface__gr.html#afb385bfd9fb2d714bb58aa7d8d9d7d51">ARM_USART_CAPABILITIES</a>
</li>
<li>synchronous_slave
: <a class="el" href="group__usart__interface__gr.html#a37dcd87df8762e2bc9af9fea368b1537">ARM_USART_CAPABILITIES</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
