var searchData=
[
  ['a0',['A0',['../structarm__pid__instance__q15.html#ad77f3a2823c7f96de42c92a3fbf3246b',1,'arm_pid_instance_q15::A0()'],['../structarm__pid__instance__q31.html#aa5332635ce9c7078cdb4c1ecf442eadd',1,'arm_pid_instance_q31::A0()'],['../structarm__pid__instance__f32.html#ad7b0bed64915d0a25a3409fa2dc45556',1,'arm_pid_instance_f32::A0()']]],
  ['a1',['A1',['../structarm__pid__instance__q15.html#a1b8412c517071962a9acfdc6778906ec',1,'arm_pid_instance_q15::A1()'],['../structarm__pid__instance__q31.html#a2f7492bd6fb92fae5e2de7fbbec39b0e',1,'arm_pid_instance_q31::A1()'],['../structarm__pid__instance__f32.html#a7def89571c50f7137a213326a396e560',1,'arm_pid_instance_f32::A1()']]],
  ['a2',['A2',['../structarm__pid__instance__q31.html#a3e34537c53af4f9ad7bfffa4dff27c82',1,'arm_pid_instance_q31::A2()'],['../structarm__pid__instance__f32.html#a155acf642ba2f521869f19d694cd7fa0',1,'arm_pid_instance_f32::A2()']]],
  ['a_5ff32',['A_f32',['../arm__matrix__example__f32_8c.html#aed27b92d9847194d9dcce40cecf2b48a',1,'arm_matrix_example_f32.c']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__convolution__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__convolution__example_2_g_c_c_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__dotproduct__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__dotproduct__example_2_g_c_c_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__fft__bin__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__fft__bin__example_2_g_c_c_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__fir__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__graphic__equalizer__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__linear__interp__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__matrix__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__signal__converge__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__sin__cos__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__variance__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__class__marks__example_2_a_r_m_2_abstract_8txt.html',1,'']]],
  ['abstract_2etxt',['Abstract.txt',['../arm__class__marks__example_2_g_c_c_2_abstract_8txt.html',1,'']]],
  ['ak',['Ak',['../_a_r_m_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705',1,'Ak():&#160;arm_convolution_example_f32.c'],['../_g_c_c_2arm__convolution__example__f32_8c.html#aed74eacd4b96cc7f71b64d18f2e95705',1,'Ak():&#160;arm_convolution_example_f32.c']]],
  ['align4',['ALIGN4',['../arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb',1,'arm_math.h']]],
  ['arm_5fabs_5ff32',['arm_abs_f32',['../group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02',1,'arm_abs_f32(float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_abs_f32.c'],['../group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02',1,'arm_abs_f32(float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_abs_f32.c']]],
  ['arm_5fabs_5ff32_2ec',['arm_abs_f32.c',['../arm__abs__f32_8c.html',1,'']]],
  ['arm_5fabs_5fq15',['arm_abs_q15',['../group___basic_abs.html#ga39f92964c9b649ba252e26ebe7b95594',1,'arm_abs_q15(q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_abs_q15.c'],['../group___basic_abs.html#ga39f92964c9b649ba252e26ebe7b95594',1,'arm_abs_q15(q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_abs_q15.c']]],
  ['arm_5fabs_5fq15_2ec',['arm_abs_q15.c',['../arm__abs__q15_8c.html',1,'']]],
  ['arm_5fabs_5fq31',['arm_abs_q31',['../group___basic_abs.html#ga59eafcdcdb52da60d37f20aec6ff4577',1,'arm_abs_q31(q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_abs_q31.c'],['../group___basic_abs.html#ga59eafcdcdb52da60d37f20aec6ff4577',1,'arm_abs_q31(q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_abs_q31.c']]],
  ['arm_5fabs_5fq31_2ec',['arm_abs_q31.c',['../arm__abs__q31_8c.html',1,'']]],
  ['arm_5fabs_5fq7',['arm_abs_q7',['../group___basic_abs.html#gadc30985e33fbf96802a5a7954dece3b1',1,'arm_abs_q7(q7_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_abs_q7.c'],['../group___basic_abs.html#gadc30985e33fbf96802a5a7954dece3b1',1,'arm_abs_q7(q7_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_abs_q7.c']]],
  ['arm_5fabs_5fq7_2ec',['arm_abs_q7.c',['../arm__abs__q7_8c.html',1,'']]],
  ['arm_5fadd_5ff32',['arm_add_f32',['../group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd',1,'arm_add_f32(float32_t *pSrcA, float32_t *pSrcB, float32_t *pDst, uint32_t blockSize):&#160;arm_add_f32.c'],['../group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd',1,'arm_add_f32(float32_t *pSrcA, float32_t *pSrcB, float32_t *pDst, uint32_t blockSize):&#160;arm_add_f32.c']]],
  ['arm_5fadd_5ff32_2ec',['arm_add_f32.c',['../arm__add__f32_8c.html',1,'']]],
  ['arm_5fadd_5fq15',['arm_add_q15',['../group___basic_add.html#gabb51285a41f511670bbff62fc0e1bf62',1,'arm_add_q15(q15_t *pSrcA, q15_t *pSrcB, q15_t *pDst, uint32_t blockSize):&#160;arm_add_q15.c'],['../group___basic_add.html#gabb51285a41f511670bbff62fc0e1bf62',1,'arm_add_q15(q15_t *pSrcA, q15_t *pSrcB, q15_t *pDst, uint32_t blockSize):&#160;arm_add_q15.c']]],
  ['arm_5fadd_5fq15_2ec',['arm_add_q15.c',['../arm__add__q15_8c.html',1,'']]],
  ['arm_5fadd_5fq31',['arm_add_q31',['../group___basic_add.html#ga24d6c3f7f8b9fae4847c0c3f26a39a3b',1,'arm_add_q31(q31_t *pSrcA, q31_t *pSrcB, q31_t *pDst, uint32_t blockSize):&#160;arm_add_q31.c'],['../group___basic_add.html#ga24d6c3f7f8b9fae4847c0c3f26a39a3b',1,'arm_add_q31(q31_t *pSrcA, q31_t *pSrcB, q31_t *pDst, uint32_t blockSize):&#160;arm_add_q31.c']]],
  ['arm_5fadd_5fq31_2ec',['arm_add_q31.c',['../arm__add__q31_8c.html',1,'']]],
  ['arm_5fadd_5fq7',['arm_add_q7',['../group___basic_add.html#gaed633f415a7840a66861debca2dfb96b',1,'arm_add_q7(q7_t *pSrcA, q7_t *pSrcB, q7_t *pDst, uint32_t blockSize):&#160;arm_add_q7.c'],['../group___basic_add.html#gaed633f415a7840a66861debca2dfb96b',1,'arm_add_q7(q7_t *pSrcA, q7_t *pSrcB, q7_t *pDst, uint32_t blockSize):&#160;arm_add_q7.c']]],
  ['arm_5fadd_5fq7_2ec',['arm_add_q7.c',['../arm__add__q7_8c.html',1,'']]],
  ['arm_5fapply_5fguard_5fbits',['arm_apply_guard_bits',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88',1,'arm_apply_guard_bits(float32_t *pIn, uint32_t numSamples, uint32_t guard_bits):&#160;math_helper.c']]],
  ['arm_5fbilinear_5finterp_5ff32',['arm_bilinear_interp_f32',['../group___bilinear_interpolate.html#gab49a4c0f64854903d996d01ba38f711a',1,'arm_math.h']]],
  ['arm_5fbilinear_5finterp_5finstance_5ff32',['arm_bilinear_interp_instance_f32',['../structarm__bilinear__interp__instance__f32.html',1,'']]],
  ['arm_5fbilinear_5finterp_5finstance_5fq15',['arm_bilinear_interp_instance_q15',['../structarm__bilinear__interp__instance__q15.html',1,'']]],
  ['arm_5fbilinear_5finterp_5finstance_5fq31',['arm_bilinear_interp_instance_q31',['../structarm__bilinear__interp__instance__q31.html',1,'']]],
  ['arm_5fbilinear_5finterp_5finstance_5fq7',['arm_bilinear_interp_instance_q7',['../structarm__bilinear__interp__instance__q7.html',1,'']]],
  ['arm_5fbilinear_5finterp_5fq15',['arm_bilinear_interp_q15',['../group___bilinear_interpolate.html#gaa8dffbc2a01bb7accf231384498ec85e',1,'arm_math.h']]],
  ['arm_5fbilinear_5finterp_5fq31',['arm_bilinear_interp_q31',['../group___bilinear_interpolate.html#ga202a033c8a2ad3678b136f93153b6d13',1,'arm_math.h']]],
  ['arm_5fbilinear_5finterp_5fq7',['arm_bilinear_interp_q7',['../group___bilinear_interpolate.html#gade8db9706a3ae9ad03b2750a239d2ee6',1,'arm_math.h']]],
  ['arm_5fbiquad_5fcas_5fdf1_5f32x64_5finit_5fq31',['arm_biquad_cas_df1_32x64_init_q31',['../group___biquad_cascade_d_f1__32x64.html#ga44900cecb8083afcaabf905ffcd656bb',1,'arm_biquad_cas_df1_32x64_init_q31(arm_biquad_cas_df1_32x64_ins_q31 *S, uint8_t numStages, q31_t *pCoeffs, q63_t *pState, uint8_t postShift):&#160;arm_biquad_cascade_df1_32x64_init_q31.c'],['../group___biquad_cascade_d_f1__32x64.html#ga44900cecb8083afcaabf905ffcd656bb',1,'arm_biquad_cas_df1_32x64_init_q31(arm_biquad_cas_df1_32x64_ins_q31 *S, uint8_t numStages, q31_t *pCoeffs, q63_t *pState, uint8_t postShift):&#160;arm_biquad_cascade_df1_32x64_init_q31.c']]],
  ['arm_5fbiquad_5fcas_5fdf1_5f32x64_5fins_5fq31',['arm_biquad_cas_df1_32x64_ins_q31',['../structarm__biquad__cas__df1__32x64__ins__q31.html',1,'']]],
  ['arm_5fbiquad_5fcas_5fdf1_5f32x64_5fq31',['arm_biquad_cas_df1_32x64_q31',['../group___biquad_cascade_d_f1__32x64.html#ga953a83e69685de6575cff37feb358a93',1,'arm_biquad_cas_df1_32x64_q31(const arm_biquad_cas_df1_32x64_ins_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_32x64_q31.c'],['../group___biquad_cascade_d_f1__32x64.html#ga953a83e69685de6575cff37feb358a93',1,'arm_biquad_cas_df1_32x64_q31(const arm_biquad_cas_df1_32x64_ins_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_32x64_q31.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5f32x64_5finit_5fq31_2ec',['arm_biquad_cascade_df1_32x64_init_q31.c',['../arm__biquad__cascade__df1__32x64__init__q31_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5f32x64_5fq31_2ec',['arm_biquad_cascade_df1_32x64_q31.c',['../arm__biquad__cascade__df1__32x64__q31_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5ff32',['arm_biquad_cascade_df1_f32',['../group___biquad_cascade_d_f1.html#gaa0dbe330d763e3c1d8030b3ef12d5bdc',1,'arm_biquad_cascade_df1_f32(const arm_biquad_casd_df1_inst_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_f32.c'],['../group___biquad_cascade_d_f1.html#gaa0dbe330d763e3c1d8030b3ef12d5bdc',1,'arm_biquad_cascade_df1_f32(const arm_biquad_casd_df1_inst_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_f32.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5ff32_2ec',['arm_biquad_cascade_df1_f32.c',['../arm__biquad__cascade__df1__f32_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5ffast_5fq15',['arm_biquad_cascade_df1_fast_q15',['../group___biquad_cascade_d_f1.html#gaffb9792c0220882efd4c58f3c6a05fd7',1,'arm_biquad_cascade_df1_fast_q15(const arm_biquad_casd_df1_inst_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_fast_q15.c'],['../group___biquad_cascade_d_f1.html#gaffb9792c0220882efd4c58f3c6a05fd7',1,'arm_biquad_cascade_df1_fast_q15(const arm_biquad_casd_df1_inst_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_fast_q15.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5ffast_5fq15_2ec',['arm_biquad_cascade_df1_fast_q15.c',['../arm__biquad__cascade__df1__fast__q15_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5ffast_5fq31',['arm_biquad_cascade_df1_fast_q31',['../group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3',1,'arm_biquad_cascade_df1_fast_q31(const arm_biquad_casd_df1_inst_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_fast_q31.c'],['../group___biquad_cascade_d_f1.html#ga456390f5e448afad3a38bed7d6e380e3',1,'arm_biquad_cascade_df1_fast_q31(const arm_biquad_casd_df1_inst_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_fast_q31.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5ffast_5fq31_2ec',['arm_biquad_cascade_df1_fast_q31.c',['../arm__biquad__cascade__df1__fast__q31_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5finit_5ff32',['arm_biquad_cascade_df1_init_f32',['../group___biquad_cascade_d_f1.html#ga8e73b69a788e681a61bccc8959d823c5',1,'arm_biquad_cascade_df1_init_f32(arm_biquad_casd_df1_inst_f32 *S, uint8_t numStages, float32_t *pCoeffs, float32_t *pState):&#160;arm_biquad_cascade_df1_init_f32.c'],['../group___biquad_cascade_d_f1.html#ga8e73b69a788e681a61bccc8959d823c5',1,'arm_biquad_cascade_df1_init_f32(arm_biquad_casd_df1_inst_f32 *S, uint8_t numStages, float32_t *pCoeffs, float32_t *pState):&#160;arm_biquad_cascade_df1_init_f32.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5finit_5ff32_2ec',['arm_biquad_cascade_df1_init_f32.c',['../arm__biquad__cascade__df1__init__f32_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5finit_5fq15',['arm_biquad_cascade_df1_init_q15',['../group___biquad_cascade_d_f1.html#gad54c724132f6d742a444eb6df0e9c731',1,'arm_biquad_cascade_df1_init_q15(arm_biquad_casd_df1_inst_q15 *S, uint8_t numStages, q15_t *pCoeffs, q15_t *pState, int8_t postShift):&#160;arm_biquad_cascade_df1_init_q15.c'],['../group___biquad_cascade_d_f1.html#gad54c724132f6d742a444eb6df0e9c731',1,'arm_biquad_cascade_df1_init_q15(arm_biquad_casd_df1_inst_q15 *S, uint8_t numStages, q15_t *pCoeffs, q15_t *pState, int8_t postShift):&#160;arm_biquad_cascade_df1_init_q15.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5finit_5fq15_2ec',['arm_biquad_cascade_df1_init_q15.c',['../arm__biquad__cascade__df1__init__q15_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5finit_5fq31',['arm_biquad_cascade_df1_init_q31',['../group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b',1,'arm_biquad_cascade_df1_init_q31(arm_biquad_casd_df1_inst_q31 *S, uint8_t numStages, q31_t *pCoeffs, q31_t *pState, int8_t postShift):&#160;arm_biquad_cascade_df1_init_q31.c'],['../group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b',1,'arm_biquad_cascade_df1_init_q31(arm_biquad_casd_df1_inst_q31 *S, uint8_t numStages, q31_t *pCoeffs, q31_t *pState, int8_t postShift):&#160;arm_biquad_cascade_df1_init_q31.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5finit_5fq31_2ec',['arm_biquad_cascade_df1_init_q31.c',['../arm__biquad__cascade__df1__init__q31_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5fq15',['arm_biquad_cascade_df1_q15',['../group___biquad_cascade_d_f1.html#gadd66a0aefdc645031d607b0a5b37a942',1,'arm_biquad_cascade_df1_q15(const arm_biquad_casd_df1_inst_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_q15.c'],['../group___biquad_cascade_d_f1.html#gadd66a0aefdc645031d607b0a5b37a942',1,'arm_biquad_cascade_df1_q15(const arm_biquad_casd_df1_inst_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_q15.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5fq15_2ec',['arm_biquad_cascade_df1_q15.c',['../arm__biquad__cascade__df1__q15_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5fq31',['arm_biquad_cascade_df1_q31',['../group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f',1,'arm_biquad_cascade_df1_q31(const arm_biquad_casd_df1_inst_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_q31.c'],['../group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f',1,'arm_biquad_cascade_df1_q31(const arm_biquad_casd_df1_inst_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df1_q31.c']]],
  ['arm_5fbiquad_5fcascade_5fdf1_5fq31_2ec',['arm_biquad_cascade_df1_q31.c',['../arm__biquad__cascade__df1__q31_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5ff32',['arm_biquad_cascade_df2T_f32',['../group___biquad_cascade_d_f2_t.html#ga114f373fbc16a314e9f293c7c7649c7f',1,'arm_biquad_cascade_df2T_f32(const arm_biquad_cascade_df2T_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df2T_f32.c'],['../group___biquad_cascade_d_f2_t.html#ga114f373fbc16a314e9f293c7c7649c7f',1,'arm_biquad_cascade_df2T_f32(const arm_biquad_cascade_df2T_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df2T_f32.c']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5ff32_2ec',['arm_biquad_cascade_df2T_f32.c',['../arm__biquad__cascade__df2_t__f32_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5ff64',['arm_biquad_cascade_df2T_f64',['../group___biquad_cascade_d_f2_t.html#gaa8735dda5f3f36d0936283794c2aa771',1,'arm_biquad_cascade_df2T_f64(const arm_biquad_cascade_df2T_instance_f64 *S, float64_t *pSrc, float64_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df2T_f64.c'],['../group___biquad_cascade_d_f2_t.html#gaa8735dda5f3f36d0936283794c2aa771',1,'arm_biquad_cascade_df2T_f64(const arm_biquad_cascade_df2T_instance_f64 *S, float64_t *pSrc, float64_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_df2T_f64.c']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5ff64_2ec',['arm_biquad_cascade_df2T_f64.c',['../arm__biquad__cascade__df2_t__f64_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5finit_5ff32',['arm_biquad_cascade_df2T_init_f32',['../group___biquad_cascade_d_f2_t.html#ga70eaddf317a4a8bde6bd6a97df67fedd',1,'arm_biquad_cascade_df2T_init_f32(arm_biquad_cascade_df2T_instance_f32 *S, uint8_t numStages, float32_t *pCoeffs, float32_t *pState):&#160;arm_biquad_cascade_df2T_init_f32.c'],['../group___biquad_cascade_d_f2_t.html#ga70eaddf317a4a8bde6bd6a97df67fedd',1,'arm_biquad_cascade_df2T_init_f32(arm_biquad_cascade_df2T_instance_f32 *S, uint8_t numStages, float32_t *pCoeffs, float32_t *pState):&#160;arm_biquad_cascade_df2T_init_f32.c']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5finit_5ff32_2ec',['arm_biquad_cascade_df2T_init_f32.c',['../arm__biquad__cascade__df2_t__init__f32_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5finit_5ff64',['arm_biquad_cascade_df2T_init_f64',['../group___biquad_cascade_d_f2_t.html#ga12dc5d8e8892806ad70e79ca2ff9f86e',1,'arm_biquad_cascade_df2T_init_f64(arm_biquad_cascade_df2T_instance_f64 *S, uint8_t numStages, float64_t *pCoeffs, float64_t *pState):&#160;arm_biquad_cascade_df2T_init_f64.c'],['../group___biquad_cascade_d_f2_t.html#ga12dc5d8e8892806ad70e79ca2ff9f86e',1,'arm_biquad_cascade_df2T_init_f64(arm_biquad_cascade_df2T_instance_f64 *S, uint8_t numStages, float64_t *pCoeffs, float64_t *pState):&#160;arm_biquad_cascade_df2T_init_f64.c']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5finit_5ff64_2ec',['arm_biquad_cascade_df2T_init_f64.c',['../arm__biquad__cascade__df2_t__init__f64_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5finstance_5ff32',['arm_biquad_cascade_df2T_instance_f32',['../structarm__biquad__cascade__df2_t__instance__f32.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fdf2t_5finstance_5ff64',['arm_biquad_cascade_df2T_instance_f64',['../structarm__biquad__cascade__df2_t__instance__f64.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fstereo_5fdf2t_5ff32',['arm_biquad_cascade_stereo_df2T_f32',['../group___biquad_cascade_d_f2_t.html#gac75de449c3e4f733477d81bd0ada5eec',1,'arm_biquad_cascade_stereo_df2T_f32(const arm_biquad_cascade_stereo_df2T_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_stereo_df2T_f32.c'],['../group___biquad_cascade_d_f2_t.html#gac75de449c3e4f733477d81bd0ada5eec',1,'arm_biquad_cascade_stereo_df2T_f32(const arm_biquad_cascade_stereo_df2T_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_biquad_cascade_stereo_df2T_f32.c']]],
  ['arm_5fbiquad_5fcascade_5fstereo_5fdf2t_5ff32_2ec',['arm_biquad_cascade_stereo_df2T_f32.c',['../arm__biquad__cascade__stereo__df2_t__f32_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fstereo_5fdf2t_5finit_5ff32',['arm_biquad_cascade_stereo_df2T_init_f32',['../group___biquad_cascade_d_f2_t.html#ga405197c89fe4d34003efd23786296425',1,'arm_biquad_cascade_stereo_df2T_init_f32(arm_biquad_cascade_stereo_df2T_instance_f32 *S, uint8_t numStages, float32_t *pCoeffs, float32_t *pState):&#160;arm_biquad_cascade_stereo_df2T_init_f32.c'],['../group___biquad_cascade_d_f2_t.html#ga405197c89fe4d34003efd23786296425',1,'arm_biquad_cascade_stereo_df2T_init_f32(arm_biquad_cascade_stereo_df2T_instance_f32 *S, uint8_t numStages, float32_t *pCoeffs, float32_t *pState):&#160;arm_biquad_cascade_stereo_df2T_init_f32.c']]],
  ['arm_5fbiquad_5fcascade_5fstereo_5fdf2t_5finit_5ff32_2ec',['arm_biquad_cascade_stereo_df2T_init_f32.c',['../arm__biquad__cascade__stereo__df2_t__init__f32_8c.html',1,'']]],
  ['arm_5fbiquad_5fcascade_5fstereo_5fdf2t_5finstance_5ff32',['arm_biquad_cascade_stereo_df2T_instance_f32',['../structarm__biquad__cascade__stereo__df2_t__instance__f32.html',1,'']]],
  ['arm_5fbiquad_5fcasd_5fdf1_5finst_5ff32',['arm_biquad_casd_df1_inst_f32',['../structarm__biquad__casd__df1__inst__f32.html',1,'']]],
  ['arm_5fbiquad_5fcasd_5fdf1_5finst_5fq15',['arm_biquad_casd_df1_inst_q15',['../structarm__biquad__casd__df1__inst__q15.html',1,'']]],
  ['arm_5fbiquad_5fcasd_5fdf1_5finst_5fq31',['arm_biquad_casd_df1_inst_q31',['../structarm__biquad__casd__df1__inst__q31.html',1,'']]],
  ['arm_5fbitreversal_2ec',['arm_bitreversal.c',['../arm__bitreversal_8c.html',1,'']]],
  ['arm_5fbitreversal_5f16',['arm_bitreversal_16',['../arm__cfft__q15_8c.html#a773957c278f4d9e728711f27e8a6e278',1,'arm_cfft_q15.c']]],
  ['arm_5fbitreversal_5f32',['arm_bitreversal_32',['../arm__cfft__f32_8c.html#ac8e7ebe1cb131a5b0f55d0464640591f',1,'arm_bitreversal_32(uint32_t *pSrc, const uint16_t bitRevLen, const uint16_t *pBitRevTable):&#160;arm_cfft_f32.c'],['../arm__cfft__q31_8c.html#ac8e7ebe1cb131a5b0f55d0464640591f',1,'arm_bitreversal_32(uint32_t *pSrc, const uint16_t bitRevLen, const uint16_t *pBitRevTable):&#160;arm_cfft_q31.c']]],
  ['arm_5fbitreversal_5ff32',['arm_bitreversal_f32',['../arm__bitreversal_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b',1,'arm_bitreversal_f32(float32_t *pSrc, uint16_t fftSize, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c'],['../arm__cfft__radix2__f32_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b',1,'arm_bitreversal_f32(float32_t *pSrc, uint16_t fftSize, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c'],['../arm__cfft__radix4__f32_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b',1,'arm_bitreversal_f32(float32_t *pSrc, uint16_t fftSize, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c'],['../arm__rfft__f32_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b',1,'arm_bitreversal_f32(float32_t *pSrc, uint16_t fftSize, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c']]],
  ['arm_5fbitreversal_5fq15',['arm_bitreversal_q15',['../arm__bitreversal_8c.html#a12a07b49948c354172ae07358309a4a5',1,'arm_bitreversal_q15(q15_t *pSrc16, uint32_t fftLen, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c'],['../arm__cfft__radix2__q15_8c.html#a73f48eaea9297605705ae25d3405343e',1,'arm_bitreversal_q15(q15_t *pSrc, uint32_t fftLen, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c'],['../arm__cfft__radix4__q15_8c.html#a73f48eaea9297605705ae25d3405343e',1,'arm_bitreversal_q15(q15_t *pSrc, uint32_t fftLen, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c']]],
  ['arm_5fbitreversal_5fq31',['arm_bitreversal_q31',['../arm__bitreversal_8c.html#a27618705158b5c42db5fb0a381f8efc1',1,'arm_bitreversal_q31(q31_t *pSrc, uint32_t fftLen, uint16_t bitRevFactor, uint16_t *pBitRevTable):&#160;arm_bitreversal.c'],['../arm__cfft__radix2__q31_8c.html#a3fab577d25c3a517973c8c214f66f268',1,'arm_bitreversal_q31(q31_t *pSrc, uint32_t fftLen, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c'],['../arm__cfft__radix4__q31_8c.html#a3fab577d25c3a517973c8c214f66f268',1,'arm_bitreversal_q31(q31_t *pSrc, uint32_t fftLen, uint16_t bitRevFactor, uint16_t *pBitRevTab):&#160;arm_bitreversal.c']]],
  ['arm_5fcalc_5f2pow',['arm_calc_2pow',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99',1,'arm_calc_2pow(uint32_t numShifts):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68',1,'arm_calc_2pow(uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99',1,'arm_calc_2pow(uint32_t numShifts):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68',1,'arm_calc_2pow(uint32_t guard_bits):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99',1,'arm_calc_2pow(uint32_t numShifts):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68',1,'arm_calc_2pow(uint32_t guard_bits):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99',1,'arm_calc_2pow(uint32_t numShifts):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68',1,'arm_calc_2pow(uint32_t guard_bits):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99',1,'arm_calc_2pow(uint32_t numShifts):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68',1,'arm_calc_2pow(uint32_t guard_bits):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99',1,'arm_calc_2pow(uint32_t numShifts):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68',1,'arm_calc_2pow(uint32_t guard_bits):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99',1,'arm_calc_2pow(uint32_t numShifts):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68',1,'arm_calc_2pow(uint32_t guard_bits):&#160;math_helper.c']]],
  ['arm_5fcalc_5fguard_5fbits',['arm_calc_guard_bits',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742',1,'arm_calc_guard_bits(uint32_t num_adds):&#160;math_helper.c']]],
  ['arm_5fcfft_5ff32',['arm_cfft_f32',['../group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80',1,'arm_cfft_f32(const arm_cfft_instance_f32 *S, float32_t *p1, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_f32.c'],['../group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80',1,'arm_cfft_f32(const arm_cfft_instance_f32 *S, float32_t *p1, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_f32.c']]],
  ['arm_5fcfft_5ff32_2ec',['arm_cfft_f32.c',['../arm__cfft__f32_8c.html',1,'']]],
  ['arm_5fcfft_5finstance_5ff32',['arm_cfft_instance_f32',['../structarm__cfft__instance__f32.html',1,'']]],
  ['arm_5fcfft_5finstance_5fq15',['arm_cfft_instance_q15',['../structarm__cfft__instance__q15.html',1,'']]],
  ['arm_5fcfft_5finstance_5fq31',['arm_cfft_instance_q31',['../structarm__cfft__instance__q31.html',1,'']]],
  ['arm_5fcfft_5fq15',['arm_cfft_q15',['../group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229',1,'arm_cfft_q15(const arm_cfft_instance_q15 *S, q15_t *p1, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_q15.c'],['../group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229',1,'arm_cfft_q15(const arm_cfft_instance_q15 *S, q15_t *p1, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_q15.c']]],
  ['arm_5fcfft_5fq15_2ec',['arm_cfft_q15.c',['../arm__cfft__q15_8c.html',1,'']]],
  ['arm_5fcfft_5fq31',['arm_cfft_q31',['../group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b',1,'arm_cfft_q31(const arm_cfft_instance_q31 *S, q31_t *p1, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_q31.c'],['../group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b',1,'arm_cfft_q31(const arm_cfft_instance_q31 *S, q31_t *p1, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_q31.c']]],
  ['arm_5fcfft_5fq31_2ec',['arm_cfft_q31.c',['../arm__cfft__q31_8c.html',1,'']]],
  ['arm_5fcfft_5fradix2_5ff32',['arm_cfft_radix2_f32',['../group___complex_f_f_t.html#ga9fadd650b802f612ae558ddaab789a6d',1,'arm_cfft_radix2_f32(const arm_cfft_radix2_instance_f32 *S, float32_t *pSrc):&#160;arm_cfft_radix2_f32.c'],['../group___complex_f_f_t.html#ga9fadd650b802f612ae558ddaab789a6d',1,'arm_cfft_radix2_f32(const arm_cfft_radix2_instance_f32 *S, float32_t *pSrc):&#160;arm_cfft_radix2_f32.c']]],
  ['arm_5fcfft_5fradix2_5ff32_2ec',['arm_cfft_radix2_f32.c',['../arm__cfft__radix2__f32_8c.html',1,'']]],
  ['arm_5fcfft_5fradix2_5finit_5ff32',['arm_cfft_radix2_init_f32',['../group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7',1,'arm_cfft_radix2_init_f32(arm_cfft_radix2_instance_f32 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix2_init_f32.c'],['../group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7',1,'arm_cfft_radix2_init_f32(arm_cfft_radix2_instance_f32 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix2_init_f32.c']]],
  ['arm_5fcfft_5fradix2_5finit_5ff32_2ec',['arm_cfft_radix2_init_f32.c',['../arm__cfft__radix2__init__f32_8c.html',1,'']]],
  ['arm_5fcfft_5fradix2_5finit_5fq15',['arm_cfft_radix2_init_q15',['../group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858',1,'arm_cfft_radix2_init_q15(arm_cfft_radix2_instance_q15 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix2_init_q15.c'],['../group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858',1,'arm_cfft_radix2_init_q15(arm_cfft_radix2_instance_q15 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix2_init_q15.c']]],
  ['arm_5fcfft_5fradix2_5finit_5fq15_2ec',['arm_cfft_radix2_init_q15.c',['../arm__cfft__radix2__init__q15_8c.html',1,'']]],
  ['arm_5fcfft_5fradix2_5finit_5fq31',['arm_cfft_radix2_init_q31',['../group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638',1,'arm_cfft_radix2_init_q31(arm_cfft_radix2_instance_q31 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix2_init_q31.c'],['../group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638',1,'arm_cfft_radix2_init_q31(arm_cfft_radix2_instance_q31 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix2_init_q31.c']]],
  ['arm_5fcfft_5fradix2_5finit_5fq31_2ec',['arm_cfft_radix2_init_q31.c',['../arm__cfft__radix2__init__q31_8c.html',1,'']]],
  ['arm_5fcfft_5fradix2_5finstance_5ff32',['arm_cfft_radix2_instance_f32',['../structarm__cfft__radix2__instance__f32.html',1,'']]],
  ['arm_5fcfft_5fradix2_5finstance_5fq15',['arm_cfft_radix2_instance_q15',['../structarm__cfft__radix2__instance__q15.html',1,'']]],
  ['arm_5fcfft_5fradix2_5finstance_5fq31',['arm_cfft_radix2_instance_q31',['../structarm__cfft__radix2__instance__q31.html',1,'']]],
  ['arm_5fcfft_5fradix2_5fq15',['arm_cfft_radix2_q15',['../group___complex_f_f_t.html#ga55b424341dc3efd3fa0bcaaff4bdbf40',1,'arm_cfft_radix2_q15(const arm_cfft_radix2_instance_q15 *S, q15_t *pSrc):&#160;arm_cfft_radix2_q15.c'],['../group___complex_f_f_t.html#ga55b424341dc3efd3fa0bcaaff4bdbf40',1,'arm_cfft_radix2_q15(const arm_cfft_radix2_instance_q15 *S, q15_t *pSrc):&#160;arm_cfft_radix2_q15.c']]],
  ['arm_5fcfft_5fradix2_5fq15_2ec',['arm_cfft_radix2_q15.c',['../arm__cfft__radix2__q15_8c.html',1,'']]],
  ['arm_5fcfft_5fradix2_5fq31',['arm_cfft_radix2_q31',['../group___complex_f_f_t.html#ga6321f703ec87a274aedaab33d3e766b4',1,'arm_cfft_radix2_q31(const arm_cfft_radix2_instance_q31 *S, q31_t *pSrc):&#160;arm_cfft_radix2_q31.c'],['../group___complex_f_f_t.html#ga6321f703ec87a274aedaab33d3e766b4',1,'arm_cfft_radix2_q31(const arm_cfft_radix2_instance_q31 *S, q31_t *pSrc):&#160;arm_cfft_radix2_q31.c']]],
  ['arm_5fcfft_5fradix2_5fq31_2ec',['arm_cfft_radix2_q31.c',['../arm__cfft__radix2__q31_8c.html',1,'']]],
  ['arm_5fcfft_5fradix4_5ff32',['arm_cfft_radix4_f32',['../group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26',1,'arm_cfft_radix4_f32(const arm_cfft_radix4_instance_f32 *S, float32_t *pSrc):&#160;arm_cfft_radix4_f32.c'],['../group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26',1,'arm_cfft_radix4_f32(const arm_cfft_radix4_instance_f32 *S, float32_t *pSrc):&#160;arm_cfft_radix4_f32.c']]],
  ['arm_5fcfft_5fradix4_5ff32_2ec',['arm_cfft_radix4_f32.c',['../arm__cfft__radix4__f32_8c.html',1,'']]],
  ['arm_5fcfft_5fradix4_5finit_5ff32',['arm_cfft_radix4_init_f32',['../group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a',1,'arm_cfft_radix4_init_f32(arm_cfft_radix4_instance_f32 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix4_init_f32.c'],['../group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a',1,'arm_cfft_radix4_init_f32(arm_cfft_radix4_instance_f32 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix4_init_f32.c']]],
  ['arm_5fcfft_5fradix4_5finit_5ff32_2ec',['arm_cfft_radix4_init_f32.c',['../arm__cfft__radix4__init__f32_8c.html',1,'']]],
  ['arm_5fcfft_5fradix4_5finit_5fq15',['arm_cfft_radix4_init_q15',['../group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef',1,'arm_cfft_radix4_init_q15(arm_cfft_radix4_instance_q15 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix4_init_q15.c'],['../group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef',1,'arm_cfft_radix4_init_q15(arm_cfft_radix4_instance_q15 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix4_init_q15.c']]],
  ['arm_5fcfft_5fradix4_5finit_5fq15_2ec',['arm_cfft_radix4_init_q15.c',['../arm__cfft__radix4__init__q15_8c.html',1,'']]],
  ['arm_5fcfft_5fradix4_5finit_5fq31',['arm_cfft_radix4_init_q31',['../group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c',1,'arm_cfft_radix4_init_q31(arm_cfft_radix4_instance_q31 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix4_init_q31.c'],['../group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c',1,'arm_cfft_radix4_init_q31(arm_cfft_radix4_instance_q31 *S, uint16_t fftLen, uint8_t ifftFlag, uint8_t bitReverseFlag):&#160;arm_cfft_radix4_init_q31.c']]],
  ['arm_5fcfft_5fradix4_5finit_5fq31_2ec',['arm_cfft_radix4_init_q31.c',['../arm__cfft__radix4__init__q31_8c.html',1,'']]],
  ['arm_5fcfft_5fradix4_5finstance_5ff32',['arm_cfft_radix4_instance_f32',['../structarm__cfft__radix4__instance__f32.html',1,'']]],
  ['arm_5fcfft_5fradix4_5finstance_5fq15',['arm_cfft_radix4_instance_q15',['../structarm__cfft__radix4__instance__q15.html',1,'']]],
  ['arm_5fcfft_5fradix4_5finstance_5fq31',['arm_cfft_radix4_instance_q31',['../structarm__cfft__radix4__instance__q31.html',1,'']]],
  ['arm_5fcfft_5fradix4_5fq15',['arm_cfft_radix4_q15',['../group___complex_f_f_t.html#ga8d66cdac41b8bf6cefdb895456eee84a',1,'arm_cfft_radix4_q15(const arm_cfft_radix4_instance_q15 *S, q15_t *pSrc):&#160;arm_cfft_radix4_q15.c'],['../group___complex_f_f_t.html#ga8d66cdac41b8bf6cefdb895456eee84a',1,'arm_cfft_radix4_q15(const arm_cfft_radix4_instance_q15 *S, q15_t *pSrc):&#160;arm_cfft_radix4_q15.c']]],
  ['arm_5fcfft_5fradix4_5fq15_2ec',['arm_cfft_radix4_q15.c',['../arm__cfft__radix4__q15_8c.html',1,'']]],
  ['arm_5fcfft_5fradix4_5fq31',['arm_cfft_radix4_q31',['../group___complex_f_f_t.html#gafde3ee1f58cf393b45a9073174fff548',1,'arm_cfft_radix4_q31(const arm_cfft_radix4_instance_q31 *S, q31_t *pSrc):&#160;arm_cfft_radix4_q31.c'],['../group___complex_f_f_t.html#gafde3ee1f58cf393b45a9073174fff548',1,'arm_cfft_radix4_q31(const arm_cfft_radix4_instance_q31 *S, q31_t *pSrc):&#160;arm_cfft_radix4_q31.c']]],
  ['arm_5fcfft_5fradix4_5fq31_2ec',['arm_cfft_radix4_q31.c',['../arm__cfft__radix4__q31_8c.html',1,'']]],
  ['arm_5fcfft_5fradix4by2_5finverse_5fq15',['arm_cfft_radix4by2_inverse_q15',['../arm__cfft__q15_8c.html#abe669acc8db57d1fb9b1e2bba30f2224',1,'arm_cfft_q15.c']]],
  ['arm_5fcfft_5fradix4by2_5finverse_5fq31',['arm_cfft_radix4by2_inverse_q31',['../arm__cfft__q31_8c.html#a3f3ae10bc2057cc1360abfa25f224c8c',1,'arm_cfft_q31.c']]],
  ['arm_5fcfft_5fradix4by2_5fq15',['arm_cfft_radix4by2_q15',['../arm__cfft__q15_8c.html#af1d4a751153857c173511e0c77ab4fa9',1,'arm_cfft_q15.c']]],
  ['arm_5fcfft_5fradix4by2_5fq31',['arm_cfft_radix4by2_q31',['../arm__cfft__q31_8c.html#af6df8bf714c30d44e6b871ea87d22b30',1,'arm_cfft_q31.c']]],
  ['arm_5fcfft_5fradix8_5ff32_2ec',['arm_cfft_radix8_f32.c',['../arm__cfft__radix8__f32_8c.html',1,'']]],
  ['arm_5fcfft_5fradix8by2_5ff32',['arm_cfft_radix8by2_f32',['../arm__cfft__f32_8c.html#ae99e2b173033e9910058869bdf0619d9',1,'arm_cfft_f32.c']]],
  ['arm_5fcfft_5fradix8by4_5ff32',['arm_cfft_radix8by4_f32',['../arm__cfft__f32_8c.html#a4bb346f59bca06cebe0defc8e15b69a6',1,'arm_cfft_f32.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen1024',['arm_cfft_sR_f32_len1024',['../arm__const__structs_8c.html#a05abc294a9159abbd6ffb4f188fe18b1',1,'arm_cfft_sR_f32_len1024():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a05abc294a9159abbd6ffb4f188fe18b1',1,'arm_cfft_sR_f32_len1024():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen128',['arm_cfft_sR_f32_len128',['../arm__const__structs_8c.html#ad283193397ba476465a330db9a955973',1,'arm_cfft_sR_f32_len128():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#ad283193397ba476465a330db9a955973',1,'arm_cfft_sR_f32_len128():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen16',['arm_cfft_sR_f32_len16',['../arm__const__structs_8c.html#a27127e9d3deb59df12747233b1b9ea31',1,'arm_cfft_sR_f32_len16():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a27127e9d3deb59df12747233b1b9ea31',1,'arm_cfft_sR_f32_len16():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen2048',['arm_cfft_sR_f32_len2048',['../arm__const__structs_8c.html#a8d2fad347dcadc47377e1226231b9f62',1,'arm_cfft_sR_f32_len2048():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a8d2fad347dcadc47377e1226231b9f62',1,'arm_cfft_sR_f32_len2048():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen256',['arm_cfft_sR_f32_len256',['../arm__const__structs_8c.html#aeb2f0a0be605963264217cc10b7bd3b2',1,'arm_cfft_sR_f32_len256():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#aeb2f0a0be605963264217cc10b7bd3b2',1,'arm_cfft_sR_f32_len256():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen32',['arm_cfft_sR_f32_len32',['../arm__const__structs_8c.html#a5fed2b5e0cc4cb5b8675f14daf226a25',1,'arm_cfft_sR_f32_len32():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a5fed2b5e0cc4cb5b8675f14daf226a25',1,'arm_cfft_sR_f32_len32():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen4096',['arm_cfft_sR_f32_len4096',['../arm__const__structs_8c.html#a01d2dbdb8193d43c2b7f003f9cb9a39d',1,'arm_cfft_sR_f32_len4096():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a01d2dbdb8193d43c2b7f003f9cb9a39d',1,'arm_cfft_sR_f32_len4096():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen512',['arm_cfft_sR_f32_len512',['../arm__const__structs_8c.html#a15f6e533f5cfeb014839303d8ed52e19',1,'arm_cfft_sR_f32_len512():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a15f6e533f5cfeb014839303d8ed52e19',1,'arm_cfft_sR_f32_len512():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5ff32_5flen64',['arm_cfft_sR_f32_len64',['../arm__const__structs_8c.html#af94d90db836f662321946154c76b5b80',1,'arm_cfft_sR_f32_len64():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#af94d90db836f662321946154c76b5b80',1,'arm_cfft_sR_f32_len64():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen1024',['arm_cfft_sR_q15_len1024',['../arm__const__structs_8c.html#ad343fb2e4cba826f092f9d72c4adc831',1,'arm_cfft_sR_q15_len1024():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#ad343fb2e4cba826f092f9d72c4adc831',1,'arm_cfft_sR_q15_len1024():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen128',['arm_cfft_sR_q15_len128',['../arm__const__structs_8c.html#a736a97efd37c6386dab8db730904f69b',1,'arm_cfft_sR_q15_len128():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a736a97efd37c6386dab8db730904f69b',1,'arm_cfft_sR_q15_len128():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen16',['arm_cfft_sR_q15_len16',['../arm__const__structs_8c.html#a7ed661717c58b18f3e557daa72f2b91b',1,'arm_cfft_sR_q15_len16():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a7ed661717c58b18f3e557daa72f2b91b',1,'arm_cfft_sR_q15_len16():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen2048',['arm_cfft_sR_q15_len2048',['../arm__const__structs_8c.html#a92c94dc79c66ec66c95f793aedb964b9',1,'arm_cfft_sR_q15_len2048():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a92c94dc79c66ec66c95f793aedb964b9',1,'arm_cfft_sR_q15_len2048():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen256',['arm_cfft_sR_q15_len256',['../arm__const__structs_8c.html#ad80be0db1ea40c66b079404c48d2dcf4',1,'arm_cfft_sR_q15_len256():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#ad80be0db1ea40c66b079404c48d2dcf4',1,'arm_cfft_sR_q15_len256():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen32',['arm_cfft_sR_q15_len32',['../arm__const__structs_8c.html#a8d5426a822a6017235b5e10119606a90',1,'arm_cfft_sR_q15_len32():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a8d5426a822a6017235b5e10119606a90',1,'arm_cfft_sR_q15_len32():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen4096',['arm_cfft_sR_q15_len4096',['../arm__const__structs_8c.html#ab57c118edaa3260f7f16686152845b18',1,'arm_cfft_sR_q15_len4096():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#ab57c118edaa3260f7f16686152845b18',1,'arm_cfft_sR_q15_len4096():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen512',['arm_cfft_sR_q15_len512',['../arm__const__structs_8c.html#a273b91ec86bb2bd8ac14e69252d487fb',1,'arm_cfft_sR_q15_len512():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a273b91ec86bb2bd8ac14e69252d487fb',1,'arm_cfft_sR_q15_len512():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq15_5flen64',['arm_cfft_sR_q15_len64',['../arm__const__structs_8c.html#a95c216e7dcfd59a8d40ef55ac223a749',1,'arm_cfft_sR_q15_len64():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a95c216e7dcfd59a8d40ef55ac223a749',1,'arm_cfft_sR_q15_len64():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen1024',['arm_cfft_sR_q31_len1024',['../arm__const__structs_8c.html#ada9813a027999f3cff066c9f7b5df51b',1,'arm_cfft_sR_q31_len1024():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#ada9813a027999f3cff066c9f7b5df51b',1,'arm_cfft_sR_q31_len1024():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen128',['arm_cfft_sR_q31_len128',['../arm__const__structs_8c.html#a9a2fcdb54300f75ef1fafe02954e9a61',1,'arm_cfft_sR_q31_len128():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a9a2fcdb54300f75ef1fafe02954e9a61',1,'arm_cfft_sR_q31_len128():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen16',['arm_cfft_sR_q31_len16',['../arm__const__structs_8c.html#a1336431c4d2a88d32c42308cfe2defa1',1,'arm_cfft_sR_q31_len16():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a1336431c4d2a88d32c42308cfe2defa1',1,'arm_cfft_sR_q31_len16():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen2048',['arm_cfft_sR_q31_len2048',['../arm__const__structs_8c.html#a420622d75b277070784083ddd44b95fb',1,'arm_cfft_sR_q31_len2048():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a420622d75b277070784083ddd44b95fb',1,'arm_cfft_sR_q31_len2048():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen256',['arm_cfft_sR_q31_len256',['../arm__const__structs_8c.html#a3f2de67938bd228918e40f60f18dd6b5',1,'arm_cfft_sR_q31_len256():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a3f2de67938bd228918e40f60f18dd6b5',1,'arm_cfft_sR_q31_len256():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen32',['arm_cfft_sR_q31_len32',['../arm__const__structs_8c.html#a4c083c013ef17920cf8f28dc6f139a39',1,'arm_cfft_sR_q31_len32():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#a4c083c013ef17920cf8f28dc6f139a39',1,'arm_cfft_sR_q31_len32():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen4096',['arm_cfft_sR_q31_len4096',['../arm__const__structs_8c.html#abfc9595f40a1c7aaba85e1328d824b1c',1,'arm_cfft_sR_q31_len4096():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#abfc9595f40a1c7aaba85e1328d824b1c',1,'arm_cfft_sR_q31_len4096():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen512',['arm_cfft_sR_q31_len512',['../arm__const__structs_8c.html#aa337272cf78aaf6075e7e19d0a097d6f',1,'arm_cfft_sR_q31_len512():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#aa337272cf78aaf6075e7e19d0a097d6f',1,'arm_cfft_sR_q31_len512():&#160;arm_const_structs.c']]],
  ['arm_5fcfft_5fsr_5fq31_5flen64',['arm_cfft_sR_q31_len64',['../arm__const__structs_8c.html#ad11668a5662334e0bc6a2811c9cb1047',1,'arm_cfft_sR_q31_len64():&#160;arm_const_structs.c'],['../arm__const__structs_8h.html#ad11668a5662334e0bc6a2811c9cb1047',1,'arm_cfft_sR_q31_len64():&#160;arm_const_structs.c']]],
  ['arm_5fcircularread_5ff32',['arm_circularRead_f32',['../arm__math_8h.html#ae469fac5e1df35f8bcf1b3d7c3136484',1,'arm_math.h']]],
  ['arm_5fcircularread_5fq15',['arm_circularRead_q15',['../arm__math_8h.html#ad5fb134f83f2c802261f172e3dceb131',1,'arm_math.h']]],
  ['arm_5fcircularread_5fq7',['arm_circularRead_q7',['../arm__math_8h.html#a30aa80ea20abe71f3afa99f2f0391ed5',1,'arm_math.h']]],
  ['arm_5fcircularwrite_5ff32',['arm_circularWrite_f32',['../arm__math_8h.html#a6ff56c0896ce00712ba8f2fcf72cacd3',1,'arm_math.h']]],
  ['arm_5fcircularwrite_5fq15',['arm_circularWrite_q15',['../arm__math_8h.html#a3ba2d215477e692def7fda46dda883ed',1,'arm_math.h']]],
  ['arm_5fcircularwrite_5fq7',['arm_circularWrite_q7',['../arm__math_8h.html#addba85b1f7fbd472fd00ddd9ce43aea8',1,'arm_math.h']]],
  ['arm_5fclarke_5ff32',['arm_clarke_f32',['../group__clarke.html#ga2b4ebec76215e1277c970c269ffdbd76',1,'arm_math.h']]],
  ['arm_5fclarke_5fq31',['arm_clarke_q31',['../group__clarke.html#ga7fd106ca8d346a2a472842e0656014c1',1,'arm_math.h']]],
  ['arm_5fclass_5fmarks_5fexample_5ff32_2ec',['arm_class_marks_example_f32.c',['../_a_r_m_2arm__class__marks__example__f32_8c.html',1,'']]],
  ['arm_5fclip_5ff32',['arm_clip_f32',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a',1,'arm_clip_f32(float *pIn, uint32_t numSamples):&#160;math_helper.c']]],
  ['arm_5fcmplx_5fconj_5ff32',['arm_cmplx_conj_f32',['../group__cmplx__conj.html#ga3a102aead6460ad9fcb0626f6b226ffb',1,'arm_cmplx_conj_f32(float32_t *pSrc, float32_t *pDst, uint32_t numSamples):&#160;arm_cmplx_conj_f32.c'],['../group__cmplx__conj.html#ga3a102aead6460ad9fcb0626f6b226ffb',1,'arm_cmplx_conj_f32(float32_t *pSrc, float32_t *pDst, uint32_t numSamples):&#160;arm_cmplx_conj_f32.c']]],
  ['arm_5fcmplx_5fconj_5ff32_2ec',['arm_cmplx_conj_f32.c',['../arm__cmplx__conj__f32_8c.html',1,'']]],
  ['arm_5fcmplx_5fconj_5fq15',['arm_cmplx_conj_q15',['../group__cmplx__conj.html#gaf47689ae07962acaecb8ddde556df4a4',1,'arm_cmplx_conj_q15(q15_t *pSrc, q15_t *pDst, uint32_t numSamples):&#160;arm_cmplx_conj_q15.c'],['../group__cmplx__conj.html#gaf47689ae07962acaecb8ddde556df4a4',1,'arm_cmplx_conj_q15(q15_t *pSrc, q15_t *pDst, uint32_t numSamples):&#160;arm_cmplx_conj_q15.c']]],
  ['arm_5fcmplx_5fconj_5fq15_2ec',['arm_cmplx_conj_q15.c',['../arm__cmplx__conj__q15_8c.html',1,'']]],
  ['arm_5fcmplx_5fconj_5fq31',['arm_cmplx_conj_q31',['../group__cmplx__conj.html#gafecc94879a383c5208ec3ef99485e4b5',1,'arm_cmplx_conj_q31(q31_t *pSrc, q31_t *pDst, uint32_t numSamples):&#160;arm_cmplx_conj_q31.c'],['../group__cmplx__conj.html#gafecc94879a383c5208ec3ef99485e4b5',1,'arm_cmplx_conj_q31(q31_t *pSrc, q31_t *pDst, uint32_t numSamples):&#160;arm_cmplx_conj_q31.c']]],
  ['arm_5fcmplx_5fconj_5fq31_2ec',['arm_cmplx_conj_q31.c',['../arm__cmplx__conj__q31_8c.html',1,'']]],
  ['arm_5fcmplx_5fdot_5fprod_5ff32',['arm_cmplx_dot_prod_f32',['../group__cmplx__dot__prod.html#gadcfaf567a25eb641da4043eafb9bb076',1,'arm_cmplx_dot_prod_f32(float32_t *pSrcA, float32_t *pSrcB, uint32_t numSamples, float32_t *realResult, float32_t *imagResult):&#160;arm_cmplx_dot_prod_f32.c'],['../group__cmplx__dot__prod.html#gadcfaf567a25eb641da4043eafb9bb076',1,'arm_cmplx_dot_prod_f32(float32_t *pSrcA, float32_t *pSrcB, uint32_t numSamples, float32_t *realResult, float32_t *imagResult):&#160;arm_cmplx_dot_prod_f32.c']]],
  ['arm_5fcmplx_5fdot_5fprod_5ff32_2ec',['arm_cmplx_dot_prod_f32.c',['../arm__cmplx__dot__prod__f32_8c.html',1,'']]],
  ['arm_5fcmplx_5fdot_5fprod_5fq15',['arm_cmplx_dot_prod_q15',['../group__cmplx__dot__prod.html#ga2b08b5e8001d2c15204639d00893fc70',1,'arm_cmplx_dot_prod_q15(q15_t *pSrcA, q15_t *pSrcB, uint32_t numSamples, q31_t *realResult, q31_t *imagResult):&#160;arm_cmplx_dot_prod_q15.c'],['../group__cmplx__dot__prod.html#ga2b08b5e8001d2c15204639d00893fc70',1,'arm_cmplx_dot_prod_q15(q15_t *pSrcA, q15_t *pSrcB, uint32_t numSamples, q31_t *realResult, q31_t *imagResult):&#160;arm_cmplx_dot_prod_q15.c']]],
  ['arm_5fcmplx_5fdot_5fprod_5fq15_2ec',['arm_cmplx_dot_prod_q15.c',['../arm__cmplx__dot__prod__q15_8c.html',1,'']]],
  ['arm_5fcmplx_5fdot_5fprod_5fq31',['arm_cmplx_dot_prod_q31',['../group__cmplx__dot__prod.html#ga5b731a59db062a9ad84562ef68a6c8af',1,'arm_cmplx_dot_prod_q31(q31_t *pSrcA, q31_t *pSrcB, uint32_t numSamples, q63_t *realResult, q63_t *imagResult):&#160;arm_cmplx_dot_prod_q31.c'],['../group__cmplx__dot__prod.html#ga5b731a59db062a9ad84562ef68a6c8af',1,'arm_cmplx_dot_prod_q31(q31_t *pSrcA, q31_t *pSrcB, uint32_t numSamples, q63_t *realResult, q63_t *imagResult):&#160;arm_cmplx_dot_prod_q31.c']]],
  ['arm_5fcmplx_5fdot_5fprod_5fq31_2ec',['arm_cmplx_dot_prod_q31.c',['../arm__cmplx__dot__prod__q31_8c.html',1,'']]],
  ['arm_5fcmplx_5fmag_5ff32',['arm_cmplx_mag_f32',['../group__cmplx__mag.html#gae45024c497392cde2ae358a76d435213',1,'arm_cmplx_mag_f32(float32_t *pSrc, float32_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_f32.c'],['../group__cmplx__mag.html#gae45024c497392cde2ae358a76d435213',1,'arm_cmplx_mag_f32(float32_t *pSrc, float32_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_f32.c']]],
  ['arm_5fcmplx_5fmag_5ff32_2ec',['arm_cmplx_mag_f32.c',['../arm__cmplx__mag__f32_8c.html',1,'']]],
  ['arm_5fcmplx_5fmag_5fq15',['arm_cmplx_mag_q15',['../group__cmplx__mag.html#ga0a4a8f77a6a51d9b3f3b9d729f85b7a4',1,'arm_cmplx_mag_q15(q15_t *pSrc, q15_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_q15.c'],['../group__cmplx__mag.html#ga0a4a8f77a6a51d9b3f3b9d729f85b7a4',1,'arm_cmplx_mag_q15(q15_t *pSrc, q15_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_q15.c']]],
  ['arm_5fcmplx_5fmag_5fq15_2ec',['arm_cmplx_mag_q15.c',['../arm__cmplx__mag__q15_8c.html',1,'']]],
  ['arm_5fcmplx_5fmag_5fq31',['arm_cmplx_mag_q31',['../group__cmplx__mag.html#ga14f82f9230e9d96d5b9774e2fefcb7be',1,'arm_cmplx_mag_q31(q31_t *pSrc, q31_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_q31.c'],['../group__cmplx__mag.html#ga14f82f9230e9d96d5b9774e2fefcb7be',1,'arm_cmplx_mag_q31(q31_t *pSrc, q31_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_q31.c']]],
  ['arm_5fcmplx_5fmag_5fq31_2ec',['arm_cmplx_mag_q31.c',['../arm__cmplx__mag__q31_8c.html',1,'']]],
  ['arm_5fcmplx_5fmag_5fsquared_5ff32',['arm_cmplx_mag_squared_f32',['../group__cmplx__mag__squared.html#gaa7faccc0d96b061d8b7d0d7d82045074',1,'arm_cmplx_mag_squared_f32(float32_t *pSrc, float32_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_squared_f32.c'],['../group__cmplx__mag__squared.html#gaa7faccc0d96b061d8b7d0d7d82045074',1,'arm_cmplx_mag_squared_f32(float32_t *pSrc, float32_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_squared_f32.c']]],
  ['arm_5fcmplx_5fmag_5fsquared_5ff32_2ec',['arm_cmplx_mag_squared_f32.c',['../arm__cmplx__mag__squared__f32_8c.html',1,'']]],
  ['arm_5fcmplx_5fmag_5fsquared_5fq15',['arm_cmplx_mag_squared_q15',['../group__cmplx__mag__squared.html#ga45537f576102d960d467eb722b8431f2',1,'arm_cmplx_mag_squared_q15(q15_t *pSrc, q15_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_squared_q15.c'],['../group__cmplx__mag__squared.html#ga45537f576102d960d467eb722b8431f2',1,'arm_cmplx_mag_squared_q15(q15_t *pSrc, q15_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_squared_q15.c']]],
  ['arm_5fcmplx_5fmag_5fsquared_5fq15_2ec',['arm_cmplx_mag_squared_q15.c',['../arm__cmplx__mag__squared__q15_8c.html',1,'']]],
  ['arm_5fcmplx_5fmag_5fsquared_5fq31',['arm_cmplx_mag_squared_q31',['../group__cmplx__mag__squared.html#ga384b0538101e8c03fa4fa14271e63b04',1,'arm_cmplx_mag_squared_q31(q31_t *pSrc, q31_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_squared_q31.c'],['../group__cmplx__mag__squared.html#ga384b0538101e8c03fa4fa14271e63b04',1,'arm_cmplx_mag_squared_q31(q31_t *pSrc, q31_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mag_squared_q31.c']]],
  ['arm_5fcmplx_5fmag_5fsquared_5fq31_2ec',['arm_cmplx_mag_squared_q31.c',['../arm__cmplx__mag__squared__q31_8c.html',1,'']]],
  ['arm_5fcmplx_5fmult_5fcmplx_5ff32',['arm_cmplx_mult_cmplx_f32',['../group___cmplx_by_cmplx_mult.html#ga14b47080054a1ba1250a86805be1ff6b',1,'arm_cmplx_mult_cmplx_f32(float32_t *pSrcA, float32_t *pSrcB, float32_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mult_cmplx_f32.c'],['../group___cmplx_by_cmplx_mult.html#ga14b47080054a1ba1250a86805be1ff6b',1,'arm_cmplx_mult_cmplx_f32(float32_t *pSrcA, float32_t *pSrcB, float32_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mult_cmplx_f32.c']]],
  ['arm_5fcmplx_5fmult_5fcmplx_5ff32_2ec',['arm_cmplx_mult_cmplx_f32.c',['../arm__cmplx__mult__cmplx__f32_8c.html',1,'']]],
  ['arm_5fcmplx_5fmult_5fcmplx_5fq15',['arm_cmplx_mult_cmplx_q15',['../group___cmplx_by_cmplx_mult.html#ga67e96abfc9c3e30efb70a2ec9d0fe7e8',1,'arm_cmplx_mult_cmplx_q15(q15_t *pSrcA, q15_t *pSrcB, q15_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mult_cmplx_q15.c'],['../group___cmplx_by_cmplx_mult.html#ga67e96abfc9c3e30efb70a2ec9d0fe7e8',1,'arm_cmplx_mult_cmplx_q15(q15_t *pSrcA, q15_t *pSrcB, q15_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mult_cmplx_q15.c']]],
  ['arm_5fcmplx_5fmult_5fcmplx_5fq15_2ec',['arm_cmplx_mult_cmplx_q15.c',['../arm__cmplx__mult__cmplx__q15_8c.html',1,'']]],
  ['arm_5fcmplx_5fmult_5fcmplx_5fq31',['arm_cmplx_mult_cmplx_q31',['../group___cmplx_by_cmplx_mult.html#ga1829e50993a90742de225a0ce4213838',1,'arm_cmplx_mult_cmplx_q31(q31_t *pSrcA, q31_t *pSrcB, q31_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mult_cmplx_q31.c'],['../group___cmplx_by_cmplx_mult.html#ga1829e50993a90742de225a0ce4213838',1,'arm_cmplx_mult_cmplx_q31(q31_t *pSrcA, q31_t *pSrcB, q31_t *pDst, uint32_t numSamples):&#160;arm_cmplx_mult_cmplx_q31.c']]],
  ['arm_5fcmplx_5fmult_5fcmplx_5fq31_2ec',['arm_cmplx_mult_cmplx_q31.c',['../arm__cmplx__mult__cmplx__q31_8c.html',1,'']]],
  ['arm_5fcmplx_5fmult_5freal_5ff32',['arm_cmplx_mult_real_f32',['../group___cmplx_by_real_mult.html#ga9c18616f56cb4d3c0889ce0b339221ca',1,'arm_cmplx_mult_real_f32(float32_t *pSrcCmplx, float32_t *pSrcReal, float32_t *pCmplxDst, uint32_t numSamples):&#160;arm_cmplx_mult_real_f32.c'],['../group___cmplx_by_real_mult.html#ga9c18616f56cb4d3c0889ce0b339221ca',1,'arm_cmplx_mult_real_f32(float32_t *pSrcCmplx, float32_t *pSrcReal, float32_t *pCmplxDst, uint32_t numSamples):&#160;arm_cmplx_mult_real_f32.c']]],
  ['arm_5fcmplx_5fmult_5freal_5ff32_2ec',['arm_cmplx_mult_real_f32.c',['../arm__cmplx__mult__real__f32_8c.html',1,'']]],
  ['arm_5fcmplx_5fmult_5freal_5fq15',['arm_cmplx_mult_real_q15',['../group___cmplx_by_real_mult.html#ga3bd8889dcb45980e1d3e53344df54e85',1,'arm_cmplx_mult_real_q15(q15_t *pSrcCmplx, q15_t *pSrcReal, q15_t *pCmplxDst, uint32_t numSamples):&#160;arm_cmplx_mult_real_q15.c'],['../group___cmplx_by_real_mult.html#ga3bd8889dcb45980e1d3e53344df54e85',1,'arm_cmplx_mult_real_q15(q15_t *pSrcCmplx, q15_t *pSrcReal, q15_t *pCmplxDst, uint32_t numSamples):&#160;arm_cmplx_mult_real_q15.c']]],
  ['arm_5fcmplx_5fmult_5freal_5fq15_2ec',['arm_cmplx_mult_real_q15.c',['../arm__cmplx__mult__real__q15_8c.html',1,'']]],
  ['arm_5fcmplx_5fmult_5freal_5fq31',['arm_cmplx_mult_real_q31',['../group___cmplx_by_real_mult.html#ga715e4bb8e945b8ca51ec5237611697ce',1,'arm_cmplx_mult_real_q31(q31_t *pSrcCmplx, q31_t *pSrcReal, q31_t *pCmplxDst, uint32_t numSamples):&#160;arm_cmplx_mult_real_q31.c'],['../group___cmplx_by_real_mult.html#ga715e4bb8e945b8ca51ec5237611697ce',1,'arm_cmplx_mult_real_q31(q31_t *pSrcCmplx, q31_t *pSrcReal, q31_t *pCmplxDst, uint32_t numSamples):&#160;arm_cmplx_mult_real_q31.c']]],
  ['arm_5fcmplx_5fmult_5freal_5fq31_2ec',['arm_cmplx_mult_real_q31.c',['../arm__cmplx__mult__real__q31_8c.html',1,'']]],
  ['arm_5fcommon_5ftables_2ec',['arm_common_tables.c',['../arm__common__tables_8c.html',1,'']]],
  ['arm_5fcommon_5ftables_2eh',['arm_common_tables.h',['../arm__common__tables_8h.html',1,'']]],
  ['arm_5fcompare_5ffixed_5fq15',['arm_compare_fixed_q15',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e',1,'arm_compare_fixed_q15(q15_t *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c']]],
  ['arm_5fcompare_5ffixed_5fq31',['arm_compare_fixed_q31',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb',1,'arm_compare_fixed_q31(q31_t *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c']]],
  ['arm_5fconst_5fstructs_2ec',['arm_const_structs.c',['../arm__const__structs_8c.html',1,'']]],
  ['arm_5fconst_5fstructs_2eh',['arm_const_structs.h',['../arm__const__structs_8h.html',1,'']]],
  ['arm_5fconv_5ff32',['arm_conv_f32',['../group___conv.html#ga3f860dc98c6fc4cafc421e4a2aed3c89',1,'arm_conv_f32(float32_t *pSrcA, uint32_t srcALen, float32_t *pSrcB, uint32_t srcBLen, float32_t *pDst):&#160;arm_conv_f32.c'],['../group___conv.html#ga3f860dc98c6fc4cafc421e4a2aed3c89',1,'arm_conv_f32(float32_t *pSrcA, uint32_t srcALen, float32_t *pSrcB, uint32_t srcBLen, float32_t *pDst):&#160;arm_conv_f32.c']]],
  ['arm_5fconv_5ff32_2ec',['arm_conv_f32.c',['../arm__conv__f32_8c.html',1,'']]],
  ['arm_5fconv_5ffast_5fopt_5fq15',['arm_conv_fast_opt_q15',['../group___conv.html#gaf16f490d245391ec18a42adc73d6d749',1,'arm_conv_fast_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_fast_opt_q15.c'],['../group___conv.html#gaf16f490d245391ec18a42adc73d6d749',1,'arm_conv_fast_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_fast_opt_q15.c']]],
  ['arm_5fconv_5ffast_5fopt_5fq15_2ec',['arm_conv_fast_opt_q15.c',['../arm__conv__fast__opt__q15_8c.html',1,'']]],
  ['arm_5fconv_5ffast_5fq15',['arm_conv_fast_q15',['../group___conv.html#gad75ca978ce906e04abdf86a8d76306d4',1,'arm_conv_fast_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst):&#160;arm_conv_fast_q15.c'],['../group___conv.html#gad75ca978ce906e04abdf86a8d76306d4',1,'arm_conv_fast_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst):&#160;arm_conv_fast_q15.c']]],
  ['arm_5fconv_5ffast_5fq15_2ec',['arm_conv_fast_q15.c',['../arm__conv__fast__q15_8c.html',1,'']]],
  ['arm_5fconv_5ffast_5fq31',['arm_conv_fast_q31',['../group___conv.html#ga51112dcdf9b3624eb05182cdc4da9ec0',1,'arm_conv_fast_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst):&#160;arm_conv_fast_q31.c'],['../group___conv.html#ga51112dcdf9b3624eb05182cdc4da9ec0',1,'arm_conv_fast_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst):&#160;arm_conv_fast_q31.c']]],
  ['arm_5fconv_5ffast_5fq31_2ec',['arm_conv_fast_q31.c',['../arm__conv__fast__q31_8c.html',1,'']]],
  ['arm_5fconv_5fopt_5fq15',['arm_conv_opt_q15',['../group___conv.html#gac77dbcaef5c754cac27eab96c4753a3c',1,'arm_conv_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_opt_q15.c'],['../group___conv.html#gac77dbcaef5c754cac27eab96c4753a3c',1,'arm_conv_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_opt_q15.c']]],
  ['arm_5fconv_5fopt_5fq15_2ec',['arm_conv_opt_q15.c',['../arm__conv__opt__q15_8c.html',1,'']]],
  ['arm_5fconv_5fopt_5fq7',['arm_conv_opt_q7',['../group___conv.html#ga4c7cf073e89d6d57cc4e711f078c3f68',1,'arm_conv_opt_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_opt_q7.c'],['../group___conv.html#ga4c7cf073e89d6d57cc4e711f078c3f68',1,'arm_conv_opt_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_opt_q7.c']]],
  ['arm_5fconv_5fopt_5fq7_2ec',['arm_conv_opt_q7.c',['../arm__conv__opt__q7_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5ff32',['arm_conv_partial_f32',['../group___partial_conv.html#ga16d10f32072cd79fc5fb6e785df45f5e',1,'arm_conv_partial_f32(float32_t *pSrcA, uint32_t srcALen, float32_t *pSrcB, uint32_t srcBLen, float32_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_f32.c'],['../group___partial_conv.html#ga16d10f32072cd79fc5fb6e785df45f5e',1,'arm_conv_partial_f32(float32_t *pSrcA, uint32_t srcALen, float32_t *pSrcB, uint32_t srcBLen, float32_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_f32.c']]],
  ['arm_5fconv_5fpartial_5ff32_2ec',['arm_conv_partial_f32.c',['../arm__conv__partial__f32_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5ffast_5fopt_5fq15',['arm_conv_partial_fast_opt_q15',['../group___partial_conv.html#ga3de9c4ddcc7886de25b70d875099a8d9',1,'arm_conv_partial_fast_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, uint32_t firstIndex, uint32_t numPoints, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_partial_fast_opt_q15.c'],['../group___partial_conv.html#ga3de9c4ddcc7886de25b70d875099a8d9',1,'arm_conv_partial_fast_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, uint32_t firstIndex, uint32_t numPoints, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_partial_fast_opt_q15.c']]],
  ['arm_5fconv_5fpartial_5ffast_5fopt_5fq15_2ec',['arm_conv_partial_fast_opt_q15.c',['../arm__conv__partial__fast__opt__q15_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5ffast_5fq15',['arm_conv_partial_fast_q15',['../group___partial_conv.html#ga1e4d43385cb62262a78c6752fe1fafb2',1,'arm_conv_partial_fast_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_fast_q15.c'],['../group___partial_conv.html#ga1e4d43385cb62262a78c6752fe1fafb2',1,'arm_conv_partial_fast_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_fast_q15.c']]],
  ['arm_5fconv_5fpartial_5ffast_5fq15_2ec',['arm_conv_partial_fast_q15.c',['../arm__conv__partial__fast__q15_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5ffast_5fq31',['arm_conv_partial_fast_q31',['../group___partial_conv.html#ga10c5294cda8c4985386f4e3944be7650',1,'arm_conv_partial_fast_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_fast_q31.c'],['../group___partial_conv.html#ga10c5294cda8c4985386f4e3944be7650',1,'arm_conv_partial_fast_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_fast_q31.c']]],
  ['arm_5fconv_5fpartial_5ffast_5fq31_2ec',['arm_conv_partial_fast_q31.c',['../arm__conv__partial__fast__q31_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5fopt_5fq15',['arm_conv_partial_opt_q15',['../group___partial_conv.html#ga834b23b4ade8682beeb55778399101f8',1,'arm_conv_partial_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, uint32_t firstIndex, uint32_t numPoints, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_partial_opt_q15.c'],['../group___partial_conv.html#ga834b23b4ade8682beeb55778399101f8',1,'arm_conv_partial_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, uint32_t firstIndex, uint32_t numPoints, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_partial_opt_q15.c']]],
  ['arm_5fconv_5fpartial_5fopt_5fq15_2ec',['arm_conv_partial_opt_q15.c',['../arm__conv__partial__opt__q15_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5fopt_5fq7',['arm_conv_partial_opt_q7',['../group___partial_conv.html#ga3707e16af1435b215840006a7ab0c98f',1,'arm_conv_partial_opt_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst, uint32_t firstIndex, uint32_t numPoints, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_partial_opt_q7.c'],['../group___partial_conv.html#ga3707e16af1435b215840006a7ab0c98f',1,'arm_conv_partial_opt_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst, uint32_t firstIndex, uint32_t numPoints, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_conv_partial_opt_q7.c']]],
  ['arm_5fconv_5fpartial_5fopt_5fq7_2ec',['arm_conv_partial_opt_q7.c',['../arm__conv__partial__opt__q7_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5fq15',['arm_conv_partial_q15',['../group___partial_conv.html#ga209a2a913a0c5e5679c5988da8f46b03',1,'arm_conv_partial_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_q15.c'],['../group___partial_conv.html#ga209a2a913a0c5e5679c5988da8f46b03',1,'arm_conv_partial_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_q15.c']]],
  ['arm_5fconv_5fpartial_5fq15_2ec',['arm_conv_partial_q15.c',['../arm__conv__partial__q15_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5fq31',['arm_conv_partial_q31',['../group___partial_conv.html#ga78e73a5f02d103168a09821fb461e77a',1,'arm_conv_partial_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_q31.c'],['../group___partial_conv.html#ga78e73a5f02d103168a09821fb461e77a',1,'arm_conv_partial_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_q31.c']]],
  ['arm_5fconv_5fpartial_5fq31_2ec',['arm_conv_partial_q31.c',['../arm__conv__partial__q31_8c.html',1,'']]],
  ['arm_5fconv_5fpartial_5fq7',['arm_conv_partial_q7',['../group___partial_conv.html#ga8567259fe18396dd972242c41741ebf4',1,'arm_conv_partial_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_q7.c'],['../group___partial_conv.html#ga8567259fe18396dd972242c41741ebf4',1,'arm_conv_partial_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst, uint32_t firstIndex, uint32_t numPoints):&#160;arm_conv_partial_q7.c']]],
  ['arm_5fconv_5fpartial_5fq7_2ec',['arm_conv_partial_q7.c',['../arm__conv__partial__q7_8c.html',1,'']]],
  ['arm_5fconv_5fq15',['arm_conv_q15',['../group___conv.html#gaccd6a89b0ff7a94df64610598e6e6893',1,'arm_conv_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst):&#160;arm_conv_q15.c'],['../group___conv.html#gaccd6a89b0ff7a94df64610598e6e6893',1,'arm_conv_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst):&#160;arm_conv_q15.c']]],
  ['arm_5fconv_5fq15_2ec',['arm_conv_q15.c',['../arm__conv__q15_8c.html',1,'']]],
  ['arm_5fconv_5fq31',['arm_conv_q31',['../group___conv.html#ga946b58da734f1e4e78c91fcaab4b12b6',1,'arm_conv_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst):&#160;arm_conv_q31.c'],['../group___conv.html#ga946b58da734f1e4e78c91fcaab4b12b6',1,'arm_conv_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst):&#160;arm_conv_q31.c']]],
  ['arm_5fconv_5fq31_2ec',['arm_conv_q31.c',['../arm__conv__q31_8c.html',1,'']]],
  ['arm_5fconv_5fq7',['arm_conv_q7',['../group___conv.html#gae2070cb792a167e78dbad8d06b97cdab',1,'arm_conv_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst):&#160;arm_conv_q7.c'],['../group___conv.html#gae2070cb792a167e78dbad8d06b97cdab',1,'arm_conv_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst):&#160;arm_conv_q7.c']]],
  ['arm_5fconv_5fq7_2ec',['arm_conv_q7.c',['../arm__conv__q7_8c.html',1,'']]],
  ['arm_5fconvolution_5fexample_5ff32_2ec',['arm_convolution_example_f32.c',['../_a_r_m_2arm__convolution__example__f32_8c.html',1,'']]],
  ['arm_5fcopy_5ff32',['arm_copy_f32',['../group__copy.html#gadd1f737e677e0e6ca31767c7001417b3',1,'arm_copy_f32(float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_copy_f32.c'],['../group__copy.html#gadd1f737e677e0e6ca31767c7001417b3',1,'arm_copy_f32(float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_copy_f32.c']]],
  ['arm_5fcopy_5ff32_2ec',['arm_copy_f32.c',['../arm__copy__f32_8c.html',1,'']]],
  ['arm_5fcopy_5fq15',['arm_copy_q15',['../group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0',1,'arm_copy_q15(q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_copy_q15.c'],['../group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0',1,'arm_copy_q15(q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_copy_q15.c']]],
  ['arm_5fcopy_5fq15_2ec',['arm_copy_q15.c',['../arm__copy__q15_8c.html',1,'']]],
  ['arm_5fcopy_5fq31',['arm_copy_q31',['../group__copy.html#gaddf70be7e3f87e535c324862b501f3f9',1,'arm_copy_q31(q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_copy_q31.c'],['../group__copy.html#gaddf70be7e3f87e535c324862b501f3f9',1,'arm_copy_q31(q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_copy_q31.c']]],
  ['arm_5fcopy_5fq31_2ec',['arm_copy_q31.c',['../arm__copy__q31_8c.html',1,'']]],
  ['arm_5fcopy_5fq7',['arm_copy_q7',['../group__copy.html#ga467579beda492aa92797529d794c88fb',1,'arm_copy_q7(q7_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_copy_q7.c'],['../group__copy.html#ga467579beda492aa92797529d794c88fb',1,'arm_copy_q7(q7_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_copy_q7.c']]],
  ['arm_5fcopy_5fq7_2ec',['arm_copy_q7.c',['../arm__copy__q7_8c.html',1,'']]],
  ['arm_5fcorrelate_5ff32',['arm_correlate_f32',['../group___corr.html#ga22021e4222773f01e9960358a531cfb8',1,'arm_correlate_f32(float32_t *pSrcA, uint32_t srcALen, float32_t *pSrcB, uint32_t srcBLen, float32_t *pDst):&#160;arm_correlate_f32.c'],['../group___corr.html#ga22021e4222773f01e9960358a531cfb8',1,'arm_correlate_f32(float32_t *pSrcA, uint32_t srcALen, float32_t *pSrcB, uint32_t srcBLen, float32_t *pDst):&#160;arm_correlate_f32.c']]],
  ['arm_5fcorrelate_5ff32_2ec',['arm_correlate_f32.c',['../arm__correlate__f32_8c.html',1,'']]],
  ['arm_5fcorrelate_5ffast_5fopt_5fq15',['arm_correlate_fast_opt_q15',['../group___corr.html#ga40a0236b17220e8e22a22b5bc1c53c6b',1,'arm_correlate_fast_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, q15_t *pScratch):&#160;arm_correlate_fast_opt_q15.c'],['../group___corr.html#ga40a0236b17220e8e22a22b5bc1c53c6b',1,'arm_correlate_fast_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, q15_t *pScratch):&#160;arm_correlate_fast_opt_q15.c']]],
  ['arm_5fcorrelate_5ffast_5fopt_5fq15_2ec',['arm_correlate_fast_opt_q15.c',['../arm__correlate__fast__opt__q15_8c.html',1,'']]],
  ['arm_5fcorrelate_5ffast_5fq15',['arm_correlate_fast_q15',['../group___corr.html#gac8de3da44f58e86c2c86156276ca154f',1,'arm_correlate_fast_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst):&#160;arm_correlate_fast_q15.c'],['../group___corr.html#gac8de3da44f58e86c2c86156276ca154f',1,'arm_correlate_fast_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst):&#160;arm_correlate_fast_q15.c']]],
  ['arm_5fcorrelate_5ffast_5fq15_2ec',['arm_correlate_fast_q15.c',['../arm__correlate__fast__q15_8c.html',1,'']]],
  ['arm_5fcorrelate_5ffast_5fq31',['arm_correlate_fast_q31',['../group___corr.html#gabecd3d7b077dbbef43f93e9e037815ed',1,'arm_correlate_fast_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst):&#160;arm_correlate_fast_q31.c'],['../group___corr.html#gabecd3d7b077dbbef43f93e9e037815ed',1,'arm_correlate_fast_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst):&#160;arm_correlate_fast_q31.c']]],
  ['arm_5fcorrelate_5ffast_5fq31_2ec',['arm_correlate_fast_q31.c',['../arm__correlate__fast__q31_8c.html',1,'']]],
  ['arm_5fcorrelate_5fopt_5fq15',['arm_correlate_opt_q15',['../group___corr.html#gad71c0ec70ec69edbc48563d9a5f68451',1,'arm_correlate_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, q15_t *pScratch):&#160;arm_correlate_opt_q15.c'],['../group___corr.html#gad71c0ec70ec69edbc48563d9a5f68451',1,'arm_correlate_opt_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst, q15_t *pScratch):&#160;arm_correlate_opt_q15.c']]],
  ['arm_5fcorrelate_5fopt_5fq15_2ec',['arm_correlate_opt_q15.c',['../arm__correlate__opt__q15_8c.html',1,'']]],
  ['arm_5fcorrelate_5fopt_5fq7',['arm_correlate_opt_q7',['../group___corr.html#ga746e8857cafe33ec5d6780729c18c311',1,'arm_correlate_opt_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_correlate_opt_q7.c'],['../group___corr.html#ga746e8857cafe33ec5d6780729c18c311',1,'arm_correlate_opt_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst, q15_t *pScratch1, q15_t *pScratch2):&#160;arm_correlate_opt_q7.c']]],
  ['arm_5fcorrelate_5fopt_5fq7_2ec',['arm_correlate_opt_q7.c',['../arm__correlate__opt__q7_8c.html',1,'']]],
  ['arm_5fcorrelate_5fq15',['arm_correlate_q15',['../group___corr.html#ga5ec96b8e420d68b0e626df0812274d46',1,'arm_correlate_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst):&#160;arm_correlate_q15.c'],['../group___corr.html#ga5ec96b8e420d68b0e626df0812274d46',1,'arm_correlate_q15(q15_t *pSrcA, uint32_t srcALen, q15_t *pSrcB, uint32_t srcBLen, q15_t *pDst):&#160;arm_correlate_q15.c']]],
  ['arm_5fcorrelate_5fq15_2ec',['arm_correlate_q15.c',['../arm__correlate__q15_8c.html',1,'']]],
  ['arm_5fcorrelate_5fq31',['arm_correlate_q31',['../group___corr.html#ga1367dc6c80476406c951e68d7fac4e8c',1,'arm_correlate_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst):&#160;arm_correlate_q31.c'],['../group___corr.html#ga1367dc6c80476406c951e68d7fac4e8c',1,'arm_correlate_q31(q31_t *pSrcA, uint32_t srcALen, q31_t *pSrcB, uint32_t srcBLen, q31_t *pDst):&#160;arm_correlate_q31.c']]],
  ['arm_5fcorrelate_5fq31_2ec',['arm_correlate_q31.c',['../arm__correlate__q31_8c.html',1,'']]],
  ['arm_5fcorrelate_5fq7',['arm_correlate_q7',['../group___corr.html#ga284ddcc49e4ac532d52a70d0383c5992',1,'arm_correlate_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst):&#160;arm_correlate_q7.c'],['../group___corr.html#ga284ddcc49e4ac532d52a70d0383c5992',1,'arm_correlate_q7(q7_t *pSrcA, uint32_t srcALen, q7_t *pSrcB, uint32_t srcBLen, q7_t *pDst):&#160;arm_correlate_q7.c']]],
  ['arm_5fcorrelate_5fq7_2ec',['arm_correlate_q7.c',['../arm__correlate__q7_8c.html',1,'']]],
  ['arm_5fcos_5ff32',['arm_cos_f32',['../group__cos.html#gace15287f9c64b9b4084d1c797d4c49d8',1,'arm_cos_f32(float32_t x):&#160;arm_cos_f32.c'],['../group__cos.html#gace15287f9c64b9b4084d1c797d4c49d8',1,'arm_cos_f32(float32_t x):&#160;arm_cos_f32.c']]],
  ['arm_5fcos_5ff32_2ec',['arm_cos_f32.c',['../arm__cos__f32_8c.html',1,'']]],
  ['arm_5fcos_5fq15',['arm_cos_q15',['../group__cos.html#gadfd60c24def501638c0d5db20f4c869b',1,'arm_cos_q15(q15_t x):&#160;arm_cos_q15.c'],['../group__cos.html#gadfd60c24def501638c0d5db20f4c869b',1,'arm_cos_q15(q15_t x):&#160;arm_cos_q15.c']]],
  ['arm_5fcos_5fq15_2ec',['arm_cos_q15.c',['../arm__cos__q15_8c.html',1,'']]],
  ['arm_5fcos_5fq31',['arm_cos_q31',['../group__cos.html#gad80f121949ef885a77d83ab36e002567',1,'arm_cos_q31(q31_t x):&#160;arm_cos_q31.c'],['../group__cos.html#gad80f121949ef885a77d83ab36e002567',1,'arm_cos_q31(q31_t x):&#160;arm_cos_q31.c']]],
  ['arm_5fcos_5fq31_2ec',['arm_cos_q31.c',['../arm__cos__q31_8c.html',1,'']]],
  ['arm_5fdct4_5ff32',['arm_dct4_f32',['../group___d_c_t4___i_d_c_t4.html#gafd538d68886848bc090ec2b0d364cc81',1,'arm_dct4_f32(const arm_dct4_instance_f32 *S, float32_t *pState, float32_t *pInlineBuffer):&#160;arm_dct4_f32.c'],['../group___d_c_t4___i_d_c_t4.html#gafd538d68886848bc090ec2b0d364cc81',1,'arm_dct4_f32(const arm_dct4_instance_f32 *S, float32_t *pState, float32_t *pInlineBuffer):&#160;arm_dct4_f32.c']]],
  ['arm_5fdct4_5ff32_2ec',['arm_dct4_f32.c',['../arm__dct4__f32_8c.html',1,'']]],
  ['arm_5fdct4_5finit_5ff32',['arm_dct4_init_f32',['../group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06',1,'arm_dct4_init_f32(arm_dct4_instance_f32 *S, arm_rfft_instance_f32 *S_RFFT, arm_cfft_radix4_instance_f32 *S_CFFT, uint16_t N, uint16_t Nby2, float32_t normalize):&#160;arm_dct4_init_f32.c'],['../group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06',1,'arm_dct4_init_f32(arm_dct4_instance_f32 *S, arm_rfft_instance_f32 *S_RFFT, arm_cfft_radix4_instance_f32 *S_CFFT, uint16_t N, uint16_t Nby2, float32_t normalize):&#160;arm_dct4_init_f32.c']]],
  ['arm_5fdct4_5finit_5ff32_2ec',['arm_dct4_init_f32.c',['../arm__dct4__init__f32_8c.html',1,'']]],
  ['arm_5fdct4_5finit_5fq15',['arm_dct4_init_q15',['../group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054',1,'arm_dct4_init_q15(arm_dct4_instance_q15 *S, arm_rfft_instance_q15 *S_RFFT, arm_cfft_radix4_instance_q15 *S_CFFT, uint16_t N, uint16_t Nby2, q15_t normalize):&#160;arm_dct4_init_q15.c'],['../group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054',1,'arm_dct4_init_q15(arm_dct4_instance_q15 *S, arm_rfft_instance_q15 *S_RFFT, arm_cfft_radix4_instance_q15 *S_CFFT, uint16_t N, uint16_t Nby2, q15_t normalize):&#160;arm_dct4_init_q15.c']]],
  ['arm_5fdct4_5finit_5fq15_2ec',['arm_dct4_init_q15.c',['../arm__dct4__init__q15_8c.html',1,'']]],
  ['arm_5fdct4_5finit_5fq31',['arm_dct4_init_q31',['../group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a',1,'arm_dct4_init_q31(arm_dct4_instance_q31 *S, arm_rfft_instance_q31 *S_RFFT, arm_cfft_radix4_instance_q31 *S_CFFT, uint16_t N, uint16_t Nby2, q31_t normalize):&#160;arm_dct4_init_q31.c'],['../group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a',1,'arm_dct4_init_q31(arm_dct4_instance_q31 *S, arm_rfft_instance_q31 *S_RFFT, arm_cfft_radix4_instance_q31 *S_CFFT, uint16_t N, uint16_t Nby2, q31_t normalize):&#160;arm_dct4_init_q31.c']]],
  ['arm_5fdct4_5finit_5fq31_2ec',['arm_dct4_init_q31.c',['../arm__dct4__init__q31_8c.html',1,'']]],
  ['arm_5fdct4_5finstance_5ff32',['arm_dct4_instance_f32',['../structarm__dct4__instance__f32.html',1,'']]],
  ['arm_5fdct4_5finstance_5fq15',['arm_dct4_instance_q15',['../structarm__dct4__instance__q15.html',1,'']]],
  ['arm_5fdct4_5finstance_5fq31',['arm_dct4_instance_q31',['../structarm__dct4__instance__q31.html',1,'']]],
  ['arm_5fdct4_5fq15',['arm_dct4_q15',['../group___d_c_t4___i_d_c_t4.html#ga114cb9635059f678df291fcc887aaf2b',1,'arm_dct4_q15(const arm_dct4_instance_q15 *S, q15_t *pState, q15_t *pInlineBuffer):&#160;arm_dct4_q15.c'],['../group___d_c_t4___i_d_c_t4.html#ga114cb9635059f678df291fcc887aaf2b',1,'arm_dct4_q15(const arm_dct4_instance_q15 *S, q15_t *pState, q15_t *pInlineBuffer):&#160;arm_dct4_q15.c']]],
  ['arm_5fdct4_5fq15_2ec',['arm_dct4_q15.c',['../arm__dct4__q15_8c.html',1,'']]],
  ['arm_5fdct4_5fq31',['arm_dct4_q31',['../group___d_c_t4___i_d_c_t4.html#gad04d0baab6ed081d8e8afe02538eb80b',1,'arm_dct4_q31(const arm_dct4_instance_q31 *S, q31_t *pState, q31_t *pInlineBuffer):&#160;arm_dct4_q31.c'],['../group___d_c_t4___i_d_c_t4.html#gad04d0baab6ed081d8e8afe02538eb80b',1,'arm_dct4_q31(const arm_dct4_instance_q31 *S, q31_t *pState, q31_t *pInlineBuffer):&#160;arm_dct4_q31.c']]],
  ['arm_5fdct4_5fq31_2ec',['arm_dct4_q31.c',['../arm__dct4__q31_8c.html',1,'']]],
  ['arm_5fdot_5fprod_5ff32',['arm_dot_prod_f32',['../group__dot__prod.html#ga55418d4362f6ba84c327f9b4f089a8c3',1,'arm_dot_prod_f32(float32_t *pSrcA, float32_t *pSrcB, uint32_t blockSize, float32_t *result):&#160;arm_dot_prod_f32.c'],['../group__dot__prod.html#ga55418d4362f6ba84c327f9b4f089a8c3',1,'arm_dot_prod_f32(float32_t *pSrcA, float32_t *pSrcB, uint32_t blockSize, float32_t *result):&#160;arm_dot_prod_f32.c']]],
  ['arm_5fdot_5fprod_5ff32_2ec',['arm_dot_prod_f32.c',['../arm__dot__prod__f32_8c.html',1,'']]],
  ['arm_5fdot_5fprod_5fq15',['arm_dot_prod_q15',['../group__dot__prod.html#ga436d5bed28a4b73b24acbde436a3044b',1,'arm_dot_prod_q15(q15_t *pSrcA, q15_t *pSrcB, uint32_t blockSize, q63_t *result):&#160;arm_dot_prod_q15.c'],['../group__dot__prod.html#ga436d5bed28a4b73b24acbde436a3044b',1,'arm_dot_prod_q15(q15_t *pSrcA, q15_t *pSrcB, uint32_t blockSize, q63_t *result):&#160;arm_dot_prod_q15.c']]],
  ['arm_5fdot_5fprod_5fq15_2ec',['arm_dot_prod_q15.c',['../arm__dot__prod__q15_8c.html',1,'']]],
  ['arm_5fdot_5fprod_5fq31',['arm_dot_prod_q31',['../group__dot__prod.html#gab15d8fa060fc85b4d948d091b7deaa11',1,'arm_dot_prod_q31(q31_t *pSrcA, q31_t *pSrcB, uint32_t blockSize, q63_t *result):&#160;arm_dot_prod_q31.c'],['../group__dot__prod.html#gab15d8fa060fc85b4d948d091b7deaa11',1,'arm_dot_prod_q31(q31_t *pSrcA, q31_t *pSrcB, uint32_t blockSize, q63_t *result):&#160;arm_dot_prod_q31.c']]],
  ['arm_5fdot_5fprod_5fq31_2ec',['arm_dot_prod_q31.c',['../arm__dot__prod__q31_8c.html',1,'']]],
  ['arm_5fdot_5fprod_5fq7',['arm_dot_prod_q7',['../group__dot__prod.html#ga9c3293a50ac7ec8ba928bf8e3aaea6c1',1,'arm_dot_prod_q7(q7_t *pSrcA, q7_t *pSrcB, uint32_t blockSize, q31_t *result):&#160;arm_dot_prod_q7.c'],['../group__dot__prod.html#ga9c3293a50ac7ec8ba928bf8e3aaea6c1',1,'arm_dot_prod_q7(q7_t *pSrcA, q7_t *pSrcB, uint32_t blockSize, q31_t *result):&#160;arm_dot_prod_q7.c']]],
  ['arm_5fdot_5fprod_5fq7_2ec',['arm_dot_prod_q7.c',['../arm__dot__prod__q7_8c.html',1,'']]],
  ['arm_5fdotproduct_5fexample_5ff32_2ec',['arm_dotproduct_example_f32.c',['../_a_r_m_2arm__dotproduct__example__f32_8c.html',1,'']]],
  ['arm_5ffft_5fbin_5fdata_2ec',['arm_fft_bin_data.c',['../_a_r_m_2arm__fft__bin__data_8c.html',1,'']]],
  ['arm_5ffft_5fbin_5fexample_5ff32_2ec',['arm_fft_bin_example_f32.c',['../_a_r_m_2arm__fft__bin__example__f32_8c.html',1,'']]],
  ['arm_5ffill_5ff32',['arm_fill_f32',['../group___fill.html#ga2248e8d3901b4afb7827163132baad94',1,'arm_fill_f32(float32_t value, float32_t *pDst, uint32_t blockSize):&#160;arm_fill_f32.c'],['../group___fill.html#ga2248e8d3901b4afb7827163132baad94',1,'arm_fill_f32(float32_t value, float32_t *pDst, uint32_t blockSize):&#160;arm_fill_f32.c']]],
  ['arm_5ffill_5ff32_2ec',['arm_fill_f32.c',['../arm__fill__f32_8c.html',1,'']]],
  ['arm_5ffill_5fq15',['arm_fill_q15',['../group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8',1,'arm_fill_q15(q15_t value, q15_t *pDst, uint32_t blockSize):&#160;arm_fill_q15.c'],['../group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8',1,'arm_fill_q15(q15_t value, q15_t *pDst, uint32_t blockSize):&#160;arm_fill_q15.c']]],
  ['arm_5ffill_5fq15_2ec',['arm_fill_q15.c',['../arm__fill__q15_8c.html',1,'']]],
  ['arm_5ffill_5fq31',['arm_fill_q31',['../group___fill.html#ga69cc781cf337bd0a31bb85c772a35f7f',1,'arm_fill_q31(q31_t value, q31_t *pDst, uint32_t blockSize):&#160;arm_fill_q31.c'],['../group___fill.html#ga69cc781cf337bd0a31bb85c772a35f7f',1,'arm_fill_q31(q31_t value, q31_t *pDst, uint32_t blockSize):&#160;arm_fill_q31.c']]],
  ['arm_5ffill_5fq31_2ec',['arm_fill_q31.c',['../arm__fill__q31_8c.html',1,'']]],
  ['arm_5ffill_5fq7',['arm_fill_q7',['../group___fill.html#ga0465cf326ada039ed792f94b033d9ec5',1,'arm_fill_q7(q7_t value, q7_t *pDst, uint32_t blockSize):&#160;arm_fill_q7.c'],['../group___fill.html#ga0465cf326ada039ed792f94b033d9ec5',1,'arm_fill_q7(q7_t value, q7_t *pDst, uint32_t blockSize):&#160;arm_fill_q7.c']]],
  ['arm_5ffill_5fq7_2ec',['arm_fill_q7.c',['../arm__fill__q7_8c.html',1,'']]],
  ['arm_5ffir_5fdata_2ec',['arm_fir_data.c',['../arm__fir__data_8c.html',1,'']]],
  ['arm_5ffir_5fdecimate_5ff32',['arm_fir_decimate_f32',['../group___f_i_r__decimate.html#ga25aa3d58a90bf91b6a82272a0bc518f7',1,'arm_fir_decimate_f32(const arm_fir_decimate_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_f32.c'],['../group___f_i_r__decimate.html#ga25aa3d58a90bf91b6a82272a0bc518f7',1,'arm_fir_decimate_f32(const arm_fir_decimate_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_f32.c']]],
  ['arm_5ffir_5fdecimate_5ff32_2ec',['arm_fir_decimate_f32.c',['../arm__fir__decimate__f32_8c.html',1,'']]],
  ['arm_5ffir_5fdecimate_5ffast_5fq15',['arm_fir_decimate_fast_q15',['../group___f_i_r__decimate.html#ga3f434c9a5d3b4e68061feac0714ea2ac',1,'arm_fir_decimate_fast_q15(const arm_fir_decimate_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_fast_q15.c'],['../group___f_i_r__decimate.html#ga3f434c9a5d3b4e68061feac0714ea2ac',1,'arm_fir_decimate_fast_q15(const arm_fir_decimate_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_fast_q15.c']]],
  ['arm_5ffir_5fdecimate_5ffast_5fq15_2ec',['arm_fir_decimate_fast_q15.c',['../arm__fir__decimate__fast__q15_8c.html',1,'']]],
  ['arm_5ffir_5fdecimate_5ffast_5fq31',['arm_fir_decimate_fast_q31',['../group___f_i_r__decimate.html#ga3c18cc3d0548a410c577f1bead9582b7',1,'arm_fir_decimate_fast_q31(arm_fir_decimate_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_fast_q31.c'],['../group___f_i_r__decimate.html#ga3c18cc3d0548a410c577f1bead9582b7',1,'arm_fir_decimate_fast_q31(arm_fir_decimate_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_fast_q31.c']]],
  ['arm_5ffir_5fdecimate_5ffast_5fq31_2ec',['arm_fir_decimate_fast_q31.c',['../arm__fir__decimate__fast__q31_8c.html',1,'']]],
  ['arm_5ffir_5fdecimate_5finit_5ff32',['arm_fir_decimate_init_f32',['../group___f_i_r__decimate.html#gaaa2524b08220fd6c3f753e692ffc7d3b',1,'arm_fir_decimate_init_f32(arm_fir_decimate_instance_f32 *S, uint16_t numTaps, uint8_t M, float32_t *pCoeffs, float32_t *pState, uint32_t blockSize):&#160;arm_fir_decimate_init_f32.c'],['../group___f_i_r__decimate.html#gaaa2524b08220fd6c3f753e692ffc7d3b',1,'arm_fir_decimate_init_f32(arm_fir_decimate_instance_f32 *S, uint16_t numTaps, uint8_t M, float32_t *pCoeffs, float32_t *pState, uint32_t blockSize):&#160;arm_fir_decimate_init_f32.c']]],
  ['arm_5ffir_5fdecimate_5finit_5ff32_2ec',['arm_fir_decimate_init_f32.c',['../arm__fir__decimate__init__f32_8c.html',1,'']]],
  ['arm_5ffir_5fdecimate_5finit_5fq15',['arm_fir_decimate_init_q15',['../group___f_i_r__decimate.html#gada660e54b93d5d32178c6f5e1c6f368d',1,'arm_fir_decimate_init_q15(arm_fir_decimate_instance_q15 *S, uint16_t numTaps, uint8_t M, q15_t *pCoeffs, q15_t *pState, uint32_t blockSize):&#160;arm_fir_decimate_init_q15.c'],['../group___f_i_r__decimate.html#gada660e54b93d5d32178c6f5e1c6f368d',1,'arm_fir_decimate_init_q15(arm_fir_decimate_instance_q15 *S, uint16_t numTaps, uint8_t M, q15_t *pCoeffs, q15_t *pState, uint32_t blockSize):&#160;arm_fir_decimate_init_q15.c']]],
  ['arm_5ffir_5fdecimate_5finit_5fq15_2ec',['arm_fir_decimate_init_q15.c',['../arm__fir__decimate__init__q15_8c.html',1,'']]],
  ['arm_5ffir_5fdecimate_5finit_5fq31',['arm_fir_decimate_init_q31',['../group___f_i_r__decimate.html#ga9ed47c4e0f58affa935d84e0508a7f39',1,'arm_fir_decimate_init_q31(arm_fir_decimate_instance_q31 *S, uint16_t numTaps, uint8_t M, q31_t *pCoeffs, q31_t *pState, uint32_t blockSize):&#160;arm_fir_decimate_init_q31.c'],['../group___f_i_r__decimate.html#ga9ed47c4e0f58affa935d84e0508a7f39',1,'arm_fir_decimate_init_q31(arm_fir_decimate_instance_q31 *S, uint16_t numTaps, uint8_t M, q31_t *pCoeffs, q31_t *pState, uint32_t blockSize):&#160;arm_fir_decimate_init_q31.c']]],
  ['arm_5ffir_5fdecimate_5finit_5fq31_2ec',['arm_fir_decimate_init_q31.c',['../arm__fir__decimate__init__q31_8c.html',1,'']]],
  ['arm_5ffir_5fdecimate_5finstance_5ff32',['arm_fir_decimate_instance_f32',['../structarm__fir__decimate__instance__f32.html',1,'']]],
  ['arm_5ffir_5fdecimate_5finstance_5fq15',['arm_fir_decimate_instance_q15',['../structarm__fir__decimate__instance__q15.html',1,'']]],
  ['arm_5ffir_5fdecimate_5finstance_5fq31',['arm_fir_decimate_instance_q31',['../structarm__fir__decimate__instance__q31.html',1,'']]],
  ['arm_5ffir_5fdecimate_5fq15',['arm_fir_decimate_q15',['../group___f_i_r__decimate.html#gab8bef6d0f6a26fdbfce9485727713ce5',1,'arm_fir_decimate_q15(const arm_fir_decimate_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_q15.c'],['../group___f_i_r__decimate.html#gab8bef6d0f6a26fdbfce9485727713ce5',1,'arm_fir_decimate_q15(const arm_fir_decimate_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_q15.c']]],
  ['arm_5ffir_5fdecimate_5fq15_2ec',['arm_fir_decimate_q15.c',['../arm__fir__decimate__q15_8c.html',1,'']]],
  ['arm_5ffir_5fdecimate_5fq31',['arm_fir_decimate_q31',['../group___f_i_r__decimate.html#gaef8e86add28f15fdc5ecc484e9dd7a4e',1,'arm_fir_decimate_q31(const arm_fir_decimate_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_q31.c'],['../group___f_i_r__decimate.html#gaef8e86add28f15fdc5ecc484e9dd7a4e',1,'arm_fir_decimate_q31(const arm_fir_decimate_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_decimate_q31.c']]],
  ['arm_5ffir_5fdecimate_5fq31_2ec',['arm_fir_decimate_q31.c',['../arm__fir__decimate__q31_8c.html',1,'']]],
  ['arm_5ffir_5fexample_5ff32_2ec',['arm_fir_example_f32.c',['../arm__fir__example__f32_8c.html',1,'']]],
  ['arm_5ffir_5ff32',['arm_fir_f32',['../group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a',1,'arm_fir_f32(const arm_fir_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_fir_f32.c'],['../group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a',1,'arm_fir_f32(const arm_fir_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_fir_f32.c']]],
  ['arm_5ffir_5ff32_2ec',['arm_fir_f32.c',['../arm__fir__f32_8c.html',1,'']]],
  ['arm_5ffir_5ffast_5fq15',['arm_fir_fast_q15',['../group___f_i_r.html#gac7d35e9472e49ccd88800f37f3476bd3',1,'arm_fir_fast_q15(const arm_fir_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_fast_q15.c'],['../group___f_i_r.html#gac7d35e9472e49ccd88800f37f3476bd3',1,'arm_fir_fast_q15(const arm_fir_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_fast_q15.c']]],
  ['arm_5ffir_5ffast_5fq15_2ec',['arm_fir_fast_q15.c',['../arm__fir__fast__q15_8c.html',1,'']]],
  ['arm_5ffir_5ffast_5fq31',['arm_fir_fast_q31',['../group___f_i_r.html#ga70d11af009dcd25594c58c75cdb5d6e3',1,'arm_fir_fast_q31(const arm_fir_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_fast_q31.c'],['../group___f_i_r.html#ga70d11af009dcd25594c58c75cdb5d6e3',1,'arm_fir_fast_q31(const arm_fir_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_fast_q31.c']]],
  ['arm_5ffir_5ffast_5fq31_2ec',['arm_fir_fast_q31.c',['../arm__fir__fast__q31_8c.html',1,'']]],
  ['arm_5ffir_5finit_5ff32',['arm_fir_init_f32',['../group___f_i_r.html#ga98d13def6427e29522829f945d0967db',1,'arm_fir_init_f32(arm_fir_instance_f32 *S, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, uint32_t blockSize):&#160;arm_fir_init_f32.c'],['../group___f_i_r.html#ga98d13def6427e29522829f945d0967db',1,'arm_fir_init_f32(arm_fir_instance_f32 *S, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, uint32_t blockSize):&#160;arm_fir_init_f32.c']]],
  ['arm_5ffir_5finit_5ff32_2ec',['arm_fir_init_f32.c',['../arm__fir__init__f32_8c.html',1,'']]],
  ['arm_5ffir_5finit_5fq15',['arm_fir_init_q15',['../group___f_i_r.html#gae2a50f692f41ba57e44ed0719b1368bd',1,'arm_fir_init_q15(arm_fir_instance_q15 *S, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, uint32_t blockSize):&#160;arm_fir_init_q15.c'],['../group___f_i_r.html#gae2a50f692f41ba57e44ed0719b1368bd',1,'arm_fir_init_q15(arm_fir_instance_q15 *S, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, uint32_t blockSize):&#160;arm_fir_init_q15.c']]],
  ['arm_5ffir_5finit_5fq15_2ec',['arm_fir_init_q15.c',['../arm__fir__init__q15_8c.html',1,'']]],
  ['arm_5ffir_5finit_5fq31',['arm_fir_init_q31',['../group___f_i_r.html#gac00d53af87684cbbe135767b55e748a5',1,'arm_fir_init_q31(arm_fir_instance_q31 *S, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, uint32_t blockSize):&#160;arm_fir_init_q31.c'],['../group___f_i_r.html#gac00d53af87684cbbe135767b55e748a5',1,'arm_fir_init_q31(arm_fir_instance_q31 *S, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, uint32_t blockSize):&#160;arm_fir_init_q31.c']]],
  ['arm_5ffir_5finit_5fq31_2ec',['arm_fir_init_q31.c',['../arm__fir__init__q31_8c.html',1,'']]],
  ['arm_5ffir_5finit_5fq7',['arm_fir_init_q7',['../group___f_i_r.html#ga88e48688224d42dc173dbcec702f0c1d',1,'arm_fir_init_q7(arm_fir_instance_q7 *S, uint16_t numTaps, q7_t *pCoeffs, q7_t *pState, uint32_t blockSize):&#160;arm_fir_init_q7.c'],['../group___f_i_r.html#ga88e48688224d42dc173dbcec702f0c1d',1,'arm_fir_init_q7(arm_fir_instance_q7 *S, uint16_t numTaps, q7_t *pCoeffs, q7_t *pState, uint32_t blockSize):&#160;arm_fir_init_q7.c']]],
  ['arm_5ffir_5finit_5fq7_2ec',['arm_fir_init_q7.c',['../arm__fir__init__q7_8c.html',1,'']]],
  ['arm_5ffir_5finstance_5ff32',['arm_fir_instance_f32',['../structarm__fir__instance__f32.html',1,'']]],
  ['arm_5ffir_5finstance_5fq15',['arm_fir_instance_q15',['../structarm__fir__instance__q15.html',1,'']]],
  ['arm_5ffir_5finstance_5fq31',['arm_fir_instance_q31',['../structarm__fir__instance__q31.html',1,'']]],
  ['arm_5ffir_5finstance_5fq7',['arm_fir_instance_q7',['../structarm__fir__instance__q7.html',1,'']]],
  ['arm_5ffir_5finterpolate_5ff32',['arm_fir_interpolate_f32',['../group___f_i_r___interpolate.html#ga9cae104c5cf60b4e7671c82264a8c12e',1,'arm_fir_interpolate_f32(const arm_fir_interpolate_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_fir_interpolate_f32.c'],['../group___f_i_r___interpolate.html#ga9cae104c5cf60b4e7671c82264a8c12e',1,'arm_fir_interpolate_f32(const arm_fir_interpolate_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_fir_interpolate_f32.c']]],
  ['arm_5ffir_5finterpolate_5ff32_2ec',['arm_fir_interpolate_f32.c',['../arm__fir__interpolate__f32_8c.html',1,'']]],
  ['arm_5ffir_5finterpolate_5finit_5ff32',['arm_fir_interpolate_init_f32',['../group___f_i_r___interpolate.html#ga0f857457a815946f7e4dca989ebf6ff6',1,'arm_fir_interpolate_init_f32(arm_fir_interpolate_instance_f32 *S, uint8_t L, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, uint32_t blockSize):&#160;arm_fir_interpolate_init_f32.c'],['../group___f_i_r___interpolate.html#ga0f857457a815946f7e4dca989ebf6ff6',1,'arm_fir_interpolate_init_f32(arm_fir_interpolate_instance_f32 *S, uint8_t L, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, uint32_t blockSize):&#160;arm_fir_interpolate_init_f32.c']]],
  ['arm_5ffir_5finterpolate_5finit_5ff32_2ec',['arm_fir_interpolate_init_f32.c',['../arm__fir__interpolate__init__f32_8c.html',1,'']]],
  ['arm_5ffir_5finterpolate_5finit_5fq15',['arm_fir_interpolate_init_q15',['../group___f_i_r___interpolate.html#ga18e8c4a74ff1d0f88876cc63f675288f',1,'arm_fir_interpolate_init_q15(arm_fir_interpolate_instance_q15 *S, uint8_t L, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, uint32_t blockSize):&#160;arm_fir_interpolate_init_q15.c'],['../group___f_i_r___interpolate.html#ga18e8c4a74ff1d0f88876cc63f675288f',1,'arm_fir_interpolate_init_q15(arm_fir_interpolate_instance_q15 *S, uint8_t L, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, uint32_t blockSize):&#160;arm_fir_interpolate_init_q15.c']]],
  ['arm_5ffir_5finterpolate_5finit_5fq15_2ec',['arm_fir_interpolate_init_q15.c',['../arm__fir__interpolate__init__q15_8c.html',1,'']]],
  ['arm_5ffir_5finterpolate_5finit_5fq31',['arm_fir_interpolate_init_q31',['../group___f_i_r___interpolate.html#ga9d0ba38ce9f12a850dd242731d307476',1,'arm_fir_interpolate_init_q31(arm_fir_interpolate_instance_q31 *S, uint8_t L, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, uint32_t blockSize):&#160;arm_fir_interpolate_init_q31.c'],['../group___f_i_r___interpolate.html#ga9d0ba38ce9f12a850dd242731d307476',1,'arm_fir_interpolate_init_q31(arm_fir_interpolate_instance_q31 *S, uint8_t L, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, uint32_t blockSize):&#160;arm_fir_interpolate_init_q31.c']]],
  ['arm_5ffir_5finterpolate_5finit_5fq31_2ec',['arm_fir_interpolate_init_q31.c',['../arm__fir__interpolate__init__q31_8c.html',1,'']]],
  ['arm_5ffir_5finterpolate_5finstance_5ff32',['arm_fir_interpolate_instance_f32',['../structarm__fir__interpolate__instance__f32.html',1,'']]],
  ['arm_5ffir_5finterpolate_5finstance_5fq15',['arm_fir_interpolate_instance_q15',['../structarm__fir__interpolate__instance__q15.html',1,'']]],
  ['arm_5ffir_5finterpolate_5finstance_5fq31',['arm_fir_interpolate_instance_q31',['../structarm__fir__interpolate__instance__q31.html',1,'']]],
  ['arm_5ffir_5finterpolate_5fq15',['arm_fir_interpolate_q15',['../group___f_i_r___interpolate.html#ga7962b5f9636e54899f75d0c5936800b5',1,'arm_fir_interpolate_q15(const arm_fir_interpolate_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_interpolate_q15.c'],['../group___f_i_r___interpolate.html#ga7962b5f9636e54899f75d0c5936800b5',1,'arm_fir_interpolate_q15(const arm_fir_interpolate_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_interpolate_q15.c']]],
  ['arm_5ffir_5finterpolate_5fq15_2ec',['arm_fir_interpolate_q15.c',['../arm__fir__interpolate__q15_8c.html',1,'']]],
  ['arm_5ffir_5finterpolate_5fq31',['arm_fir_interpolate_q31',['../group___f_i_r___interpolate.html#gaac9c0f01ed91c53f7083995d7411f5ee',1,'arm_fir_interpolate_q31(const arm_fir_interpolate_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_interpolate_q31.c'],['../group___f_i_r___interpolate.html#gaac9c0f01ed91c53f7083995d7411f5ee',1,'arm_fir_interpolate_q31(const arm_fir_interpolate_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_interpolate_q31.c']]],
  ['arm_5ffir_5finterpolate_5fq31_2ec',['arm_fir_interpolate_q31.c',['../arm__fir__interpolate__q31_8c.html',1,'']]],
  ['arm_5ffir_5flattice_5ff32',['arm_fir_lattice_f32',['../group___f_i_r___lattice.html#gae63a45a63a11a65f2eae8b8b1fe370a8',1,'arm_fir_lattice_f32(const arm_fir_lattice_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_fir_lattice_f32.c'],['../group___f_i_r___lattice.html#gae63a45a63a11a65f2eae8b8b1fe370a8',1,'arm_fir_lattice_f32(const arm_fir_lattice_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_fir_lattice_f32.c']]],
  ['arm_5ffir_5flattice_5ff32_2ec',['arm_fir_lattice_f32.c',['../arm__fir__lattice__f32_8c.html',1,'']]],
  ['arm_5ffir_5flattice_5finit_5ff32',['arm_fir_lattice_init_f32',['../group___f_i_r___lattice.html#ga86199a1590af2b8941c6532ee9d03229',1,'arm_fir_lattice_init_f32(arm_fir_lattice_instance_f32 *S, uint16_t numStages, float32_t *pCoeffs, float32_t *pState):&#160;arm_fir_lattice_init_f32.c'],['../group___f_i_r___lattice.html#ga86199a1590af2b8941c6532ee9d03229',1,'arm_fir_lattice_init_f32(arm_fir_lattice_instance_f32 *S, uint16_t numStages, float32_t *pCoeffs, float32_t *pState):&#160;arm_fir_lattice_init_f32.c']]],
  ['arm_5ffir_5flattice_5finit_5ff32_2ec',['arm_fir_lattice_init_f32.c',['../arm__fir__lattice__init__f32_8c.html',1,'']]],
  ['arm_5ffir_5flattice_5finit_5fq15',['arm_fir_lattice_init_q15',['../group___f_i_r___lattice.html#ga1b22f30ce1cc19bf5a5d7c9fca154d72',1,'arm_fir_lattice_init_q15(arm_fir_lattice_instance_q15 *S, uint16_t numStages, q15_t *pCoeffs, q15_t *pState):&#160;arm_fir_lattice_init_q15.c'],['../group___f_i_r___lattice.html#ga1b22f30ce1cc19bf5a5d7c9fca154d72',1,'arm_fir_lattice_init_q15(arm_fir_lattice_instance_q15 *S, uint16_t numStages, q15_t *pCoeffs, q15_t *pState):&#160;arm_fir_lattice_init_q15.c']]],
  ['arm_5ffir_5flattice_5finit_5fq15_2ec',['arm_fir_lattice_init_q15.c',['../arm__fir__lattice__init__q15_8c.html',1,'']]],
  ['arm_5ffir_5flattice_5finit_5fq31',['arm_fir_lattice_init_q31',['../group___f_i_r___lattice.html#gac05a17a0188bb851b58d19e572870a54',1,'arm_fir_lattice_init_q31(arm_fir_lattice_instance_q31 *S, uint16_t numStages, q31_t *pCoeffs, q31_t *pState):&#160;arm_fir_lattice_init_q31.c'],['../group___f_i_r___lattice.html#gac05a17a0188bb851b58d19e572870a54',1,'arm_fir_lattice_init_q31(arm_fir_lattice_instance_q31 *S, uint16_t numStages, q31_t *pCoeffs, q31_t *pState):&#160;arm_fir_lattice_init_q31.c']]],
  ['arm_5ffir_5flattice_5finit_5fq31_2ec',['arm_fir_lattice_init_q31.c',['../arm__fir__lattice__init__q31_8c.html',1,'']]],
  ['arm_5ffir_5flattice_5finstance_5ff32',['arm_fir_lattice_instance_f32',['../structarm__fir__lattice__instance__f32.html',1,'']]],
  ['arm_5ffir_5flattice_5finstance_5fq15',['arm_fir_lattice_instance_q15',['../structarm__fir__lattice__instance__q15.html',1,'']]],
  ['arm_5ffir_5flattice_5finstance_5fq31',['arm_fir_lattice_instance_q31',['../structarm__fir__lattice__instance__q31.html',1,'']]],
  ['arm_5ffir_5flattice_5fq15',['arm_fir_lattice_q15',['../group___f_i_r___lattice.html#gabb0ab07fd313b4d863070c3ddca51542',1,'arm_fir_lattice_q15(const arm_fir_lattice_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_lattice_q15.c'],['../group___f_i_r___lattice.html#gabb0ab07fd313b4d863070c3ddca51542',1,'arm_fir_lattice_q15(const arm_fir_lattice_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_lattice_q15.c']]],
  ['arm_5ffir_5flattice_5fq15_2ec',['arm_fir_lattice_q15.c',['../arm__fir__lattice__q15_8c.html',1,'']]],
  ['arm_5ffir_5flattice_5fq31',['arm_fir_lattice_q31',['../group___f_i_r___lattice.html#ga2e36fd210e4a1a5dd333ce80dd6d9a88',1,'arm_fir_lattice_q31(const arm_fir_lattice_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_lattice_q31.c'],['../group___f_i_r___lattice.html#ga2e36fd210e4a1a5dd333ce80dd6d9a88',1,'arm_fir_lattice_q31(const arm_fir_lattice_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_lattice_q31.c']]],
  ['arm_5ffir_5flattice_5fq31_2ec',['arm_fir_lattice_q31.c',['../arm__fir__lattice__q31_8c.html',1,'']]],
  ['arm_5ffir_5fq15',['arm_fir_q15',['../group___f_i_r.html#ga262d173058d6f80fdf60404ba262a8f5',1,'arm_fir_q15(const arm_fir_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_q15.c'],['../group___f_i_r.html#ga262d173058d6f80fdf60404ba262a8f5',1,'arm_fir_q15(const arm_fir_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_fir_q15.c']]],
  ['arm_5ffir_5fq15_2ec',['arm_fir_q15.c',['../arm__fir__q15_8c.html',1,'']]],
  ['arm_5ffir_5fq31',['arm_fir_q31',['../group___f_i_r.html#gaadd938c68ab08967cbb5fc696f384bb5',1,'arm_fir_q31(const arm_fir_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_q31.c'],['../group___f_i_r.html#gaadd938c68ab08967cbb5fc696f384bb5',1,'arm_fir_q31(const arm_fir_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_fir_q31.c']]],
  ['arm_5ffir_5fq31_2ec',['arm_fir_q31.c',['../arm__fir__q31_8c.html',1,'']]],
  ['arm_5ffir_5fq7',['arm_fir_q7',['../group___f_i_r.html#ga31c91a0bf0962327ef8f626fae68ea32',1,'arm_fir_q7(const arm_fir_instance_q7 *S, q7_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_fir_q7.c'],['../group___f_i_r.html#ga31c91a0bf0962327ef8f626fae68ea32',1,'arm_fir_q7(const arm_fir_instance_q7 *S, q7_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_fir_q7.c']]],
  ['arm_5ffir_5fq7_2ec',['arm_fir_q7.c',['../arm__fir__q7_8c.html',1,'']]],
  ['arm_5ffir_5fsparse_5ff32',['arm_fir_sparse_f32',['../group___f_i_r___sparse.html#ga23a9284de5ee39406713b91d18ac8838',1,'arm_fir_sparse_f32(arm_fir_sparse_instance_f32 *S, float32_t *pSrc, float32_t *pDst, float32_t *pScratchIn, uint32_t blockSize):&#160;arm_fir_sparse_f32.c'],['../group___f_i_r___sparse.html#ga23a9284de5ee39406713b91d18ac8838',1,'arm_fir_sparse_f32(arm_fir_sparse_instance_f32 *S, float32_t *pSrc, float32_t *pDst, float32_t *pScratchIn, uint32_t blockSize):&#160;arm_fir_sparse_f32.c']]],
  ['arm_5ffir_5fsparse_5ff32_2ec',['arm_fir_sparse_f32.c',['../arm__fir__sparse__f32_8c.html',1,'']]],
  ['arm_5ffir_5fsparse_5finit_5ff32',['arm_fir_sparse_init_f32',['../group___f_i_r___sparse.html#ga86378a08a9d9e1e0e5de77843b34d396',1,'arm_fir_sparse_init_f32(arm_fir_sparse_instance_f32 *S, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t blockSize):&#160;arm_fir_sparse_init_f32.c'],['../group___f_i_r___sparse.html#ga86378a08a9d9e1e0e5de77843b34d396',1,'arm_fir_sparse_init_f32(arm_fir_sparse_instance_f32 *S, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t blockSize):&#160;arm_fir_sparse_init_f32.c']]],
  ['arm_5ffir_5fsparse_5finit_5ff32_2ec',['arm_fir_sparse_init_f32.c',['../arm__fir__sparse__init__f32_8c.html',1,'']]],
  ['arm_5ffir_5fsparse_5finit_5fq15',['arm_fir_sparse_init_q15',['../group___f_i_r___sparse.html#ga5eaa80bf72bcccef5a2c5fc6648d1baa',1,'arm_fir_sparse_init_q15(arm_fir_sparse_instance_q15 *S, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t blockSize):&#160;arm_fir_sparse_init_q15.c'],['../group___f_i_r___sparse.html#ga5eaa80bf72bcccef5a2c5fc6648d1baa',1,'arm_fir_sparse_init_q15(arm_fir_sparse_instance_q15 *S, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t blockSize):&#160;arm_fir_sparse_init_q15.c']]],
  ['arm_5ffir_5fsparse_5finit_5fq15_2ec',['arm_fir_sparse_init_q15.c',['../arm__fir__sparse__init__q15_8c.html',1,'']]],
  ['arm_5ffir_5fsparse_5finit_5fq31',['arm_fir_sparse_init_q31',['../group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634',1,'arm_fir_sparse_init_q31(arm_fir_sparse_instance_q31 *S, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t blockSize):&#160;arm_fir_sparse_init_q31.c'],['../group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634',1,'arm_fir_sparse_init_q31(arm_fir_sparse_instance_q31 *S, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t blockSize):&#160;arm_fir_sparse_init_q31.c']]],
  ['arm_5ffir_5fsparse_5finit_5fq31_2ec',['arm_fir_sparse_init_q31.c',['../arm__fir__sparse__init__q31_8c.html',1,'']]],
  ['arm_5ffir_5fsparse_5finit_5fq7',['arm_fir_sparse_init_q7',['../group___f_i_r___sparse.html#ga98f5c1a097d4572ce4ff3b0c58ebcdbd',1,'arm_fir_sparse_init_q7(arm_fir_sparse_instance_q7 *S, uint16_t numTaps, q7_t *pCoeffs, q7_t *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t blockSize):&#160;arm_fir_sparse_init_q7.c'],['../group___f_i_r___sparse.html#ga98f5c1a097d4572ce4ff3b0c58ebcdbd',1,'arm_fir_sparse_init_q7(arm_fir_sparse_instance_q7 *S, uint16_t numTaps, q7_t *pCoeffs, q7_t *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t blockSize):&#160;arm_fir_sparse_init_q7.c']]],
  ['arm_5ffir_5fsparse_5finit_5fq7_2ec',['arm_fir_sparse_init_q7.c',['../arm__fir__sparse__init__q7_8c.html',1,'']]],
  ['arm_5ffir_5fsparse_5finstance_5ff32',['arm_fir_sparse_instance_f32',['../structarm__fir__sparse__instance__f32.html',1,'']]],
  ['arm_5ffir_5fsparse_5finstance_5fq15',['arm_fir_sparse_instance_q15',['../structarm__fir__sparse__instance__q15.html',1,'']]],
  ['arm_5ffir_5fsparse_5finstance_5fq31',['arm_fir_sparse_instance_q31',['../structarm__fir__sparse__instance__q31.html',1,'']]],
  ['arm_5ffir_5fsparse_5finstance_5fq7',['arm_fir_sparse_instance_q7',['../structarm__fir__sparse__instance__q7.html',1,'']]],
  ['arm_5ffir_5fsparse_5fq15',['arm_fir_sparse_q15',['../group___f_i_r___sparse.html#ga2bffda2e156e72427e19276cd9c3d3cc',1,'arm_fir_sparse_q15(arm_fir_sparse_instance_q15 *S, q15_t *pSrc, q15_t *pDst, q15_t *pScratchIn, q31_t *pScratchOut, uint32_t blockSize):&#160;arm_fir_sparse_q15.c'],['../group___f_i_r___sparse.html#ga2bffda2e156e72427e19276cd9c3d3cc',1,'arm_fir_sparse_q15(arm_fir_sparse_instance_q15 *S, q15_t *pSrc, q15_t *pDst, q15_t *pScratchIn, q31_t *pScratchOut, uint32_t blockSize):&#160;arm_fir_sparse_q15.c']]],
  ['arm_5ffir_5fsparse_5fq15_2ec',['arm_fir_sparse_q15.c',['../arm__fir__sparse__q15_8c.html',1,'']]],
  ['arm_5ffir_5fsparse_5fq31',['arm_fir_sparse_q31',['../group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec',1,'arm_fir_sparse_q31(arm_fir_sparse_instance_q31 *S, q31_t *pSrc, q31_t *pDst, q31_t *pScratchIn, uint32_t blockSize):&#160;arm_fir_sparse_q31.c'],['../group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec',1,'arm_fir_sparse_q31(arm_fir_sparse_instance_q31 *S, q31_t *pSrc, q31_t *pDst, q31_t *pScratchIn, uint32_t blockSize):&#160;arm_fir_sparse_q31.c']]],
  ['arm_5ffir_5fsparse_5fq31_2ec',['arm_fir_sparse_q31.c',['../arm__fir__sparse__q31_8c.html',1,'']]],
  ['arm_5ffir_5fsparse_5fq7',['arm_fir_sparse_q7',['../group___f_i_r___sparse.html#gae86c145efc2d9ec32dc6d8c1ad2ccb3c',1,'arm_fir_sparse_q7(arm_fir_sparse_instance_q7 *S, q7_t *pSrc, q7_t *pDst, q7_t *pScratchIn, q31_t *pScratchOut, uint32_t blockSize):&#160;arm_fir_sparse_q7.c'],['../group___f_i_r___sparse.html#gae86c145efc2d9ec32dc6d8c1ad2ccb3c',1,'arm_fir_sparse_q7(arm_fir_sparse_instance_q7 *S, q7_t *pSrc, q7_t *pDst, q7_t *pScratchIn, q31_t *pScratchOut, uint32_t blockSize):&#160;arm_fir_sparse_q7.c']]],
  ['arm_5ffir_5fsparse_5fq7_2ec',['arm_fir_sparse_q7.c',['../arm__fir__sparse__q7_8c.html',1,'']]],
  ['arm_5ffloat_5fto_5fq12_5f20',['arm_float_to_q12_20',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8',1,'arm_float_to_q12_20(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c']]],
  ['arm_5ffloat_5fto_5fq14',['arm_float_to_q14',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac',1,'arm_float_to_q14(float *pIn, q15_t *pOut, uint32_t numSamples):&#160;math_helper.c']]],
  ['arm_5ffloat_5fto_5fq15',['arm_float_to_q15',['../group__float__to__x.html#ga215456e35a18db86882e1d3f0d24e1f2',1,'arm_float_to_q15(float32_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_float_to_q15.c'],['../group__float__to__x.html#ga215456e35a18db86882e1d3f0d24e1f2',1,'arm_float_to_q15(float32_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_float_to_q15.c']]],
  ['arm_5ffloat_5fto_5fq15_2ec',['arm_float_to_q15.c',['../arm__float__to__q15_8c.html',1,'']]],
  ['arm_5ffloat_5fto_5fq28',['arm_float_to_q28',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625',1,'arm_float_to_q28(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c']]],
  ['arm_5ffloat_5fto_5fq29',['arm_float_to_q29',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9',1,'arm_float_to_q29(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c']]],
  ['arm_5ffloat_5fto_5fq30',['arm_float_to_q30',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079',1,'arm_float_to_q30(float *pIn, q31_t *pOut, uint32_t numSamples):&#160;math_helper.c']]],
  ['arm_5ffloat_5fto_5fq31',['arm_float_to_q31',['../group__float__to__x.html#ga177704107f94564e9abe4daaa36f4554',1,'arm_float_to_q31(float32_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_float_to_q31.c'],['../group__float__to__x.html#ga177704107f94564e9abe4daaa36f4554',1,'arm_float_to_q31(float32_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_float_to_q31.c']]],
  ['arm_5ffloat_5fto_5fq31_2ec',['arm_float_to_q31.c',['../arm__float__to__q31_8c.html',1,'']]],
  ['arm_5ffloat_5fto_5fq7',['arm_float_to_q7',['../group__float__to__x.html#ga44a393818cdee8dce80f2d66add25411',1,'arm_float_to_q7(float32_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_float_to_q7.c'],['../group__float__to__x.html#ga44a393818cdee8dce80f2d66add25411',1,'arm_float_to_q7(float32_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_float_to_q7.c']]],
  ['arm_5ffloat_5fto_5fq7_2ec',['arm_float_to_q7.c',['../arm__float__to__q7_8c.html',1,'']]],
  ['arm_5fgraphic_5fequalizer_5fdata_2ec',['arm_graphic_equalizer_data.c',['../arm__graphic__equalizer__data_8c.html',1,'']]],
  ['arm_5fgraphic_5fequalizer_5fexample_5fq31_2ec',['arm_graphic_equalizer_example_q31.c',['../arm__graphic__equalizer__example__q31_8c.html',1,'']]],
  ['arm_5fiir_5flattice_5ff32',['arm_iir_lattice_f32',['../group___i_i_r___lattice.html#ga56164a0fe48619b8ceec160347bdd2ff',1,'arm_iir_lattice_f32(const arm_iir_lattice_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_iir_lattice_f32.c'],['../group___i_i_r___lattice.html#ga56164a0fe48619b8ceec160347bdd2ff',1,'arm_iir_lattice_f32(const arm_iir_lattice_instance_f32 *S, float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_iir_lattice_f32.c']]],
  ['arm_5fiir_5flattice_5ff32_2ec',['arm_iir_lattice_f32.c',['../arm__iir__lattice__f32_8c.html',1,'']]],
  ['arm_5fiir_5flattice_5finit_5ff32',['arm_iir_lattice_init_f32',['../group___i_i_r___lattice.html#gaed3b0230bb77439dc902daa625985e04',1,'arm_iir_lattice_init_f32(arm_iir_lattice_instance_f32 *S, uint16_t numStages, float32_t *pkCoeffs, float32_t *pvCoeffs, float32_t *pState, uint32_t blockSize):&#160;arm_iir_lattice_init_f32.c'],['../group___i_i_r___lattice.html#gaed3b0230bb77439dc902daa625985e04',1,'arm_iir_lattice_init_f32(arm_iir_lattice_instance_f32 *S, uint16_t numStages, float32_t *pkCoeffs, float32_t *pvCoeffs, float32_t *pState, uint32_t blockSize):&#160;arm_iir_lattice_init_f32.c']]],
  ['arm_5fiir_5flattice_5finit_5ff32_2ec',['arm_iir_lattice_init_f32.c',['../arm__iir__lattice__init__f32_8c.html',1,'']]],
  ['arm_5fiir_5flattice_5finit_5fq15',['arm_iir_lattice_init_q15',['../group___i_i_r___lattice.html#ga1f4bc2dd3d5641e96815d3a5aad58998',1,'arm_iir_lattice_init_q15(arm_iir_lattice_instance_q15 *S, uint16_t numStages, q15_t *pkCoeffs, q15_t *pvCoeffs, q15_t *pState, uint32_t blockSize):&#160;arm_iir_lattice_init_q15.c'],['../group___i_i_r___lattice.html#ga1f4bc2dd3d5641e96815d3a5aad58998',1,'arm_iir_lattice_init_q15(arm_iir_lattice_instance_q15 *S, uint16_t numStages, q15_t *pkCoeffs, q15_t *pvCoeffs, q15_t *pState, uint32_t blockSize):&#160;arm_iir_lattice_init_q15.c']]],
  ['arm_5fiir_5flattice_5finit_5fq15_2ec',['arm_iir_lattice_init_q15.c',['../arm__iir__lattice__init__q15_8c.html',1,'']]],
  ['arm_5fiir_5flattice_5finit_5fq31',['arm_iir_lattice_init_q31',['../group___i_i_r___lattice.html#gab686c14175581797d9c3ad7bf1d5cc1e',1,'arm_iir_lattice_init_q31(arm_iir_lattice_instance_q31 *S, uint16_t numStages, q31_t *pkCoeffs, q31_t *pvCoeffs, q31_t *pState, uint32_t blockSize):&#160;arm_iir_lattice_init_q31.c'],['../group___i_i_r___lattice.html#gab686c14175581797d9c3ad7bf1d5cc1e',1,'arm_iir_lattice_init_q31(arm_iir_lattice_instance_q31 *S, uint16_t numStages, q31_t *pkCoeffs, q31_t *pvCoeffs, q31_t *pState, uint32_t blockSize):&#160;arm_iir_lattice_init_q31.c']]],
  ['arm_5fiir_5flattice_5finit_5fq31_2ec',['arm_iir_lattice_init_q31.c',['../arm__iir__lattice__init__q31_8c.html',1,'']]],
  ['arm_5fiir_5flattice_5finstance_5ff32',['arm_iir_lattice_instance_f32',['../structarm__iir__lattice__instance__f32.html',1,'']]],
  ['arm_5fiir_5flattice_5finstance_5fq15',['arm_iir_lattice_instance_q15',['../structarm__iir__lattice__instance__q15.html',1,'']]],
  ['arm_5fiir_5flattice_5finstance_5fq31',['arm_iir_lattice_instance_q31',['../structarm__iir__lattice__instance__q31.html',1,'']]],
  ['arm_5fiir_5flattice_5fq15',['arm_iir_lattice_q15',['../group___i_i_r___lattice.html#gaeb9e9599a288832ed123183eaa8b294a',1,'arm_iir_lattice_q15(const arm_iir_lattice_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_iir_lattice_q15.c'],['../group___i_i_r___lattice.html#gaeb9e9599a288832ed123183eaa8b294a',1,'arm_iir_lattice_q15(const arm_iir_lattice_instance_q15 *S, q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_iir_lattice_q15.c']]],
  ['arm_5fiir_5flattice_5fq15_2ec',['arm_iir_lattice_q15.c',['../arm__iir__lattice__q15_8c.html',1,'']]],
  ['arm_5fiir_5flattice_5fq31',['arm_iir_lattice_q31',['../group___i_i_r___lattice.html#ga123b26fa9156cd8d3622dd85931741ed',1,'arm_iir_lattice_q31(const arm_iir_lattice_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_iir_lattice_q31.c'],['../group___i_i_r___lattice.html#ga123b26fa9156cd8d3622dd85931741ed',1,'arm_iir_lattice_q31(const arm_iir_lattice_instance_q31 *S, q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_iir_lattice_q31.c']]],
  ['arm_5fiir_5flattice_5fq31_2ec',['arm_iir_lattice_q31.c',['../arm__iir__lattice__q31_8c.html',1,'']]],
  ['arm_5finv_5fclarke_5ff32',['arm_inv_clarke_f32',['../group__inv__clarke.html#ga137f0396d837477b899ecae89f075a50',1,'arm_math.h']]],
  ['arm_5finv_5fclarke_5fq31',['arm_inv_clarke_q31',['../group__inv__clarke.html#ga2d0c60f114f095a2f27442d98781ba02',1,'arm_math.h']]],
  ['arm_5finv_5fpark_5ff32',['arm_inv_park_f32',['../group__inv__park.html#gaaf6bef0de21946f774d49df050dd8b05',1,'arm_math.h']]],
  ['arm_5finv_5fpark_5fq31',['arm_inv_park_q31',['../group__inv__park.html#ga0b33822b988a15455773d28440c5579a',1,'arm_math.h']]],
  ['arm_5flinear_5finterep_5ftable',['arm_linear_interep_table',['../arm__linear__interp__data_8c.html#a1a5a6c95f39221fcf8129fe478f54696',1,'arm_linear_interep_table():&#160;arm_linear_interp_data.c'],['../arm__linear__interp__example__f32_8c.html#a1a5a6c95f39221fcf8129fe478f54696',1,'arm_linear_interep_table():&#160;arm_linear_interp_data.c']]],
  ['arm_5flinear_5finterp_5fdata_2ec',['arm_linear_interp_data.c',['../arm__linear__interp__data_8c.html',1,'']]],
  ['arm_5flinear_5finterp_5fexample_5ff32_2ec',['arm_linear_interp_example_f32.c',['../arm__linear__interp__example__f32_8c.html',1,'']]],
  ['arm_5flinear_5finterp_5ff32',['arm_linear_interp_f32',['../group___linear_interpolate.html#ga2269263d810cafcd19681957b37d5cf6',1,'arm_math.h']]],
  ['arm_5flinear_5finterp_5finstance_5ff32',['arm_linear_interp_instance_f32',['../structarm__linear__interp__instance__f32.html',1,'']]],
  ['arm_5flinear_5finterp_5fq15',['arm_linear_interp_q15',['../group___linear_interpolate.html#ga42c9206e5d2d22b8808716dc30622846',1,'arm_math.h']]],
  ['arm_5flinear_5finterp_5fq31',['arm_linear_interp_q31',['../group___linear_interpolate.html#ga690e63e9a513ca0a741b1b174805d031',1,'arm_math.h']]],
  ['arm_5flinear_5finterp_5fq7',['arm_linear_interp_q7',['../group___linear_interpolate.html#gacb0d44fe00aca0ba1d036d469a1763fc',1,'arm_math.h']]],
  ['arm_5flms_5ff32',['arm_lms_f32',['../group___l_m_s.html#gae266d009e682180421601627c79a3843',1,'arm_lms_f32(const arm_lms_instance_f32 *S, float32_t *pSrc, float32_t *pRef, float32_t *pOut, float32_t *pErr, uint32_t blockSize):&#160;arm_lms_f32.c'],['../group___l_m_s.html#gae266d009e682180421601627c79a3843',1,'arm_lms_f32(const arm_lms_instance_f32 *S, float32_t *pSrc, float32_t *pRef, float32_t *pOut, float32_t *pErr, uint32_t blockSize):&#160;arm_lms_f32.c']]],
  ['arm_5flms_5ff32_2ec',['arm_lms_f32.c',['../arm__lms__f32_8c.html',1,'']]],
  ['arm_5flms_5finit_5ff32',['arm_lms_init_f32',['../group___l_m_s.html#ga9fc7adca0966ff2cec1746fca8364cee',1,'arm_lms_init_f32(arm_lms_instance_f32 *S, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, float32_t mu, uint32_t blockSize):&#160;arm_lms_init_f32.c'],['../group___l_m_s.html#ga9fc7adca0966ff2cec1746fca8364cee',1,'arm_lms_init_f32(arm_lms_instance_f32 *S, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, float32_t mu, uint32_t blockSize):&#160;arm_lms_init_f32.c']]],
  ['arm_5flms_5finit_5ff32_2ec',['arm_lms_init_f32.c',['../arm__lms__init__f32_8c.html',1,'']]],
  ['arm_5flms_5finit_5fq15',['arm_lms_init_q15',['../group___l_m_s.html#ga9544cc26f18cd4465cfbed371be822b3',1,'arm_lms_init_q15(arm_lms_instance_q15 *S, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, q15_t mu, uint32_t blockSize, uint32_t postShift):&#160;arm_lms_init_q15.c'],['../group___l_m_s.html#ga9544cc26f18cd4465cfbed371be822b3',1,'arm_lms_init_q15(arm_lms_instance_q15 *S, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, q15_t mu, uint32_t blockSize, uint32_t postShift):&#160;arm_lms_init_q15.c']]],
  ['arm_5flms_5finit_5fq15_2ec',['arm_lms_init_q15.c',['../arm__lms__init__q15_8c.html',1,'']]],
  ['arm_5flms_5finit_5fq31',['arm_lms_init_q31',['../group___l_m_s.html#ga8d4bc251169f4b102355097a9f7530d6',1,'arm_lms_init_q31(arm_lms_instance_q31 *S, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, q31_t mu, uint32_t blockSize, uint32_t postShift):&#160;arm_lms_init_q31.c'],['../group___l_m_s.html#ga8d4bc251169f4b102355097a9f7530d6',1,'arm_lms_init_q31(arm_lms_instance_q31 *S, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, q31_t mu, uint32_t blockSize, uint32_t postShift):&#160;arm_lms_init_q31.c']]],
  ['arm_5flms_5finit_5fq31_2ec',['arm_lms_init_q31.c',['../arm__lms__init__q31_8c.html',1,'']]],
  ['arm_5flms_5finstance_5ff32',['arm_lms_instance_f32',['../structarm__lms__instance__f32.html',1,'']]],
  ['arm_5flms_5finstance_5fq15',['arm_lms_instance_q15',['../structarm__lms__instance__q15.html',1,'']]],
  ['arm_5flms_5finstance_5fq31',['arm_lms_instance_q31',['../structarm__lms__instance__q31.html',1,'']]],
  ['arm_5flms_5fnorm_5ff32',['arm_lms_norm_f32',['../group___l_m_s___n_o_r_m.html#ga2418c929087c6eba719758eaae3f3300',1,'arm_lms_norm_f32(arm_lms_norm_instance_f32 *S, float32_t *pSrc, float32_t *pRef, float32_t *pOut, float32_t *pErr, uint32_t blockSize):&#160;arm_lms_norm_f32.c'],['../group___l_m_s___n_o_r_m.html#ga2418c929087c6eba719758eaae3f3300',1,'arm_lms_norm_f32(arm_lms_norm_instance_f32 *S, float32_t *pSrc, float32_t *pRef, float32_t *pOut, float32_t *pErr, uint32_t blockSize):&#160;arm_lms_norm_f32.c']]],
  ['arm_5flms_5fnorm_5ff32_2ec',['arm_lms_norm_f32.c',['../arm__lms__norm__f32_8c.html',1,'']]],
  ['arm_5flms_5fnorm_5finit_5ff32',['arm_lms_norm_init_f32',['../group___l_m_s___n_o_r_m.html#gac7ccbaea863882056eee815456464670',1,'arm_lms_norm_init_f32(arm_lms_norm_instance_f32 *S, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, float32_t mu, uint32_t blockSize):&#160;arm_lms_norm_init_f32.c'],['../group___l_m_s___n_o_r_m.html#gac7ccbaea863882056eee815456464670',1,'arm_lms_norm_init_f32(arm_lms_norm_instance_f32 *S, uint16_t numTaps, float32_t *pCoeffs, float32_t *pState, float32_t mu, uint32_t blockSize):&#160;arm_lms_norm_init_f32.c']]],
  ['arm_5flms_5fnorm_5finit_5ff32_2ec',['arm_lms_norm_init_f32.c',['../arm__lms__norm__init__f32_8c.html',1,'']]],
  ['arm_5flms_5fnorm_5finit_5fq15',['arm_lms_norm_init_q15',['../group___l_m_s___n_o_r_m.html#ga213ab1ee2e154cc2fa30d667b1994b89',1,'arm_lms_norm_init_q15(arm_lms_norm_instance_q15 *S, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, q15_t mu, uint32_t blockSize, uint8_t postShift):&#160;arm_lms_norm_init_q15.c'],['../group___l_m_s___n_o_r_m.html#ga213ab1ee2e154cc2fa30d667b1994b89',1,'arm_lms_norm_init_q15(arm_lms_norm_instance_q15 *S, uint16_t numTaps, q15_t *pCoeffs, q15_t *pState, q15_t mu, uint32_t blockSize, uint8_t postShift):&#160;arm_lms_norm_init_q15.c']]],
  ['arm_5flms_5fnorm_5finit_5fq15_2ec',['arm_lms_norm_init_q15.c',['../arm__lms__norm__init__q15_8c.html',1,'']]],
  ['arm_5flms_5fnorm_5finit_5fq31',['arm_lms_norm_init_q31',['../group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490',1,'arm_lms_norm_init_q31(arm_lms_norm_instance_q31 *S, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, q31_t mu, uint32_t blockSize, uint8_t postShift):&#160;arm_lms_norm_init_q31.c'],['../group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490',1,'arm_lms_norm_init_q31(arm_lms_norm_instance_q31 *S, uint16_t numTaps, q31_t *pCoeffs, q31_t *pState, q31_t mu, uint32_t blockSize, uint8_t postShift):&#160;arm_lms_norm_init_q31.c']]],
  ['arm_5flms_5fnorm_5finit_5fq31_2ec',['arm_lms_norm_init_q31.c',['../arm__lms__norm__init__q31_8c.html',1,'']]],
  ['arm_5flms_5fnorm_5finstance_5ff32',['arm_lms_norm_instance_f32',['../structarm__lms__norm__instance__f32.html',1,'']]],
  ['arm_5flms_5fnorm_5finstance_5fq15',['arm_lms_norm_instance_q15',['../structarm__lms__norm__instance__q15.html',1,'']]],
  ['arm_5flms_5fnorm_5finstance_5fq31',['arm_lms_norm_instance_q31',['../structarm__lms__norm__instance__q31.html',1,'']]],
  ['arm_5flms_5fnorm_5fq15',['arm_lms_norm_q15',['../group___l_m_s___n_o_r_m.html#gad47486a399dedb0bc85a5990ec5cf981',1,'arm_lms_norm_q15(arm_lms_norm_instance_q15 *S, q15_t *pSrc, q15_t *pRef, q15_t *pOut, q15_t *pErr, uint32_t blockSize):&#160;arm_lms_norm_q15.c'],['../group___l_m_s___n_o_r_m.html#gad47486a399dedb0bc85a5990ec5cf981',1,'arm_lms_norm_q15(arm_lms_norm_instance_q15 *S, q15_t *pSrc, q15_t *pRef, q15_t *pOut, q15_t *pErr, uint32_t blockSize):&#160;arm_lms_norm_q15.c']]],
  ['arm_5flms_5fnorm_5fq15_2ec',['arm_lms_norm_q15.c',['../arm__lms__norm__q15_8c.html',1,'']]],
  ['arm_5flms_5fnorm_5fq31',['arm_lms_norm_q31',['../group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05',1,'arm_lms_norm_q31(arm_lms_norm_instance_q31 *S, q31_t *pSrc, q31_t *pRef, q31_t *pOut, q31_t *pErr, uint32_t blockSize):&#160;arm_lms_norm_q31.c'],['../group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05',1,'arm_lms_norm_q31(arm_lms_norm_instance_q31 *S, q31_t *pSrc, q31_t *pRef, q31_t *pOut, q31_t *pErr, uint32_t blockSize):&#160;arm_lms_norm_q31.c']]],
  ['arm_5flms_5fnorm_5fq31_2ec',['arm_lms_norm_q31.c',['../arm__lms__norm__q31_8c.html',1,'']]],
  ['arm_5flms_5fq15',['arm_lms_q15',['../group___l_m_s.html#gacde16c17eb75979f81b34e2e2a58c7ac',1,'arm_lms_q15(const arm_lms_instance_q15 *S, q15_t *pSrc, q15_t *pRef, q15_t *pOut, q15_t *pErr, uint32_t blockSize):&#160;arm_lms_q15.c'],['../group___l_m_s.html#gacde16c17eb75979f81b34e2e2a58c7ac',1,'arm_lms_q15(const arm_lms_instance_q15 *S, q15_t *pSrc, q15_t *pRef, q15_t *pOut, q15_t *pErr, uint32_t blockSize):&#160;arm_lms_q15.c']]],
  ['arm_5flms_5fq15_2ec',['arm_lms_q15.c',['../arm__lms__q15_8c.html',1,'']]],
  ['arm_5flms_5fq31',['arm_lms_q31',['../group___l_m_s.html#ga6a0abfe6041253a6f91c63b383a64257',1,'arm_lms_q31(const arm_lms_instance_q31 *S, q31_t *pSrc, q31_t *pRef, q31_t *pOut, q31_t *pErr, uint32_t blockSize):&#160;arm_lms_q31.c'],['../group___l_m_s.html#ga6a0abfe6041253a6f91c63b383a64257',1,'arm_lms_q31(const arm_lms_instance_q31 *S, q31_t *pSrc, q31_t *pRef, q31_t *pOut, q31_t *pErr, uint32_t blockSize):&#160;arm_lms_q31.c']]],
  ['arm_5flms_5fq31_2ec',['arm_lms_q31.c',['../arm__lms__q31_8c.html',1,'']]],
  ['arm_5fmat_5fadd_5ff32',['arm_mat_add_f32',['../group___matrix_add.html#ga04bbf64a5f9c9e57dd1efb26a768aba1',1,'arm_mat_add_f32(const arm_matrix_instance_f32 *pSrcA, const arm_matrix_instance_f32 *pSrcB, arm_matrix_instance_f32 *pDst):&#160;arm_mat_add_f32.c'],['../group___matrix_add.html#ga04bbf64a5f9c9e57dd1efb26a768aba1',1,'arm_mat_add_f32(const arm_matrix_instance_f32 *pSrcA, const arm_matrix_instance_f32 *pSrcB, arm_matrix_instance_f32 *pDst):&#160;arm_mat_add_f32.c']]],
  ['arm_5fmat_5fadd_5ff32_2ec',['arm_mat_add_f32.c',['../arm__mat__add__f32_8c.html',1,'']]],
  ['arm_5fmat_5fadd_5fq15',['arm_mat_add_q15',['../group___matrix_add.html#ga147e90b7c12a162735ab8824127a33ee',1,'arm_mat_add_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst):&#160;arm_mat_add_q15.c'],['../group___matrix_add.html#ga147e90b7c12a162735ab8824127a33ee',1,'arm_mat_add_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst):&#160;arm_mat_add_q15.c']]],
  ['arm_5fmat_5fadd_5fq15_2ec',['arm_mat_add_q15.c',['../arm__mat__add__q15_8c.html',1,'']]],
  ['arm_5fmat_5fadd_5fq31',['arm_mat_add_q31',['../group___matrix_add.html#ga7d9d7d81a0832a17b831aad1e4a5dc16',1,'arm_mat_add_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_add_q31.c'],['../group___matrix_add.html#ga7d9d7d81a0832a17b831aad1e4a5dc16',1,'arm_mat_add_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_add_q31.c']]],
  ['arm_5fmat_5fadd_5fq31_2ec',['arm_mat_add_q31.c',['../arm__mat__add__q31_8c.html',1,'']]],
  ['arm_5fmat_5fcmplx_5fmult_5ff32',['arm_mat_cmplx_mult_f32',['../group___cmplx_matrix_mult.html#ga1adb839ac84445b8c2f04efa43faef35',1,'arm_mat_cmplx_mult_f32(const arm_matrix_instance_f32 *pSrcA, const arm_matrix_instance_f32 *pSrcB, arm_matrix_instance_f32 *pDst):&#160;arm_mat_cmplx_mult_f32.c'],['../group___cmplx_matrix_mult.html#ga1adb839ac84445b8c2f04efa43faef35',1,'arm_mat_cmplx_mult_f32(const arm_matrix_instance_f32 *pSrcA, const arm_matrix_instance_f32 *pSrcB, arm_matrix_instance_f32 *pDst):&#160;arm_mat_cmplx_mult_f32.c']]],
  ['arm_5fmat_5fcmplx_5fmult_5ff32_2ec',['arm_mat_cmplx_mult_f32.c',['../arm__mat__cmplx__mult__f32_8c.html',1,'']]],
  ['arm_5fmat_5fcmplx_5fmult_5fq15',['arm_mat_cmplx_mult_q15',['../group___cmplx_matrix_mult.html#ga63066615e7d6f6a44f4358725092419e',1,'arm_mat_cmplx_mult_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst, q15_t *pScratch):&#160;arm_mat_cmplx_mult_q15.c'],['../group___cmplx_matrix_mult.html#ga63066615e7d6f6a44f4358725092419e',1,'arm_mat_cmplx_mult_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst, q15_t *pScratch):&#160;arm_mat_cmplx_mult_q15.c']]],
  ['arm_5fmat_5fcmplx_5fmult_5fq15_2ec',['arm_mat_cmplx_mult_q15.c',['../arm__mat__cmplx__mult__q15_8c.html',1,'']]],
  ['arm_5fmat_5fcmplx_5fmult_5fq31',['arm_mat_cmplx_mult_q31',['../group___cmplx_matrix_mult.html#gaaf3c0b171ca8412c77bab9fa90804737',1,'arm_mat_cmplx_mult_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_cmplx_mult_q31.c'],['../group___cmplx_matrix_mult.html#gaaf3c0b171ca8412c77bab9fa90804737',1,'arm_mat_cmplx_mult_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_cmplx_mult_q31.c']]],
  ['arm_5fmat_5fcmplx_5fmult_5fq31_2ec',['arm_mat_cmplx_mult_q31.c',['../arm__mat__cmplx__mult__q31_8c.html',1,'']]],
  ['arm_5fmat_5finit_5ff32',['arm_mat_init_f32',['../group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27',1,'arm_mat_init_f32(arm_matrix_instance_f32 *S, uint16_t nRows, uint16_t nColumns, float32_t *pData):&#160;arm_mat_init_f32.c'],['../group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27',1,'arm_mat_init_f32(arm_matrix_instance_f32 *S, uint16_t nRows, uint16_t nColumns, float32_t *pData):&#160;arm_mat_init_f32.c']]],
  ['arm_5fmat_5finit_5ff32_2ec',['arm_mat_init_f32.c',['../arm__mat__init__f32_8c.html',1,'']]],
  ['arm_5fmat_5finit_5fq15',['arm_mat_init_q15',['../group___matrix_init.html#ga31a7c2b991803d49719393eb2d53dc26',1,'arm_mat_init_q15(arm_matrix_instance_q15 *S, uint16_t nRows, uint16_t nColumns, q15_t *pData):&#160;arm_mat_init_q15.c'],['../group___matrix_init.html#ga31a7c2b991803d49719393eb2d53dc26',1,'arm_mat_init_q15(arm_matrix_instance_q15 *S, uint16_t nRows, uint16_t nColumns, q15_t *pData):&#160;arm_mat_init_q15.c']]],
  ['arm_5fmat_5finit_5fq15_2ec',['arm_mat_init_q15.c',['../arm__mat__init__q15_8c.html',1,'']]],
  ['arm_5fmat_5finit_5fq31',['arm_mat_init_q31',['../group___matrix_init.html#ga48a5e5d37e1f062cc57fcfaf683343cc',1,'arm_mat_init_q31(arm_matrix_instance_q31 *S, uint16_t nRows, uint16_t nColumns, q31_t *pData):&#160;arm_mat_init_q31.c'],['../group___matrix_init.html#ga48a5e5d37e1f062cc57fcfaf683343cc',1,'arm_mat_init_q31(arm_matrix_instance_q31 *S, uint16_t nRows, uint16_t nColumns, q31_t *pData):&#160;arm_mat_init_q31.c']]],
  ['arm_5fmat_5finit_5fq31_2ec',['arm_mat_init_q31.c',['../arm__mat__init__q31_8c.html',1,'']]],
  ['arm_5fmat_5finverse_5ff32',['arm_mat_inverse_f32',['../group___matrix_inv.html#ga542be7aabbf7a2297a4b62cf212910e3',1,'arm_mat_inverse_f32(const arm_matrix_instance_f32 *pSrc, arm_matrix_instance_f32 *pDst):&#160;arm_mat_inverse_f32.c'],['../group___matrix_inv.html#ga542be7aabbf7a2297a4b62cf212910e3',1,'arm_mat_inverse_f32(const arm_matrix_instance_f32 *src, arm_matrix_instance_f32 *dst):&#160;arm_mat_inverse_f32.c']]],
  ['arm_5fmat_5finverse_5ff32_2ec',['arm_mat_inverse_f32.c',['../arm__mat__inverse__f32_8c.html',1,'']]],
  ['arm_5fmat_5finverse_5ff64',['arm_mat_inverse_f64',['../group___matrix_inv.html#gaede2367c02df083cc915ddd5d8fae838',1,'arm_mat_inverse_f64(const arm_matrix_instance_f64 *pSrc, arm_matrix_instance_f64 *pDst):&#160;arm_mat_inverse_f64.c'],['../group___matrix_inv.html#gaede2367c02df083cc915ddd5d8fae838',1,'arm_mat_inverse_f64(const arm_matrix_instance_f64 *src, arm_matrix_instance_f64 *dst):&#160;arm_mat_inverse_f64.c']]],
  ['arm_5fmat_5finverse_5ff64_2ec',['arm_mat_inverse_f64.c',['../arm__mat__inverse__f64_8c.html',1,'']]],
  ['arm_5fmat_5fmult_5ff32',['arm_mat_mult_f32',['../group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0',1,'arm_mat_mult_f32(const arm_matrix_instance_f32 *pSrcA, const arm_matrix_instance_f32 *pSrcB, arm_matrix_instance_f32 *pDst):&#160;arm_mat_mult_f32.c'],['../group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0',1,'arm_mat_mult_f32(const arm_matrix_instance_f32 *pSrcA, const arm_matrix_instance_f32 *pSrcB, arm_matrix_instance_f32 *pDst):&#160;arm_mat_mult_f32.c']]],
  ['arm_5fmat_5fmult_5ff32_2ec',['arm_mat_mult_f32.c',['../arm__mat__mult__f32_8c.html',1,'']]],
  ['arm_5fmat_5fmult_5ffast_5fq15',['arm_mat_mult_fast_q15',['../group___matrix_mult.html#ga08f37d93a5bfef0c5000dc5e0a411f93',1,'arm_mat_mult_fast_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst, q15_t *pState):&#160;arm_mat_mult_fast_q15.c'],['../group___matrix_mult.html#ga08f37d93a5bfef0c5000dc5e0a411f93',1,'arm_mat_mult_fast_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst, q15_t *pState):&#160;arm_mat_mult_fast_q15.c']]],
  ['arm_5fmat_5fmult_5ffast_5fq15_2ec',['arm_mat_mult_fast_q15.c',['../arm__mat__mult__fast__q15_8c.html',1,'']]],
  ['arm_5fmat_5fmult_5ffast_5fq31',['arm_mat_mult_fast_q31',['../group___matrix_mult.html#ga2785e8c1b785348b0c439b56aaf585a3',1,'arm_mat_mult_fast_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_mult_fast_q31.c'],['../group___matrix_mult.html#ga2785e8c1b785348b0c439b56aaf585a3',1,'arm_mat_mult_fast_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_mult_fast_q31.c']]],
  ['arm_5fmat_5fmult_5ffast_5fq31_2ec',['arm_mat_mult_fast_q31.c',['../arm__mat__mult__fast__q31_8c.html',1,'']]],
  ['arm_5fmat_5fmult_5fq15',['arm_mat_mult_q15',['../group___matrix_mult.html#ga3657b99a9667945373e520dbac0f4516',1,'arm_mat_mult_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst, q15_t *pState CMSIS_UNUSED):&#160;arm_mat_mult_q15.c'],['../arm__math_8h.html#a7521d59196189bb6dde26e8cdfb66e21',1,'arm_mat_mult_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst, q15_t *pState):&#160;arm_math.h']]],
  ['arm_5fmat_5fmult_5fq15_2ec',['arm_mat_mult_q15.c',['../arm__mat__mult__q15_8c.html',1,'']]],
  ['arm_5fmat_5fmult_5fq31',['arm_mat_mult_q31',['../group___matrix_mult.html#ga2ec612a8c2c4916477fb9bc1ab548a6e',1,'arm_mat_mult_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_mult_q31.c'],['../group___matrix_mult.html#ga2ec612a8c2c4916477fb9bc1ab548a6e',1,'arm_mat_mult_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_mult_q31.c']]],
  ['arm_5fmat_5fmult_5fq31_2ec',['arm_mat_mult_q31.c',['../arm__mat__mult__q31_8c.html',1,'']]],
  ['arm_5fmat_5fscale_5ff32',['arm_mat_scale_f32',['../group___matrix_scale.html#ga9cb4e385b18c9a0b9cbc940c1067ca12',1,'arm_mat_scale_f32(const arm_matrix_instance_f32 *pSrc, float32_t scale, arm_matrix_instance_f32 *pDst):&#160;arm_mat_scale_f32.c'],['../group___matrix_scale.html#ga9cb4e385b18c9a0b9cbc940c1067ca12',1,'arm_mat_scale_f32(const arm_matrix_instance_f32 *pSrc, float32_t scale, arm_matrix_instance_f32 *pDst):&#160;arm_mat_scale_f32.c']]],
  ['arm_5fmat_5fscale_5ff32_2ec',['arm_mat_scale_f32.c',['../arm__mat__scale__f32_8c.html',1,'']]],
  ['arm_5fmat_5fscale_5fq15',['arm_mat_scale_q15',['../group___matrix_scale.html#ga7521769e2cf1c3d9c4656138cd2ae2ca',1,'arm_mat_scale_q15(const arm_matrix_instance_q15 *pSrc, q15_t scaleFract, int32_t shift, arm_matrix_instance_q15 *pDst):&#160;arm_mat_scale_q15.c'],['../group___matrix_scale.html#ga7521769e2cf1c3d9c4656138cd2ae2ca',1,'arm_mat_scale_q15(const arm_matrix_instance_q15 *pSrc, q15_t scaleFract, int32_t shift, arm_matrix_instance_q15 *pDst):&#160;arm_mat_scale_q15.c']]],
  ['arm_5fmat_5fscale_5fq15_2ec',['arm_mat_scale_q15.c',['../arm__mat__scale__q15_8c.html',1,'']]],
  ['arm_5fmat_5fscale_5fq31',['arm_mat_scale_q31',['../group___matrix_scale.html#ga609743821ee81fa8c34c4bcdc1ed9744',1,'arm_mat_scale_q31(const arm_matrix_instance_q31 *pSrc, q31_t scaleFract, int32_t shift, arm_matrix_instance_q31 *pDst):&#160;arm_mat_scale_q31.c'],['../group___matrix_scale.html#ga609743821ee81fa8c34c4bcdc1ed9744',1,'arm_mat_scale_q31(const arm_matrix_instance_q31 *pSrc, q31_t scaleFract, int32_t shift, arm_matrix_instance_q31 *pDst):&#160;arm_mat_scale_q31.c']]],
  ['arm_5fmat_5fscale_5fq31_2ec',['arm_mat_scale_q31.c',['../arm__mat__scale__q31_8c.html',1,'']]],
  ['arm_5fmat_5fsub_5ff32',['arm_mat_sub_f32',['../group___matrix_sub.html#gac8b72fb70246ccfee3b372002345732c',1,'arm_mat_sub_f32(const arm_matrix_instance_f32 *pSrcA, const arm_matrix_instance_f32 *pSrcB, arm_matrix_instance_f32 *pDst):&#160;arm_mat_sub_f32.c'],['../group___matrix_sub.html#gac8b72fb70246ccfee3b372002345732c',1,'arm_mat_sub_f32(const arm_matrix_instance_f32 *pSrcA, const arm_matrix_instance_f32 *pSrcB, arm_matrix_instance_f32 *pDst):&#160;arm_mat_sub_f32.c']]],
  ['arm_5fmat_5fsub_5ff32_2ec',['arm_mat_sub_f32.c',['../arm__mat__sub__f32_8c.html',1,'']]],
  ['arm_5fmat_5fsub_5fq15',['arm_mat_sub_q15',['../group___matrix_sub.html#gaf647776a425b7f9dd0aca3e11d81f02f',1,'arm_mat_sub_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst):&#160;arm_mat_sub_q15.c'],['../group___matrix_sub.html#gaf647776a425b7f9dd0aca3e11d81f02f',1,'arm_mat_sub_q15(const arm_matrix_instance_q15 *pSrcA, const arm_matrix_instance_q15 *pSrcB, arm_matrix_instance_q15 *pDst):&#160;arm_mat_sub_q15.c']]],
  ['arm_5fmat_5fsub_5fq15_2ec',['arm_mat_sub_q15.c',['../arm__mat__sub__q15_8c.html',1,'']]],
  ['arm_5fmat_5fsub_5fq31',['arm_mat_sub_q31',['../group___matrix_sub.html#ga39f42e0e3b7f115fbb909d6ff4e1329d',1,'arm_mat_sub_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_sub_q31.c'],['../group___matrix_sub.html#ga39f42e0e3b7f115fbb909d6ff4e1329d',1,'arm_mat_sub_q31(const arm_matrix_instance_q31 *pSrcA, const arm_matrix_instance_q31 *pSrcB, arm_matrix_instance_q31 *pDst):&#160;arm_mat_sub_q31.c']]],
  ['arm_5fmat_5fsub_5fq31_2ec',['arm_mat_sub_q31.c',['../arm__mat__sub__q31_8c.html',1,'']]],
  ['arm_5fmat_5ftrans_5ff32',['arm_mat_trans_f32',['../group___matrix_trans.html#gad7dd9f108429da13d3864696ceeec789',1,'arm_mat_trans_f32(const arm_matrix_instance_f32 *pSrc, arm_matrix_instance_f32 *pDst):&#160;arm_mat_trans_f32.c'],['../group___matrix_trans.html#gad7dd9f108429da13d3864696ceeec789',1,'arm_mat_trans_f32(const arm_matrix_instance_f32 *pSrc, arm_matrix_instance_f32 *pDst):&#160;arm_mat_trans_f32.c']]],
  ['arm_5fmat_5ftrans_5ff32_2ec',['arm_mat_trans_f32.c',['../arm__mat__trans__f32_8c.html',1,'']]],
  ['arm_5fmat_5ftrans_5fq15',['arm_mat_trans_q15',['../group___matrix_trans.html#ga4f4f821cc695fd0ef9061d702e08050a',1,'arm_mat_trans_q15(const arm_matrix_instance_q15 *pSrc, arm_matrix_instance_q15 *pDst):&#160;arm_mat_trans_q15.c'],['../group___matrix_trans.html#ga4f4f821cc695fd0ef9061d702e08050a',1,'arm_mat_trans_q15(const arm_matrix_instance_q15 *pSrc, arm_matrix_instance_q15 *pDst):&#160;arm_mat_trans_q15.c']]],
  ['arm_5fmat_5ftrans_5fq15_2ec',['arm_mat_trans_q15.c',['../arm__mat__trans__q15_8c.html',1,'']]],
  ['arm_5fmat_5ftrans_5fq31',['arm_mat_trans_q31',['../group___matrix_trans.html#ga30a4d49489ac67ff98a46b9f58f73bf1',1,'arm_mat_trans_q31(const arm_matrix_instance_q31 *pSrc, arm_matrix_instance_q31 *pDst):&#160;arm_mat_trans_q31.c'],['../group___matrix_trans.html#ga30a4d49489ac67ff98a46b9f58f73bf1',1,'arm_mat_trans_q31(const arm_matrix_instance_q31 *pSrc, arm_matrix_instance_q31 *pDst):&#160;arm_mat_trans_q31.c']]],
  ['arm_5fmat_5ftrans_5fq31_2ec',['arm_mat_trans_q31.c',['../arm__mat__trans__q31_8c.html',1,'']]],
  ['arm_5fmath_2eh',['arm_math.h',['../arm__math_8h.html',1,'']]],
  ['arm_5fmath_5fargument_5ferror',['ARM_MATH_ARGUMENT_ERROR',['../arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35',1,'arm_math.h']]],
  ['arm_5fmath_5flength_5ferror',['ARM_MATH_LENGTH_ERROR',['../arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9ae74d7f8a53aec0512ae8f0a421e0e1',1,'arm_math.h']]],
  ['arm_5fmath_5fnaninf',['ARM_MATH_NANINF',['../arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6ac55996aaf19245238a8f57a91aeaefcc',1,'arm_math.h']]],
  ['arm_5fmath_5fsingular',['ARM_MATH_SINGULAR',['../arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a91509ea9c819dbd592ac13a6b05382dc',1,'arm_math.h']]],
  ['arm_5fmath_5fsize_5fmismatch',['ARM_MATH_SIZE_MISMATCH',['../arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b',1,'arm_math.h']]],
  ['arm_5fmath_5fsuccess',['ARM_MATH_SUCCESS',['../arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0',1,'arm_math.h']]],
  ['arm_5fmath_5ftest_5ffailure',['ARM_MATH_TEST_FAILURE',['../arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376',1,'arm_math.h']]],
  ['arm_5fmatrix_5fexample_5ff32_2ec',['arm_matrix_example_f32.c',['../arm__matrix__example__f32_8c.html',1,'']]],
  ['arm_5fmatrix_5finstance_5ff32',['arm_matrix_instance_f32',['../structarm__matrix__instance__f32.html',1,'']]],
  ['arm_5fmatrix_5finstance_5ff64',['arm_matrix_instance_f64',['../structarm__matrix__instance__f64.html',1,'']]],
  ['arm_5fmatrix_5finstance_5fq15',['arm_matrix_instance_q15',['../structarm__matrix__instance__q15.html',1,'']]],
  ['arm_5fmatrix_5finstance_5fq31',['arm_matrix_instance_q31',['../structarm__matrix__instance__q31.html',1,'']]],
  ['arm_5fmax_5ff32',['arm_max_f32',['../group___max.html#ga5b89d1b04575aeec494f678695fb87d8',1,'arm_max_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult, uint32_t *pIndex):&#160;arm_max_f32.c'],['../group___max.html#ga5b89d1b04575aeec494f678695fb87d8',1,'arm_max_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult, uint32_t *pIndex):&#160;arm_max_f32.c']]],
  ['arm_5fmax_5ff32_2ec',['arm_max_f32.c',['../arm__max__f32_8c.html',1,'']]],
  ['arm_5fmax_5fq15',['arm_max_q15',['../group___max.html#gac132856c68f4bf2a056eaad5921c7880',1,'arm_max_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult, uint32_t *pIndex):&#160;arm_max_q15.c'],['../group___max.html#gac132856c68f4bf2a056eaad5921c7880',1,'arm_max_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult, uint32_t *pIndex):&#160;arm_max_q15.c']]],
  ['arm_5fmax_5fq15_2ec',['arm_max_q15.c',['../arm__max__q15_8c.html',1,'']]],
  ['arm_5fmax_5fq31',['arm_max_q31',['../group___max.html#gaff7cbd4e955382def06724cc4cc85795',1,'arm_max_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult, uint32_t *pIndex):&#160;arm_max_q31.c'],['../group___max.html#gaff7cbd4e955382def06724cc4cc85795',1,'arm_max_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult, uint32_t *pIndex):&#160;arm_max_q31.c']]],
  ['arm_5fmax_5fq31_2ec',['arm_max_q31.c',['../arm__max__q31_8c.html',1,'']]],
  ['arm_5fmax_5fq7',['arm_max_q7',['../group___max.html#ga6afd64d381b5c232de59163ebfe71e35',1,'arm_max_q7(q7_t *pSrc, uint32_t blockSize, q7_t *pResult, uint32_t *pIndex):&#160;arm_max_q7.c'],['../group___max.html#ga6afd64d381b5c232de59163ebfe71e35',1,'arm_max_q7(q7_t *pSrc, uint32_t blockSize, q7_t *pResult, uint32_t *pIndex):&#160;arm_max_q7.c']]],
  ['arm_5fmax_5fq7_2ec',['arm_max_q7.c',['../arm__max__q7_8c.html',1,'']]],
  ['arm_5fmean_5ff32',['arm_mean_f32',['../group__mean.html#ga74ce08c49ab61e57bd50c3a0ca1fdb2b',1,'arm_mean_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_mean_f32.c'],['../group__mean.html#ga74ce08c49ab61e57bd50c3a0ca1fdb2b',1,'arm_mean_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_mean_f32.c']]],
  ['arm_5fmean_5ff32_2ec',['arm_mean_f32.c',['../arm__mean__f32_8c.html',1,'']]],
  ['arm_5fmean_5fq15',['arm_mean_q15',['../group__mean.html#gac882495d5f098819fd3939c1ef7795b3',1,'arm_mean_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult):&#160;arm_mean_q15.c'],['../group__mean.html#gac882495d5f098819fd3939c1ef7795b3',1,'arm_mean_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult):&#160;arm_mean_q15.c']]],
  ['arm_5fmean_5fq15_2ec',['arm_mean_q15.c',['../arm__mean__q15_8c.html',1,'']]],
  ['arm_5fmean_5fq31',['arm_mean_q31',['../group__mean.html#gacf2526d8c2d75e486e8f0b0e31877ad0',1,'arm_mean_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_mean_q31.c'],['../group__mean.html#gacf2526d8c2d75e486e8f0b0e31877ad0',1,'arm_mean_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_mean_q31.c']]],
  ['arm_5fmean_5fq31_2ec',['arm_mean_q31.c',['../arm__mean__q31_8c.html',1,'']]],
  ['arm_5fmean_5fq7',['arm_mean_q7',['../group__mean.html#gaebc707ee539020357c25da4c75b52eb7',1,'arm_mean_q7(q7_t *pSrc, uint32_t blockSize, q7_t *pResult):&#160;arm_mean_q7.c'],['../group__mean.html#gaebc707ee539020357c25da4c75b52eb7',1,'arm_mean_q7(q7_t *pSrc, uint32_t blockSize, q7_t *pResult):&#160;arm_mean_q7.c']]],
  ['arm_5fmean_5fq7_2ec',['arm_mean_q7.c',['../arm__mean__q7_8c.html',1,'']]],
  ['arm_5fmin_5ff32',['arm_min_f32',['../group___min.html#gaf62b1673740fc516ea64daf777b7d74a',1,'arm_min_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult, uint32_t *pIndex):&#160;arm_min_f32.c'],['../group___min.html#gaf62b1673740fc516ea64daf777b7d74a',1,'arm_min_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult, uint32_t *pIndex):&#160;arm_min_f32.c']]],
  ['arm_5fmin_5ff32_2ec',['arm_min_f32.c',['../arm__min__f32_8c.html',1,'']]],
  ['arm_5fmin_5fq15',['arm_min_q15',['../group___min.html#gad065e37535ebb726750ac1545cb3fa6f',1,'arm_min_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult, uint32_t *pIndex):&#160;arm_min_q15.c'],['../group___min.html#gad065e37535ebb726750ac1545cb3fa6f',1,'arm_min_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult, uint32_t *pIndex):&#160;arm_min_q15.c']]],
  ['arm_5fmin_5fq15_2ec',['arm_min_q15.c',['../arm__min__q15_8c.html',1,'']]],
  ['arm_5fmin_5fq31',['arm_min_q31',['../group___min.html#gab20faeceb5ff5d2d9dd628c2ecf41303',1,'arm_min_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult, uint32_t *pIndex):&#160;arm_min_q31.c'],['../group___min.html#gab20faeceb5ff5d2d9dd628c2ecf41303',1,'arm_min_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult, uint32_t *pIndex):&#160;arm_min_q31.c']]],
  ['arm_5fmin_5fq31_2ec',['arm_min_q31.c',['../arm__min__q31_8c.html',1,'']]],
  ['arm_5fmin_5fq7',['arm_min_q7',['../group___min.html#ga3631d38ac8d715fc14f6f1b343f4c4ed',1,'arm_min_q7(q7_t *pSrc, uint32_t blockSize, q7_t *pResult, uint32_t *pIndex):&#160;arm_min_q7.c'],['../group___min.html#ga3631d38ac8d715fc14f6f1b343f4c4ed',1,'arm_min_q7(q7_t *pSrc, uint32_t blockSize, q7_t *result, uint32_t *index):&#160;arm_min_q7.c']]],
  ['arm_5fmin_5fq7_2ec',['arm_min_q7.c',['../arm__min__q7_8c.html',1,'']]],
  ['arm_5fmult_5ff32',['arm_mult_f32',['../group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32',1,'arm_mult_f32(float32_t *pSrcA, float32_t *pSrcB, float32_t *pDst, uint32_t blockSize):&#160;arm_mult_f32.c'],['../group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32',1,'arm_mult_f32(float32_t *pSrcA, float32_t *pSrcB, float32_t *pDst, uint32_t blockSize):&#160;arm_mult_f32.c']]],
  ['arm_5fmult_5ff32_2ec',['arm_mult_f32.c',['../arm__mult__f32_8c.html',1,'']]],
  ['arm_5fmult_5fq15',['arm_mult_q15',['../group___basic_mult.html#gafb0778d27ed98a2a6f2ecb7d48cc8c75',1,'arm_mult_q15(q15_t *pSrcA, q15_t *pSrcB, q15_t *pDst, uint32_t blockSize):&#160;arm_mult_q15.c'],['../group___basic_mult.html#gafb0778d27ed98a2a6f2ecb7d48cc8c75',1,'arm_mult_q15(q15_t *pSrcA, q15_t *pSrcB, q15_t *pDst, uint32_t blockSize):&#160;arm_mult_q15.c']]],
  ['arm_5fmult_5fq15_2ec',['arm_mult_q15.c',['../arm__mult__q15_8c.html',1,'']]],
  ['arm_5fmult_5fq31',['arm_mult_q31',['../group___basic_mult.html#ga3528c0f54a0607acc603f0490d3ca6c6',1,'arm_mult_q31(q31_t *pSrcA, q31_t *pSrcB, q31_t *pDst, uint32_t blockSize):&#160;arm_mult_q31.c'],['../group___basic_mult.html#ga3528c0f54a0607acc603f0490d3ca6c6',1,'arm_mult_q31(q31_t *pSrcA, q31_t *pSrcB, q31_t *pDst, uint32_t blockSize):&#160;arm_mult_q31.c']]],
  ['arm_5fmult_5fq31_2ec',['arm_mult_q31.c',['../arm__mult__q31_8c.html',1,'']]],
  ['arm_5fmult_5fq7',['arm_mult_q7',['../group___basic_mult.html#ga16677275ed83ff0878da531e875c27ef',1,'arm_mult_q7(q7_t *pSrcA, q7_t *pSrcB, q7_t *pDst, uint32_t blockSize):&#160;arm_mult_q7.c'],['../group___basic_mult.html#ga16677275ed83ff0878da531e875c27ef',1,'arm_mult_q7(q7_t *pSrcA, q7_t *pSrcB, q7_t *pDst, uint32_t blockSize):&#160;arm_mult_q7.c']]],
  ['arm_5fmult_5fq7_2ec',['arm_mult_q7.c',['../arm__mult__q7_8c.html',1,'']]],
  ['arm_5fnegate_5ff32',['arm_negate_f32',['../group__negate.html#ga2e169c4de6cc6e3ba4be9473531e6657',1,'arm_negate_f32(float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_negate_f32.c'],['../group__negate.html#ga2e169c4de6cc6e3ba4be9473531e6657',1,'arm_negate_f32(float32_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_negate_f32.c']]],
  ['arm_5fnegate_5ff32_2ec',['arm_negate_f32.c',['../arm__negate__f32_8c.html',1,'']]],
  ['arm_5fnegate_5fq15',['arm_negate_q15',['../group__negate.html#ga0239a833d72cf00290b9723c394e5042',1,'arm_negate_q15(q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_negate_q15.c'],['../group__negate.html#ga0239a833d72cf00290b9723c394e5042',1,'arm_negate_q15(q15_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_negate_q15.c']]],
  ['arm_5fnegate_5fq15_2ec',['arm_negate_q15.c',['../arm__negate__q15_8c.html',1,'']]],
  ['arm_5fnegate_5fq31',['arm_negate_q31',['../group__negate.html#ga2784c6887686a73dc7c364e2e41c776c',1,'arm_negate_q31(q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_negate_q31.c'],['../group__negate.html#ga2784c6887686a73dc7c364e2e41c776c',1,'arm_negate_q31(q31_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_negate_q31.c']]],
  ['arm_5fnegate_5fq31_2ec',['arm_negate_q31.c',['../arm__negate__q31_8c.html',1,'']]],
  ['arm_5fnegate_5fq7',['arm_negate_q7',['../group__negate.html#gaae78fc079a43bdaa3055f9b32e2a1f4c',1,'arm_negate_q7(q7_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_negate_q7.c'],['../group__negate.html#gaae78fc079a43bdaa3055f9b32e2a1f4c',1,'arm_negate_q7(q7_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_negate_q7.c']]],
  ['arm_5fnegate_5fq7_2ec',['arm_negate_q7.c',['../arm__negate__q7_8c.html',1,'']]],
  ['arm_5foffset_5ff32',['arm_offset_f32',['../group__offset.html#ga989dfae15235799d82f62ef9d356abb4',1,'arm_offset_f32(float32_t *pSrc, float32_t offset, float32_t *pDst, uint32_t blockSize):&#160;arm_offset_f32.c'],['../group__offset.html#ga989dfae15235799d82f62ef9d356abb4',1,'arm_offset_f32(float32_t *pSrc, float32_t offset, float32_t *pDst, uint32_t blockSize):&#160;arm_offset_f32.c']]],
  ['arm_5foffset_5ff32_2ec',['arm_offset_f32.c',['../arm__offset__f32_8c.html',1,'']]],
  ['arm_5foffset_5fq15',['arm_offset_q15',['../group__offset.html#gab4c1d2391b599549e5a06fdfbc2747bf',1,'arm_offset_q15(q15_t *pSrc, q15_t offset, q15_t *pDst, uint32_t blockSize):&#160;arm_offset_q15.c'],['../group__offset.html#gab4c1d2391b599549e5a06fdfbc2747bf',1,'arm_offset_q15(q15_t *pSrc, q15_t offset, q15_t *pDst, uint32_t blockSize):&#160;arm_offset_q15.c']]],
  ['arm_5foffset_5fq15_2ec',['arm_offset_q15.c',['../arm__offset__q15_8c.html',1,'']]],
  ['arm_5foffset_5fq31',['arm_offset_q31',['../group__offset.html#gac84ec42cbbebc5c197a87d0221819acf',1,'arm_offset_q31(q31_t *pSrc, q31_t offset, q31_t *pDst, uint32_t blockSize):&#160;arm_offset_q31.c'],['../group__offset.html#gac84ec42cbbebc5c197a87d0221819acf',1,'arm_offset_q31(q31_t *pSrc, q31_t offset, q31_t *pDst, uint32_t blockSize):&#160;arm_offset_q31.c']]],
  ['arm_5foffset_5fq31_2ec',['arm_offset_q31.c',['../arm__offset__q31_8c.html',1,'']]],
  ['arm_5foffset_5fq7',['arm_offset_q7',['../group__offset.html#ga00bd9cc17c5bf905e76c91ad50886393',1,'arm_offset_q7(q7_t *pSrc, q7_t offset, q7_t *pDst, uint32_t blockSize):&#160;arm_offset_q7.c'],['../group__offset.html#ga00bd9cc17c5bf905e76c91ad50886393',1,'arm_offset_q7(q7_t *pSrc, q7_t offset, q7_t *pDst, uint32_t blockSize):&#160;arm_offset_q7.c']]],
  ['arm_5foffset_5fq7_2ec',['arm_offset_q7.c',['../arm__offset__q7_8c.html',1,'']]],
  ['arm_5fpark_5ff32',['arm_park_f32',['../group__park.html#ga08b3a683197de7e143fb00497787683c',1,'arm_math.h']]],
  ['arm_5fpark_5fq31',['arm_park_q31',['../group__park.html#gaf4cc6370c0cfc14ea66774ed3c5bb10f',1,'arm_math.h']]],
  ['arm_5fpid_5ff32',['arm_pid_f32',['../group___p_i_d.html#gac5c79ed46abf2d72b8cf41fa6c708bda',1,'arm_math.h']]],
  ['arm_5fpid_5finit_5ff32',['arm_pid_init_f32',['../group___p_i_d.html#gae31536b19b82b93ed184fb1ab73cfcb3',1,'arm_pid_init_f32(arm_pid_instance_f32 *S, int32_t resetStateFlag):&#160;arm_pid_init_f32.c'],['../group___p_i_d.html#gae31536b19b82b93ed184fb1ab73cfcb3',1,'arm_pid_init_f32(arm_pid_instance_f32 *S, int32_t resetStateFlag):&#160;arm_pid_init_f32.c']]],
  ['arm_5fpid_5finit_5ff32_2ec',['arm_pid_init_f32.c',['../arm__pid__init__f32_8c.html',1,'']]],
  ['arm_5fpid_5finit_5fq15',['arm_pid_init_q15',['../group___p_i_d.html#ga2cb1e3d3ebb167348fdabec74653d5c3',1,'arm_pid_init_q15(arm_pid_instance_q15 *S, int32_t resetStateFlag):&#160;arm_pid_init_q15.c'],['../group___p_i_d.html#ga2cb1e3d3ebb167348fdabec74653d5c3',1,'arm_pid_init_q15(arm_pid_instance_q15 *S, int32_t resetStateFlag):&#160;arm_pid_init_q15.c']]],
  ['arm_5fpid_5finit_5fq15_2ec',['arm_pid_init_q15.c',['../arm__pid__init__q15_8c.html',1,'']]],
  ['arm_5fpid_5finit_5fq31',['arm_pid_init_q31',['../group___p_i_d.html#gad9d88485234fa9460b1ce9e64989ac86',1,'arm_pid_init_q31(arm_pid_instance_q31 *S, int32_t resetStateFlag):&#160;arm_pid_init_q31.c'],['../group___p_i_d.html#gad9d88485234fa9460b1ce9e64989ac86',1,'arm_pid_init_q31(arm_pid_instance_q31 *S, int32_t resetStateFlag):&#160;arm_pid_init_q31.c']]],
  ['arm_5fpid_5finit_5fq31_2ec',['arm_pid_init_q31.c',['../arm__pid__init__q31_8c.html',1,'']]],
  ['arm_5fpid_5finstance_5ff32',['arm_pid_instance_f32',['../structarm__pid__instance__f32.html',1,'']]],
  ['arm_5fpid_5finstance_5fq15',['arm_pid_instance_q15',['../structarm__pid__instance__q15.html',1,'']]],
  ['arm_5fpid_5finstance_5fq31',['arm_pid_instance_q31',['../structarm__pid__instance__q31.html',1,'']]],
  ['arm_5fpid_5fq15',['arm_pid_q15',['../group___p_i_d.html#ga084f646bbb20d55f225c3efafcf7fc1f',1,'arm_math.h']]],
  ['arm_5fpid_5fq31',['arm_pid_q31',['../group___p_i_d.html#ga5f6f941e7ae981728dd3a662f8f4ecd7',1,'arm_math.h']]],
  ['arm_5fpid_5freset_5ff32',['arm_pid_reset_f32',['../group___p_i_d.html#ga9ec860bcb6f8ca31205bf0f1b51ab723',1,'arm_pid_reset_f32(arm_pid_instance_f32 *S):&#160;arm_pid_reset_f32.c'],['../group___p_i_d.html#ga9ec860bcb6f8ca31205bf0f1b51ab723',1,'arm_pid_reset_f32(arm_pid_instance_f32 *S):&#160;arm_pid_reset_f32.c']]],
  ['arm_5fpid_5freset_5ff32_2ec',['arm_pid_reset_f32.c',['../arm__pid__reset__f32_8c.html',1,'']]],
  ['arm_5fpid_5freset_5fq15',['arm_pid_reset_q15',['../group___p_i_d.html#ga408566dacb4fa6e0458b2c75672e525f',1,'arm_pid_reset_q15(arm_pid_instance_q15 *S):&#160;arm_pid_reset_q15.c'],['../group___p_i_d.html#ga408566dacb4fa6e0458b2c75672e525f',1,'arm_pid_reset_q15(arm_pid_instance_q15 *S):&#160;arm_pid_reset_q15.c']]],
  ['arm_5fpid_5freset_5fq15_2ec',['arm_pid_reset_q15.c',['../arm__pid__reset__q15_8c.html',1,'']]],
  ['arm_5fpid_5freset_5fq31',['arm_pid_reset_q31',['../group___p_i_d.html#gaeecbacd3fb37c608ec25474d3a0dffa9',1,'arm_pid_reset_q31(arm_pid_instance_q31 *S):&#160;arm_pid_reset_q31.c'],['../group___p_i_d.html#gaeecbacd3fb37c608ec25474d3a0dffa9',1,'arm_pid_reset_q31(arm_pid_instance_q31 *S):&#160;arm_pid_reset_q31.c']]],
  ['arm_5fpid_5freset_5fq31_2ec',['arm_pid_reset_q31.c',['../arm__pid__reset__q31_8c.html',1,'']]],
  ['arm_5fpower_5ff32',['arm_power_f32',['../group__power.html#ga993c00dd7f661d66bdb6e58426e893aa',1,'arm_power_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_power_f32.c'],['../group__power.html#ga993c00dd7f661d66bdb6e58426e893aa',1,'arm_power_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_power_f32.c']]],
  ['arm_5fpower_5ff32_2ec',['arm_power_f32.c',['../arm__power__f32_8c.html',1,'']]],
  ['arm_5fpower_5fq15',['arm_power_q15',['../group__power.html#ga7050c04b7515e01a75c38f1abbaf71ba',1,'arm_power_q15(q15_t *pSrc, uint32_t blockSize, q63_t *pResult):&#160;arm_power_q15.c'],['../group__power.html#ga7050c04b7515e01a75c38f1abbaf71ba',1,'arm_power_q15(q15_t *pSrc, uint32_t blockSize, q63_t *pResult):&#160;arm_power_q15.c']]],
  ['arm_5fpower_5fq15_2ec',['arm_power_q15.c',['../arm__power__q15_8c.html',1,'']]],
  ['arm_5fpower_5fq31',['arm_power_q31',['../group__power.html#ga0b93d31bb5b5ed214c2b94d8a7744cd2',1,'arm_power_q31(q31_t *pSrc, uint32_t blockSize, q63_t *pResult):&#160;arm_power_q31.c'],['../group__power.html#ga0b93d31bb5b5ed214c2b94d8a7744cd2',1,'arm_power_q31(q31_t *pSrc, uint32_t blockSize, q63_t *pResult):&#160;arm_power_q31.c']]],
  ['arm_5fpower_5fq31_2ec',['arm_power_q31.c',['../arm__power__q31_8c.html',1,'']]],
  ['arm_5fpower_5fq7',['arm_power_q7',['../group__power.html#gaf969c85c5655e3d72d7b99ff188f92c9',1,'arm_power_q7(q7_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_power_q7.c'],['../group__power.html#gaf969c85c5655e3d72d7b99ff188f92c9',1,'arm_power_q7(q7_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_power_q7.c']]],
  ['arm_5fpower_5fq7_2ec',['arm_power_q7.c',['../arm__power__q7_8c.html',1,'']]],
  ['arm_5fprovide_5fguard_5fbits_5fq15',['arm_provide_guard_bits_q15',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b',1,'arm_provide_guard_bits_q15(q15_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c']]],
  ['arm_5fprovide_5fguard_5fbits_5fq31',['arm_provide_guard_bits_q31',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27',1,'arm_provide_guard_bits_q31(q31_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c']]],
  ['arm_5fprovide_5fguard_5fbits_5fq7',['arm_provide_guard_bits_q7',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029',1,'arm_provide_guard_bits_q7(q7_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029',1,'arm_provide_guard_bits_q7(q7_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029',1,'arm_provide_guard_bits_q7(q7_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029',1,'arm_provide_guard_bits_q7(q7_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029',1,'arm_provide_guard_bits_q7(q7_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029',1,'arm_provide_guard_bits_q7(q7_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029',1,'arm_provide_guard_bits_q7(q7_t *input_buf, uint32_t blockSize, uint32_t guard_bits):&#160;math_helper.c']]],
  ['arm_5fq15_5fto_5ffloat',['arm_q15_to_float',['../group__q15__to__x.html#gaf8b0d2324de273fc430b0e61ad4e9eb2',1,'arm_q15_to_float(q15_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_q15_to_float.c'],['../group__q15__to__x.html#gaf8b0d2324de273fc430b0e61ad4e9eb2',1,'arm_q15_to_float(q15_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_q15_to_float.c']]],
  ['arm_5fq15_5fto_5ffloat_2ec',['arm_q15_to_float.c',['../arm__q15__to__float_8c.html',1,'']]],
  ['arm_5fq15_5fto_5fq31',['arm_q15_to_q31',['../group__q15__to__x.html#ga7ba2d87366990ad5380439e2b4a4c0a5',1,'arm_q15_to_q31(q15_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_q15_to_q31.c'],['../group__q15__to__x.html#ga7ba2d87366990ad5380439e2b4a4c0a5',1,'arm_q15_to_q31(q15_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_q15_to_q31.c']]],
  ['arm_5fq15_5fto_5fq31_2ec',['arm_q15_to_q31.c',['../arm__q15__to__q31_8c.html',1,'']]],
  ['arm_5fq15_5fto_5fq7',['arm_q15_to_q7',['../group__q15__to__x.html#ga8fb31855ff8cce09c2ec9308f48ded69',1,'arm_q15_to_q7(q15_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_q15_to_q7.c'],['../group__q15__to__x.html#ga8fb31855ff8cce09c2ec9308f48ded69',1,'arm_q15_to_q7(q15_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_q15_to_q7.c']]],
  ['arm_5fq15_5fto_5fq7_2ec',['arm_q15_to_q7.c',['../arm__q15__to__q7_8c.html',1,'']]],
  ['arm_5fq31_5fto_5ffloat',['arm_q31_to_float',['../group__q31__to__x.html#gacf407b007a37da18e99dabd9023c56b4',1,'arm_q31_to_float(q31_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_q31_to_float.c'],['../group__q31__to__x.html#gacf407b007a37da18e99dabd9023c56b4',1,'arm_q31_to_float(q31_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_q31_to_float.c']]],
  ['arm_5fq31_5fto_5ffloat_2ec',['arm_q31_to_float.c',['../arm__q31__to__float_8c.html',1,'']]],
  ['arm_5fq31_5fto_5fq15',['arm_q31_to_q15',['../group__q31__to__x.html#ga901dede4661365c9e7c630d3eb31c32c',1,'arm_q31_to_q15(q31_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_q31_to_q15.c'],['../group__q31__to__x.html#ga901dede4661365c9e7c630d3eb31c32c',1,'arm_q31_to_q15(q31_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_q31_to_q15.c']]],
  ['arm_5fq31_5fto_5fq15_2ec',['arm_q31_to_q15.c',['../arm__q31__to__q15_8c.html',1,'']]],
  ['arm_5fq31_5fto_5fq7',['arm_q31_to_q7',['../group__q31__to__x.html#ga7f297d1a7d776805395095fdb24a8071',1,'arm_q31_to_q7(q31_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_q31_to_q7.c'],['../group__q31__to__x.html#ga7f297d1a7d776805395095fdb24a8071',1,'arm_q31_to_q7(q31_t *pSrc, q7_t *pDst, uint32_t blockSize):&#160;arm_q31_to_q7.c']]],
  ['arm_5fq31_5fto_5fq7_2ec',['arm_q31_to_q7.c',['../arm__q31__to__q7_8c.html',1,'']]],
  ['arm_5fq7_5fto_5ffloat',['arm_q7_to_float',['../group__q7__to__x.html#ga656620f957b65512ed83db03fd455ec5',1,'arm_q7_to_float(q7_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_q7_to_float.c'],['../group__q7__to__x.html#ga656620f957b65512ed83db03fd455ec5',1,'arm_q7_to_float(q7_t *pSrc, float32_t *pDst, uint32_t blockSize):&#160;arm_q7_to_float.c']]],
  ['arm_5fq7_5fto_5ffloat_2ec',['arm_q7_to_float.c',['../arm__q7__to__float_8c.html',1,'']]],
  ['arm_5fq7_5fto_5fq15',['arm_q7_to_q15',['../group__q7__to__x.html#gabc02597fc3f01033daf43ec0547a2f78',1,'arm_q7_to_q15(q7_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_q7_to_q15.c'],['../group__q7__to__x.html#gabc02597fc3f01033daf43ec0547a2f78',1,'arm_q7_to_q15(q7_t *pSrc, q15_t *pDst, uint32_t blockSize):&#160;arm_q7_to_q15.c']]],
  ['arm_5fq7_5fto_5fq15_2ec',['arm_q7_to_q15.c',['../arm__q7__to__q15_8c.html',1,'']]],
  ['arm_5fq7_5fto_5fq31',['arm_q7_to_q31',['../group__q7__to__x.html#gad8958cd3cb7f521466168b46a25b7908',1,'arm_q7_to_q31(q7_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_q7_to_q31.c'],['../group__q7__to__x.html#gad8958cd3cb7f521466168b46a25b7908',1,'arm_q7_to_q31(q7_t *pSrc, q31_t *pDst, uint32_t blockSize):&#160;arm_q7_to_q31.c']]],
  ['arm_5fq7_5fto_5fq31_2ec',['arm_q7_to_q31.c',['../arm__q7__to__q31_8c.html',1,'']]],
  ['arm_5fradix2_5fbutterfly_5ff32',['arm_radix2_butterfly_f32',['../arm__cfft__radix2__f32_8c.html#a04631e102b5209af3402b225b1abe868',1,'arm_cfft_radix2_f32.c']]],
  ['arm_5fradix2_5fbutterfly_5finverse_5ff32',['arm_radix2_butterfly_inverse_f32',['../arm__cfft__radix2__f32_8c.html#abda34af152e515a95ac38470ac053b77',1,'arm_cfft_radix2_f32.c']]],
  ['arm_5fradix2_5fbutterfly_5finverse_5fq15',['arm_radix2_butterfly_inverse_q15',['../arm__cfft__radix2__q15_8c.html#a91ff93fa10757b3872680fec4835c412',1,'arm_cfft_radix2_q15.c']]],
  ['arm_5fradix2_5fbutterfly_5finverse_5fq31',['arm_radix2_butterfly_inverse_q31',['../arm__cfft__radix2__q31_8c.html#a4d665f67080455b95cafec04009fd52d',1,'arm_cfft_radix2_q31.c']]],
  ['arm_5fradix2_5fbutterfly_5fq15',['arm_radix2_butterfly_q15',['../arm__cfft__radix2__q15_8c.html#a521780ba2fd3450cbf02784e38859699',1,'arm_cfft_radix2_q15.c']]],
  ['arm_5fradix2_5fbutterfly_5fq31',['arm_radix2_butterfly_q31',['../arm__cfft__radix2__q31_8c.html#a740f4fe69e6148d22fc99f374d304e7e',1,'arm_cfft_radix2_q31.c']]],
  ['arm_5fradix4_5fbutterfly_5ff32',['arm_radix4_butterfly_f32',['../group__group_transforms.html#gae239ddf995d1607115f9e84d5c069b9c',1,'arm_radix4_butterfly_f32(float32_t *pSrc, uint16_t fftLen, float32_t *pCoef, uint16_t twidCoefModifier):&#160;arm_cfft_radix4_f32.c'],['../group__group_transforms.html#gae239ddf995d1607115f9e84d5c069b9c',1,'arm_radix4_butterfly_f32(float32_t *pSrc, uint16_t fftLen, float32_t *pCoef, uint16_t twidCoefModifier):&#160;arm_cfft_radix4_f32.c']]],
  ['arm_5fradix4_5fbutterfly_5finverse_5ff32',['arm_radix4_butterfly_inverse_f32',['../arm__cfft__radix4__f32_8c.html#a2a78df6e4bbf080624f2b6349224ec93',1,'arm_radix4_butterfly_inverse_f32(float32_t *pSrc, uint16_t fftLen, float32_t *pCoef, uint16_t twidCoefModifier, float32_t onebyfftLen):&#160;arm_cfft_radix4_f32.c'],['../arm__rfft__f32_8c.html#a2a78df6e4bbf080624f2b6349224ec93',1,'arm_radix4_butterfly_inverse_f32(float32_t *pSrc, uint16_t fftLen, float32_t *pCoef, uint16_t twidCoefModifier, float32_t onebyfftLen):&#160;arm_cfft_radix4_f32.c']]],
  ['arm_5fradix4_5fbutterfly_5finverse_5fq15',['arm_radix4_butterfly_inverse_q15',['../arm__cfft__q15_8c.html#a734ecff00f21a6a10e9ec437c8987eb1',1,'arm_radix4_butterfly_inverse_q15(q15_t *pSrc, uint32_t fftLen, q15_t *pCoef, uint32_t twidCoefModifier):&#160;arm_cfft_radix4_q15.c'],['../arm__cfft__radix4__q15_8c.html#aad04e8439d17dab5617bf1be268bb391',1,'arm_radix4_butterfly_inverse_q15(q15_t *pSrc16, uint32_t fftLen, q15_t *pCoef16, uint32_t twidCoefModifier):&#160;arm_cfft_radix4_q15.c']]],
  ['arm_5fradix4_5fbutterfly_5finverse_5fq31',['arm_radix4_butterfly_inverse_q31',['../arm__cfft__q31_8c.html#ac9c7c553114c1201a3a987a11b8a6d01',1,'arm_radix4_butterfly_inverse_q31(q31_t *pSrc, uint32_t fftLen, q31_t *pCoef, uint32_t twidCoefModifier):&#160;arm_cfft_radix4_q31.c'],['../arm__cfft__radix4__q31_8c.html#ac9c7c553114c1201a3a987a11b8a6d01',1,'arm_radix4_butterfly_inverse_q31(q31_t *pSrc, uint32_t fftLen, q31_t *pCoef, uint32_t twidCoefModifier):&#160;arm_cfft_radix4_q31.c']]],
  ['arm_5fradix4_5fbutterfly_5fq15',['arm_radix4_butterfly_q15',['../arm__cfft__q15_8c.html#abf1a2f9aa9f44ad5da1f0dbae8b54f2f',1,'arm_radix4_butterfly_q15(q15_t *pSrc, uint32_t fftLen, q15_t *pCoef, uint32_t twidCoefModifier):&#160;arm_cfft_radix4_q15.c'],['../arm__cfft__radix4__q15_8c.html#a2d01d2045f280c32036da97d33c52440',1,'arm_radix4_butterfly_q15(q15_t *pSrc16, uint32_t fftLen, q15_t *pCoef16, uint32_t twidCoefModifier):&#160;arm_cfft_radix4_q15.c']]],
  ['arm_5fradix4_5fbutterfly_5fq31',['arm_radix4_butterfly_q31',['../arm__cfft__q31_8c.html#ac12f1e7f159d5741358cdc36830a0395',1,'arm_radix4_butterfly_q31(q31_t *pSrc, uint32_t fftLen, q31_t *pCoef, uint32_t twidCoefModifier):&#160;arm_cfft_radix4_q31.c'],['../arm__cfft__radix4__q31_8c.html#ac12f1e7f159d5741358cdc36830a0395',1,'arm_radix4_butterfly_q31(q31_t *pSrc, uint32_t fftLen, q31_t *pCoef, uint32_t twidCoefModifier):&#160;arm_cfft_radix4_q31.c']]],
  ['arm_5fradix8_5fbutterfly_5ff32',['arm_radix8_butterfly_f32',['../arm__cfft__f32_8c.html#a72350c6eaa1eef8796ab43c1497c6b9c',1,'arm_radix8_butterfly_f32(float32_t *pSrc, uint16_t fftLen, const float32_t *pCoef, uint16_t twidCoefModifier):&#160;arm_cfft_radix8_f32.c'],['../arm__cfft__radix8__f32_8c.html#a72350c6eaa1eef8796ab43c1497c6b9c',1,'arm_radix8_butterfly_f32(float32_t *pSrc, uint16_t fftLen, const float32_t *pCoef, uint16_t twidCoefModifier):&#160;arm_cfft_radix8_f32.c']]],
  ['arm_5frecip_5fq15',['arm_recip_q15',['../arm__math_8h.html#a1c66e370a6ae91aaafbaec5e979198d7',1,'arm_math.h']]],
  ['arm_5frecip_5fq31',['arm_recip_q31',['../arm__math_8h.html#a43140f04ca94c2a7394e7a222e2d8fb4',1,'arm_math.h']]],
  ['arm_5frfft_5ff32',['arm_rfft_f32',['../group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc',1,'arm_rfft_f32(const arm_rfft_instance_f32 *S, float32_t *pSrc, float32_t *pDst):&#160;arm_rfft_f32.c'],['../group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc',1,'arm_rfft_f32(const arm_rfft_instance_f32 *S, float32_t *pSrc, float32_t *pDst):&#160;arm_rfft_f32.c']]],
  ['arm_5frfft_5ff32_2ec',['arm_rfft_f32.c',['../arm__rfft__f32_8c.html',1,'']]],
  ['arm_5frfft_5ffast_5ff32',['arm_rfft_fast_f32',['../group___real_f_f_t.html#ga180d8b764d59cbb85d37a2d5f7cd9799',1,'arm_rfft_fast_f32(arm_rfft_fast_instance_f32 *S, float32_t *p, float32_t *pOut, uint8_t ifftFlag):&#160;arm_rfft_fast_f32.c'],['../group___real_f_f_t.html#ga180d8b764d59cbb85d37a2d5f7cd9799',1,'arm_rfft_fast_f32(arm_rfft_fast_instance_f32 *S, float32_t *p, float32_t *pOut, uint8_t ifftFlag):&#160;arm_rfft_fast_f32.c']]],
  ['arm_5frfft_5ffast_5ff32_2ec',['arm_rfft_fast_f32.c',['../arm__rfft__fast__f32_8c.html',1,'']]],
  ['arm_5frfft_5ffast_5finit_5ff32',['arm_rfft_fast_init_f32',['../group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3',1,'arm_rfft_fast_init_f32(arm_rfft_fast_instance_f32 *S, uint16_t fftLen):&#160;arm_rfft_fast_init_f32.c'],['../group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3',1,'arm_rfft_fast_init_f32(arm_rfft_fast_instance_f32 *S, uint16_t fftLen):&#160;arm_rfft_fast_init_f32.c']]],
  ['arm_5frfft_5ffast_5finit_5ff32_2ec',['arm_rfft_fast_init_f32.c',['../arm__rfft__fast__init__f32_8c.html',1,'']]],
  ['arm_5frfft_5ffast_5finstance_5ff32',['arm_rfft_fast_instance_f32',['../structarm__rfft__fast__instance__f32.html',1,'']]],
  ['arm_5frfft_5finit_5ff32',['arm_rfft_init_f32',['../group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068',1,'arm_rfft_init_f32(arm_rfft_instance_f32 *S, arm_cfft_radix4_instance_f32 *S_CFFT, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag):&#160;arm_rfft_init_f32.c'],['../group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068',1,'arm_rfft_init_f32(arm_rfft_instance_f32 *S, arm_cfft_radix4_instance_f32 *S_CFFT, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag):&#160;arm_rfft_init_f32.c']]],
  ['arm_5frfft_5finit_5ff32_2ec',['arm_rfft_init_f32.c',['../arm__rfft__init__f32_8c.html',1,'']]],
  ['arm_5frfft_5finit_5fq15',['arm_rfft_init_q15',['../group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d',1,'arm_rfft_init_q15(arm_rfft_instance_q15 *S, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag):&#160;arm_rfft_init_q15.c'],['../group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d',1,'arm_rfft_init_q15(arm_rfft_instance_q15 *S, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag):&#160;arm_rfft_init_q15.c']]],
  ['arm_5frfft_5finit_5fq15_2ec',['arm_rfft_init_q15.c',['../arm__rfft__init__q15_8c.html',1,'']]],
  ['arm_5frfft_5finit_5fq31',['arm_rfft_init_q31',['../group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45',1,'arm_rfft_init_q31(arm_rfft_instance_q31 *S, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag):&#160;arm_rfft_init_q31.c'],['../group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45',1,'arm_rfft_init_q31(arm_rfft_instance_q31 *S, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag):&#160;arm_rfft_init_q31.c']]],
  ['arm_5frfft_5finit_5fq31_2ec',['arm_rfft_init_q31.c',['../arm__rfft__init__q31_8c.html',1,'']]],
  ['arm_5frfft_5finstance_5ff32',['arm_rfft_instance_f32',['../structarm__rfft__instance__f32.html',1,'']]],
  ['arm_5frfft_5finstance_5fq15',['arm_rfft_instance_q15',['../structarm__rfft__instance__q15.html',1,'']]],
  ['arm_5frfft_5finstance_5fq31',['arm_rfft_instance_q31',['../structarm__rfft__instance__q31.html',1,'']]],
  ['arm_5frfft_5fq15',['arm_rfft_q15',['../group___real_f_f_t.html#ga00e615f5db21736ad5b27fb6146f3fc5',1,'arm_rfft_q15(const arm_rfft_instance_q15 *S, q15_t *pSrc, q15_t *pDst):&#160;arm_rfft_q15.c'],['../group___real_f_f_t.html#ga00e615f5db21736ad5b27fb6146f3fc5',1,'arm_rfft_q15(const arm_rfft_instance_q15 *S, q15_t *pSrc, q15_t *pDst):&#160;arm_rfft_q15.c']]],
  ['arm_5frfft_5fq15_2ec',['arm_rfft_q15.c',['../arm__rfft__q15_8c.html',1,'']]],
  ['arm_5frfft_5fq31',['arm_rfft_q31',['../group___real_f_f_t.html#gabaeab5646aeea9844e6d42ca8c73fe3a',1,'arm_rfft_q31(const arm_rfft_instance_q31 *S, q31_t *pSrc, q31_t *pDst):&#160;arm_rfft_q31.c'],['../group___real_f_f_t.html#gabaeab5646aeea9844e6d42ca8c73fe3a',1,'arm_rfft_q31(const arm_rfft_instance_q31 *S, q31_t *pSrc, q31_t *pDst):&#160;arm_rfft_q31.c']]],
  ['arm_5frfft_5fq31_2ec',['arm_rfft_q31.c',['../arm__rfft__q31_8c.html',1,'']]],
  ['arm_5frms_5ff32',['arm_rms_f32',['../group___r_m_s.html#ga0e3ab1b57da32d45388d1fa90d7fd88c',1,'arm_rms_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_rms_f32.c'],['../group___r_m_s.html#ga0e3ab1b57da32d45388d1fa90d7fd88c',1,'arm_rms_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_rms_f32.c']]],
  ['arm_5frms_5ff32_2ec',['arm_rms_f32.c',['../arm__rms__f32_8c.html',1,'']]],
  ['arm_5frms_5fq15',['arm_rms_q15',['../group___r_m_s.html#gaf5b836b72dda9e5dfbbd17c7906fd13f',1,'arm_rms_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult):&#160;arm_rms_q15.c'],['../group___r_m_s.html#gaf5b836b72dda9e5dfbbd17c7906fd13f',1,'arm_rms_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult):&#160;arm_rms_q15.c']]],
  ['arm_5frms_5fq15_2ec',['arm_rms_q15.c',['../arm__rms__q15_8c.html',1,'']]],
  ['arm_5frms_5fq31',['arm_rms_q31',['../group___r_m_s.html#gae33015fda23fc44e7ead5e5ed7e8d314',1,'arm_rms_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_rms_q31.c'],['../group___r_m_s.html#gae33015fda23fc44e7ead5e5ed7e8d314',1,'arm_rms_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_rms_q31.c']]],
  ['arm_5frms_5fq31_2ec',['arm_rms_q31.c',['../arm__rms__q31_8c.html',1,'']]],
  ['arm_5fscale_5ff32',['arm_scale_f32',['../group__scale.html#ga3487af88b112f682ee90589cd419e123',1,'arm_scale_f32(float32_t *pSrc, float32_t scale, float32_t *pDst, uint32_t blockSize):&#160;arm_scale_f32.c'],['../group__scale.html#ga3487af88b112f682ee90589cd419e123',1,'arm_scale_f32(float32_t *pSrc, float32_t scale, float32_t *pDst, uint32_t blockSize):&#160;arm_scale_f32.c']]],
  ['arm_5fscale_5ff32_2ec',['arm_scale_f32.c',['../arm__scale__f32_8c.html',1,'']]],
  ['arm_5fscale_5fq15',['arm_scale_q15',['../group__scale.html#gafaac0e1927daffeb68a42719b53ea780',1,'arm_scale_q15(q15_t *pSrc, q15_t scaleFract, int8_t shift, q15_t *pDst, uint32_t blockSize):&#160;arm_scale_q15.c'],['../group__scale.html#gafaac0e1927daffeb68a42719b53ea780',1,'arm_scale_q15(q15_t *pSrc, q15_t scaleFract, int8_t shift, q15_t *pDst, uint32_t blockSize):&#160;arm_scale_q15.c']]],
  ['arm_5fscale_5fq15_2ec',['arm_scale_q15.c',['../arm__scale__q15_8c.html',1,'']]],
  ['arm_5fscale_5fq31',['arm_scale_q31',['../group__scale.html#ga83e36cd82bf51ce35406a199e477d47c',1,'arm_scale_q31(q31_t *pSrc, q31_t scaleFract, int8_t shift, q31_t *pDst, uint32_t blockSize):&#160;arm_scale_q31.c'],['../group__scale.html#ga83e36cd82bf51ce35406a199e477d47c',1,'arm_scale_q31(q31_t *pSrc, q31_t scaleFract, int8_t shift, q31_t *pDst, uint32_t blockSize):&#160;arm_scale_q31.c']]],
  ['arm_5fscale_5fq31_2ec',['arm_scale_q31.c',['../arm__scale__q31_8c.html',1,'']]],
  ['arm_5fscale_5fq7',['arm_scale_q7',['../group__scale.html#gabc9fd3d37904c58df56492b351d21fb0',1,'arm_scale_q7(q7_t *pSrc, q7_t scaleFract, int8_t shift, q7_t *pDst, uint32_t blockSize):&#160;arm_scale_q7.c'],['../group__scale.html#gabc9fd3d37904c58df56492b351d21fb0',1,'arm_scale_q7(q7_t *pSrc, q7_t scaleFract, int8_t shift, q7_t *pDst, uint32_t blockSize):&#160;arm_scale_q7.c']]],
  ['arm_5fscale_5fq7_2ec',['arm_scale_q7.c',['../arm__scale__q7_8c.html',1,'']]],
  ['arm_5fshift_5fq15',['arm_shift_q15',['../group__shift.html#gaa1757e53279780107acc92cf100adb61',1,'arm_shift_q15(q15_t *pSrc, int8_t shiftBits, q15_t *pDst, uint32_t blockSize):&#160;arm_shift_q15.c'],['../group__shift.html#gaa1757e53279780107acc92cf100adb61',1,'arm_shift_q15(q15_t *pSrc, int8_t shiftBits, q15_t *pDst, uint32_t blockSize):&#160;arm_shift_q15.c']]],
  ['arm_5fshift_5fq15_2ec',['arm_shift_q15.c',['../arm__shift__q15_8c.html',1,'']]],
  ['arm_5fshift_5fq31',['arm_shift_q31',['../group__shift.html#ga387dd8b7b87377378280978f16cdb13d',1,'arm_shift_q31(q31_t *pSrc, int8_t shiftBits, q31_t *pDst, uint32_t blockSize):&#160;arm_shift_q31.c'],['../group__shift.html#ga387dd8b7b87377378280978f16cdb13d',1,'arm_shift_q31(q31_t *pSrc, int8_t shiftBits, q31_t *pDst, uint32_t blockSize):&#160;arm_shift_q31.c']]],
  ['arm_5fshift_5fq31_2ec',['arm_shift_q31.c',['../arm__shift__q31_8c.html',1,'']]],
  ['arm_5fshift_5fq7',['arm_shift_q7',['../group__shift.html#ga47295d08a685f7de700a48dafb4db6fb',1,'arm_shift_q7(q7_t *pSrc, int8_t shiftBits, q7_t *pDst, uint32_t blockSize):&#160;arm_shift_q7.c'],['../group__shift.html#ga47295d08a685f7de700a48dafb4db6fb',1,'arm_shift_q7(q7_t *pSrc, int8_t shiftBits, q7_t *pDst, uint32_t blockSize):&#160;arm_shift_q7.c']]],
  ['arm_5fshift_5fq7_2ec',['arm_shift_q7.c',['../arm__shift__q7_8c.html',1,'']]],
  ['arm_5fsignal_5fconverge_5fdata_2ec',['arm_signal_converge_data.c',['../arm__signal__converge__data_8c.html',1,'']]],
  ['arm_5fsignal_5fconverge_5fexample_5ff32_2ec',['arm_signal_converge_example_f32.c',['../arm__signal__converge__example__f32_8c.html',1,'']]],
  ['arm_5fsin_5fcos_5fexample_5ff32_2ec',['arm_sin_cos_example_f32.c',['../arm__sin__cos__example__f32_8c.html',1,'']]],
  ['arm_5fsin_5fcos_5ff32',['arm_sin_cos_f32',['../group___sin_cos.html#ga4420d45c37d58c310ef9ae1b5fe58020',1,'arm_sin_cos_f32(float32_t theta, float32_t *pSinVal, float32_t *pCosVal):&#160;arm_sin_cos_f32.c'],['../group___sin_cos.html#ga4420d45c37d58c310ef9ae1b5fe58020',1,'arm_sin_cos_f32(float32_t theta, float32_t *pSinVal, float32_t *pCcosVal):&#160;arm_sin_cos_f32.c']]],
  ['arm_5fsin_5fcos_5ff32_2ec',['arm_sin_cos_f32.c',['../arm__sin__cos__f32_8c.html',1,'']]],
  ['arm_5fsin_5fcos_5fq31',['arm_sin_cos_q31',['../group___sin_cos.html#gae9e4ddebff9d4eb5d0a093e28e0bc504',1,'arm_sin_cos_q31(q31_t theta, q31_t *pSinVal, q31_t *pCosVal):&#160;arm_sin_cos_q31.c'],['../group___sin_cos.html#gae9e4ddebff9d4eb5d0a093e28e0bc504',1,'arm_sin_cos_q31(q31_t theta, q31_t *pSinVal, q31_t *pCosVal):&#160;arm_sin_cos_q31.c']]],
  ['arm_5fsin_5fcos_5fq31_2ec',['arm_sin_cos_q31.c',['../arm__sin__cos__q31_8c.html',1,'']]],
  ['arm_5fsin_5ff32',['arm_sin_f32',['../group__sin.html#gae164899c4a3fc0e946dc5d55555fe541',1,'arm_sin_f32(float32_t x):&#160;arm_sin_f32.c'],['../group__sin.html#gae164899c4a3fc0e946dc5d55555fe541',1,'arm_sin_f32(float32_t x):&#160;arm_sin_f32.c']]],
  ['arm_5fsin_5ff32_2ec',['arm_sin_f32.c',['../arm__sin__f32_8c.html',1,'']]],
  ['arm_5fsin_5fq15',['arm_sin_q15',['../group__sin.html#ga1fc6d6640be6cfa688a8bea0a48397ee',1,'arm_sin_q15(q15_t x):&#160;arm_sin_q15.c'],['../group__sin.html#ga1fc6d6640be6cfa688a8bea0a48397ee',1,'arm_sin_q15(q15_t x):&#160;arm_sin_q15.c']]],
  ['arm_5fsin_5fq15_2ec',['arm_sin_q15.c',['../arm__sin__q15_8c.html',1,'']]],
  ['arm_5fsin_5fq31',['arm_sin_q31',['../group__sin.html#ga57aade7d8892585992cdc6375bd82f9c',1,'arm_sin_q31(q31_t x):&#160;arm_sin_q31.c'],['../group__sin.html#ga57aade7d8892585992cdc6375bd82f9c',1,'arm_sin_q31(q31_t x):&#160;arm_sin_q31.c']]],
  ['arm_5fsin_5fq31_2ec',['arm_sin_q31.c',['../arm__sin__q31_8c.html',1,'']]],
  ['arm_5fsnr_5ff32',['arm_snr_f32',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__convolution__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__convolution__example_2_g_c_c_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__fir__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__matrix__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c'],['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#aeea2952e70a1040a6efa555564bbeeab',1,'arm_snr_f32(float *pRef, float *pTest, uint32_t buffSize):&#160;math_helper.c']]],
  ['arm_5fsplit_5frfft_5ff32',['arm_split_rfft_f32',['../group__group_transforms.html#ga6cfdb6bdc66b13732ef2351caf98fdbb',1,'arm_rfft_f32.c']]],
  ['arm_5fsplit_5frfft_5fq15',['arm_split_rfft_q15',['../arm__rfft__q15_8c.html#a7c2a21793586f9a69c42140665550e09',1,'arm_rfft_q15.c']]],
  ['arm_5fsplit_5frfft_5fq31',['arm_split_rfft_q31',['../arm__rfft__q31_8c.html#a520e1c358d44fcd2724cb19d46eb5dfa',1,'arm_rfft_q31.c']]],
  ['arm_5fsplit_5frifft_5ff32',['arm_split_rifft_f32',['../arm__rfft__f32_8c.html#a585bef78c103d150a116241a4feb6442',1,'arm_rfft_f32.c']]],
  ['arm_5fsplit_5frifft_5fq15',['arm_split_rifft_q15',['../arm__rfft__q15_8c.html#aa72a531dd15a53570dddaf01b62158f4',1,'arm_rfft_q15.c']]],
  ['arm_5fsplit_5frifft_5fq31',['arm_split_rifft_q31',['../arm__rfft__q31_8c.html#acc62dd39a59091c4d6a80d4e55adeb13',1,'arm_rfft_q31.c']]],
  ['arm_5fsqrt_5ff32',['arm_sqrt_f32',['../group___s_q_r_t.html#ga56a40d1cf842b0b45267df6761975da0',1,'arm_math.h']]],
  ['arm_5fsqrt_5fq15',['arm_sqrt_q15',['../group___s_q_r_t.html#ga5abe5ca724f3e15849662b03752c1238',1,'arm_sqrt_q15.c']]],
  ['arm_5fsqrt_5fq15_2ec',['arm_sqrt_q15.c',['../arm__sqrt__q15_8c.html',1,'']]],
  ['arm_5fsqrt_5fq31',['arm_sqrt_q31',['../group___s_q_r_t.html#ga119e25831e141d734d7ef10636670058',1,'arm_sqrt_q31.c']]],
  ['arm_5fsqrt_5fq31_2ec',['arm_sqrt_q31.c',['../arm__sqrt__q31_8c.html',1,'']]],
  ['arm_5fstatus',['arm_status',['../arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6',1,'arm_math.h']]],
  ['arm_5fstd_5ff32',['arm_std_f32',['../group___s_t_d.html#ga4969b5b5f3d001377bc401a3ee99dfc2',1,'arm_std_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_std_f32.c'],['../group___s_t_d.html#ga4969b5b5f3d001377bc401a3ee99dfc2',1,'arm_std_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_std_f32.c']]],
  ['arm_5fstd_5ff32_2ec',['arm_std_f32.c',['../arm__std__f32_8c.html',1,'']]],
  ['arm_5fstd_5fq15',['arm_std_q15',['../group___s_t_d.html#gaf9d27afa9928ff28a63cd98ea9218a72',1,'arm_std_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult):&#160;arm_std_q15.c'],['../group___s_t_d.html#gaf9d27afa9928ff28a63cd98ea9218a72',1,'arm_std_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult):&#160;arm_std_q15.c']]],
  ['arm_5fstd_5fq15_2ec',['arm_std_q15.c',['../arm__std__q15_8c.html',1,'']]],
  ['arm_5fstd_5fq31',['arm_std_q31',['../group___s_t_d.html#ga39495e74f96116178be085c9dc7742f5',1,'arm_std_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_std_q31.c'],['../group___s_t_d.html#ga39495e74f96116178be085c9dc7742f5',1,'arm_std_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_std_q31.c']]],
  ['arm_5fstd_5fq31_2ec',['arm_std_q31.c',['../arm__std__q31_8c.html',1,'']]],
  ['arm_5fsub_5ff32',['arm_sub_f32',['../group___basic_sub.html#ga7f975a472de286331134227c08aad826',1,'arm_sub_f32(float32_t *pSrcA, float32_t *pSrcB, float32_t *pDst, uint32_t blockSize):&#160;arm_sub_f32.c'],['../group___basic_sub.html#ga7f975a472de286331134227c08aad826',1,'arm_sub_f32(float32_t *pSrcA, float32_t *pSrcB, float32_t *pDst, uint32_t blockSize):&#160;arm_sub_f32.c']]],
  ['arm_5fsub_5ff32_2ec',['arm_sub_f32.c',['../arm__sub__f32_8c.html',1,'']]],
  ['arm_5fsub_5fq15',['arm_sub_q15',['../group___basic_sub.html#ga997a8ee93088d15bda23c325d455b588',1,'arm_sub_q15(q15_t *pSrcA, q15_t *pSrcB, q15_t *pDst, uint32_t blockSize):&#160;arm_sub_q15.c'],['../group___basic_sub.html#ga997a8ee93088d15bda23c325d455b588',1,'arm_sub_q15(q15_t *pSrcA, q15_t *pSrcB, q15_t *pDst, uint32_t blockSize):&#160;arm_sub_q15.c']]],
  ['arm_5fsub_5fq15_2ec',['arm_sub_q15.c',['../arm__sub__q15_8c.html',1,'']]],
  ['arm_5fsub_5fq31',['arm_sub_q31',['../group___basic_sub.html#ga28aa6908d092752144413e21933dc878',1,'arm_sub_q31(q31_t *pSrcA, q31_t *pSrcB, q31_t *pDst, uint32_t blockSize):&#160;arm_sub_q31.c'],['../group___basic_sub.html#ga28aa6908d092752144413e21933dc878',1,'arm_sub_q31(q31_t *pSrcA, q31_t *pSrcB, q31_t *pDst, uint32_t blockSize):&#160;arm_sub_q31.c']]],
  ['arm_5fsub_5fq31_2ec',['arm_sub_q31.c',['../arm__sub__q31_8c.html',1,'']]],
  ['arm_5fsub_5fq7',['arm_sub_q7',['../group___basic_sub.html#gab09941de7dfeb247e5c29b406a435fcc',1,'arm_sub_q7(q7_t *pSrcA, q7_t *pSrcB, q7_t *pDst, uint32_t blockSize):&#160;arm_sub_q7.c'],['../group___basic_sub.html#gab09941de7dfeb247e5c29b406a435fcc',1,'arm_sub_q7(q7_t *pSrcA, q7_t *pSrcB, q7_t *pDst, uint32_t blockSize):&#160;arm_sub_q7.c']]],
  ['arm_5fsub_5fq7_2ec',['arm_sub_q7.c',['../arm__sub__q7_8c.html',1,'']]],
  ['arm_5fvar_5ff32',['arm_var_f32',['../group__variance.html#ga393f26c5a3bfa05624fb8d32232a6d96',1,'arm_var_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_var_f32.c'],['../group__variance.html#ga393f26c5a3bfa05624fb8d32232a6d96',1,'arm_var_f32(float32_t *pSrc, uint32_t blockSize, float32_t *pResult):&#160;arm_var_f32.c']]],
  ['arm_5fvar_5ff32_2ec',['arm_var_f32.c',['../arm__var__f32_8c.html',1,'']]],
  ['arm_5fvar_5fq15',['arm_var_q15',['../group__variance.html#ga79dce009ed2de28a125aeb3f19631654',1,'arm_var_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult):&#160;arm_var_q15.c'],['../group__variance.html#ga79dce009ed2de28a125aeb3f19631654',1,'arm_var_q15(q15_t *pSrc, uint32_t blockSize, q15_t *pResult):&#160;arm_var_q15.c']]],
  ['arm_5fvar_5fq15_2ec',['arm_var_q15.c',['../arm__var__q15_8c.html',1,'']]],
  ['arm_5fvar_5fq31',['arm_var_q31',['../group__variance.html#gac02873f1c2cc80adfd799305f0e6465d',1,'arm_var_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_var_q31.c'],['../group__variance.html#gac02873f1c2cc80adfd799305f0e6465d',1,'arm_var_q31(q31_t *pSrc, uint32_t blockSize, q31_t *pResult):&#160;arm_var_q31.c']]],
  ['arm_5fvar_5fq31_2ec',['arm_var_q31.c',['../arm__var__q31_8c.html',1,'']]],
  ['arm_5fvariance_5fexample_5ff32_2ec',['arm_variance_example_f32.c',['../arm__variance__example__f32_8c.html',1,'']]],
  ['armbitrevindextable1024',['armBitRevIndexTable1024',['../arm__common__tables_8c.html#ae69b72fb0be5dab9a0ea76e9b6995cb6',1,'armBitRevIndexTable1024():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#ae69b72fb0be5dab9a0ea76e9b6995cb6',1,'armBitRevIndexTable1024():&#160;arm_common_tables.c']]],
  ['armbitrevindextable1024_5ftable_5flength',['ARMBITREVINDEXTABLE1024_TABLE_LENGTH',['../arm__common__tables_8h.html#af3b3659a55efaf414757d15e6c0ea9cc',1,'arm_common_tables.h']]],
  ['armbitrevindextable128',['armBitRevIndexTable128',['../arm__common__tables_8c.html#a04711bbb245f2ac7202db666eaaf10f2',1,'armBitRevIndexTable128():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a04711bbb245f2ac7202db666eaaf10f2',1,'armBitRevIndexTable128():&#160;arm_common_tables.c']]],
  ['armbitrevindextable16',['armBitRevIndexTable16',['../arm__common__tables_8c.html#a5ab065857509fe5780d79fdcdce801cb',1,'armBitRevIndexTable16():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a5ab065857509fe5780d79fdcdce801cb',1,'armBitRevIndexTable16():&#160;arm_common_tables.c']]],
  ['armbitrevindextable2048',['armBitRevIndexTable2048',['../arm__common__tables_8c.html#a68b7fcd07ae5433082e600dc7e7c7430',1,'armBitRevIndexTable2048():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a68b7fcd07ae5433082e600dc7e7c7430',1,'armBitRevIndexTable2048():&#160;arm_common_tables.c']]],
  ['armbitrevindextable2048_5ftable_5flength',['ARMBITREVINDEXTABLE2048_TABLE_LENGTH',['../arm__common__tables_8h.html#a1137f42be79c5941e942b58e262b5225',1,'arm_common_tables.h']]],
  ['armbitrevindextable256',['armBitRevIndexTable256',['../arm__common__tables_8c.html#a77b17c8e7539af315c57de27610d8407',1,'armBitRevIndexTable256():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a77b17c8e7539af315c57de27610d8407',1,'armBitRevIndexTable256():&#160;arm_common_tables.c']]],
  ['armbitrevindextable32',['armBitRevIndexTable32',['../arm__common__tables_8c.html#afae094ea3df14c134012c4cb7b816637',1,'armBitRevIndexTable32():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#afae094ea3df14c134012c4cb7b816637',1,'armBitRevIndexTable32():&#160;arm_common_tables.c']]],
  ['armbitrevindextable4096',['armBitRevIndexTable4096',['../arm__common__tables_8c.html#ac6bd23609f5bb10182e8eae65400541b',1,'armBitRevIndexTable4096():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#ac6bd23609f5bb10182e8eae65400541b',1,'armBitRevIndexTable4096():&#160;arm_common_tables.c']]],
  ['armbitrevindextable4096_5ftable_5flength',['ARMBITREVINDEXTABLE4096_TABLE_LENGTH',['../arm__common__tables_8h.html#af08eb635c0e1cf0ab3e29931f9bf1492',1,'arm_common_tables.h']]],
  ['armbitrevindextable512',['armBitRevIndexTable512',['../arm__common__tables_8c.html#a297a311183fb6d17d7ee0152ad1e43f3',1,'armBitRevIndexTable512():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a297a311183fb6d17d7ee0152ad1e43f3',1,'armBitRevIndexTable512():&#160;arm_common_tables.c']]],
  ['armbitrevindextable64',['armBitRevIndexTable64',['../arm__common__tables_8c.html#aafcb5c9203dada88ed6d1bdcf16aaba4',1,'armBitRevIndexTable64():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#aafcb5c9203dada88ed6d1bdcf16aaba4',1,'armBitRevIndexTable64():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5f128_5ftable_5flength',['ARMBITREVINDEXTABLE_128_TABLE_LENGTH',['../arm__common__tables_8h.html#abb73376f7efda869394aab2acef4291c',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5f256_5ftable_5flength',['ARMBITREVINDEXTABLE_256_TABLE_LENGTH',['../arm__common__tables_8h.html#aa7dc18c3b4f8d76f5a29f7b182007934',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5f512_5ftable_5flength',['ARMBITREVINDEXTABLE_512_TABLE_LENGTH',['../arm__common__tables_8h.html#ab21231782baf177ef3edad11aeba5a4f',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5f_5f16_5ftable_5flength',['ARMBITREVINDEXTABLE__16_TABLE_LENGTH',['../arm__common__tables_8h.html#a52289ebb691669410fbc40d1a8a1562a',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5f_5f32_5ftable_5flength',['ARMBITREVINDEXTABLE__32_TABLE_LENGTH',['../arm__common__tables_8h.html#a6e12fc7073f15899078a1b2d8f4afb4c',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5f_5f64_5ftable_5flength',['ARMBITREVINDEXTABLE__64_TABLE_LENGTH',['../arm__common__tables_8h.html#a73e1987baf5282c699168bccf635930e',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f1024',['armBitRevIndexTable_fixed_1024',['../arm__common__tables_8c.html#a579beb19201fab01210c37253447fa52',1,'armBitRevIndexTable_fixed_1024():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a579beb19201fab01210c37253447fa52',1,'armBitRevIndexTable_fixed_1024():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f1024_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED_1024_TABLE_LENGTH',['../arm__common__tables_8h.html#ab78db333c5f36a927cf5f6b492e93dd3',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f128',['armBitRevIndexTable_fixed_128',['../arm__common__tables_8c.html#aa10281deffc0cb708a08d55cfa513507',1,'armBitRevIndexTable_fixed_128():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#aa10281deffc0cb708a08d55cfa513507',1,'armBitRevIndexTable_fixed_128():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f16',['armBitRevIndexTable_fixed_16',['../arm__common__tables_8c.html#a6c3b510a7d499dccaaea1ff164397ffb',1,'armBitRevIndexTable_fixed_16():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a6c3b510a7d499dccaaea1ff164397ffb',1,'armBitRevIndexTable_fixed_16():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f2048',['armBitRevIndexTable_fixed_2048',['../arm__common__tables_8c.html#ad888a207e20f601ed80b2ad43428c8cc',1,'armBitRevIndexTable_fixed_2048():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#ad888a207e20f601ed80b2ad43428c8cc',1,'armBitRevIndexTable_fixed_2048():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f2048_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED_2048_TABLE_LENGTH',['../arm__common__tables_8h.html#a7dbfc9019953b525d83184a50f9976cc',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f256',['armBitRevIndexTable_fixed_256',['../arm__common__tables_8c.html#a721d01114016584629f03c0af37dd21e',1,'armBitRevIndexTable_fixed_256():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a721d01114016584629f03c0af37dd21e',1,'armBitRevIndexTable_fixed_256():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f32',['armBitRevIndexTable_fixed_32',['../arm__common__tables_8c.html#a59710415522cc38defa30402021f1f6b',1,'armBitRevIndexTable_fixed_32():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a59710415522cc38defa30402021f1f6b',1,'armBitRevIndexTable_fixed_32():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f4096',['armBitRevIndexTable_fixed_4096',['../arm__common__tables_8c.html#a2db644df1e878209441166cbb8d0db4f',1,'armBitRevIndexTable_fixed_4096():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a2db644df1e878209441166cbb8d0db4f',1,'armBitRevIndexTable_fixed_4096():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f4096_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED_4096_TABLE_LENGTH',['../arm__common__tables_8h.html#acbf48883fbb31d3dc71d232aa8e8f91f',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f512',['armBitRevIndexTable_fixed_512',['../arm__common__tables_8c.html#a03d1de7bfc5f318bc4fcfddd920bcb5a',1,'armBitRevIndexTable_fixed_512():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a03d1de7bfc5f318bc4fcfddd920bcb5a',1,'armBitRevIndexTable_fixed_512():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f64',['armBitRevIndexTable_fixed_64',['../arm__common__tables_8c.html#af9e1bbd7d535806a170786b069863b47',1,'armBitRevIndexTable_fixed_64():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#af9e1bbd7d535806a170786b069863b47',1,'armBitRevIndexTable_fixed_64():&#160;arm_common_tables.c']]],
  ['armbitrevindextable_5ffixed_5f_5f128_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED__128_TABLE_LENGTH',['../arm__common__tables_8h.html#aa3b70f6b0a87ecd706fc51bb3551977b',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f_5f256_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED__256_TABLE_LENGTH',['../arm__common__tables_8h.html#ac0711126d0e162366ec7d0ebcb2a4420',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f_5f512_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED__512_TABLE_LENGTH',['../arm__common__tables_8h.html#a5486cba85dce51ffbfe6c0475882cc82',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f_5f_5f16_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED___16_TABLE_LENGTH',['../arm__common__tables_8h.html#a1dfdb9f7a5ad88ba7105c6cbc7e2c76e',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f_5f_5f32_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED___32_TABLE_LENGTH',['../arm__common__tables_8h.html#aaa9ecdc043a73fa12c941cbe6613f9fa',1,'arm_common_tables.h']]],
  ['armbitrevindextable_5ffixed_5f_5f_5f64_5ftable_5flength',['ARMBITREVINDEXTABLE_FIXED___64_TABLE_LENGTH',['../arm__common__tables_8h.html#ae53dc7c3198f9cfb5393e3a2644a12ac',1,'arm_common_tables.h']]],
  ['armbitrevtable',['armBitRevTable',['../group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b',1,'armBitRevTable():&#160;arm_common_tables.c'],['../group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b',1,'armBitRevTable():&#160;arm_common_tables.c']]],
  ['armreciptableq15',['armRecipTableQ15',['../arm__common__tables_8c.html#a66ca8ac5f3a63d9962f501ae60aa32be',1,'armRecipTableQ15():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#a56d3642e4ee33e3ada57ff11ecda1498',1,'armRecipTableQ15():&#160;arm_common_tables.c']]],
  ['armreciptableq31',['armRecipTableQ31',['../arm__common__tables_8c.html#aae6056f6c4e8f7e494445196bf864479',1,'armRecipTableQ31():&#160;arm_common_tables.c'],['../arm__common__tables_8h.html#aae6056f6c4e8f7e494445196bf864479',1,'armRecipTableQ31():&#160;arm_common_tables.c']]],
  ['at_5ff32',['AT_f32',['../arm__matrix__example__f32_8c.html#a46dc2aa6dfc692af7b4a1379d7329ccd',1,'arm_matrix_example_f32.c']]],
  ['atma_5ff32',['ATMA_f32',['../arm__matrix__example__f32_8c.html#a867497c6bf86014513bf2ad3551aa896',1,'arm_matrix_example_f32.c']]],
  ['atmai_5ff32',['ATMAI_f32',['../arm__matrix__example__f32_8c.html#a44425c149c52b326a3b7a77676686f00',1,'arm_matrix_example_f32.c']]],
  ['axb',['AxB',['../_a_r_m_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098',1,'AxB():&#160;arm_convolution_example_f32.c'],['../_g_c_c_2arm__convolution__example__f32_8c.html#a13521f3164dc55679f43b7cb2e41e098',1,'AxB():&#160;arm_convolution_example_f32.c']]],
  ['math_5fhelper_2ec',['math_helper.c',['../arm__fir__example_2_a_r_m_2math__helper_8c.html',1,'']]],
  ['math_5fhelper_2ec',['math_helper.c',['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html',1,'']]],
  ['math_5fhelper_2ec',['math_helper.c',['../arm__convolution__example_2_a_r_m_2math__helper_8c.html',1,'']]],
  ['math_5fhelper_2ec',['math_helper.c',['../arm__matrix__example_2_a_r_m_2math__helper_8c.html',1,'']]],
  ['math_5fhelper_2ec',['math_helper.c',['../arm__linear__interp__example_2_a_r_m_2math__helper_8c.html',1,'']]],
  ['math_5fhelper_2ec',['math_helper.c',['../arm__convolution__example_2_g_c_c_2math__helper_8c.html',1,'']]],
  ['math_5fhelper_2ec',['math_helper.c',['../arm__signal__converge__example_2_a_r_m_2math__helper_8c.html',1,'']]],
  ['math_5fhelper_2eh',['math_helper.h',['../arm__convolution__example_2_a_r_m_2math__helper_8h.html',1,'']]],
  ['math_5fhelper_2eh',['math_helper.h',['../arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html',1,'']]],
  ['math_5fhelper_2eh',['math_helper.h',['../arm__signal__converge__example_2_a_r_m_2math__helper_8h.html',1,'']]],
  ['math_5fhelper_2eh',['math_helper.h',['../arm__linear__interp__example_2_a_r_m_2math__helper_8h.html',1,'']]],
  ['math_5fhelper_2eh',['math_helper.h',['../arm__fir__example_2_a_r_m_2math__helper_8h.html',1,'']]],
  ['math_5fhelper_2eh',['math_helper.h',['../arm__convolution__example_2_g_c_c_2math__helper_8h.html',1,'']]],
  ['math_5fhelper_2eh',['math_helper.h',['../arm__matrix__example_2_a_r_m_2math__helper_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__linear__interp__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__class__marks__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__signal__converge__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__fft__bin__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__convolution__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__fir__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__sin__cos__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__dotproduct__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__variance__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['rte_5fcomponents_2eh',['RTE_Components.h',['../arm__matrix__example_2_a_r_m_2_r_t_e_2_r_t_e___components_8h.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm0_2ec',['system_ARMCM0.c',['../arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm3_2ec',['system_ARMCM3.c',['../arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m3_2system___a_r_m_c_m3_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__fft__bin__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__convolution__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__class__marks__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__dotproduct__example_2_g_c_c_2_startup_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm4_2ec',['system_ARMCM4.c',['../arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m4___f_p_2system___a_r_m_c_m4_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__variance__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__graphic__equalizer__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__fft__bin__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__convolution__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__signal__converge__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__sin__cos__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__dotproduct__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__matrix__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__fir__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]],
  ['system_5farmcm7_2ec',['system_ARMCM7.c',['../arm__linear__interp__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m7___s_p_2system___a_r_m_c_m7_8c.html',1,'']]]
];
