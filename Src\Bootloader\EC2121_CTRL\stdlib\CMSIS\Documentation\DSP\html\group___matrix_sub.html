<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Matrix Subtraction</title>
<title>CMSIS-DSP: Matrix Subtraction</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___matrix_sub.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Matrix Subtraction</div>  </div>
<div class="ingroups"><a class="el" href="group__group_matrix.html">Matrix Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gac8b72fb70246ccfee3b372002345732c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___matrix_sub.html#gac8b72fb70246ccfee3b372002345732c">arm_mat_sub_f32</a> (const <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *pSrcA, const <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *pSrcB, <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *pDst)</td></tr>
<tr class="memdesc:gac8b72fb70246ccfee3b372002345732c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Floating-point matrix subtraction.  <a href="#gac8b72fb70246ccfee3b372002345732c"></a><br/></td></tr>
<tr class="separator:gac8b72fb70246ccfee3b372002345732c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf647776a425b7f9dd0aca3e11d81f02f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___matrix_sub.html#gaf647776a425b7f9dd0aca3e11d81f02f">arm_mat_sub_q15</a> (const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pSrcA, const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pSrcB, <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *pDst)</td></tr>
<tr class="memdesc:gaf647776a425b7f9dd0aca3e11d81f02f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q15 matrix subtraction.  <a href="#gaf647776a425b7f9dd0aca3e11d81f02f"></a><br/></td></tr>
<tr class="separator:gaf647776a425b7f9dd0aca3e11d81f02f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39f42e0e3b7f115fbb909d6ff4e1329d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___matrix_sub.html#ga39f42e0e3b7f115fbb909d6ff4e1329d">arm_mat_sub_q31</a> (const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pSrcA, const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pSrcB, <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *pDst)</td></tr>
<tr class="memdesc:ga39f42e0e3b7f115fbb909d6ff4e1329d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q31 matrix subtraction.  <a href="#ga39f42e0e3b7f115fbb909d6ff4e1329d"></a><br/></td></tr>
<tr class="separator:ga39f42e0e3b7f115fbb909d6ff4e1329d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Subtract two matrices. </p>
<div class="image">
<img src="MatrixSubtraction.gif" alt="MatrixSubtraction.gif"/>
<div class="caption">
Subraction of two 3 x 3 matrices</div></div>
<p>The functions check to make sure that <code>pSrcA</code>, <code>pSrcB</code>, and <code>pDst</code> have the same number of rows and columns. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gac8b72fb70246ccfee3b372002345732c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_mat_sub_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__matrix__instance__f32.html">arm_matrix_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input matrix structure </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to output matrix structure </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns either <code>ARM_MATH_SIZE_MISMATCH</code> or <code>ARM_MATH_SUCCESS</code> based on the outcome of size checking. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">ARM_MATH_SIZE_MISMATCH</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__matrix__instance__f32.html#acdd1fb73734df68b89565c54f1dd8ae2">arm_matrix_instance_f32::numCols</a>, <a class="el" href="structarm__matrix__instance__f32.html#a23f4e34d70a82c9cad7612add5640b7b">arm_matrix_instance_f32::numRows</a>, <a class="el" href="structarm__matrix__instance__f32.html#af3917c032600a9dfd5ed4a96f074910a">arm_matrix_instance_f32::pData</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="gaf647776a425b7f9dd0aca3e11d81f02f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_mat_sub_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__matrix__instance__q15.html">arm_matrix_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input matrix structure </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to output matrix structure </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns either <code>ARM_MATH_SIZE_MISMATCH</code> or <code>ARM_MATH_SUCCESS</code> based on the outcome of size checking.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function uses saturating arithmetic. Results outside of the allowable Q15 range [0x8000 0x7FFF] will be saturated. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">ARM_MATH_SIZE_MISMATCH</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__matrix__instance__q15.html#acbbce67ba058d8e1c867c71d57288c97">arm_matrix_instance_q15::numCols</a>, <a class="el" href="structarm__matrix__instance__q15.html#a9bac6ed54be287c4d4f01a1a28be65f5">arm_matrix_instance_q15::numRows</a>, <a class="el" href="structarm__matrix__instance__q15.html#a6da33a5553e634787d0f515cf8d724af">arm_matrix_instance_q15::pData</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
<a class="anchor" id="ga39f42e0e3b7f115fbb909d6ff4e1329d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_mat_sub_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pSrcA</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pSrcB</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__matrix__instance__q31.html">arm_matrix_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcA</td><td>points to the first input matrix structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrcB</td><td>points to the second input matrix structure </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to output matrix structure </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns either <code>ARM_MATH_SIZE_MISMATCH</code> or <code>ARM_MATH_SUCCESS</code> based on the outcome of size checking.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function uses saturating arithmetic. Results outside of the allowable Q31 range [0x80000000 0x7FFFFFFF] will be saturated. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a7071b92f1f6bc3c5c312a237ea91105b">ARM_MATH_SIZE_MISMATCH</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__matrix__instance__q31.html#abd161da7614eda927157f18b698074b1">arm_matrix_instance_q31::numCols</a>, <a class="el" href="structarm__matrix__instance__q31.html#a63bacac158a821c8cfc06088d251598c">arm_matrix_instance_q31::numRows</a>, <a class="el" href="structarm__matrix__instance__q31.html#a09a64267c0579fef086efc9059741e56">arm_matrix_instance_q31::pData</a>, and <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
