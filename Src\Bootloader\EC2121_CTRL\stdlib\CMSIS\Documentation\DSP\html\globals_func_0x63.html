<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_func.html#index_a"><span>a</span></a></li>
      <li><a href="globals_func_0x62.html#index_b"><span>b</span></a></li>
      <li class="current"><a href="globals_func_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_func_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_func_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_func_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_func_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_func_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_func_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_func_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_func_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_func_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_func_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_func_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_func_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_func_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_func_0x76.html#index_v"><span>v</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_func_0x63.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_c"></a>- c -</h3><ul>
<li>arm_calc_2pow()
: <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#ace1e1f7b72573d1934782ec999a04f99">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a7c94faac575a175e824d5f9879c97c68">arm_signal_converge_example/ARM/math_helper.h</a>
</li>
<li>arm_calc_guard_bits()
: <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a60ff6e0b31a5e9105c7280797e457742">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a60ff6e0b31a5e9105c7280797e457742">arm_convolution_example/ARM/math_helper.c</a>
</li>
<li>arm_cfft_f32()
: <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32.c</a>
, <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_math.h</a>
</li>
<li>arm_cfft_q15()
: <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_math.h</a>
, <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15.c</a>
</li>
<li>arm_cfft_q31()
: <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31.c</a>
, <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_math.h</a>
</li>
<li>arm_cfft_radix2_f32()
: <a class="el" href="group___complex_f_f_t.html#ga9fadd650b802f612ae558ddaab789a6d">arm_cfft_radix2_f32.c</a>
, <a class="el" href="group___complex_f_f_t.html#ga9fadd650b802f612ae558ddaab789a6d">arm_math.h</a>
</li>
<li>arm_cfft_radix2_init_f32()
: <a class="el" href="group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7">arm_cfft_radix2_init_f32.c</a>
, <a class="el" href="group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7">arm_math.h</a>
</li>
<li>arm_cfft_radix2_init_q15()
: <a class="el" href="group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858">arm_cfft_radix2_init_q15.c</a>
, <a class="el" href="group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858">arm_math.h</a>
</li>
<li>arm_cfft_radix2_init_q31()
: <a class="el" href="group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638">arm_cfft_radix2_init_q31.c</a>
, <a class="el" href="group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638">arm_math.h</a>
</li>
<li>arm_cfft_radix2_q15()
: <a class="el" href="group___complex_f_f_t.html#ga55b424341dc3efd3fa0bcaaff4bdbf40">arm_cfft_radix2_q15.c</a>
, <a class="el" href="group___complex_f_f_t.html#ga55b424341dc3efd3fa0bcaaff4bdbf40">arm_math.h</a>
</li>
<li>arm_cfft_radix2_q31()
: <a class="el" href="group___complex_f_f_t.html#ga6321f703ec87a274aedaab33d3e766b4">arm_cfft_radix2_q31.c</a>
, <a class="el" href="group___complex_f_f_t.html#ga6321f703ec87a274aedaab33d3e766b4">arm_math.h</a>
</li>
<li>arm_cfft_radix4_f32()
: <a class="el" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26">arm_cfft_radix4_f32.c</a>
, <a class="el" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26">arm_math.h</a>
</li>
<li>arm_cfft_radix4_init_f32()
: <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32.c</a>
, <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_math.h</a>
</li>
<li>arm_cfft_radix4_init_q15()
: <a class="el" href="group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef">arm_cfft_radix4_init_q15.c</a>
, <a class="el" href="group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef">arm_math.h</a>
</li>
<li>arm_cfft_radix4_init_q31()
: <a class="el" href="group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c">arm_cfft_radix4_init_q31.c</a>
, <a class="el" href="group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c">arm_math.h</a>
</li>
<li>arm_cfft_radix4_q15()
: <a class="el" href="group___complex_f_f_t.html#ga8d66cdac41b8bf6cefdb895456eee84a">arm_cfft_radix4_q15.c</a>
, <a class="el" href="group___complex_f_f_t.html#ga8d66cdac41b8bf6cefdb895456eee84a">arm_math.h</a>
</li>
<li>arm_cfft_radix4_q31()
: <a class="el" href="group___complex_f_f_t.html#gafde3ee1f58cf393b45a9073174fff548">arm_cfft_radix4_q31.c</a>
, <a class="el" href="group___complex_f_f_t.html#gafde3ee1f58cf393b45a9073174fff548">arm_math.h</a>
</li>
<li>arm_cfft_radix4by2_inverse_q15()
: <a class="el" href="arm__cfft__q15_8c.html#abe669acc8db57d1fb9b1e2bba30f2224">arm_cfft_q15.c</a>
</li>
<li>arm_cfft_radix4by2_inverse_q31()
: <a class="el" href="arm__cfft__q31_8c.html#a3f3ae10bc2057cc1360abfa25f224c8c">arm_cfft_q31.c</a>
</li>
<li>arm_cfft_radix4by2_q15()
: <a class="el" href="arm__cfft__q15_8c.html#af1d4a751153857c173511e0c77ab4fa9">arm_cfft_q15.c</a>
</li>
<li>arm_cfft_radix4by2_q31()
: <a class="el" href="arm__cfft__q31_8c.html#af6df8bf714c30d44e6b871ea87d22b30">arm_cfft_q31.c</a>
</li>
<li>arm_cfft_radix8by2_f32()
: <a class="el" href="arm__cfft__f32_8c.html#ae99e2b173033e9910058869bdf0619d9">arm_cfft_f32.c</a>
</li>
<li>arm_cfft_radix8by4_f32()
: <a class="el" href="arm__cfft__f32_8c.html#a4bb346f59bca06cebe0defc8e15b69a6">arm_cfft_f32.c</a>
</li>
<li>arm_circularRead_f32()
: <a class="el" href="arm__math_8h.html#ae469fac5e1df35f8bcf1b3d7c3136484">arm_math.h</a>
</li>
<li>arm_circularRead_q15()
: <a class="el" href="arm__math_8h.html#ad5fb134f83f2c802261f172e3dceb131">arm_math.h</a>
</li>
<li>arm_circularRead_q7()
: <a class="el" href="arm__math_8h.html#a30aa80ea20abe71f3afa99f2f0391ed5">arm_math.h</a>
</li>
<li>arm_circularWrite_f32()
: <a class="el" href="arm__math_8h.html#a6ff56c0896ce00712ba8f2fcf72cacd3">arm_math.h</a>
</li>
<li>arm_circularWrite_q15()
: <a class="el" href="arm__math_8h.html#a3ba2d215477e692def7fda46dda883ed">arm_math.h</a>
</li>
<li>arm_circularWrite_q7()
: <a class="el" href="arm__math_8h.html#addba85b1f7fbd472fd00ddd9ce43aea8">arm_math.h</a>
</li>
<li>arm_clarke_f32()
: <a class="el" href="group__clarke.html#ga2b4ebec76215e1277c970c269ffdbd76">arm_math.h</a>
</li>
<li>arm_clarke_q31()
: <a class="el" href="group__clarke.html#ga7fd106ca8d346a2a472842e0656014c1">arm_math.h</a>
</li>
<li>arm_clip_f32()
: <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#ab9768d92bb94894d8294047bdf76a16a">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#ab9768d92bb94894d8294047bdf76a16a">arm_signal_converge_example/ARM/math_helper.h</a>
</li>
<li>arm_cmplx_conj_f32()
: <a class="el" href="group__cmplx__conj.html#ga3a102aead6460ad9fcb0626f6b226ffb">arm_cmplx_conj_f32.c</a>
, <a class="el" href="group__cmplx__conj.html#ga3a102aead6460ad9fcb0626f6b226ffb">arm_math.h</a>
</li>
<li>arm_cmplx_conj_q15()
: <a class="el" href="group__cmplx__conj.html#gaf47689ae07962acaecb8ddde556df4a4">arm_cmplx_conj_q15.c</a>
, <a class="el" href="group__cmplx__conj.html#gaf47689ae07962acaecb8ddde556df4a4">arm_math.h</a>
</li>
<li>arm_cmplx_conj_q31()
: <a class="el" href="group__cmplx__conj.html#gafecc94879a383c5208ec3ef99485e4b5">arm_cmplx_conj_q31.c</a>
, <a class="el" href="group__cmplx__conj.html#gafecc94879a383c5208ec3ef99485e4b5">arm_math.h</a>
</li>
<li>arm_cmplx_dot_prod_f32()
: <a class="el" href="group__cmplx__dot__prod.html#gadcfaf567a25eb641da4043eafb9bb076">arm_cmplx_dot_prod_f32.c</a>
, <a class="el" href="group__cmplx__dot__prod.html#gadcfaf567a25eb641da4043eafb9bb076">arm_math.h</a>
</li>
<li>arm_cmplx_dot_prod_q15()
: <a class="el" href="group__cmplx__dot__prod.html#ga2b08b5e8001d2c15204639d00893fc70">arm_cmplx_dot_prod_q15.c</a>
, <a class="el" href="group__cmplx__dot__prod.html#ga2b08b5e8001d2c15204639d00893fc70">arm_math.h</a>
</li>
<li>arm_cmplx_dot_prod_q31()
: <a class="el" href="group__cmplx__dot__prod.html#ga5b731a59db062a9ad84562ef68a6c8af">arm_cmplx_dot_prod_q31.c</a>
, <a class="el" href="group__cmplx__dot__prod.html#ga5b731a59db062a9ad84562ef68a6c8af">arm_math.h</a>
</li>
<li>arm_cmplx_mag_f32()
: <a class="el" href="group__cmplx__mag.html#gae45024c497392cde2ae358a76d435213">arm_cmplx_mag_f32.c</a>
, <a class="el" href="group__cmplx__mag.html#gae45024c497392cde2ae358a76d435213">arm_math.h</a>
</li>
<li>arm_cmplx_mag_q15()
: <a class="el" href="group__cmplx__mag.html#ga0a4a8f77a6a51d9b3f3b9d729f85b7a4">arm_cmplx_mag_q15.c</a>
, <a class="el" href="group__cmplx__mag.html#ga0a4a8f77a6a51d9b3f3b9d729f85b7a4">arm_math.h</a>
</li>
<li>arm_cmplx_mag_q31()
: <a class="el" href="group__cmplx__mag.html#ga14f82f9230e9d96d5b9774e2fefcb7be">arm_cmplx_mag_q31.c</a>
, <a class="el" href="group__cmplx__mag.html#ga14f82f9230e9d96d5b9774e2fefcb7be">arm_math.h</a>
</li>
<li>arm_cmplx_mag_squared_f32()
: <a class="el" href="group__cmplx__mag__squared.html#gaa7faccc0d96b061d8b7d0d7d82045074">arm_cmplx_mag_squared_f32.c</a>
, <a class="el" href="group__cmplx__mag__squared.html#gaa7faccc0d96b061d8b7d0d7d82045074">arm_math.h</a>
</li>
<li>arm_cmplx_mag_squared_q15()
: <a class="el" href="group__cmplx__mag__squared.html#ga45537f576102d960d467eb722b8431f2">arm_cmplx_mag_squared_q15.c</a>
, <a class="el" href="group__cmplx__mag__squared.html#ga45537f576102d960d467eb722b8431f2">arm_math.h</a>
</li>
<li>arm_cmplx_mag_squared_q31()
: <a class="el" href="group__cmplx__mag__squared.html#ga384b0538101e8c03fa4fa14271e63b04">arm_cmplx_mag_squared_q31.c</a>
, <a class="el" href="group__cmplx__mag__squared.html#ga384b0538101e8c03fa4fa14271e63b04">arm_math.h</a>
</li>
<li>arm_cmplx_mult_cmplx_f32()
: <a class="el" href="group___cmplx_by_cmplx_mult.html#ga14b47080054a1ba1250a86805be1ff6b">arm_cmplx_mult_cmplx_f32.c</a>
, <a class="el" href="group___cmplx_by_cmplx_mult.html#ga14b47080054a1ba1250a86805be1ff6b">arm_math.h</a>
</li>
<li>arm_cmplx_mult_cmplx_q15()
: <a class="el" href="group___cmplx_by_cmplx_mult.html#ga67e96abfc9c3e30efb70a2ec9d0fe7e8">arm_cmplx_mult_cmplx_q15.c</a>
, <a class="el" href="group___cmplx_by_cmplx_mult.html#ga67e96abfc9c3e30efb70a2ec9d0fe7e8">arm_math.h</a>
</li>
<li>arm_cmplx_mult_cmplx_q31()
: <a class="el" href="group___cmplx_by_cmplx_mult.html#ga1829e50993a90742de225a0ce4213838">arm_cmplx_mult_cmplx_q31.c</a>
, <a class="el" href="group___cmplx_by_cmplx_mult.html#ga1829e50993a90742de225a0ce4213838">arm_math.h</a>
</li>
<li>arm_cmplx_mult_real_f32()
: <a class="el" href="group___cmplx_by_real_mult.html#ga9c18616f56cb4d3c0889ce0b339221ca">arm_cmplx_mult_real_f32.c</a>
, <a class="el" href="group___cmplx_by_real_mult.html#ga9c18616f56cb4d3c0889ce0b339221ca">arm_math.h</a>
</li>
<li>arm_cmplx_mult_real_q15()
: <a class="el" href="group___cmplx_by_real_mult.html#ga3bd8889dcb45980e1d3e53344df54e85">arm_cmplx_mult_real_q15.c</a>
, <a class="el" href="group___cmplx_by_real_mult.html#ga3bd8889dcb45980e1d3e53344df54e85">arm_math.h</a>
</li>
<li>arm_cmplx_mult_real_q31()
: <a class="el" href="group___cmplx_by_real_mult.html#ga715e4bb8e945b8ca51ec5237611697ce">arm_cmplx_mult_real_q31.c</a>
, <a class="el" href="group___cmplx_by_real_mult.html#ga715e4bb8e945b8ca51ec5237611697ce">arm_math.h</a>
</li>
<li>arm_compare_fixed_q15()
: <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a64d5207c035db13cddde479317dd131e">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a64d5207c035db13cddde479317dd131e">arm_convolution_example/ARM/math_helper.c</a>
</li>
<li>arm_compare_fixed_q31()
: <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a32f9f3d19e53161382c5bd39e3df50fb">arm_convolution_example/GCC/math_helper.c</a>
</li>
<li>arm_conv_f32()
: <a class="el" href="group___conv.html#ga3f860dc98c6fc4cafc421e4a2aed3c89">arm_conv_f32.c</a>
, <a class="el" href="group___conv.html#ga3f860dc98c6fc4cafc421e4a2aed3c89">arm_math.h</a>
</li>
<li>arm_conv_fast_opt_q15()
: <a class="el" href="group___conv.html#gaf16f490d245391ec18a42adc73d6d749">arm_conv_fast_opt_q15.c</a>
, <a class="el" href="group___conv.html#gaf16f490d245391ec18a42adc73d6d749">arm_math.h</a>
</li>
<li>arm_conv_fast_q15()
: <a class="el" href="group___conv.html#gad75ca978ce906e04abdf86a8d76306d4">arm_conv_fast_q15.c</a>
, <a class="el" href="group___conv.html#gad75ca978ce906e04abdf86a8d76306d4">arm_math.h</a>
</li>
<li>arm_conv_fast_q31()
: <a class="el" href="group___conv.html#ga51112dcdf9b3624eb05182cdc4da9ec0">arm_math.h</a>
, <a class="el" href="group___conv.html#ga51112dcdf9b3624eb05182cdc4da9ec0">arm_conv_fast_q31.c</a>
</li>
<li>arm_conv_opt_q15()
: <a class="el" href="group___conv.html#gac77dbcaef5c754cac27eab96c4753a3c">arm_math.h</a>
, <a class="el" href="group___conv.html#gac77dbcaef5c754cac27eab96c4753a3c">arm_conv_opt_q15.c</a>
</li>
<li>arm_conv_opt_q7()
: <a class="el" href="group___conv.html#ga4c7cf073e89d6d57cc4e711f078c3f68">arm_math.h</a>
, <a class="el" href="group___conv.html#ga4c7cf073e89d6d57cc4e711f078c3f68">arm_conv_opt_q7.c</a>
</li>
<li>arm_conv_partial_f32()
: <a class="el" href="group___partial_conv.html#ga16d10f32072cd79fc5fb6e785df45f5e">arm_math.h</a>
, <a class="el" href="group___partial_conv.html#ga16d10f32072cd79fc5fb6e785df45f5e">arm_conv_partial_f32.c</a>
</li>
<li>arm_conv_partial_fast_opt_q15()
: <a class="el" href="group___partial_conv.html#ga3de9c4ddcc7886de25b70d875099a8d9">arm_math.h</a>
, <a class="el" href="group___partial_conv.html#ga3de9c4ddcc7886de25b70d875099a8d9">arm_conv_partial_fast_opt_q15.c</a>
</li>
<li>arm_conv_partial_fast_q15()
: <a class="el" href="group___partial_conv.html#ga1e4d43385cb62262a78c6752fe1fafb2">arm_math.h</a>
, <a class="el" href="group___partial_conv.html#ga1e4d43385cb62262a78c6752fe1fafb2">arm_conv_partial_fast_q15.c</a>
</li>
<li>arm_conv_partial_fast_q31()
: <a class="el" href="group___partial_conv.html#ga10c5294cda8c4985386f4e3944be7650">arm_math.h</a>
, <a class="el" href="group___partial_conv.html#ga10c5294cda8c4985386f4e3944be7650">arm_conv_partial_fast_q31.c</a>
</li>
<li>arm_conv_partial_opt_q15()
: <a class="el" href="group___partial_conv.html#ga834b23b4ade8682beeb55778399101f8">arm_math.h</a>
, <a class="el" href="group___partial_conv.html#ga834b23b4ade8682beeb55778399101f8">arm_conv_partial_opt_q15.c</a>
</li>
<li>arm_conv_partial_opt_q7()
: <a class="el" href="group___partial_conv.html#ga3707e16af1435b215840006a7ab0c98f">arm_conv_partial_opt_q7.c</a>
, <a class="el" href="group___partial_conv.html#ga3707e16af1435b215840006a7ab0c98f">arm_math.h</a>
</li>
<li>arm_conv_partial_q15()
: <a class="el" href="group___partial_conv.html#ga209a2a913a0c5e5679c5988da8f46b03">arm_math.h</a>
, <a class="el" href="group___partial_conv.html#ga209a2a913a0c5e5679c5988da8f46b03">arm_conv_partial_q15.c</a>
</li>
<li>arm_conv_partial_q31()
: <a class="el" href="group___partial_conv.html#ga78e73a5f02d103168a09821fb461e77a">arm_conv_partial_q31.c</a>
, <a class="el" href="group___partial_conv.html#ga78e73a5f02d103168a09821fb461e77a">arm_math.h</a>
</li>
<li>arm_conv_partial_q7()
: <a class="el" href="group___partial_conv.html#ga8567259fe18396dd972242c41741ebf4">arm_math.h</a>
, <a class="el" href="group___partial_conv.html#ga8567259fe18396dd972242c41741ebf4">arm_conv_partial_q7.c</a>
</li>
<li>arm_conv_q15()
: <a class="el" href="group___conv.html#gaccd6a89b0ff7a94df64610598e6e6893">arm_math.h</a>
, <a class="el" href="group___conv.html#gaccd6a89b0ff7a94df64610598e6e6893">arm_conv_q15.c</a>
</li>
<li>arm_conv_q31()
: <a class="el" href="group___conv.html#ga946b58da734f1e4e78c91fcaab4b12b6">arm_math.h</a>
, <a class="el" href="group___conv.html#ga946b58da734f1e4e78c91fcaab4b12b6">arm_conv_q31.c</a>
</li>
<li>arm_conv_q7()
: <a class="el" href="group___conv.html#gae2070cb792a167e78dbad8d06b97cdab">arm_math.h</a>
, <a class="el" href="group___conv.html#gae2070cb792a167e78dbad8d06b97cdab">arm_conv_q7.c</a>
</li>
<li>arm_copy_f32()
: <a class="el" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3">arm_math.h</a>
, <a class="el" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3">arm_copy_f32.c</a>
</li>
<li>arm_copy_q15()
: <a class="el" href="group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0">arm_math.h</a>
, <a class="el" href="group__copy.html#ga872ca4cfc18c680b8991ccd569a5fda0">arm_copy_q15.c</a>
</li>
<li>arm_copy_q31()
: <a class="el" href="group__copy.html#gaddf70be7e3f87e535c324862b501f3f9">arm_math.h</a>
, <a class="el" href="group__copy.html#gaddf70be7e3f87e535c324862b501f3f9">arm_copy_q31.c</a>
</li>
<li>arm_copy_q7()
: <a class="el" href="group__copy.html#ga467579beda492aa92797529d794c88fb">arm_copy_q7.c</a>
, <a class="el" href="group__copy.html#ga467579beda492aa92797529d794c88fb">arm_math.h</a>
</li>
<li>arm_correlate_f32()
: <a class="el" href="group___corr.html#ga22021e4222773f01e9960358a531cfb8">arm_math.h</a>
, <a class="el" href="group___corr.html#ga22021e4222773f01e9960358a531cfb8">arm_correlate_f32.c</a>
</li>
<li>arm_correlate_fast_opt_q15()
: <a class="el" href="group___corr.html#ga40a0236b17220e8e22a22b5bc1c53c6b">arm_math.h</a>
, <a class="el" href="group___corr.html#ga40a0236b17220e8e22a22b5bc1c53c6b">arm_correlate_fast_opt_q15.c</a>
</li>
<li>arm_correlate_fast_q15()
: <a class="el" href="group___corr.html#gac8de3da44f58e86c2c86156276ca154f">arm_math.h</a>
, <a class="el" href="group___corr.html#gac8de3da44f58e86c2c86156276ca154f">arm_correlate_fast_q15.c</a>
</li>
<li>arm_correlate_fast_q31()
: <a class="el" href="group___corr.html#gabecd3d7b077dbbef43f93e9e037815ed">arm_correlate_fast_q31.c</a>
, <a class="el" href="group___corr.html#gabecd3d7b077dbbef43f93e9e037815ed">arm_math.h</a>
</li>
<li>arm_correlate_opt_q15()
: <a class="el" href="group___corr.html#gad71c0ec70ec69edbc48563d9a5f68451">arm_correlate_opt_q15.c</a>
, <a class="el" href="group___corr.html#gad71c0ec70ec69edbc48563d9a5f68451">arm_math.h</a>
</li>
<li>arm_correlate_opt_q7()
: <a class="el" href="group___corr.html#ga746e8857cafe33ec5d6780729c18c311">arm_correlate_opt_q7.c</a>
, <a class="el" href="group___corr.html#ga746e8857cafe33ec5d6780729c18c311">arm_math.h</a>
</li>
<li>arm_correlate_q15()
: <a class="el" href="group___corr.html#ga5ec96b8e420d68b0e626df0812274d46">arm_math.h</a>
, <a class="el" href="group___corr.html#ga5ec96b8e420d68b0e626df0812274d46">arm_correlate_q15.c</a>
</li>
<li>arm_correlate_q31()
: <a class="el" href="group___corr.html#ga1367dc6c80476406c951e68d7fac4e8c">arm_correlate_q31.c</a>
, <a class="el" href="group___corr.html#ga1367dc6c80476406c951e68d7fac4e8c">arm_math.h</a>
</li>
<li>arm_correlate_q7()
: <a class="el" href="group___corr.html#ga284ddcc49e4ac532d52a70d0383c5992">arm_math.h</a>
, <a class="el" href="group___corr.html#ga284ddcc49e4ac532d52a70d0383c5992">arm_correlate_q7.c</a>
</li>
<li>arm_cos_f32()
: <a class="el" href="group__cos.html#gace15287f9c64b9b4084d1c797d4c49d8">arm_math.h</a>
, <a class="el" href="group__cos.html#gace15287f9c64b9b4084d1c797d4c49d8">arm_cos_f32.c</a>
</li>
<li>arm_cos_q15()
: <a class="el" href="group__cos.html#gadfd60c24def501638c0d5db20f4c869b">arm_math.h</a>
, <a class="el" href="group__cos.html#gadfd60c24def501638c0d5db20f4c869b">arm_cos_q15.c</a>
</li>
<li>arm_cos_q31()
: <a class="el" href="group__cos.html#gad80f121949ef885a77d83ab36e002567">arm_cos_q31.c</a>
, <a class="el" href="group__cos.html#gad80f121949ef885a77d83ab36e002567">arm_math.h</a>
</li>
<li>clip_q31_to_q15()
: <a class="el" href="arm__math_8h.html#a4af3ca330e14587289518e6565fd04bd">arm_math.h</a>
</li>
<li>clip_q31_to_q7()
: <a class="el" href="arm__math_8h.html#aa9918ce19228b0d4f072fb84776eabc1">arm_math.h</a>
</li>
<li>clip_q63_to_q15()
: <a class="el" href="arm__math_8h.html#aa6f1e5d0d276f42217e75f071ca84a2e">arm_math.h</a>
</li>
<li>clip_q63_to_q31()
: <a class="el" href="arm__math_8h.html#ad7373e53d3c2e1adfeafc8c2e9720b5c">arm_math.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
