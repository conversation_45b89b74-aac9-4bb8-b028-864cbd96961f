<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_cfft_q31.c File Reference</title>
<title>CMSIS-DSP: arm_cfft_q31.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm__cfft__q31_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">arm_cfft_q31.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ac12f1e7f159d5741358cdc36830a0395"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__cfft__q31_8c.html#ac12f1e7f159d5741358cdc36830a0395">arm_radix4_butterfly_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, uint32_t fftLen, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pCoef, uint32_t twidCoefModifier)</td></tr>
<tr class="memdesc:ac12f1e7f159d5741358cdc36830a0395"><td class="mdescLeft">&#160;</td><td class="mdescRight">Core function for the Q31 CFFT butterfly process.  <a href="#ac12f1e7f159d5741358cdc36830a0395"></a><br/></td></tr>
<tr class="separator:ac12f1e7f159d5741358cdc36830a0395"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9c7c553114c1201a3a987a11b8a6d01"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__cfft__q31_8c.html#ac9c7c553114c1201a3a987a11b8a6d01">arm_radix4_butterfly_inverse_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, uint32_t fftLen, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pCoef, uint32_t twidCoefModifier)</td></tr>
<tr class="memdesc:ac9c7c553114c1201a3a987a11b8a6d01"><td class="mdescLeft">&#160;</td><td class="mdescRight">Core function for the Q31 CIFFT butterfly process.  <a href="#ac9c7c553114c1201a3a987a11b8a6d01"></a><br/></td></tr>
<tr class="separator:ac9c7c553114c1201a3a987a11b8a6d01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8e7ebe1cb131a5b0f55d0464640591f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__cfft__q31_8c.html#ac8e7ebe1cb131a5b0f55d0464640591f">arm_bitreversal_32</a> (uint32_t *pSrc, const uint16_t bitRevLen, const uint16_t *pBitRevTable)</td></tr>
<tr class="separator:ac8e7ebe1cb131a5b0f55d0464640591f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6df8bf714c30d44e6b871ea87d22b30"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__cfft__q31_8c.html#af6df8bf714c30d44e6b871ea87d22b30">arm_cfft_radix4by2_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, uint32_t fftLen, const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pCoef)</td></tr>
<tr class="separator:af6df8bf714c30d44e6b871ea87d22b30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f3ae10bc2057cc1360abfa25f224c8c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="arm__cfft__q31_8c.html#a3f3ae10bc2057cc1360abfa25f224c8c">arm_cfft_radix4by2_inverse_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, uint32_t fftLen, const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pCoef)</td></tr>
<tr class="separator:a3f3ae10bc2057cc1360abfa25f224c8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a0008bd997ab6e2e299ef2fb272fb4b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31</a> (const <a class="el" href="structarm__cfft__instance__q31.html">arm_cfft_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *p1, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, uint8_t bitReverseFlag)</td></tr>
<tr class="memdesc:ga5a0008bd997ab6e2e299ef2fb272fb4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the fixed-point complex FFT in Q31 format.  <a href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b"></a><br/></td></tr>
<tr class="separator:ga5a0008bd997ab6e2e299ef2fb272fb4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ac8e7ebe1cb131a5b0f55d0464640591f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_bitreversal_32 </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint16_t&#160;</td>
          <td class="paramname"><em>bitRevLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint16_t *&#160;</td>
          <td class="paramname"><em>pBitRevTable</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3f3ae10bc2057cc1360abfa25f224c8c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_radix4by2_inverse_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pCoef</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>References <a class="el" href="arm__cfft__q31_8c.html#ac9c7c553114c1201a3a987a11b8a6d01">arm_radix4_butterfly_inverse_q31()</a>, <a class="el" href="arm__math_8h.html#a960f210642058d2b3d5368729a6e8375">mult_32x32_keep32_R</a>, <a class="el" href="arm__math_8h.html#aba3e538352fc7f9d6d15f9a18d469399">multAcc_32x32_keep32_R</a>, and <a class="el" href="arm__math_8h.html#a668fbf1cd1c3bc8faf1b1c83964ade23">multSub_32x32_keep32_R</a>.</p>

<p>Referenced by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="af6df8bf714c30d44e6b871ea87d22b30"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_cfft_radix4by2_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pCoef</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>end of ComplexFFT group </p>

<p>References <a class="el" href="arm__cfft__q31_8c.html#ac12f1e7f159d5741358cdc36830a0395">arm_radix4_butterfly_q31()</a>, <a class="el" href="arm__math_8h.html#a960f210642058d2b3d5368729a6e8375">mult_32x32_keep32_R</a>, <a class="el" href="arm__math_8h.html#aba3e538352fc7f9d6d15f9a18d469399">multAcc_32x32_keep32_R</a>, and <a class="el" href="arm__math_8h.html#a668fbf1cd1c3bc8faf1b1c83964ade23">multSub_32x32_keep32_R</a>.</p>

<p>Referenced by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ac9c7c553114c1201a3a987a11b8a6d01"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_radix4_butterfly_inverse_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pCoef</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>twidCoefModifier</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pSrc</td><td>points to the in-place buffer of Q31 data type. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoef</td><td>points to twiddle coefficient buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">twidCoefModifier</td><td>twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#ad1b053da364f9fd82ca1a381df7590b6">__SIMD64</a>.</p>

<p>Referenced by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31()</a>, <a class="el" href="group___complex_f_f_t.html#gafde3ee1f58cf393b45a9073174fff548">arm_cfft_radix4_q31()</a>, and <a class="el" href="arm__cfft__q31_8c.html#a3f3ae10bc2057cc1360abfa25f224c8c">arm_cfft_radix4by2_inverse_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ac12f1e7f159d5741358cdc36830a0395"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_radix4_butterfly_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fftLen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pCoef</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>twidCoefModifier</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>end of ComplexFFT group </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pSrc</td><td>points to the in-place buffer of Q31 data type. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoef</td><td>points to twiddle coefficient buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">twidCoefModifier</td><td>twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#ad1b053da364f9fd82ca1a381df7590b6">__SIMD64</a>.</p>

<p>Referenced by <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31()</a>, <a class="el" href="group___complex_f_f_t.html#gafde3ee1f58cf393b45a9073174fff548">arm_cfft_radix4_q31()</a>, and <a class="el" href="arm__cfft__q31_8c.html#af6df8bf714c30d44e6b871ea87d22b30">arm_cfft_radix4by2_q31()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_7e8aa87db1ad6b3d9b1f25792e7c5208.html">Source</a></li><li class="navelem"><a class="el" href="dir_9c857f0e41082f634e50072d001e0d4f.html">TransformFunctions</a></li><li class="navelem"><a class="el" href="arm__cfft__q31_8c.html">arm_cfft_q31.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:48 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
