<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Data Fields - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="classes.html"><span>Data&#160;Structure&#160;Index</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions_vars.html#index_a"><span>a</span></a></li>
      <li><a href="functions_vars_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_vars_0x63.html#index_c"><span>c</span></a></li>
      <li class="current"><a href="functions_vars_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_vars_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_vars_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_vars_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_vars_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_vars_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_vars_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_vars_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_vars_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_vars_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_vars_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_vars_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_vars_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_vars_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_vars_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_vars_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions_vars_0x64.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_d"></a>- d -</h3><ul>
<li>data_lost
: <a class="el" href="group__spi__interface__gr.html#a9675630df67587ecd171c7ef12b9d22a">ARM_SPI_STATUS</a>
</li>
<li>data_width
: <a class="el" href="group__flash__interface__gr.html#a04c173610dd0a545ecae308e342aafb0">ARM_FLASH_CAPABILITIES</a>
</li>
<li>data_width_16
: <a class="el" href="group__nand__interface__gr.html#a0f22baea13daa9101bf6fc1fdfddc747">ARM_NAND_CAPABILITIES</a>
</li>
<li>data_width_4
: <a class="el" href="group__mci__interface__gr.html#a950669a8c88b49c8da4c56163b45a79d">ARM_MCI_CAPABILITIES</a>
</li>
<li>data_width_4_ddr
: <a class="el" href="group__mci__interface__gr.html#abb1a604b0ee4f7e3510409747890e41e">ARM_MCI_CAPABILITIES</a>
</li>
<li>data_width_8
: <a class="el" href="group__mci__interface__gr.html#a808703d6c70a501464e156e55f5cabd2">ARM_MCI_CAPABILITIES</a>
</li>
<li>data_width_8_ddr
: <a class="el" href="group__mci__interface__gr.html#acd5f6dce3a548d12c292e8cd17e4e9e2">ARM_MCI_CAPABILITIES</a>
</li>
<li>dcd
: <a class="el" href="group__usart__interface__gr.html#aa56a9ad6e266df78157f0e04feb4b78c">ARM_USART_MODEM_STATUS</a>
, <a class="el" href="group__usart__interface__gr.html#aa56a9ad6e266df78157f0e04feb4b78c">ARM_USART_CAPABILITIES</a>
</li>
<li>ddr
: <a class="el" href="group__nand__interface__gr.html#aa9acfde38637fe749aa9271c0a8dae1a">ARM_NAND_CAPABILITIES</a>
</li>
<li>ddr2
: <a class="el" href="group__nand__interface__gr.html#ae086693990cbd5d628014c0fcc7c1f2c">ARM_NAND_CAPABILITIES</a>
</li>
<li>ddr2_timing_mode
: <a class="el" href="group__nand__interface__gr.html#a6d9b66da0e56d04d545e0bb6841891b2">ARM_NAND_CAPABILITIES</a>
</li>
<li>ddr_timing_mode
: <a class="el" href="group__nand__interface__gr.html#a00c1f5db7d7c4abe7556733c36da7783">ARM_NAND_CAPABILITIES</a>
</li>
<li>DeviceConnect
: <a class="el" href="group__usbd__interface__gr.html#a71d312ce5c5335c6a035da55c25848e4">ARM_DRIVER_USBD</a>
</li>
<li>DeviceDisconnect
: <a class="el" href="group__usbd__interface__gr.html#adff9dd8a0dc764e78b0271113ae3b0af">ARM_DRIVER_USBD</a>
</li>
<li>DeviceGetState
: <a class="el" href="group__usbd__interface__gr.html#ab906727173cbe8372bdc26ef20581baa">ARM_DRIVER_USBD</a>
</li>
<li>DevicePower
: <a class="el" href="group__nand__interface__gr.html#a9ba6f3066cda5c8d781c309a17315a58">ARM_DRIVER_NAND</a>
</li>
<li>DeviceRemoteWakeup
: <a class="el" href="group__usbd__interface__gr.html#a649343be3fcfc44431d19f51d13e03b3">ARM_DRIVER_USBD</a>
</li>
<li>DeviceSetAddress
: <a class="el" href="group__usbd__interface__gr.html#a4e927b5593f416a8641e12016208b5d5">ARM_DRIVER_USBD</a>
</li>
<li>direction
: <a class="el" href="group__i2c__interface__gr.html#a2148ffb99828aeaced6a5655502434ac">ARM_I2C_STATUS</a>
</li>
<li>driver_strength_18
: <a class="el" href="group__nand__interface__gr.html#ae672b2a65dd3d0b93812c088491c4552">ARM_NAND_CAPABILITIES</a>
</li>
<li>driver_strength_25
: <a class="el" href="group__nand__interface__gr.html#ae87c19872b838dac7d3136a3fd466f6a">ARM_NAND_CAPABILITIES</a>
</li>
<li>driver_strength_50
: <a class="el" href="group__nand__interface__gr.html#aef3d6e1522a6cf7fb87fd113dcd43ad5">ARM_NAND_CAPABILITIES</a>
</li>
<li>drv
: <a class="el" href="group__common__drv__gr.html#adcd153bc4507926c792e86ebe74e6455">ARM_DRIVER_VERSION</a>
</li>
<li>dsr
: <a class="el" href="group__usart__interface__gr.html#a437895b17519a16f920ae07461dd67d2">ARM_USART_CAPABILITIES</a>
, <a class="el" href="group__usart__interface__gr.html#a437895b17519a16f920ae07461dd67d2">ARM_USART_MODEM_STATUS</a>
</li>
<li>dtr
: <a class="el" href="group__usart__interface__gr.html#aa3cc092c82fdc3e5e6646460be6ae9fd">ARM_USART_CAPABILITIES</a>
</li>
<li>duplex
: <a class="el" href="group__eth__interface__gr.html#a44b6cae894d7311dcdae7e93969c3c09">ARM_ETH_LINK_INFO</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
