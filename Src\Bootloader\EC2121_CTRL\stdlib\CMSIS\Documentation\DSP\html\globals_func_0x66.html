<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_func.html#index_a"><span>a</span></a></li>
      <li><a href="globals_func_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_func_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_func_0x64.html#index_d"><span>d</span></a></li>
      <li class="current"><a href="globals_func_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_func_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_func_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_func_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_func_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_func_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_func_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_func_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_func_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_func_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_func_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_func_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_func_0x76.html#index_v"><span>v</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_func_0x66.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_f"></a>- f -</h3><ul>
<li>arm_fill_f32()
: <a class="el" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94">arm_fill_f32.c</a>
, <a class="el" href="group___fill.html#ga2248e8d3901b4afb7827163132baad94">arm_math.h</a>
</li>
<li>arm_fill_q15()
: <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_math.h</a>
, <a class="el" href="group___fill.html#ga76b21c32a3783a2b3334d930a646e5d8">arm_fill_q15.c</a>
</li>
<li>arm_fill_q31()
: <a class="el" href="group___fill.html#ga69cc781cf337bd0a31bb85c772a35f7f">arm_fill_q31.c</a>
, <a class="el" href="group___fill.html#ga69cc781cf337bd0a31bb85c772a35f7f">arm_math.h</a>
</li>
<li>arm_fill_q7()
: <a class="el" href="group___fill.html#ga0465cf326ada039ed792f94b033d9ec5">arm_math.h</a>
, <a class="el" href="group___fill.html#ga0465cf326ada039ed792f94b033d9ec5">arm_fill_q7.c</a>
</li>
<li>arm_fir_decimate_f32()
: <a class="el" href="group___f_i_r__decimate.html#ga25aa3d58a90bf91b6a82272a0bc518f7">arm_fir_decimate_f32.c</a>
, <a class="el" href="group___f_i_r__decimate.html#ga25aa3d58a90bf91b6a82272a0bc518f7">arm_math.h</a>
</li>
<li>arm_fir_decimate_fast_q15()
: <a class="el" href="group___f_i_r__decimate.html#ga3f434c9a5d3b4e68061feac0714ea2ac">arm_fir_decimate_fast_q15.c</a>
, <a class="el" href="group___f_i_r__decimate.html#ga3f434c9a5d3b4e68061feac0714ea2ac">arm_math.h</a>
</li>
<li>arm_fir_decimate_fast_q31()
: <a class="el" href="group___f_i_r__decimate.html#ga3c18cc3d0548a410c577f1bead9582b7">arm_fir_decimate_fast_q31.c</a>
, <a class="el" href="group___f_i_r__decimate.html#ga3c18cc3d0548a410c577f1bead9582b7">arm_math.h</a>
</li>
<li>arm_fir_decimate_init_f32()
: <a class="el" href="group___f_i_r__decimate.html#gaaa2524b08220fd6c3f753e692ffc7d3b">arm_math.h</a>
, <a class="el" href="group___f_i_r__decimate.html#gaaa2524b08220fd6c3f753e692ffc7d3b">arm_fir_decimate_init_f32.c</a>
</li>
<li>arm_fir_decimate_init_q15()
: <a class="el" href="group___f_i_r__decimate.html#gada660e54b93d5d32178c6f5e1c6f368d">arm_fir_decimate_init_q15.c</a>
, <a class="el" href="group___f_i_r__decimate.html#gada660e54b93d5d32178c6f5e1c6f368d">arm_math.h</a>
</li>
<li>arm_fir_decimate_init_q31()
: <a class="el" href="group___f_i_r__decimate.html#ga9ed47c4e0f58affa935d84e0508a7f39">arm_fir_decimate_init_q31.c</a>
, <a class="el" href="group___f_i_r__decimate.html#ga9ed47c4e0f58affa935d84e0508a7f39">arm_math.h</a>
</li>
<li>arm_fir_decimate_q15()
: <a class="el" href="group___f_i_r__decimate.html#gab8bef6d0f6a26fdbfce9485727713ce5">arm_fir_decimate_q15.c</a>
, <a class="el" href="group___f_i_r__decimate.html#gab8bef6d0f6a26fdbfce9485727713ce5">arm_math.h</a>
</li>
<li>arm_fir_decimate_q31()
: <a class="el" href="group___f_i_r__decimate.html#gaef8e86add28f15fdc5ecc484e9dd7a4e">arm_fir_decimate_q31.c</a>
, <a class="el" href="group___f_i_r__decimate.html#gaef8e86add28f15fdc5ecc484e9dd7a4e">arm_math.h</a>
</li>
<li>arm_fir_f32()
: <a class="el" href="group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a">arm_fir_f32.c</a>
, <a class="el" href="group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a">arm_math.h</a>
</li>
<li>arm_fir_fast_q15()
: <a class="el" href="group___f_i_r.html#gac7d35e9472e49ccd88800f37f3476bd3">arm_fir_fast_q15.c</a>
, <a class="el" href="group___f_i_r.html#gac7d35e9472e49ccd88800f37f3476bd3">arm_math.h</a>
</li>
<li>arm_fir_fast_q31()
: <a class="el" href="group___f_i_r.html#ga70d11af009dcd25594c58c75cdb5d6e3">arm_fir_fast_q31.c</a>
, <a class="el" href="group___f_i_r.html#ga70d11af009dcd25594c58c75cdb5d6e3">arm_math.h</a>
</li>
<li>arm_fir_init_f32()
: <a class="el" href="group___f_i_r.html#ga98d13def6427e29522829f945d0967db">arm_math.h</a>
, <a class="el" href="group___f_i_r.html#ga98d13def6427e29522829f945d0967db">arm_fir_init_f32.c</a>
</li>
<li>arm_fir_init_q15()
: <a class="el" href="group___f_i_r.html#gae2a50f692f41ba57e44ed0719b1368bd">arm_fir_init_q15.c</a>
, <a class="el" href="group___f_i_r.html#gae2a50f692f41ba57e44ed0719b1368bd">arm_math.h</a>
</li>
<li>arm_fir_init_q31()
: <a class="el" href="group___f_i_r.html#gac00d53af87684cbbe135767b55e748a5">arm_fir_init_q31.c</a>
, <a class="el" href="group___f_i_r.html#gac00d53af87684cbbe135767b55e748a5">arm_math.h</a>
</li>
<li>arm_fir_init_q7()
: <a class="el" href="group___f_i_r.html#ga88e48688224d42dc173dbcec702f0c1d">arm_fir_init_q7.c</a>
, <a class="el" href="group___f_i_r.html#ga88e48688224d42dc173dbcec702f0c1d">arm_math.h</a>
</li>
<li>arm_fir_interpolate_f32()
: <a class="el" href="group___f_i_r___interpolate.html#ga9cae104c5cf60b4e7671c82264a8c12e">arm_fir_interpolate_f32.c</a>
, <a class="el" href="group___f_i_r___interpolate.html#ga9cae104c5cf60b4e7671c82264a8c12e">arm_math.h</a>
</li>
<li>arm_fir_interpolate_init_f32()
: <a class="el" href="group___f_i_r___interpolate.html#ga0f857457a815946f7e4dca989ebf6ff6">arm_fir_interpolate_init_f32.c</a>
, <a class="el" href="group___f_i_r___interpolate.html#ga0f857457a815946f7e4dca989ebf6ff6">arm_math.h</a>
</li>
<li>arm_fir_interpolate_init_q15()
: <a class="el" href="group___f_i_r___interpolate.html#ga18e8c4a74ff1d0f88876cc63f675288f">arm_fir_interpolate_init_q15.c</a>
, <a class="el" href="group___f_i_r___interpolate.html#ga18e8c4a74ff1d0f88876cc63f675288f">arm_math.h</a>
</li>
<li>arm_fir_interpolate_init_q31()
: <a class="el" href="group___f_i_r___interpolate.html#ga9d0ba38ce9f12a850dd242731d307476">arm_fir_interpolate_init_q31.c</a>
, <a class="el" href="group___f_i_r___interpolate.html#ga9d0ba38ce9f12a850dd242731d307476">arm_math.h</a>
</li>
<li>arm_fir_interpolate_q15()
: <a class="el" href="group___f_i_r___interpolate.html#ga7962b5f9636e54899f75d0c5936800b5">arm_fir_interpolate_q15.c</a>
, <a class="el" href="group___f_i_r___interpolate.html#ga7962b5f9636e54899f75d0c5936800b5">arm_math.h</a>
</li>
<li>arm_fir_interpolate_q31()
: <a class="el" href="group___f_i_r___interpolate.html#gaac9c0f01ed91c53f7083995d7411f5ee">arm_fir_interpolate_q31.c</a>
, <a class="el" href="group___f_i_r___interpolate.html#gaac9c0f01ed91c53f7083995d7411f5ee">arm_math.h</a>
</li>
<li>arm_fir_lattice_f32()
: <a class="el" href="group___f_i_r___lattice.html#gae63a45a63a11a65f2eae8b8b1fe370a8">arm_fir_lattice_f32.c</a>
, <a class="el" href="group___f_i_r___lattice.html#gae63a45a63a11a65f2eae8b8b1fe370a8">arm_math.h</a>
</li>
<li>arm_fir_lattice_init_f32()
: <a class="el" href="group___f_i_r___lattice.html#ga86199a1590af2b8941c6532ee9d03229">arm_fir_lattice_init_f32.c</a>
, <a class="el" href="group___f_i_r___lattice.html#ga86199a1590af2b8941c6532ee9d03229">arm_math.h</a>
</li>
<li>arm_fir_lattice_init_q15()
: <a class="el" href="group___f_i_r___lattice.html#ga1b22f30ce1cc19bf5a5d7c9fca154d72">arm_fir_lattice_init_q15.c</a>
, <a class="el" href="group___f_i_r___lattice.html#ga1b22f30ce1cc19bf5a5d7c9fca154d72">arm_math.h</a>
</li>
<li>arm_fir_lattice_init_q31()
: <a class="el" href="group___f_i_r___lattice.html#gac05a17a0188bb851b58d19e572870a54">arm_fir_lattice_init_q31.c</a>
, <a class="el" href="group___f_i_r___lattice.html#gac05a17a0188bb851b58d19e572870a54">arm_math.h</a>
</li>
<li>arm_fir_lattice_q15()
: <a class="el" href="group___f_i_r___lattice.html#gabb0ab07fd313b4d863070c3ddca51542">arm_fir_lattice_q15.c</a>
, <a class="el" href="group___f_i_r___lattice.html#gabb0ab07fd313b4d863070c3ddca51542">arm_math.h</a>
</li>
<li>arm_fir_lattice_q31()
: <a class="el" href="group___f_i_r___lattice.html#ga2e36fd210e4a1a5dd333ce80dd6d9a88">arm_fir_lattice_q31.c</a>
, <a class="el" href="group___f_i_r___lattice.html#ga2e36fd210e4a1a5dd333ce80dd6d9a88">arm_math.h</a>
</li>
<li>arm_fir_q15()
: <a class="el" href="group___f_i_r.html#ga262d173058d6f80fdf60404ba262a8f5">arm_math.h</a>
, <a class="el" href="group___f_i_r.html#ga262d173058d6f80fdf60404ba262a8f5">arm_fir_q15.c</a>
</li>
<li>arm_fir_q31()
: <a class="el" href="group___f_i_r.html#gaadd938c68ab08967cbb5fc696f384bb5">arm_fir_q31.c</a>
, <a class="el" href="group___f_i_r.html#gaadd938c68ab08967cbb5fc696f384bb5">arm_math.h</a>
</li>
<li>arm_fir_q7()
: <a class="el" href="group___f_i_r.html#ga31c91a0bf0962327ef8f626fae68ea32">arm_fir_q7.c</a>
, <a class="el" href="group___f_i_r.html#ga31c91a0bf0962327ef8f626fae68ea32">arm_math.h</a>
</li>
<li>arm_fir_sparse_f32()
: <a class="el" href="group___f_i_r___sparse.html#ga23a9284de5ee39406713b91d18ac8838">arm_fir_sparse_f32.c</a>
, <a class="el" href="group___f_i_r___sparse.html#ga23a9284de5ee39406713b91d18ac8838">arm_math.h</a>
</li>
<li>arm_fir_sparse_init_f32()
: <a class="el" href="group___f_i_r___sparse.html#ga86378a08a9d9e1e0e5de77843b34d396">arm_fir_sparse_init_f32.c</a>
, <a class="el" href="group___f_i_r___sparse.html#ga86378a08a9d9e1e0e5de77843b34d396">arm_math.h</a>
</li>
<li>arm_fir_sparse_init_q15()
: <a class="el" href="group___f_i_r___sparse.html#ga5eaa80bf72bcccef5a2c5fc6648d1baa">arm_fir_sparse_init_q15.c</a>
, <a class="el" href="group___f_i_r___sparse.html#ga5eaa80bf72bcccef5a2c5fc6648d1baa">arm_math.h</a>
</li>
<li>arm_fir_sparse_init_q31()
: <a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_fir_sparse_init_q31.c</a>
, <a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_math.h</a>
</li>
<li>arm_fir_sparse_init_q7()
: <a class="el" href="group___f_i_r___sparse.html#ga98f5c1a097d4572ce4ff3b0c58ebcdbd">arm_fir_sparse_init_q7.c</a>
, <a class="el" href="group___f_i_r___sparse.html#ga98f5c1a097d4572ce4ff3b0c58ebcdbd">arm_math.h</a>
</li>
<li>arm_fir_sparse_q15()
: <a class="el" href="group___f_i_r___sparse.html#ga2bffda2e156e72427e19276cd9c3d3cc">arm_fir_sparse_q15.c</a>
, <a class="el" href="group___f_i_r___sparse.html#ga2bffda2e156e72427e19276cd9c3d3cc">arm_math.h</a>
</li>
<li>arm_fir_sparse_q31()
: <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31.c</a>
, <a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_math.h</a>
</li>
<li>arm_fir_sparse_q7()
: <a class="el" href="group___f_i_r___sparse.html#gae86c145efc2d9ec32dc6d8c1ad2ccb3c">arm_fir_sparse_q7.c</a>
, <a class="el" href="group___f_i_r___sparse.html#gae86c145efc2d9ec32dc6d8c1ad2ccb3c">arm_math.h</a>
</li>
<li>arm_float_to_q12_20()
: <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a23f94b0fbfed6d620f38e26bc64cf2f8">arm_signal_converge_example/ARM/math_helper.h</a>
</li>
<li>arm_float_to_q14()
: <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a23cdb5202efd9233f4e92b5f22287eac">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a23cdb5202efd9233f4e92b5f22287eac">arm_fir_example/ARM/math_helper.c</a>
</li>
<li>arm_float_to_q15()
: <a class="el" href="group__float__to__x.html#ga215456e35a18db86882e1d3f0d24e1f2">arm_math.h</a>
, <a class="el" href="group__float__to__x.html#ga215456e35a18db86882e1d3f0d24e1f2">arm_float_to_q15.c</a>
</li>
<li>arm_float_to_q28()
: <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#aa1049b3adb14331612bb762237391625">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#aa1049b3adb14331612bb762237391625">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#aa1049b3adb14331612bb762237391625">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#aa1049b3adb14331612bb762237391625">arm_linear_interp_example/ARM/math_helper.h</a>
</li>
<li>arm_float_to_q29()
: <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a098c587b93469a7a6bcc521d42fdf6f9">arm_fir_example/ARM/math_helper.h</a>
</li>
<li>arm_float_to_q30()
: <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a16764fdbc174a79f04b07032cf902079">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a16764fdbc174a79f04b07032cf902079">arm_signal_converge_example/ARM/math_helper.h</a>
</li>
<li>arm_float_to_q31()
: <a class="el" href="group__float__to__x.html#ga177704107f94564e9abe4daaa36f4554">arm_float_to_q31.c</a>
, <a class="el" href="group__float__to__x.html#ga177704107f94564e9abe4daaa36f4554">arm_math.h</a>
</li>
<li>arm_float_to_q7()
: <a class="el" href="group__float__to__x.html#ga44a393818cdee8dce80f2d66add25411">arm_math.h</a>
, <a class="el" href="group__float__to__x.html#ga44a393818cdee8dce80f2d66add25411">arm_float_to_q7.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
