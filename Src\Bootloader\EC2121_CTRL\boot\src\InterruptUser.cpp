/**
  ******************************************************************************
  * @file    stm32_it.c
  * <AUTHOR> Application Team
  * @version V3.4.0
  * @date    29-June-2012
  * @brief   Main Interrupt Service Routines.
  *          This file provides template for all exceptions handler and peripherals
  *          interrupt service routine.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT 2012 STMicroelectronics</center></h2>
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */

#include "CTimer.h"
//#include "CUart.h"

#include "InterruptUser.h"
#include "DebugConsole.h"
//#include "MemDebug.h"


extern "C"
{
#include "misc.h"
};


/*
const INTERRUPT_SOURCE_MANAGER IntSourceManager[23] =
{
    {TIM1,   INTERRUPT_SOURCE_TIM1},
    {TIM2,   INTERRUPT_SOURCE_TIM2},
    {TIM3,   INTERRUPT_SOURCE_TIM3},
    {TIM4,   INTERRUPT_SOURCE_TIM4},
    {TIM5,   INTERRUPT_SOURCE_TIM5},
    {TIM6,   INTERRUPT_SOURCE_TIM6},
    {TIM7,   INTERRUPT_SOURCE_TIM7},
    {TIM8,   INTERRUPT_SOURCE_TIM8},
    {TIM9,   INTERRUPT_SOURCE_TIM9},
    {TIM10,  INTERRUPT_SOURCE_TIM10},
    {TIM11,  INTERRUPT_SOURCE_TIM11},
    {TIM12,  INTERRUPT_SOURCE_TIM12},
    {TIM13,  INTERRUPT_SOURCE_TIM13},
    {TIM14,  INTERRUPT_SOURCE_TIM14},
    {TIM1,  INTERRUPT_SOURCE_TIM1},
};
*/

/******************************************************************************/
/*            Cortex-M Processor Exceptions Handlers                         */
/******************************************************************************/

/*******************************************************************************
* Function Name  : NMI_Handler
* Description    : This function handles NMI exception.
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
extern "C" void NMI_Handler(void)
{
}

/*******************************************************************************
* Function Name  : HardFault_Handler
* Description    : This function handles Hard Fault exception.
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
extern "C" void HardFault_Handler(void)
{
    //    __ASM("TST r1,#4\n");
    __ASM("ITE EQ\n"
          "MRSEQ r0,MSP\n"
          "MRSNE r1,PSP\n");
    //    __ASM("MOV r1,LR\n");
    //
    //    __ASM("B hard_hault_handler_c\n");
    /* Go to infinite loop when Hard Fault exception occurs */
    while(1)
    {
    }
}

/*******************************************************************************
* Function Name  : MemManage_Handler
* Description    : This function handles Memory Manage exception.
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
extern "C" void MemManage_Handler(void)
{
    /* Go to infinite loop when Memory Manage exception occurs */
    while(1)
    {
    }
}

/*******************************************************************************
* Function Name  : BusFault_Handler
* Description    : This function handles Bus Fault exception.
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
extern "C" void BusFault_Handler(void)
{
    /* Go to infinite loop when Bus Fault exception occurs */
    while(1)
    {
    }
}

/*******************************************************************************
* Function Name  : UsageFault_Handler
* Description    : This function handles Usage Fault exception.
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
extern "C" void UsageFault_Handler(void)
{
    /* Go to infinite loop when Usage Fault exception occurs */
    while(1)
    {
    }
}

/*******************************************************************************
* Function Name  : DebugMon_Handler
* Description    : This function handles Debug Monitor exception.
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
extern "C" void DebugMon_Handler(void)
{
}

/******************************************************************************/
/*            STM32 Peripherals Interrupt Handlers                        */
/******************************************************************************/

void Update_Timer_Handler(TIM_TypeDef* TIMx, CTimer::TIMER_MANAGER * pTimerCtl, uint8_t IntSource)
{
    if(pTimerCtl->pTimerObj && pTimerCtl->AllocState)
    {
        CTimer* ptemTimer = (CTimer*)(pTimerCtl->pTimerObj);
        if(ptemTimer->IsPeriod() == false)
        {
            TIM_Cmd(TIMx, DISABLE);//非周期定时器，关闭中断
        }
        ptemTimer->OnHardTimer(IntSource);
    }
    else
    {
        //走到这里肯定是有错误，如果打印中有锁，中断函数中不能直接打印，这里的错误需要想其他方法,暂时待定，赵命华20210327
        //while(1);
    }
}

extern "C" void TIM1_UP_TIM10_IRQHandler(void)//timer1 timer10
{
    if(TIM_GetITStatus(TIM1, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM1, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER1];
        Update_Timer_Handler(TIM1, ptemTimerCtl, INTERRUPT_SOURCE_TIM1);
    }
    else if(TIM_GetITStatus(TIM10, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM10, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER10];
        Update_Timer_Handler(TIM10, ptemTimerCtl, INTERRUPT_SOURCE_TIM10);
    }
}

extern "C" void  TIM2_IRQHandler(void)//timer2
{
    if(TIM_GetITStatus(TIM2, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER2];
        Update_Timer_Handler(TIM2, ptemTimerCtl, INTERRUPT_SOURCE_TIM2);
    }
}

extern "C" void  TIM3_IRQHandler(void)//timer3
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER3];
        Update_Timer_Handler(TIM3, ptemTimerCtl, INTERRUPT_SOURCE_TIM3);
    }
}

extern "C" void  TIM4_IRQHandler(void)//timer4
{
    if(TIM_GetITStatus(TIM4, TIM_IT_Update) == SET) //timer中断函数中本逻辑都一样，可以把这些弄成一个函数
    {
        TIM_ClearITPendingBit(TIM4, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER4];
        Update_Timer_Handler(TIM4, ptemTimerCtl, INTERRUPT_SOURCE_TIM4);
    }
}

extern "C" void  TIM5_IRQHandler(void)//timer5
{
    if(TIM_GetITStatus(TIM5, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM5, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER5];
        Update_Timer_Handler(TIM5, ptemTimerCtl, INTERRUPT_SOURCE_TIM5);
    }
}

extern "C" void TIM6_DAC_IRQHandler(void)//timer6
{
    if(TIM_GetITStatus(TIM6, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM6, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER6];
        Update_Timer_Handler(TIM6, ptemTimerCtl, INTERRUPT_SOURCE_TIM6);
    }
}
extern "C" void  TIM7_IRQHandler(void)//timer7 timer时间戳，不隔离
{
    if(TIM_GetITStatus(TIM7, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM7, TIM_IT_Update);
        CTimer::OnSysTick();
    }
}
extern "C" void TIM8_UP_TIM13_IRQHandler(void)//timer8 timer13
{
    if(TIM_GetITStatus(TIM8, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM8, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER8];
        Update_Timer_Handler(TIM8, ptemTimerCtl, INTERRUPT_SOURCE_TIM8);
    }
    else if(TIM_GetITStatus(TIM13, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM13, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER13];
        Update_Timer_Handler(TIM13, ptemTimerCtl, INTERRUPT_SOURCE_TIM13);
    }
}
extern "C" void TIM1_BRK_TIM9_IRQHandler(void)//timer9
{
    if(TIM_GetITStatus(TIM9, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM9, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER9];
        Update_Timer_Handler(TIM9, ptemTimerCtl, INTERRUPT_SOURCE_TIM9);
    }
}
//timer10和timer1在一个回调
extern "C" void TIM1_TRG_COM_TIM11_IRQHandler(void)//timer11
{
    if(TIM_GetITStatus(TIM11, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM11, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER11];
        Update_Timer_Handler(TIM11, ptemTimerCtl, INTERRUPT_SOURCE_TIM11);
    }
}
extern "C" void TIM8_BRK_TIM12_IRQHandler(void)//timer12
{
    if(TIM_GetITStatus(TIM12, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM12, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER12];
        Update_Timer_Handler(TIM12, ptemTimerCtl, INTERRUPT_SOURCE_TIM12);
    }
}
//timer13和timer8在一个回调
extern "C" void TIM8_TRG_COM_TIM14_IRQHandler(void)//timer14
{
    if(TIM_GetITStatus(TIM14, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM14, TIM_IT_Update);
        CTimer::TIMER_MANAGER * ptemTimerCtl = &CTimer::m_timerManager[CTimer::TIMER14];
        Update_Timer_Handler(TIM14, ptemTimerCtl, INTERRUPT_SOURCE_TIM14);
    }
}


/******************************************************************************/
/*                 STM32 Peripherals Interrupt Handlers                   */
/*  Add here the Interrupt Handler for the used peripheral(s) (PPP), for the  */
/*  available peripheral interrupt handler's name please refer to the startup */
/*  file (startup_stm32xxx.s).                                            */
/******************************************************************************/

/*******************************************************************************
* Function Name  : PPP_IRQHandler
* Description    : This function handles PPP interrupt request.
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
/*void PPP_IRQHandler(void)
{
}*/

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

