<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="classes.html"><span>Data&#160;Structure&#160;Index</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li class="current"><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions_0x63.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a class="anchor" id="index_c"></a>- c -</h3><ul>
<li>CardPower
: <a class="el" href="group__mci__interface__gr.html#a73334c737658b227ef3097343d5c78bb">ARM_DRIVER_MCI</a>
</li>
<li>ccs
: <a class="el" href="group__mci__interface__gr.html#a13c956ba993083f1e59379968e2badbe">ARM_MCI_STATUS</a>
, <a class="el" href="group__mci__interface__gr.html#a13c956ba993083f1e59379968e2badbe">ARM_MCI_CAPABILITIES</a>
</li>
<li>ccs_timeout
: <a class="el" href="group__mci__interface__gr.html#a9739c230a13b46482feb5475d257e482">ARM_MCI_CAPABILITIES</a>
</li>
<li>cd_event
: <a class="el" href="group__mci__interface__gr.html#abcabfa504d3226c723d9bf5debe2f164">ARM_MCI_CAPABILITIES</a>
</li>
<li>cd_state
: <a class="el" href="group__mci__interface__gr.html#af47e73979b028c86c7c1fbe39b095140">ARM_MCI_CAPABILITIES</a>
</li>
<li>ce_lines
: <a class="el" href="group__nand__interface__gr.html#ad5dd0fcdd7f6d5e5cd739f73323a2b11">ARM_NAND_CAPABILITIES</a>
</li>
<li>ce_manual
: <a class="el" href="group__nand__interface__gr.html#a2b8044d986995b183b057217643466bf">ARM_NAND_CAPABILITIES</a>
</li>
<li>checksum_offload_rx_icmp
: <a class="el" href="group__eth__mac__interface__gr.html#a142179445bfdbaaaf0d451f277fb0e96">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_rx_ip4
: <a class="el" href="group__eth__mac__interface__gr.html#a0051111be2e389c3161da1c444746216">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_rx_ip6
: <a class="el" href="group__eth__mac__interface__gr.html#a674b2306c64901e924b3cb7bb882f32f">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_rx_tcp
: <a class="el" href="group__eth__mac__interface__gr.html#a730d6be6a7b868e0690d9548e77b7aae">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_rx_udp
: <a class="el" href="group__eth__mac__interface__gr.html#a5a447f05a5fbfd35896aad9cd769511c">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_tx_icmp
: <a class="el" href="group__eth__mac__interface__gr.html#a7b701bac9d66886b5c6964b20c6ca55a">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_tx_ip4
: <a class="el" href="group__eth__mac__interface__gr.html#ac787d70407ce70e28724932fb32ef0ba">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_tx_ip6
: <a class="el" href="group__eth__mac__interface__gr.html#a8f7a154565e652d976b9e65bf3516504">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_tx_tcp
: <a class="el" href="group__eth__mac__interface__gr.html#a6c2b80bbfe520f3e7808cf3d4aaedb45">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>checksum_offload_tx_udp
: <a class="el" href="group__eth__mac__interface__gr.html#ab3f9560668a087606c40cd81b935396b">ARM_ETH_MAC_CAPABILITIES</a>
</li>
<li>ChipEnable
: <a class="el" href="group__nand__interface__gr.html#ac090c205fe3d1b3dcb7288b06468bbe5">ARM_DRIVER_NAND</a>
</li>
<li>codeword_size
: <a class="el" href="group__nand__interface__gr.html#ae8cff208d9efb5067d38ced675916c66">ARM_NAND_ECC_INFO</a>
</li>
<li>command_active
: <a class="el" href="group__mci__interface__gr.html#aa22ef7c7597e90835bd67d5795ba757e">ARM_MCI_STATUS</a>
</li>
<li>command_error
: <a class="el" href="group__mci__interface__gr.html#afca11cd2ce661c67455a6d75328848cc">ARM_MCI_STATUS</a>
</li>
<li>command_timeout
: <a class="el" href="group__mci__interface__gr.html#a56e426979c3872254c156e9ae7eead5b">ARM_MCI_STATUS</a>
</li>
<li>connected
: <a class="el" href="group__usbh__host__gr.html#abf1a0792d6af28877b0abd141d5524ac">ARM_USBH_PORT_STATE</a>
</li>
<li>Control
: <a class="el" href="group__usart__interface__gr.html#a6e0f47a92f626a971c5197fca6545505">ARM_DRIVER_USART</a>
, <a class="el" href="group__eth__mac__interface__gr.html#a6e0f47a92f626a971c5197fca6545505">ARM_DRIVER_ETH_MAC</a>
, <a class="el" href="group__nand__interface__gr.html#a706fedbc88921808e210d75b7b5da168">ARM_DRIVER_NAND</a>
, <a class="el" href="group__mci__interface__gr.html#a6e0f47a92f626a971c5197fca6545505">ARM_DRIVER_MCI</a>
, <a class="el" href="group__i2c__interface__gr.html#a6e0f47a92f626a971c5197fca6545505">ARM_DRIVER_I2C</a>
, <a class="el" href="group__spi__interface__gr.html#a6e0f47a92f626a971c5197fca6545505">ARM_DRIVER_SPI</a>
</li>
<li>ControlTimer
: <a class="el" href="group__eth__mac__interface__gr.html#ab6bdbdc7fdfcc52e027201738b88b431">ARM_DRIVER_ETH_MAC</a>
</li>
<li>correctable_bits
: <a class="el" href="group__nand__interface__gr.html#ae65f920c4ad99fd0c6bdf5fd8c4d161a">ARM_NAND_ECC_INFO</a>
</li>
<li>cts
: <a class="el" href="group__usart__interface__gr.html#a0a4ccfb729b3a40a5fd611021268c262">ARM_USART_MODEM_STATUS</a>
, <a class="el" href="group__usart__interface__gr.html#a0a4ccfb729b3a40a5fd611021268c262">ARM_USART_CAPABILITIES</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
