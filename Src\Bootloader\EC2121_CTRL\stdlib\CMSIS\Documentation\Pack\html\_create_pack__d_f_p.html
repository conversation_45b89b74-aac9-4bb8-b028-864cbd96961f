<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Pack: Pack with Device Support</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Pack
   &#160;<span id="projectnumber">Version 1.3.1</span>
   </div>
   <div id="projectbrief">Delivery Mechanism for Software Packs</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li class="current"><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_create_pack__d_f_p.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Pack with Device Support </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>A <a class="el" href="_c_p__s_w_components.html">Software Pack</a> that contains a <a class="el" href="pdsc_devices_pg.html">&lt;devices&gt;</a> element is called Device Family Pack (DFP). A DFP may contain additional <b>Software Components</b> or <b>Example Projects</b> or any combination of those. The typical content of a DFP is:</p>
<ul>
<li><a class="el" href="_create_pack__d_f_p.html#CP_DeviceProperties">Device Properties</a> explaining the capabilities of a device or a device family in more detail.</li>
<li><a class="el" href="_c_p__s_w_components.html#CP_Components">Software Components</a> that configure the device and basic device drivers, in particular:<ul>
<li>CMSIS-compliant <a class="el" href="_create_pack__d_f_p.html#CP_System_Startp">System and Startup Files</a> required to setup the C run-time library, device clock, and memory interface.</li>
<li>Peripheral Driver Interfaces that provide software routines for physical device peripherals used by middleware stacks.</li>
</ul>
</li>
<li>One or more <a class="el" href="_create_pack__d_f_p.html#CP_SVD">System View Description Files</a> describing the programmer's view of the device's peripherals. Drivers may adhere to the <a href="http://www.keil.com/cmsis/driver" class="el" target="_blank">CMSIS-Driver</a> standard.</li>
<li><a class="el" href="_create_pack__d_f_p.html#CP_FlashProgrammingAlgorithm">Flash Programming Algorithms</a> for erasing and downloading code into on-chip Flash.</li>
<li><a class="el" href="_c_p__s_w_components.html#CP_Examples">Example Projects</a> that show the usage of the device and its peripherals.</li>
<li><a class="el" href="_c_p__s_w_components.html#CP_CodeTemplates">User Code Templates</a> that can be used as a starting point for application development.</li>
</ul>
<p>This section is a tutorial that explains how to create a DFP. Initially the PDSC file in the DFP only describes devices. This DFP is then extended to contain also SVD files, Flash algorithms, and device related software components such as system and HAL files. Example projects and code templates may be added as described in the section <a class="el" href="_c_p__s_w_components.html">Pack with Software Components</a>.</p>
<h2>DFP Use Cases</h2>
<p>A <a class="el" href="pdsc_devices_pg.html">Device Family Pack (DFP)</a> may be provided by a Silicon Vendor and is used to extend development tools with support for new devices. In general, the DFP enables Silicon Vendors to distribute tool independent device support for their device families.<br/>
 DFPs can also be used to provide information for the display on <b>web sites</b>. One example is the new device database on <a href="http://www.keil.com/dd2/" class="el" target="_blank">www.keil.com/dd2/</a>:</p>
<p><a class="anchor" id="DevWebSite"></a></p>
<div class="image">
<img src="DeviceDatabase2.png" alt="DeviceDatabase2.png"/>
<div class="caption">
Device information on a web site extracted from a DFP</div></div>
 <h2>Steps to Create a DFP</h2>
<div class="image">
<img src="DFPFlow.png" alt="DFPFlow.png"/>
</div>
<h1><a class="anchor" id="CP_BasicDFP"></a>
Basic Device Family Pack</h1>
<p>In the following sections, a DFP will be created for a fictional device family called <b>MVCM3</b> from the device vendor <b>MyVendor</b>. The device family consists of four member that are separated into two sub-families. The specification of the MVCM3 family is as follows:</p>
<div class="image">
<img src="MVCM3SpecTable.png" alt="MVCM3SpecTable.png"/>
<div class="caption">
MVCM3 Device Family Specification</div></div>
<p> <b>Preparations</b></p>
<ol type="1">
<li>Create a working directory on you PC, for example <b>C:\temp\working_dfp</b>.</li>
<li>Go to the directory <b>\CMSIS\Pack\Tutorials</b> available in the <b>ARM::CMSIS</b> Pack installation. Please consult your development tool's documentation for more information on the Pack installation directory structure. In µVision, you will find it below <b>C:\Keil\ARM\Pack\ARM\CMSIS\<em>version</em></b>.</li>
<li>Open the file <b>Pack_with_Device_Support.zip</b>.</li>
<li>Copy the <em>content</em> of the ZIP file's <b>01_Basic_Pack</b> directory into your working directory.</li>
<li>Make sure that files/directories are not write protected (remove read-only flag).</li>
<li>Copy from the directory <b>\CMSIS\Utilities</b> available in the <b>ARM::CMSIS</b> Pack installation the following files into your working directory:<ul>
<li>PackChk.exe</li>
<li>PACK.xsd</li>
<li>SVDConv.exe</li>
</ul>
</li>
<li>Open the <b>MyVendor.MVCM3.pdsc</b> file in an editor.</li>
</ol>
<p><b>Code Example</b> </p>
<ol>
<li>
Uncomment the <a class="el" href="pdsc_family_pg.html#element_device">&lt;devices&gt;</a> section in the PDSC file and add the following: <div class="fragment"><div class="line">&lt;family Dfamily=<span class="stringliteral">&quot;MVCM3 Series&quot;</span> Dvendor=<span class="stringliteral">&quot;Generic:5&quot;</span>&gt;</div>
<div class="line">  &lt;processor Dcore=<span class="stringliteral">&quot;Cortex-M3&quot;</span> DcoreVersion=<span class="stringliteral">&quot;r2p1&quot;</span> Dfpu=<span class="stringliteral">&quot;0&quot;</span> Dmpu=<span class="stringliteral">&quot;0&quot;</span> Dendian=<span class="stringliteral">&quot;Little-endian&quot;</span>/&gt;</div>
<div class="line">  &lt;description&gt;</div>
<div class="line">    The MVCM3 device family contains an ARM Cortex-M3 processor, running up to 100 MHz with a versatile <span class="keyword">set</span> of on-chip peripherals.</div>
<div class="line">  &lt;/description&gt;</div>
<div class="line">  &lt;!-- ************************  Subfamily <span class="stringliteral">&#39;MVCM3100&#39;</span>  **************************** --&gt;</div>
<div class="line">  &lt;subFamily DsubFamily=<span class="stringliteral">&quot;MVCM3100&quot;</span>&gt;</div>
<div class="line">    &lt;processor  Dclock=<span class="stringliteral">&quot;50000000&quot;</span>/&gt;</div>
<div class="line">    &lt;!-- *************************  Device <span class="stringliteral">&#39;MVCM3110&#39;</span>  ***************************** --&gt;</div>
<div class="line">    &lt;device Dname=<span class="stringliteral">&quot;MVCM3110&quot;</span>&gt;</div>
<div class="line">      &lt;memory     <span class="keywordtype">id</span>=<span class="stringliteral">&quot;IROM1&quot;</span>                      start=<span class="stringliteral">&quot;0x00000000&quot;</span>  size=<span class="stringliteral">&quot;0x4000&quot;</span>     startup=<span class="stringliteral">&quot;1&quot;</span>   <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">      &lt;memory     <span class="keywordtype">id</span>=<span class="stringliteral">&quot;IRAM1&quot;</span>                      start=<span class="stringliteral">&quot;0x20000000&quot;</span>  size=<span class="stringliteral">&quot;0x0800&quot;</span>                   <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">    &lt;/device&gt;</div>
<div class="line">    &lt;!-- *************************  Device <span class="stringliteral">&#39;MVCM3120&#39;</span>  ***************************** --&gt;</div>
<div class="line">    &lt;device Dname=<span class="stringliteral">&quot;MVCM3120&quot;</span>&gt;</div>
<div class="line">      &lt;memory     <span class="keywordtype">id</span>=<span class="stringliteral">&quot;IROM1&quot;</span>                      start=<span class="stringliteral">&quot;0x00000000&quot;</span>  size=<span class="stringliteral">&quot;0x8000&quot;</span>     startup=<span class="stringliteral">&quot;1&quot;</span>   <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">      &lt;memory     <span class="keywordtype">id</span>=<span class="stringliteral">&quot;IRAM1&quot;</span>                      start=<span class="stringliteral">&quot;0x20000000&quot;</span>  size=<span class="stringliteral">&quot;0x1000&quot;</span>                   <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">    &lt;/device&gt;</div>
<div class="line">  &lt;/subFamily&gt;</div>
<div class="line">  &lt;!-- ************************  Subfamily <span class="stringliteral">&#39;MVCM3200&#39;</span>  **************************** --&gt;</div>
<div class="line">  &lt;subFamily DsubFamily=<span class="stringliteral">&quot;MVCM3200&quot;</span>&gt;</div>
<div class="line">    &lt;processor  Dclock=<span class="stringliteral">&quot;100000000&quot;</span>/&gt;</div>
<div class="line">    &lt;!-- *************************  Device <span class="stringliteral">&#39;MVCM3250&#39;</span>  ***************************** --&gt;</div>
<div class="line">    &lt;device Dname=<span class="stringliteral">&quot;MVCM3250&quot;</span>&gt;</div>
<div class="line">      &lt;memory     <span class="keywordtype">id</span>=<span class="stringliteral">&quot;IROM1&quot;</span>                      start=<span class="stringliteral">&quot;0x00000000&quot;</span>  size=<span class="stringliteral">&quot;0x4000&quot;</span>     startup=<span class="stringliteral">&quot;1&quot;</span>   <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">      &lt;memory     <span class="keywordtype">id</span>=<span class="stringliteral">&quot;IRAM1&quot;</span>                      start=<span class="stringliteral">&quot;0x20000000&quot;</span>  size=<span class="stringliteral">&quot;0x0800&quot;</span>                   <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">    &lt;/device&gt;</div>
<div class="line">    &lt;!-- *************************  Device <span class="stringliteral">&#39;MVCM3260&#39;</span>  ***************************** --&gt;</div>
<div class="line">    &lt;device Dname=<span class="stringliteral">&quot;MVCM3260&quot;</span>&gt;</div>
<div class="line">      &lt;memory     <span class="keywordtype">id</span>=<span class="stringliteral">&quot;IROM1&quot;</span>                      start=<span class="stringliteral">&quot;0x00000000&quot;</span>  size=<span class="stringliteral">&quot;0x8000&quot;</span>     startup=<span class="stringliteral">&quot;1&quot;</span>   <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">      &lt;memory     <span class="keywordtype">id</span>=<span class="stringliteral">&quot;IRAM1&quot;</span>                      start=<span class="stringliteral">&quot;0x20000000&quot;</span>  size=<span class="stringliteral">&quot;0x1000&quot;</span>                   <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">    &lt;/device&gt;</div>
<div class="line">  &lt;/subFamily&gt;</div>
<div class="line">&lt;/family&gt;</div>
</div><!-- fragment --> <dl class="section note"><dt>Note</dt><dd><ul>
<li><code>Dvendor</code> IDs are not freely selectable. The current set of IDs can be found in the PACK.xsd file (available in the <b>ARM::CMSIS</b> Pack in the <b>CMSIS\Pack\Utilities</b> directory).</li>
<li>All code examples in this and the following sections can be found in the <code>snippets.xml</code> file in the <b>01_Basic_Pack</b> directory.</li>
</ul>
</dd></dl>
</li>
<li>
<p class="startli">Save the PDSC file and generate the Pack file using the <b>gen_pack.bat</b> script. See <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">Generate a Pack</a> for further details. Afterwards, install the Pack in your development tool.</p>
<div class="image">
<img src="BasicDFPDisplay.png" alt="BasicDFPDisplay.png"/>
<div class="caption">
Display of microcontroller devices in development tools</div></div>
 </li>
</ol>
<h1><a class="anchor" id="CP_System_Startp"></a>
System and Startup Files</h1>
<p><a href="http://www.keil.com/cmsis/core" class="el" target="_blank">CMSIS-CORE</a> defines the following files to be used by an embedded application:</p>
<ul>
<li><code>startup_&lt;device&gt;.s</code> with reset handler and exception vectors. It is executed after reset and calls <code>SystemInit</code> and may contain stack and heap configurations for the user application.</li>
<li><code>system_&lt;device&gt;.c</code> and <code>system_&lt;device&gt;.h</code> with general system configuration information (i.e. for clock and BUS setup).</li>
<li><code>&lt;device.h&gt;</code> gives access to processor core and all peripherals. This file should be generated out of the <a class="el" href="_create_pack__d_f_p.html#CP_SVD">SVD</a> file using <a href="../../SVD/html/svd__s_v_d_conv_pg.html"><b>SVDConv.exe</b></a> to ensure consistency between the header file and what is being displayed by the debugger.</li>
</ul>
<div class="image">
<img src="SystemStartupFiles.png" alt="SystemStartupFiles.png"/>
<div class="caption">
System and startup files in relation to user code</div></div>
 <dl class="section note"><dt>Note</dt><dd><a href="http://www.keil.com/pack/doc/CMSIS/Core/html/_templates_pg.html" class="el" target="_blank">CMSIS-CORE</a> explains the structure of the system and startup files and how to create them.</dd></dl>
<p>Copy the <em>content</em> of the <b>02_System_and_Startup</b> directory of the <b>Pack_with_Device_Support.zip</b> file to the <b>Files</b> directory in your working environment: </p>
<ol>
<li>
Uncomment the <code>&lt;conditions&gt;</code> section in your PDSC and add the following (the <a class="el" href="_c_p__s_w_components.html#CP_Conditions">conditions</a> section provides more information on this step): <div class="fragment"><div class="line">&lt;condition <span class="keywordtype">id</span>=<span class="stringliteral">&quot;MVCM3 CMSIS-CORE&quot;</span>&gt;</div>
<div class="line">  &lt;!-- conditions selecting Devices --&gt;</div>
<div class="line">  &lt;description&gt;MyVendor MVCM3 Series devices and CMSIS-CORE&lt;/description&gt;</div>
<div class="line">  &lt;require Cclass=<span class="stringliteral">&quot;CMSIS&quot;</span> Cgroup=<span class="stringliteral">&quot;CORE&quot;</span>/&gt;</div>
<div class="line">  &lt;require Dvendor=<span class="stringliteral">&quot;Generic:5&quot;</span> Dname=<span class="stringliteral">&quot;MVCM3*&quot;</span>/&gt;</div>
<div class="line">&lt;/condition&gt;</div>
<div class="line">&lt;condition <span class="keywordtype">id</span>=<span class="stringliteral">&quot;Startup ARM&quot;</span>&gt;</div>
<div class="line">  &lt;description&gt;Startup assembler file <span class="keywordflow">for</span> ARMCC&lt;/description&gt;</div>
<div class="line">  &lt;require Tcompiler=<span class="stringliteral">&quot;ARMCC&quot;</span>/&gt;</div>
<div class="line">&lt;/condition&gt;</div>
<div class="line">&lt;condition <span class="keywordtype">id</span>=<span class="stringliteral">&quot;Startup GCC&quot;</span>&gt;</div>
<div class="line">  &lt;description&gt;Startup assembler file <span class="keywordflow">for</span> GCC&lt;/description&gt;</div>
<div class="line">  &lt;require Tcompiler=<span class="stringliteral">&quot;GCC&quot;</span>/&gt;</div>
<div class="line">&lt;/condition&gt;</div>
<div class="line">&lt;condition <span class="keywordtype">id</span>=<span class="stringliteral">&quot;Startup IAR&quot;</span>&gt;</div>
<div class="line">  &lt;description&gt;Startup assembler file <span class="keywordflow">for</span> IAR&lt;/description&gt;</div>
<div class="line">  &lt;require Tcompiler=<span class="stringliteral">&quot;IAR&quot;</span>/&gt;</div>
<div class="line">&lt;/condition&gt;</div>
</div><!-- fragment --> <dl class="section note"><dt>Note</dt><dd>The assembler based <code>startup_&lt;device&gt;.s</code> file is tool dependent. Therefore, separate conditions for the various tool vendors are required.  </dd></dl>
</li>
<li>
Uncomment the <code>&lt;components&gt;</code> section in your PDSC and add the following (the <a class="el" href="_c_p__s_w_components.html#CP_Components">components</a> section provides more information on this step): <div class="fragment"><div class="line">&lt;component Cclass=<span class="stringliteral">&quot;Device&quot;</span> Cgroup=<span class="stringliteral">&quot;Startup&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.0&quot;</span> condition=<span class="stringliteral">&quot;MVCM3 CMSIS-CORE&quot;</span>&gt;</div>
<div class="line">  &lt;description&gt;System Startup <span class="keywordflow">for</span> MyVendor MVCM3 Series&lt;/description&gt;</div>
<div class="line">  &lt;files&gt;</div>
<div class="line">   &lt;!--  include folder --&gt;</div>
<div class="line">   &lt;file category=<span class="stringliteral">&quot;include&quot;</span> name=<span class="stringliteral">&quot;Device\Include\&quot;/&gt;</span></div>
<div class="line"><span class="stringliteral"></span></div>
<div class="line"><span class="stringliteral">    &lt;file category=&quot;</span>source<span class="stringliteral">&quot;  name=&quot;</span>Device\Source\ARM\startup_MVCM3xxx.s<span class="stringliteral">&quot; attr=&quot;</span>config<span class="stringliteral">&quot; condition=&quot;</span>Startup ARM<span class="stringliteral">&quot;/&gt;</span></div>
<div class="line"><span class="stringliteral">    &lt;file category=&quot;</span>source<span class="stringliteral">&quot;  name=&quot;</span>Device\Source\GCC\startup_MVCM3xxx.s<span class="stringliteral">&quot; attr=&quot;</span>config<span class="stringliteral">&quot; condition=&quot;</span>Startup GCC<span class="stringliteral">&quot;/&gt;</span></div>
<div class="line"><span class="stringliteral">    &lt;file category=&quot;</span>source<span class="stringliteral">&quot;  name=&quot;</span>Device\Source\IAR\startup_MVCM3xxx.s<span class="stringliteral">&quot; attr=&quot;</span>config<span class="stringliteral">&quot; condition=&quot;</span>Startup IAR<span class="stringliteral">&quot;/&gt;</span></div>
<div class="line"><span class="stringliteral"></span></div>
<div class="line"><span class="stringliteral">    &lt;file category=&quot;</span>source<span class="stringliteral">&quot;  name=&quot;</span>Device\Source\system_MVCM3xxx.c<span class="stringliteral">&quot;  attr=&quot;</span>config<span class="stringliteral">&quot; /&gt;</span></div>
<div class="line"><span class="stringliteral">  &lt;/files&gt;</span></div>
<div class="line"><span class="stringliteral">&lt;/component&gt;</span></div>
</div><!-- fragment -->  </li>
<li>
Add a new version number: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.1&quot;</span>&gt;</div>
<div class="line">  Startup files included</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
<p class="startli">Finally, save the PDSC file and regenerate the Pack file using the <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">gen_pack.bat</a> script. Afterwards, install the Pack in your development tool and create a new project. Select the software components <b>::CMSIS:CORE</b> and <b>::Device:Startup</b> for the project:</p>
<div class="image">
<img src="StartupSystemFilesAdded.png" alt="StartupSystemFilesAdded.png"/>
<div class="caption">
Startup and system files added to a project</div></div>
 </li>
</ol>
<h1><a class="anchor" id="CP_SVD"></a>
System View Description File</h1>
<p><a href="http://www.keil.com/cmsis/svd" class="el" target="_blank">CMSIS-SVD</a> formalizes the description of the programmer's view for the system contained in ARM Cortex-M processor-based microcontrollers, in particular the memory mapped registers of the peripherals. The level of detail contained in system view descriptions is comparable to what is found in device reference manuals published by silicon vendors. The information ranges from a high level functional description of a peripheral all the way down to the definition and purpose of an individual bit field in a memory mapped register. CMSIS-SVD files are developed and maintained by the silicon vendors. The XML based SVD file is the input for <a href="../../SVD/html/svd__s_v_d_conv_pg.html"><b>SVDConv.exe</b></a> that generates the tool dependent debug view and the device header file.</p>
<div class="image">
<img src="SVDFlow.png" alt="SVDFlow.png"/>
<div class="caption">
SVD file conversion flow</div></div>
<p> Copy the <em>content</em> of the <b>03_SVD_File</b> directory of the <b>Pack_with_Device_Support.zip</b> file to the <b>Files</b> directory in your working environment: </p>
<ol>
<li>
Open the file MVCM3xxx.svd in the <b>Files\SVD</b> directory in an editor and change the following: <div class="fragment"><div class="line">&lt;device schemaVersion=<span class="stringliteral">&quot;1.1&quot;</span> xmlns:xs=<span class="stringliteral">&quot;http://www.w3.org/2001/XMLSchema-instance&quot;</span> xs:noNamespaceSchemaLocation=<span class="stringliteral">&quot;CMSIS-SVD.xsd&quot;</span> &gt;</div>
<div class="line">  &lt;vendor&gt;MyVendor&lt;/vendor&gt;              &lt;!-- device vendor name --&gt;</div>
<div class="line">  &lt;vendorID&gt;Generic&lt;/vendorID&gt;           &lt;!-- device vendor <span class="keywordtype">short</span> name --&gt;</div>
<div class="line">  &lt;name&gt;MVCM3xxx&lt;/name&gt;                  &lt;!-- name of part--&gt;</div>
<div class="line">  &lt;series&gt;MVCM3xxx&lt;/series&gt;              &lt;!-- device series the device belongs to --&gt;</div>
<div class="line">  &lt;version&gt;1.2&lt;/version&gt;                 &lt;!-- version of <span class="keyword">this</span> description, adding CMSIS-SVD 1.1 tags --&gt;</div>
<div class="line">  &lt;description&gt;ARM 32-bit Cortex-M3 Microcontroller based device, CPU clock up to 100 MHz.&lt;/description&gt;</div>
</div><!-- fragment -->  </li>
<li>
Open a command window in your working directory and enter: <pre class="fragment">C:\temp\working_dfp&gt;SVDConv.exe Files\SVD\MVCM3xxx.svd --generate=header –-fields=macro
</pre>  </li>
<li>
You should see some output of SVDConv similar to this: <pre class="fragment">CMSIS-SVD SVD Consistency Checker / Header File Generator V2.82g
Copyright (C) 2010 - 2014 ARM Ltd and ARM Germany GmbH. All rights reserved.
Options: "Files\SVD\MVCM3xxx.svd" --generate=header --fields=macro
Reading file: "Files\SVD\MVCM3xxx.svd"
 
Decoding using XML SVD Schema V1.1
 
Found 0 Errors and 0 Warnings
 
Return Code: 0 (OK)
</pre> Move the generated header file <b>MVCM3xxx.h</b> to the <b>Device\Include</b> directory.  </li>
<li>
Add the following two lines at the family level in the PDSC file: <div class="fragment"><div class="line">&lt;compile    header=<span class="stringliteral">&quot;Device\Include\MVCM3xxx.h&quot;</span>/&gt;</div>
<div class="line">&lt;debug      svd=<span class="stringliteral">&quot;SVD\MVCM3xxx.svd&quot;</span>/&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new version number: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.2&quot;</span>&gt;</div>
<div class="line">  SVD and header file included</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
<p class="startli">Finally, save the PDSC file and regenerate the Pack file using the <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">gen_pack.bat</a> script. Afterwards, install the Pack in your development tool and create a new project. Depending on your development environment, you will be able to see the SVD file included in your project:</p>
<div class="image">
<img src="SVDIncluded.png" alt="SVDIncluded.png"/>
<div class="caption">
Project with SVD file</div></div>
 <dl class="section note"><dt>Note</dt><dd>For more information on CMSIS-SVD, please visit <a href="http://www.keil.com/cmsis/svd" class="el" target="_blank">www.keil.com/cmsis/svd</a>  </dd></dl>
</li>
</ol>
<h1><a class="anchor" id="CP_FlashProgrammingAlgorithm"></a>
Flash Programming Algorithms</h1>
<p><a class="el" href="_flash_algorithm.html">Flash Programming Algorithms</a> are used to erase or download applications to Flash devices. A DFP usually contains predefined Flash algorithms for programming its supported devices. The page <a class="el" href="_algorithm_func.html">Algorithm Functions</a> explains the mechanisms in more detail.</p>
<p>For the MVCM3 device family, two flash algorithms have to be created. MVCM3110/250 have 16 kB of Flash, whereas MVCM3120/260 have 32 kB of Flash memory.</p>
<p>Copy the <em>content</em> of the <b>04_Flash_Programming</b> directory of the <b>Pack_with_Device_Support.zip</b> file to the <b>working</b> directory: </p>
<ol>
<li>
Rename the project file <b>NewDevice.uvproj</b> (in the <b>_Template_Flash</b> directory) to represent the new Flash ROM device name, for example MVCM3XXX_16.uvproj.  </li>
<li>
<p class="startli">Open the project with uVision. The selected target (Cortex-M) is fine for the device.</p>
<dl class="section note"><dt>Note</dt><dd>Creating a Flash programming algorithm with <a href="http://www.keil.com/arm/selector.asp" class="el" target="_blank">MDK-Lite</a> is not supported.  </dd></dl>
</li>
<li>
<p class="startli">Open the dialog <b>Project - Options for Target - Output</b> and change the content of the field <b>Name of Executable</b> to represent the device, here MCVM3XXX_16.</p>
<div class="image">
<img src="FPATargetOptions.png" alt="FPATargetOptions.png"/>
<div class="caption">
Options for Target 'Cortex-M'</div></div>
 </li>
<li>
In the real world, you would now start to adapt the programming algorithms in the file <b>FlashPrg.c</b>. For now, only change the device parameters (Device Name, Device Size, and Sector Size) in the <b>FlashDev.c</b> file: <div class="fragment"><div class="line"><span class="keyword">struct </span>FlashDevice const FlashDevice  =  {</div>
<div class="line">   FLASH_DRV_VERS,             <span class="comment">// Driver Version, do not modify!</span></div>
<div class="line">   <span class="stringliteral">&quot;MVCM3110/250 Flash&quot;</span>,       <span class="comment">// Device Name </span></div>
<div class="line">   ONCHIP,                     <span class="comment">// Device Type</span></div>
<div class="line">   0x00000000,                 <span class="comment">// Device Start Address</span></div>
<div class="line">   0x00004000,                 <span class="comment">// Device Size in Bytes (16kB)</span></div>
<div class="line">   1024,                       <span class="comment">// Programming Page Size</span></div>
<div class="line">   0,                          <span class="comment">// Reserved, must be 0</span></div>
<div class="line">   0xFF,                       <span class="comment">// Initial Content of Erased Memory</span></div>
<div class="line">   100,                        <span class="comment">// Program Page Timeout 100 mSec</span></div>
<div class="line">   3000,                       <span class="comment">// Erase Sector Timeout 3000 mSec</span></div>
<div class="line"></div>
<div class="line"><span class="comment">// Specify Size and Address of Sectors</span></div>
<div class="line">   0x002000, 0x000000,         <span class="comment">// Sector Size  8kB (2 Sectors)</span></div>
<div class="line">   SECTOR_END</div>
<div class="line">};</div>
</div><!-- fragment -->  </li>
<li>
Use <b>Project - Build Target</b> to generate the new Flash programming algorithm. The algorithm will be created in the parent directory of the <b>_Template_Flash</b> directory.  </li>
<li>
Copy the output file <b>..\MVCM3XXX_16.FLM</b> to a new subdirectory called <b>Files\Flash</b>. Add these lines to the MVCM3110/250 device sections: <div class="fragment"><div class="line">algorithm name=<span class="stringliteral">&quot;Flash\MVCM3XXX_16.FLM&quot;</span> start=<span class="stringliteral">&quot;0x00000000&quot;</span> size=<span class="stringliteral">&quot;0x4000&quot;</span> <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
</div><!-- fragment -->  </li>
<li>
Create a <b>MVCM3XXX_32.flm</b> for the MVCM3120/260 devices.  </li>
<li>
Add these lines to the MVCM3120/260 device sections: <div class="fragment"><div class="line">algorithm name=<span class="stringliteral">&quot;Flash\MVCM3XXX_32.FLM&quot;</span> start=<span class="stringliteral">&quot;0x00000000&quot;</span> size=<span class="stringliteral">&quot;0x8000&quot;</span> <span class="keywordflow">default</span>=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new version number: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.3&quot;</span>&gt;</div>
<div class="line">  Flash Programming Algorithms added</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
<p class="startli">Finally, save the PDSC file and regenerate the Pack file using the <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">gen_pack.bat</a> script. Afterwards, install the Pack in your development tool. Depending on your development environment, you will be able to see the Flash Programming Algorithms included in your project:</p>
<div class="image">
<img src="FPADisplay.png" alt="FPADisplay.png"/>
<div class="caption">
Display of Flash Programming Algorithm</div></div>
 <dl class="section note"><dt>Note</dt><dd>The page <a class="el" href="_flash_algorithm.html">Flash Programming Algorithms</a> gives more information on the subject.  </dd></dl>
</li>
</ol>
<h1><a class="anchor" id="CP_DeviceProperties"></a>
Device Properties</h1>
<p>To limit redundancy, devices can be organized in two hierarchy groups:</p>
<ul>
<li><a class="el" href="pdsc_family_pg.html#element_family">family</a>: the attributes of a device family which includes also the processor.</li>
<li><a class="el" href="pdsc_family_pg.html#element_subfamily">sub-family</a>: attributes of an optional sub-family; this adds or modifies features of a family.</li>
</ul>
<p>Parameters of each single device are defined in:</p>
<ul>
<li><a class="el" href="pdsc_family_pg.html#element_device">device</a>: attributes of a specific silicon device</li>
<li><a class="el" href="pdsc_family_pg.html#element_variant">variant</a>: attributes of a device variant (for example, with different package or temperature range) or an OEM device or board that integrates the device.</li>
</ul>
<p>A device is described by the following properties:</p>
<ul>
<li><a class="el" href="pdsc_family_pg.html#element_description">description</a>: textual device description</li>
<li><a class="el" href="pdsc_family_pg.html#element_feature">feature</a>: categorized list of device peripherals and features. This list is used on <a class="el" href="_create_pack__d_f_p.html#DevWebSite">web sites</a> for the display of device features.</li>
<li><a class="el" href="pdsc_family_pg.html#element_book">book</a>: documentation about the device and its processor(s)</li>
<li><a class="el" href="pdsc_family_pg.html#element_processor">processor</a>: processor and processor features embedded in the device</li>
<li><a class="el" href="pdsc_family_pg.html#element_compile">compile</a>: device specific general settings for the build tools</li>
<li><a class="el" href="pdsc_family_pg.html#element_debugconfig">debugconfig</a>: default settings for a debug connection</li>
<li><a class="el" href="pdsc_family_pg.html#element_debugport">debugport</a>: debug port description of the device for the debugger to configure a debug connection.</li>
<li><a class="el" href="pdsc_family_pg.html#element_debug">debug</a>: device specific information for the debugger to configure a debug connection including System View Description files.</li>
<li><a class="el" href="pdsc_family_pg.html#element_trace">trace</a>: device specific information for the debugger to configure trace.</li>
<li><a class="el" href="pdsc_family_pg.html#element_memory">memory</a>: memory layout of the device for internal and external RAM and ROM regions</li>
<li><a class="el" href="pdsc_family_pg.html#element_algorithm">algorithm</a>: device specific Flash programming algorithms</li>
</ul>
<p>A device will inherit the specifications from both the family and sub-family levels. Some properties are required to be unique. For example, the name of the SVD file attribute of the <b>&lt;debug&gt;</b> property. Therefore, the SVD file specified on the family level can be redefined by the attribute on the sub-family or device level. Information like the description and feature entries are concatenated, starting with the information from the family and sub-family level and finalizing with the information on the device level.</p>
<p>The following will show how to specify the device properties for the MVCM3110 device (refer to the specification shown in <a class="el" href="_create_pack__d_f_p.html#CP_BasicDFP">Basic Device Family Pack</a>). The other members of the family are specified accordingly.</p>
<p>Copy the <em>content</em> of the <b>05_Device_Properties</b> directory of the <b>Pack_with_Device_Support.zip</b> file to the <b>Files</b> directory of your working environment: </p>
<ol>
<li>
The <b>MVCM3 Series</b> family properties <a class="el" href="pdsc_family_pg.html#element_processor">processor</a>, <a class="el" href="pdsc_family_pg.html#element_compile">compile</a>, <a class="el" href="pdsc_family_pg.html#element_description">description</a>, and <a class="el" href="pdsc_family_pg.html#element_debug">debug</a> are already specified. There are additional common properties that can be specified on the family level: <a class="el" href="pdsc_family_pg.html#element_book">book</a> and <a class="el" href="pdsc_family_pg.html#element_feature">feature</a>. Add the following lines to the PDSC file in the <b>&lt;family&gt;</b> section: <div class="fragment"><div class="line">&lt;book      name=<span class="stringliteral">&quot;Docs\dui0552a_cortex_m3_dgug.pdf&quot;</span>   title=<span class="stringliteral">&quot;Cortex-M3 Generic User Guide&quot;</span>/&gt;</div>
<div class="line">&lt;book      name=<span class="stringliteral">&quot;Docs\MVCM3XXX_Datasheet.pdf&quot;</span>        title=<span class="stringliteral">&quot;MVCM3 Series Datasheet&quot;</span>/&gt;</div>
<div class="line">&lt;book      name=<span class="stringliteral">&quot;Docs\MVCM3XXX_Product_Brief.pdf&quot;</span>    title=<span class="stringliteral">&quot;MVCM3 Product Brief&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;ExtInt&quot;</span>        n=<span class="stringliteral">&quot;16&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;Temp&quot;</span>          n=<span class="stringliteral">&quot;-40&quot;</span>     m=<span class="stringliteral">&quot;105&quot;</span>   name=<span class="stringliteral">&quot;Extended Temperature Range&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;VCC&quot;</span>           n=<span class="stringliteral">&quot;2.5&quot;</span>     m=<span class="stringliteral">&quot;3.6&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;RTC&quot;</span>           n=<span class="stringliteral">&quot;32768&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;WDT&quot;</span>           n=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
</div><!-- fragment -->  </li>
<li>
The <b>MVCM31xx</b> sub-family has some features that are common for the two member devices. Please add the following code to the MVCM31100 <b>&lt;subFamily&gt;</b> section: <div class="fragment"><div class="line">&lt;feature type=<span class="stringliteral">&quot;IOs&quot;</span>           n=<span class="stringliteral">&quot;26&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;I2C&quot;</span>           n=<span class="stringliteral">&quot;1&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;UART&quot;</span>          n=<span class="stringliteral">&quot;4&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;Timer&quot;</span>         n=<span class="stringliteral">&quot;6&quot;</span>       m=<span class="stringliteral">&quot;32&quot;</span>/&gt;</div>
<div class="line">&lt;feature type=<span class="stringliteral">&quot;QFP&quot;</span>           n=<span class="stringliteral">&quot;32&quot;</span>/&gt;</div>
</div><!-- fragment -->  </li>
<li>
The <b>MVCM3110</b> device has some features that are unique to that device. Please add the following code to the MVCM3110 <b>&lt;device&gt;</b> section: <div class="fragment"><div class="line">&lt;feature    type=<span class="stringliteral">&quot;PWM&quot;</span>           n=<span class="stringliteral">&quot;2&quot;</span> m=<span class="stringliteral">&quot;16&quot;</span>/&gt;</div>
</div><!-- fragment -->  </li>
</ol>
<h2><a class="anchor" id="CP_DeviceSWComp"></a>
Device Specific Software Components</h2>
<p>Finally, <a class="el" href="_c_p__s_w_components.html#CP_Components">software components</a> and <a class="el" href="_c_p__s_w_components.html#CP_Examples">example projects</a> need to be added to the DFP. </p>
<ol>
<li>
Add the following lines to the PDSC file in the <b>&lt;components&gt;</b> section: <div class="fragment"><div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;Device&quot;</span> Cgroup=<span class="stringliteral">&quot;HAL&quot;</span> Csub=<span class="stringliteral">&quot;GPIO&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.0&quot;</span> condition=<span class="stringliteral">&quot;MVCM3 CMSIS-CORE&quot;</span>&gt;</div>
<div class="line">      &lt;description&gt;GPIO HAL <span class="keywordflow">for</span> MyVendor MVCM3 Series&lt;/description&gt;</div>
<div class="line">      &lt;files&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;Device\Include\GPIO.h&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;Device\Source\GPIO.c&quot;</span>/&gt;</div>
<div class="line">      &lt;/files&gt;</div>
<div class="line">    &lt;/component&gt;</div>
<div class="line"></div>
<div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;Device&quot;</span> Cgroup=<span class="stringliteral">&quot;HAL&quot;</span> Csub=<span class="stringliteral">&quot;ADC&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.0&quot;</span> condition=<span class="stringliteral">&quot;MVCM3 CMSIS-CORE&quot;</span>&gt;</div>
<div class="line">      &lt;description&gt;ADC HAL <span class="keywordflow">for</span> MyVendor MVCM3 Series&lt;/description&gt;</div>
<div class="line">      &lt;files&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;Device\Include\ADC.h&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;Device\Source\ADC.c&quot;</span>/&gt;</div>
<div class="line">      &lt;/files&gt;</div>
<div class="line">    &lt;/component&gt;</div>
<div class="line"></div>
<div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;CMSIS Driver&quot;</span> Cgroup=<span class="stringliteral">&quot;I2C&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.0&quot;</span> condition=<span class="stringliteral">&quot;MVCM3 CMSIS-CORE&quot;</span> maxInstances=<span class="stringliteral">&quot;3&quot;</span>&gt;</div>
<div class="line">      &lt;description&gt;I2C Driver <span class="keywordflow">for</span> MVCM3 Series&lt;/description&gt;</div>
<div class="line">      &lt;RTE_Components_h&gt;</div>
<div class="line"><span class="preprocessor">        #define RTE_Drivers_I2C0                </span><span class="comment">/* Driver I2C0 */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define RTE_Drivers_I2C1</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define RTE_Drivers_I2C2</span></div>
<div class="line"><span class="preprocessor"></span>      &lt;/RTE_Components_h&gt;</div>
<div class="line">      &lt;files&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;Drivers\I2C\I2C_MVCM3.c&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;Drivers\I2C\I2C_MVCM3.h&quot;</span>/&gt;</div>
<div class="line">      &lt;/files&gt;</div>
<div class="line">    &lt;/component&gt;</div>
<div class="line"></div>
<div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;CMSIS Driver&quot;</span> Cgroup=<span class="stringliteral">&quot;UART&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.0&quot;</span> condition=<span class="stringliteral">&quot;MVCM3 CMSIS-CORE&quot;</span> maxInstances=<span class="stringliteral">&quot;5&quot;</span>&gt;</div>
<div class="line">      &lt;description&gt;UART Driver <span class="keywordflow">for</span> MVCM3 Series&lt;/description&gt;</div>
<div class="line">      &lt;RTE_Components_h&gt;</div>
<div class="line"><span class="preprocessor">#define RTE_Drivers_UART0</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define RTE_Drivers_UART1</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define RTE_Drivers_UART2</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define RTE_Drivers_UART3</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define RTE_Drivers_UART4</span></div>
<div class="line"><span class="preprocessor"></span>      &lt;/RTE_Components_h&gt;</div>
<div class="line">      &lt;files&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;Drivers\UART\UART_MVCM3.c&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;Drivers\UART\UART_MVCM3.h&quot;</span>/&gt;</div>
<div class="line">      &lt;/files&gt;</div>
<div class="line">    &lt;/component&gt;</div>
</div><!-- fragment --> <dl class="section note"><dt>Note</dt><dd>The first two software components are added to <b>Device::HAL</b> because they are specific to the device family and are not using a published API. The drivers for I2C and UART are adhering to the <a href="http://www.keil.com/cmsis/driver" class="el" target="_blank">CMSIS-Driver</a> specification. Thus, they are added to the Cclass <b>CMSIS Driver</b>.  </dd></dl>
</li>
<li>
An example project has also been created. Uncomment the <b>&lt;examples&gt;</b> section and add this to it: <div class="fragment"><div class="line">&lt;example name=<span class="stringliteral">&quot;Dummy&quot;</span> doc=<span class="stringliteral">&quot;Abstract.txt&quot;</span> folder=<span class="stringliteral">&quot;Examples\dummy&quot;</span>&gt;</div>
<div class="line">  &lt;description&gt;Dummy project&lt;/description&gt;</div>
<div class="line">  &lt;board name=<span class="stringliteral">&quot;MVCM3 Starter Kit&quot;</span> vendor=<span class="stringliteral">&quot;MyVendor&quot;</span>/&gt;</div>
<div class="line">  &lt;project&gt;</div>
<div class="line">    &lt;environment name=<span class="stringliteral">&quot;uv&quot;</span> load=<span class="stringliteral">&quot;dummy.uvprojx&quot;</span>/&gt;</div>
<div class="line">  &lt;/project&gt;</div>
<div class="line">  &lt;attributes&gt;</div>
<div class="line">    &lt;category&gt;Getting Started&lt;/category&gt;</div>
<div class="line">  &lt;/attributes&gt;</div>
<div class="line">&lt;/example&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new version number: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.4&quot;</span>&gt;</div>
<div class="line">  DFP finalized</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
Finally, save the PDSC file and regenerate the Pack file using the <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">gen_pack.bat</a> script. Afterwards, install the Pack in your development tool.  </li>
</ol>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:18:10 for CMSIS-Pack by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
