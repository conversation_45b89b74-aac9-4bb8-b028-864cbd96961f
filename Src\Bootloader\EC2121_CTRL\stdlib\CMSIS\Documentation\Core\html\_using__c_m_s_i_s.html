<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Basic CMSIS Example</title>
<title>CMSIS-CORE: Basic CMSIS Example</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_using__c_m_s_i_s.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Basic CMSIS Example </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>A typical example for using the CMSIS layer is provided below. The example is based on a STM32F10x Device.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;stm32f10x.h&gt;</span>                           <span class="comment">// File name depends on device used</span></div>
<div class="line"> </div>
<div class="line">uint32_t <span class="keyword">volatile</span> msTicks;                       <span class="comment">// Counter for millisecond Interval</span></div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> SysTick_Handler (<span class="keywordtype">void</span>) {                    <span class="comment">// SysTick Interrupt Handler</span></div>
<div class="line">  msTicks++;                                     <span class="comment">// Increment Counter</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> WaitForTick (<span class="keywordtype">void</span>)  {</div>
<div class="line">  uint32_t curTicks;</div>
<div class="line"> </div>
<div class="line">  curTicks = msTicks;                            <span class="comment">// Save Current SysTick Value</span></div>
<div class="line">  <span class="keywordflow">while</span> (msTicks == curTicks)  {                 <span class="comment">// Wait for next SysTick Interrupt</span></div>
<div class="line">    <a class="code" href="group__intrinsic___c_p_u__gr.html#gad3efec76c3bfa2b8528ded530386c563" title="Wait For Event.">__WFE</a> ();                                    <span class="comment">// Power-Down until next Event/Interrupt</span></div>
<div class="line">  }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> TIM1_UP_IRQHandler (<span class="keywordtype">void</span>) {                 <span class="comment">// Timer Interrupt Handler</span></div>
<div class="line">  ;                                              <span class="comment">// Add user code here</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> timer1_init(<span class="keywordtype">int</span> frequency) {                <span class="comment">// Set up Timer (device specific)</span></div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#ga5bb7f43ad92937c039dee3d36c3c2798" title="Set the priority for an interrupt.">NVIC_SetPriority</a> (TIM1_UP_IRQn, 1);            <span class="comment">// Set Timer priority</span></div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#ga530ad9fda2ed1c8b70e439ecfe80591f" title="Enable an external interrupt.">NVIC_EnableIRQ</a> (TIM1_UP_IRQn);                 <span class="comment">// Enable Timer Interrupt</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> Device_Initialization (<span class="keywordtype">void</span>)  {             <span class="comment">// Configure &amp; Initialize MCU</span></div>
<div class="line">  <span class="keywordflow">if</span> (<a class="code" href="group___sys_tick__gr.html#gabe47de40e9b0ad465b752297a9d9f427" title="System Tick Timer Configuration.">SysTick_Config</a> (<a class="code" href="group__system__init__gr.html#gaa3cd3e43291e81e795d642b79b6088e6" title="Variable to hold the system core clock value.">SystemCoreClock</a> / 1000)) { <span class="comment">// SysTick 1mSec</span></div>
<div class="line">       : <span class="comment">// Handle Error </span></div>
<div class="line">  }</div>
<div class="line">  timer1_init ();                                <span class="comment">// setup device-specific timer</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">// The processor clock is initialized by CMSIS startup + system file</span></div>
<div class="line"><span class="keywordtype">void</span> main (<span class="keywordtype">void</span>) {                                   <span class="comment">// user application starts here</span></div>
<div class="line">  Device_Initialization ();                      <span class="comment">// Configure &amp; Initialize MCU</span></div>
<div class="line">  <span class="keywordflow">while</span> (1)  {                                   <span class="comment">// Endless Loop (the Super-Loop)</span></div>
<div class="line">    <a class="code" href="group___core___register__gr.html#gaeb8e5f7564a8ea23678fe3c987b04013" title="Globally disables interrupts and configurable fault handlers.">__disable_irq</a> ();                            <span class="comment">// Disable all interrupts</span></div>
<div class="line">    Get_InputValues ();                          <span class="comment">// Read Values</span></div>
<div class="line">    <a class="code" href="group___core___register__gr.html#ga0f98dfbd252b89d12564472dbeba9c27" title="Globally enables interrupts and configurable fault handlers.">__enable_irq</a> ();                             <span class="comment">// Enable all interrupts </span></div>
<div class="line">    Calculation_Response ();                     <span class="comment">// Calculate Results</span></div>
<div class="line">    Output_Response ();                          <span class="comment">// Output Results</span></div>
<div class="line">    WaitForTick ();                              <span class="comment">// Synchronize to SysTick Timer</span></div>
<div class="line">  }</div>
<div class="line">}</div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="_using_pg.html">Using CMSIS in Embedded Applications</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
