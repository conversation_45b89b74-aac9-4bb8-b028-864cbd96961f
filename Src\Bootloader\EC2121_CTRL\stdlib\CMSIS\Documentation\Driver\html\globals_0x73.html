<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__"><span>_</span></a></li>
      <li><a href="globals_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_0x70.html#index_p"><span>p</span></a></li>
      <li class="current"><a href="globals_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_0x75.html#index_u"><span>u</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_0x73.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index_s"></a>- s -</h3><ul>
<li>ARM_SPI_ABORT_TRANSFER
: <a class="el" href="group__spi__misc__ctrls.html#ga44708b80e48984be099cd6eb11780dc3">Driver_SPI.h</a>
</li>
<li>ARM_SPI_API_VERSION
: <a class="el" href="_driver___s_p_i_8h.html#acf1275c15e53a573d7db89da66839d97">Driver_SPI.h</a>
</li>
<li>ARM_SPI_BIT_ORDER_Msk
: <a class="el" href="_driver___s_p_i_8h.html#a7d407682d2cb5a7fea5e38ae62fa42f8">Driver_SPI.h</a>
</li>
<li>ARM_SPI_BIT_ORDER_Pos
: <a class="el" href="_driver___s_p_i_8h.html#a84a8f90504df32ec77832a0285a47081">Driver_SPI.h</a>
</li>
<li>ARM_SPI_Control()
: <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">Driver_SPI.c</a>
</li>
<li>ARM_SPI_CONTROL_Msk
: <a class="el" href="_driver___s_p_i_8h.html#a646c834efef12377b372ea546459315b">Driver_SPI.h</a>
</li>
<li>ARM_SPI_CONTROL_Pos
: <a class="el" href="_driver___s_p_i_8h.html#a129dc5d38b4ba2c776c0b90aecf12a63">Driver_SPI.h</a>
</li>
<li>ARM_SPI_CONTROL_SS
: <a class="el" href="group__spi__misc__ctrls.html#ga5776272b82decff92da003568540c92f">Driver_SPI.h</a>
</li>
<li>ARM_SPI_CPOL0_CPHA0
: <a class="el" href="group__spi__frame__format__ctrls.html#gab4ac9a609c078d1e8332cf95da34e50e">Driver_SPI.h</a>
</li>
<li>ARM_SPI_CPOL0_CPHA1
: <a class="el" href="group__spi__frame__format__ctrls.html#ga5498eb08c2ba8de2e1c2801428e79d71">Driver_SPI.h</a>
</li>
<li>ARM_SPI_CPOL1_CPHA0
: <a class="el" href="group__spi__frame__format__ctrls.html#ga67193d9b5af1ec312a66d007c33b597f">Driver_SPI.h</a>
</li>
<li>ARM_SPI_CPOL1_CPHA1
: <a class="el" href="group__spi__frame__format__ctrls.html#ga7fab572b2fec303e979e47eb2d13ca74">Driver_SPI.h</a>
</li>
<li>ARM_SPI_DATA_BITS
: <a class="el" href="group__spi__data__bits__ctrls.html#gaf6c099a1d67256a32010120c66c55250">Driver_SPI.h</a>
</li>
<li>ARM_SPI_DATA_BITS_Msk
: <a class="el" href="_driver___s_p_i_8h.html#a0b6e14fe55f4d92ddab6ca230da77f46">Driver_SPI.h</a>
</li>
<li>ARM_SPI_DATA_BITS_Pos
: <a class="el" href="_driver___s_p_i_8h.html#a89e1140c07c9805112b6de4541c3b59a">Driver_SPI.h</a>
</li>
<li>ARM_SPI_ERROR_BIT_ORDER
: <a class="el" href="group__spi__execution__status.html#ga6b8ac31930ea6ca3a9635f2ac935466d">Driver_SPI.h</a>
</li>
<li>ARM_SPI_ERROR_DATA_BITS
: <a class="el" href="group__spi__execution__status.html#ga76f895d3380ca474124f83acbebc5651">Driver_SPI.h</a>
</li>
<li>ARM_SPI_ERROR_FRAME_FORMAT
: <a class="el" href="group__spi__execution__status.html#gac47584fe5691889c056611bc589b25aa">Driver_SPI.h</a>
</li>
<li>ARM_SPI_ERROR_MODE
: <a class="el" href="group__spi__execution__status.html#ga273a55c5d19491c565e5f05d03d66f3f">Driver_SPI.h</a>
</li>
<li>ARM_SPI_ERROR_SS_MODE
: <a class="el" href="group__spi__execution__status.html#gaae7b1a1feb46faa1830c92b73bd775ad">Driver_SPI.h</a>
</li>
<li>ARM_SPI_EVENT_DATA_LOST
: <a class="el" href="group___s_p_i__events.html#ga8e63d99c80ea56de596a8d0a51fd8244">Driver_SPI.h</a>
</li>
<li>ARM_SPI_EVENT_MODE_FAULT
: <a class="el" href="group___s_p_i__events.html#ga7eaa229003689aa18598273490b3e630">Driver_SPI.h</a>
</li>
<li>ARM_SPI_EVENT_TRANSFER_COMPLETE
: <a class="el" href="group___s_p_i__events.html#gaabdfc9e17641144cd50d36d15511a1b8">Driver_SPI.h</a>
</li>
<li>ARM_SPI_FRAME_FORMAT_Msk
: <a class="el" href="_driver___s_p_i_8h.html#af459192fe14b4b725816fa0029149298">Driver_SPI.h</a>
</li>
<li>ARM_SPI_FRAME_FORMAT_Pos
: <a class="el" href="_driver___s_p_i_8h.html#ac47e4ed093d8c054021121f89c64023e">Driver_SPI.h</a>
</li>
<li>ARM_SPI_GET_BUS_SPEED
: <a class="el" href="group__spi__misc__ctrls.html#gafc00fe35bb4c89b076d014b43168b2b3">Driver_SPI.h</a>
</li>
<li>ARM_SPI_GetCapabilities()
: <a class="el" href="group__spi__interface__gr.html#gaf4823a11ab5efcd47c79b13801513ddc">Driver_SPI.c</a>
</li>
<li>ARM_SPI_GetDataCount()
: <a class="el" href="group__spi__interface__gr.html#gaaaecaaf4ec1922f22e7f9de63af5ccdb">Driver_SPI.c</a>
</li>
<li>ARM_SPI_GetStatus()
: <a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">Driver_SPI.c</a>
</li>
<li>ARM_SPI_GetVersion()
: <a class="el" href="group__spi__interface__gr.html#gad5db9209ef1d64a7915a7278d6a402c8">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Initialize()
: <a class="el" href="group__spi__interface__gr.html#ga1a3c11ed523a4355cd91069527945906">Driver_SPI.c</a>
</li>
<li>ARM_SPI_LSB_MSB
: <a class="el" href="group__spi__bit__order__ctrls.html#ga41c53c3b396a89ce78018467e561aaaf">Driver_SPI.h</a>
</li>
<li>ARM_SPI_MICROWIRE
: <a class="el" href="group__spi__frame__format__ctrls.html#ga44f481d32b9a9ea93673f05af82ccf86">Driver_SPI.h</a>
</li>
<li>ARM_SPI_MODE_INACTIVE
: <a class="el" href="group__spi__mode__ctrls.html#ga974e3d7c178b76b0540d7644b977bff3">Driver_SPI.h</a>
</li>
<li>ARM_SPI_MODE_MASTER
: <a class="el" href="group__spi__mode__ctrls.html#ga3143ef07c1607b9bc57e29df35cf2fa8">Driver_SPI.h</a>
</li>
<li>ARM_SPI_MODE_MASTER_SIMPLEX
: <a class="el" href="group__spi__mode__ctrls.html#gaf34d849c7cde1151a768887f154e19bd">Driver_SPI.h</a>
</li>
<li>ARM_SPI_MODE_SLAVE
: <a class="el" href="group__spi__mode__ctrls.html#ga382b394c5e68f7d1206b837843732a3e">Driver_SPI.h</a>
</li>
<li>ARM_SPI_MODE_SLAVE_SIMPLEX
: <a class="el" href="group__spi__mode__ctrls.html#ga9b113d8b336047e1c22f73ad44851fdf">Driver_SPI.h</a>
</li>
<li>ARM_SPI_MSB_LSB
: <a class="el" href="group__spi__bit__order__ctrls.html#ga98228a708cbab6e214c7ac696f77dab6">Driver_SPI.h</a>
</li>
<li>ARM_SPI_PowerControl()
: <a class="el" href="group__spi__interface__gr.html#ga1a1e7e80ea32ae381b75213c32aa8067">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Receive()
: <a class="el" href="group__spi__interface__gr.html#ga726aff54e782ed9b47f7ba1280a3d8f6">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Send()
: <a class="el" href="group__spi__interface__gr.html#gab2a303d1071e926280d50682f4808479">Driver_SPI.c</a>
</li>
<li>ARM_SPI_SET_BUS_SPEED
: <a class="el" href="group__spi__misc__ctrls.html#ga5ef3d114979f3fd6010d0df16c2bf5c1">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SET_DEFAULT_TX_VALUE
: <a class="el" href="group__spi__misc__ctrls.html#gae9861221dee78d52bd1522b7846535ce">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SignalEvent()
: <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">Driver_SPI.c</a>
</li>
<li>ARM_SPI_SignalEvent_t
: <a class="el" href="group__spi__interface__gr.html#gafde9205364241ee81290adc0481c6640">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_ACTIVE
: <a class="el" href="_driver___s_p_i_8h.html#a3f465cdbd1238ddd74f78e14457076c4">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_INACTIVE
: <a class="el" href="_driver___s_p_i_8h.html#a335b448e07422e9c25616a693ec581cc">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_MASTER_HW_INPUT
: <a class="el" href="group__spi__slave__select__mode__ctrls.html#ga8561bd0cc25ab2bb02b138c1c6a586cd">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_MASTER_HW_OUTPUT
: <a class="el" href="group__spi__slave__select__mode__ctrls.html#ga07762709a40dc90aca85553f500c8761">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_MASTER_MODE_Msk
: <a class="el" href="_driver___s_p_i_8h.html#aaefa5b36525296a43071968cac43a4af">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_MASTER_MODE_Pos
: <a class="el" href="_driver___s_p_i_8h.html#ac467bd067b72370b23546767e63ce693">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_MASTER_SW
: <a class="el" href="group__spi__slave__select__mode__ctrls.html#gab5e319aa3f9d4d8c9ed92f0fe865f624">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_MASTER_UNUSED
: <a class="el" href="group__spi__slave__select__mode__ctrls.html#gae19343adc7bd71408b51733171f99dc7">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_SLAVE_HW
: <a class="el" href="group__spi__slave__select__mode__ctrls.html#ga2bd0d1f3ade2dc0cc48cc0593336ad70">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_SLAVE_MODE_Msk
: <a class="el" href="_driver___s_p_i_8h.html#a2e9a0ac10df1b90b785c5d23079873e0">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_SLAVE_MODE_Pos
: <a class="el" href="_driver___s_p_i_8h.html#a4aed772149cc33c6ee70663adef90956">Driver_SPI.h</a>
</li>
<li>ARM_SPI_SS_SLAVE_SW
: <a class="el" href="group__spi__slave__select__mode__ctrls.html#gad371f6ba0d12a57bdcc3217c351abfb0">Driver_SPI.h</a>
</li>
<li>ARM_SPI_TI_SSI
: <a class="el" href="group__spi__frame__format__ctrls.html#ga225185710ba38848a489013ba4475915">Driver_SPI.h</a>
</li>
<li>ARM_SPI_Transfer()
: <a class="el" href="group__spi__interface__gr.html#gaa24026b3822c10272e301f1505136ec2">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Uninitialize()
: <a class="el" href="group__spi__interface__gr.html#ga0c480ee3eabb82fc746e89741ed2e03e">Driver_SPI.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
