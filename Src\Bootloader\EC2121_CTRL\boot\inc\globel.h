#ifndef GLOBLE_H
#define GLOBLE_H


#include <string.h>
#include <stdio.h>

enum
{
	COLOR_NO = 0, //熄灭
	COLOR_YELLOW, //黄色
	COLOR_BLUE    //蓝色
}; //灯语颜色定义

typedef unsigned char       uint8_t;
typedef unsigned short      uint16_t;
typedef unsigned int        uint32_t;
typedef unsigned long       ulong_t;
typedef signed char         int8_t;
typedef signed short        int16_t;
typedef signed int          int32_t;
typedef signed long         long_t;
typedef long long           int64_t;
typedef unsigned long long  uint64_t;

//有一些定义为uint8_t 类型的变量但是实际其的作用确实bool型的作用只有真，假两种值。这两个宏是用来给这类变量赋值的
#define UINT8_T_TRUE                  1
#define UINT8_T_FALSE                 0

#define ALIGNED_DWORD(a)              ((a) += ((~(a) + 1) & 0x03))     //4字节外扩展对齐
#define ALIGNED_64BYTE(a)             ((a) += ((~(a) + 1) & 0x3f))     //64字节外扩展对齐
#define ABS(a)                        (((a)>0)?(a):(-(a)))             //返回a的绝对值


#define MAX_ERROR_CODE     20

#define MAX_Priority_VALUE  15//STM32,使用gruap4，抢占优先级0-15


//各个使用的中断优先级在这里统一用宏定义，方便管理
#define IRQPriority_TIMER_STAMP  3//时间戳优先级为3


/*
class CBoard;                                      //提前定义CBoard类，防止一些头文件在Board.h之前预编译而不识别CBoard类
extern CBoard *g_pBoard;                           //外部引用g_pBoard变量，实际上起到全局变量的作用。这样其他头文件不用再定义

class CServiceNetBase; 
extern CServiceNetBase* g_pServiceNet; 
#if MONITORNETENABLE
class CServiceNetTcpUdp; 
extern CServiceNetTcpUdp* g_pServiceMonitorNet; 
#endif
class COBDTBox; 
extern COBDTBox* g_pServiceOBD;

class CFlash; 
extern CFlash* g_pServiceFlash;

class CGPS; 
extern CGPS* g_pServiceGPS; 

class CServiceEncryptBase; 
extern CServiceEncryptBase* g_pServiceEncrypt; 

class RealTimeUnit; 
extern RealTimeUnit* g_pServiceTime;

class CAppCloudCommBase; 
extern CAppCloudCommBase* g_pAppCloudCommObj;

class CServiceExtendSensorBase; 
extern CServiceExtendSensorBase* g_pServiceExtendSensorNOx;

class CServiceExtendSensorBase; 
extern CServiceExtendSensorBase* g_pServiceExtendSensorPM;

*/

//声明32位、8位循环冗余校验函数，不同型号HAL层或者LINK层都可能调用。这里做声明，其他头文件不用再定义
extern uint32_t CRC32Bit(uint32_t CRCCode, const uint8_t *Data, uint32_t Length);
extern uint8_t CRC8Bit(uint32_t *pTr, uint8_t Len);



#endif

