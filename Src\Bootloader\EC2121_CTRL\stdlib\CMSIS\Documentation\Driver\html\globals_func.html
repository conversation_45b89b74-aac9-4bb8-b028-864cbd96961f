<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="#index_e"><span>e</span></a></li>
      <li><a href="#index_f"><span>f</span></a></li>
      <li><a href="#index_i"><span>i</span></a></li>
      <li><a href="#index_m"><span>m</span></a></li>
      <li><a href="#index_n"><span>n</span></a></li>
      <li><a href="#index_s"><span>s</span></a></li>
      <li><a href="#index_u"><span>u</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_func.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_e"></a>- e -</h3><ul>
<li>ARM_ETH_MAC_Control()
: <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_ControlTimer()
: <a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_GetCapabilities()
: <a class="el" href="group__eth__mac__interface__gr.html#ga2b13b230502736d8c7679b359dff20d0">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_GetMacAddress()
: <a class="el" href="group__eth__mac__interface__gr.html#ga66308c1e791952047e974bd653037fae">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_GetRxFrameSize()
: <a class="el" href="group__eth__mac__interface__gr.html#ga5ee86d6b0efab5329b9bc191c23a466d">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_GetRxFrameTime()
: <a class="el" href="group__eth__mac__interface__gr.html#gaa7c6865fb09754be869778142466c5e4">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_GetTxFrameTime()
: <a class="el" href="group__eth__mac__interface__gr.html#ga115b5c7e149aec2b181de760f5d83f60">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_GetVersion()
: <a class="el" href="group__eth__mac__interface__gr.html#ga86b15062c297384ad5842dd57b9d6b1d">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_Initialize()
: <a class="el" href="group__eth__mac__interface__gr.html#gacf42d11b171cd032f0ec1de6db2b6832">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_PHY_Read()
: <a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_PHY_Write()
: <a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_PowerControl()
: <a class="el" href="group__eth__mac__interface__gr.html#ga346fef040a0e9bac5762a04a306b1be7">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_ReadFrame()
: <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_SendFrame()
: <a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_SetAddressFilter()
: <a class="el" href="group__eth__mac__interface__gr.html#ga150fe30290275a4b32756f94208124e8">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_SetMacAddress()
: <a class="el" href="group__eth__mac__interface__gr.html#ga7cc3d17c7312c5032202dfd9a915f24a">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_SignalEvent()
: <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_MAC_Uninitialize()
: <a class="el" href="group__eth__mac__interface__gr.html#gacb2c2ae06f32328775bffbdeaaabfb5d">Driver_ETH_MAC.c</a>
</li>
<li>ARM_ETH_PHY_GetLinkInfo()
: <a class="el" href="group__eth__phy__interface__gr.html#ga8c79dcd7a12656403f3befab3c8605a2">Driver_ETH_PHY.c</a>
</li>
<li>ARM_ETH_PHY_GetLinkState()
: <a class="el" href="group__eth__phy__interface__gr.html#ga4085cd24ebe33b78d51a3c003da4a5ba">Driver_ETH_PHY.c</a>
</li>
<li>ARM_ETH_PHY_GetVersion()
: <a class="el" href="group__eth__phy__interface__gr.html#ga6850d33d699d9deee4e983a2c99e9734">Driver_ETH_PHY.c</a>
</li>
<li>ARM_ETH_PHY_Initialize()
: <a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">Driver_ETH_PHY.c</a>
</li>
<li>ARM_ETH_PHY_PowerControl()
: <a class="el" href="group__eth__phy__interface__gr.html#gaba0f92561754dad8f8f03feb1cf2855e">Driver_ETH_PHY.c</a>
</li>
<li>ARM_ETH_PHY_SetInterface()
: <a class="el" href="group__eth__phy__interface__gr.html#gaedd8b5650a1259d572a1f303d3e2c01c">Driver_ETH_PHY.c</a>
</li>
<li>ARM_ETH_PHY_SetMode()
: <a class="el" href="group__eth__phy__interface__gr.html#ga9aa688c951f01ed9ca7c88cf51be8a09">Driver_ETH_PHY.c</a>
</li>
<li>ARM_ETH_PHY_Uninitialize()
: <a class="el" href="group__eth__phy__interface__gr.html#ga26ea7e1e9825b959284241ebff6eea3f">Driver_ETH_PHY.c</a>
</li>
</ul>


<h3><a class="anchor" id="index_f"></a>- f -</h3><ul>
<li>ARM_Flash_EraseChip()
: <a class="el" href="group__flash__interface__gr.html#ga6cbaebe069d31d56c70b1f8f847e2d55">Driver_Flash.c</a>
</li>
<li>ARM_Flash_EraseSector()
: <a class="el" href="group__flash__interface__gr.html#ga0b2b4fe5a7be579cf3644995a765ea20">Driver_Flash.c</a>
</li>
<li>ARM_Flash_GetCapabilities()
: <a class="el" href="group__flash__interface__gr.html#ga27c23c998032cd47cb47293c0185ee5d">Driver_Flash.c</a>
</li>
<li>ARM_Flash_GetInfo()
: <a class="el" href="group__flash__interface__gr.html#gac047b7509356e888502e0424a9d189ae">Driver_Flash.c</a>
</li>
<li>ARM_Flash_GetStatus()
: <a class="el" href="group__flash__interface__gr.html#ga06885c0d4587d5a23f97614a8b849ef1">Driver_Flash.c</a>
</li>
<li>ARM_Flash_GetVersion()
: <a class="el" href="group__flash__interface__gr.html#ga1cfe24b2ffa571ee50ae544bd922b604">Driver_Flash.c</a>
</li>
<li>ARM_Flash_Initialize()
: <a class="el" href="group__flash__interface__gr.html#gaa5b4bbe529d620d4ad4825588a4c4cf0">Driver_Flash.c</a>
</li>
<li>ARM_Flash_PowerControl()
: <a class="el" href="group__flash__interface__gr.html#gaa8baa4618ea33568f8b3752afb2ab5a2">Driver_Flash.c</a>
</li>
<li>ARM_Flash_ProgramData()
: <a class="el" href="group__flash__interface__gr.html#ga947f24ea4042093fdb5605a68ae74f9d">Driver_Flash.c</a>
</li>
<li>ARM_Flash_ReadData()
: <a class="el" href="group__flash__interface__gr.html#ga223138342383219896ed7e255faeb99a">Driver_Flash.c</a>
</li>
<li>ARM_Flash_SignalEvent()
: <a class="el" href="group__flash__interface__gr.html#ga97b75555b5433b268add81f2e60f095a">Driver_Flash.c</a>
</li>
<li>ARM_Flash_Uninitialize()
: <a class="el" href="group__flash__interface__gr.html#gae23af293e9f8a67cdb19c7d0d562d415">Driver_Flash.c</a>
</li>
</ul>


<h3><a class="anchor" id="index_i"></a>- i -</h3><ul>
<li>ARM_I2C_Control()
: <a class="el" href="group__i2c__interface__gr.html#ga828f5fa289d065675ef78a9a73d129dc">Driver_I2C.c</a>
</li>
<li>ARM_I2C_GetCapabilities()
: <a class="el" href="group__i2c__interface__gr.html#gad20e6731f627aa7b9d6e99a50806122e">Driver_I2C.c</a>
</li>
<li>ARM_I2C_GetDataCount()
: <a class="el" href="group__i2c__interface__gr.html#ga19db20ad8d7fde84d07f6db4d75f4b7c">Driver_I2C.c</a>
</li>
<li>ARM_I2C_GetStatus()
: <a class="el" href="group__i2c__interface__gr.html#gaba4e0f3eb4018e7dafd51b675c465f3e">Driver_I2C.c</a>
</li>
<li>ARM_I2C_GetVersion()
: <a class="el" href="group__i2c__interface__gr.html#ga956bd87590c7fb6e23609a0abfb5412c">Driver_I2C.c</a>
</li>
<li>ARM_I2C_Initialize()
: <a class="el" href="group__i2c__interface__gr.html#ga79d2f7d01b3a681d1cf0d70ac6692696">Driver_I2C.c</a>
</li>
<li>ARM_I2C_MasterReceive()
: <a class="el" href="group__i2c__interface__gr.html#gafa22504bcf88a85584dfe6e0dd270ad5">Driver_I2C.c</a>
</li>
<li>ARM_I2C_MasterTransmit()
: <a class="el" href="group__i2c__interface__gr.html#ga8bf4214580149d5a5d2360f71f0feb94">Driver_I2C.c</a>
</li>
<li>ARM_I2C_PowerControl()
: <a class="el" href="group__i2c__interface__gr.html#ga734a69200e063fdbfb5110062afe9329">Driver_I2C.c</a>
</li>
<li>ARM_I2C_SignalEvent()
: <a class="el" href="group__i2c__interface__gr.html#gad4f93d2895794b416dc8d8e9de91c05e">Driver_I2C.c</a>
</li>
<li>ARM_I2C_SlaveReceive()
: <a class="el" href="group__i2c__interface__gr.html#gae3c9abccd1d377385d3d4cfe29035164">Driver_I2C.c</a>
</li>
<li>ARM_I2C_SlaveTransmit()
: <a class="el" href="group__i2c__interface__gr.html#gafe164f30eba78f066272373b98a62cd4">Driver_I2C.c</a>
</li>
<li>ARM_I2C_Uninitialize()
: <a class="el" href="group__i2c__interface__gr.html#ga30d8bf600b6b3182a1f867407b3d6e75">Driver_I2C.c</a>
</li>
</ul>


<h3><a class="anchor" id="index_m"></a>- m -</h3><ul>
<li>ARM_MCI_AbortTransfer()
: <a class="el" href="group__mci__interface__gr.html#ga78fd8cd818542a03df45abb117fa916e">Driver_MCI.c</a>
</li>
<li>ARM_MCI_CardPower()
: <a class="el" href="group__mci__interface__gr.html#gab161f80e0eda2815f3e0ebbba1314ff0">Driver_MCI.c</a>
</li>
<li>ARM_MCI_Control()
: <a class="el" href="group__mci__interface__gr.html#gaec0506a2aa4ae75cf6bc02528f36fe30">Driver_MCI.c</a>
</li>
<li>ARM_MCI_GetCapabilities()
: <a class="el" href="group__mci__interface__gr.html#ga7e5a78b6e6409189833a0b72a0a3c48a">Driver_MCI.c</a>
</li>
<li>ARM_MCI_GetStatus()
: <a class="el" href="group__mci__interface__gr.html#ga8d61aa42ce78d1864fa928c1f273cbd9">Driver_MCI.c</a>
</li>
<li>ARM_MCI_GetVersion()
: <a class="el" href="group__mci__interface__gr.html#ga3418183015dbf3025b94eebaedb00ab1">Driver_MCI.c</a>
</li>
<li>ARM_MCI_Initialize()
: <a class="el" href="group__mci__interface__gr.html#ga6f34d4ab362e596ddaf23aac093268cf">Driver_MCI.c</a>
</li>
<li>ARM_MCI_PowerControl()
: <a class="el" href="group__mci__interface__gr.html#ga19752749d04ed22dc91c4294645e0244">Driver_MCI.c</a>
</li>
<li>ARM_MCI_ReadCD()
: <a class="el" href="group__mci__interface__gr.html#ga012fca8f1ce5366fce14b708c771c635">Driver_MCI.c</a>
</li>
<li>ARM_MCI_ReadWP()
: <a class="el" href="group__mci__interface__gr.html#ga3d70286918405ac81fa795c7d09dc6fd">Driver_MCI.c</a>
</li>
<li>ARM_MCI_SendCommand()
: <a class="el" href="group__mci__interface__gr.html#ga5a431da89feabc2b4bc0c27943dff6f2">Driver_MCI.c</a>
</li>
<li>ARM_MCI_SetupTransfer()
: <a class="el" href="group__mci__interface__gr.html#gaaec681bcd8e6811c5743e33ee0f35ed1">Driver_MCI.c</a>
</li>
<li>ARM_MCI_SignalEvent()
: <a class="el" href="group__mci__interface__gr.html#gaac2dbd1c1a98436938c5d0d6248cb700">Driver_MCI.c</a>
</li>
<li>ARM_MCI_Uninitialize()
: <a class="el" href="group__mci__interface__gr.html#gaef8183e77797e74997551d03646d42c2">Driver_MCI.c</a>
</li>
</ul>


<h3><a class="anchor" id="index_n"></a>- n -</h3><ul>
<li>ARM_NAND_AbortSequence()
: <a class="el" href="group__nand__interface__gr.html#ga00832861f018db0d8368900b099ecd30">Driver_NAND.c</a>
</li>
<li>ARM_NAND_ChipEnable()
: <a class="el" href="group__nand__interface__gr.html#ga1c0cba87cb7b706ad5986dc67c831ad1">Driver_NAND.c</a>
</li>
<li>ARM_NAND_Control()
: <a class="el" href="group__nand__interface__gr.html#ga83061d6d53ffb148853efbc87a864607">Driver_NAND.c</a>
</li>
<li>ARM_NAND_DevicePower()
: <a class="el" href="group__nand__interface__gr.html#ga11adcbaaace09746581a36befbd563c9">Driver_NAND.c</a>
</li>
<li>ARM_NAND_ExecuteSequence()
: <a class="el" href="group__nand__interface__gr.html#ga8a0108dba757a4610475151144b52825">Driver_NAND.c</a>
</li>
<li>ARM_NAND_GetCapabilities()
: <a class="el" href="group__nand__interface__gr.html#ga9f2609975c2008d21b9ae28f15daf147">Driver_NAND.c</a>
</li>
<li>ARM_NAND_GetDeviceBusy()
: <a class="el" href="group__nand__interface__gr.html#ga43011066306bd716b580e6aa9a80cf65">Driver_NAND.c</a>
</li>
<li>ARM_NAND_GetStatus()
: <a class="el" href="group__nand__interface__gr.html#ga4578642f37a556b58b0bba0ad5d42641">Driver_NAND.c</a>
</li>
<li>ARM_NAND_GetVersion()
: <a class="el" href="group__nand__interface__gr.html#ga01255fd4f15e7fa4751c7ea59648ef5a">Driver_NAND.c</a>
</li>
<li>ARM_NAND_Initialize()
: <a class="el" href="group__nand__interface__gr.html#ga74ad34718a595e7a4375b90f33e72750">Driver_NAND.c</a>
</li>
<li>ARM_NAND_InquireECC()
: <a class="el" href="group__nand__interface__gr.html#gac21425454d586ef48fdfc35e7bd78947">Driver_NAND.c</a>
</li>
<li>ARM_NAND_PowerControl()
: <a class="el" href="group__nand__interface__gr.html#ga9c9975637980b5d42db7baba0191fda1">Driver_NAND.c</a>
</li>
<li>ARM_NAND_ReadData()
: <a class="el" href="group__nand__interface__gr.html#gae1899a20ef107400c8bf84fad477a8ce">Driver_NAND.c</a>
</li>
<li>ARM_NAND_SendAddress()
: <a class="el" href="group__nand__interface__gr.html#ga00e195031e03d364db7595858a7e76f3">Driver_NAND.c</a>
</li>
<li>ARM_NAND_SendCommand()
: <a class="el" href="group__nand__interface__gr.html#ga9f70b89ba478eadfe7f5dee7453a4fb7">Driver_NAND.c</a>
</li>
<li>ARM_NAND_SignalEvent()
: <a class="el" href="group__nand__interface__gr.html#gaf4ce80b0fd6717de7ddfb1cfaf7dd754">Driver_NAND.c</a>
</li>
<li>ARM_NAND_Uninitialize()
: <a class="el" href="group__nand__interface__gr.html#gaa788b638ab696b166fee2f4a4bc8d97a">Driver_NAND.c</a>
</li>
<li>ARM_NAND_WriteData()
: <a class="el" href="group__nand__interface__gr.html#ga1fa497dd51a86fc308e946b4419fd006">Driver_NAND.c</a>
</li>
<li>ARM_NAND_WriteProtect()
: <a class="el" href="group__nand__interface__gr.html#ga1987e65a4e756d748db86332c9fb1cec">Driver_NAND.c</a>
</li>
</ul>


<h3><a class="anchor" id="index_s"></a>- s -</h3><ul>
<li>ARM_SPI_Control()
: <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">Driver_SPI.c</a>
</li>
<li>ARM_SPI_GetCapabilities()
: <a class="el" href="group__spi__interface__gr.html#gaf4823a11ab5efcd47c79b13801513ddc">Driver_SPI.c</a>
</li>
<li>ARM_SPI_GetDataCount()
: <a class="el" href="group__spi__interface__gr.html#gaaaecaaf4ec1922f22e7f9de63af5ccdb">Driver_SPI.c</a>
</li>
<li>ARM_SPI_GetStatus()
: <a class="el" href="group__spi__interface__gr.html#ga60d33d8788a76c388cc36e066240b817">Driver_SPI.c</a>
</li>
<li>ARM_SPI_GetVersion()
: <a class="el" href="group__spi__interface__gr.html#gad5db9209ef1d64a7915a7278d6a402c8">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Initialize()
: <a class="el" href="group__spi__interface__gr.html#ga1a3c11ed523a4355cd91069527945906">Driver_SPI.c</a>
</li>
<li>ARM_SPI_PowerControl()
: <a class="el" href="group__spi__interface__gr.html#ga1a1e7e80ea32ae381b75213c32aa8067">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Receive()
: <a class="el" href="group__spi__interface__gr.html#ga726aff54e782ed9b47f7ba1280a3d8f6">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Send()
: <a class="el" href="group__spi__interface__gr.html#gab2a303d1071e926280d50682f4808479">Driver_SPI.c</a>
</li>
<li>ARM_SPI_SignalEvent()
: <a class="el" href="group__spi__interface__gr.html#ga505b2d787348d51351d38fee98ccba7e">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Transfer()
: <a class="el" href="group__spi__interface__gr.html#gaa24026b3822c10272e301f1505136ec2">Driver_SPI.c</a>
</li>
<li>ARM_SPI_Uninitialize()
: <a class="el" href="group__spi__interface__gr.html#ga0c480ee3eabb82fc746e89741ed2e03e">Driver_SPI.c</a>
</li>
</ul>


<h3><a class="anchor" id="index_u"></a>- u -</h3><ul>
<li>ARM_USART_Control()
: <a class="el" href="group__usart__interface__gr.html#gad8ffdde2123b5412de3005c456da677d">Driver_USART.c</a>
</li>
<li>ARM_USART_GetCapabilities()
: <a class="el" href="group__usart__interface__gr.html#gad2d3ace1fe7627bb72945efefaeddf0a">Driver_USART.c</a>
</li>
<li>ARM_USART_GetModemStatus()
: <a class="el" href="group__usart__interface__gr.html#ga198af0d6a7c85b7c0b96f3d9db8c34e0">Driver_USART.c</a>
</li>
<li>ARM_USART_GetRxCount()
: <a class="el" href="group__usart__interface__gr.html#ga1a8799aeeba1363a9e5d22bada715a29">Driver_USART.c</a>
</li>
<li>ARM_USART_GetStatus()
: <a class="el" href="group__usart__interface__gr.html#ga1e8fdd54294b587438b2b72f4dbde004">Driver_USART.c</a>
</li>
<li>ARM_USART_GetTxCount()
: <a class="el" href="group__usart__interface__gr.html#gacb355584bcdf4ebd36f11d945800fa03">Driver_USART.c</a>
</li>
<li>ARM_USART_GetVersion()
: <a class="el" href="group__usart__interface__gr.html#gabca6151cef47565832decaf484781b61">Driver_USART.c</a>
</li>
<li>ARM_USART_Initialize()
: <a class="el" href="group__usart__interface__gr.html#ga51f06805e9a6197c553fa9513ac7b9d6">Driver_USART.c</a>
</li>
<li>ARM_USART_PowerControl()
: <a class="el" href="group__usart__interface__gr.html#ga9bad012b28d544f3eeeea9c2f71a4086">Driver_USART.c</a>
</li>
<li>ARM_USART_Receive()
: <a class="el" href="group__usart__interface__gr.html#gae9efabdabb5aaa17bce83339f8a58803">Driver_USART.c</a>
</li>
<li>ARM_USART_Send()
: <a class="el" href="group__usart__interface__gr.html#ga5cf758b0b9d03dca68846962f73c0b08">Driver_USART.c</a>
</li>
<li>ARM_USART_SetModemControl()
: <a class="el" href="group__usart__interface__gr.html#gad8eb0eb1d1c24fc725584ab93214cfc7">Driver_USART.c</a>
</li>
<li>ARM_USART_SignalEvent()
: <a class="el" href="group__usart__interface__gr.html#gad796cd023f8f6300a6caadcc39d43cbf">Driver_USART.c</a>
</li>
<li>ARM_USART_Transfer()
: <a class="el" href="group__usart__interface__gr.html#ga878899928d34a818edd3e97d67b65c2a">Driver_USART.c</a>
</li>
<li>ARM_USART_Uninitialize()
: <a class="el" href="group__usart__interface__gr.html#ga96f31f07a6721cf75de2a7a0ab723d26">Driver_USART.c</a>
</li>
<li>ARM_USBD_DeviceConnect()
: <a class="el" href="group__usbd__interface__gr.html#ga99207f7ff5e97a7f65754eab7f775fca">Driver_USBD.c</a>
</li>
<li>ARM_USBD_DeviceDisconnect()
: <a class="el" href="group__usbd__interface__gr.html#ga37234abecbb63e4e739f9676e489d2d1">Driver_USBD.c</a>
</li>
<li>ARM_USBD_DeviceGetState()
: <a class="el" href="group__usbd__interface__gr.html#ga7624d6b2cbe5e6ab5016206ce641eee2">Driver_USBD.c</a>
</li>
<li>ARM_USBD_DeviceRemoteWakeup()
: <a class="el" href="group__usbd__interface__gr.html#ga7e149a4c6a0196da24a44f4fada75fb1">Driver_USBD.c</a>
</li>
<li>ARM_USBD_DeviceSetAddress()
: <a class="el" href="group__usbd__interface__gr.html#gae66f696584e25fb2ddabe9070fa38670">Driver_USBD.c</a>
</li>
<li>ARM_USBD_EndpointConfigure()
: <a class="el" href="group__usbd__interface__gr.html#ga62d7d5bdcf9ca7bf7e6d8434368abad8">Driver_USBD.c</a>
</li>
<li>ARM_USBD_EndpointStall()
: <a class="el" href="group__usbd__interface__gr.html#ga9502cd7b8e4c583920fccadc4ccf7975">Driver_USBD.c</a>
</li>
<li>ARM_USBD_EndpointTransfer()
: <a class="el" href="group__usbd__interface__gr.html#ga6e69ad097553125bb01a22dc719e0d30">Driver_USBD.c</a>
</li>
<li>ARM_USBD_EndpointTransferAbort()
: <a class="el" href="group__usbd__interface__gr.html#ga7cf3bcc105dbb8cbdc915e8caca8529e">Driver_USBD.c</a>
</li>
<li>ARM_USBD_EndpointTransferGetResult()
: <a class="el" href="group__usbd__interface__gr.html#gab81fac01522a4d504edcb7b7d3abba6c">Driver_USBD.c</a>
</li>
<li>ARM_USBD_EndpointUnconfigure()
: <a class="el" href="group__usbd__interface__gr.html#gaca913df5188dc0f0c4f707b57c2a88fc">Driver_USBD.c</a>
</li>
<li>ARM_USBD_GetCapabilities()
: <a class="el" href="group__usbd__interface__gr.html#ga178d01ab7896e1c675b90bbccfe8ea7d">Driver_USBD.c</a>
</li>
<li>ARM_USBD_GetFrameNumber()
: <a class="el" href="group__usbd__interface__gr.html#ga4cd050b8343407fe465a27ad1cb7c264">Driver_USBD.c</a>
</li>
<li>ARM_USBD_GetVersion()
: <a class="el" href="group__usbd__interface__gr.html#ga1dcb8b7790f0e3613ee3da77e5fd18fc">Driver_USBD.c</a>
</li>
<li>ARM_USBD_Initialize()
: <a class="el" href="group__usbd__interface__gr.html#ga60b95c9c0c6767ff5938464cfd748f81">Driver_USBD.c</a>
</li>
<li>ARM_USBD_PowerControl()
: <a class="el" href="group__usbd__interface__gr.html#gaa5bdaac19f6df30c6e569abef17cdb42">Driver_USBD.c</a>
</li>
<li>ARM_USBD_ReadSetupPacket()
: <a class="el" href="group__usbd__interface__gr.html#ga6bc0ebf699a0f28330f21cab83f85e9e">Driver_USBD.c</a>
</li>
<li>ARM_USBD_SignalDeviceEvent()
: <a class="el" href="group__usbd__interface__gr.html#gafe58a4db1d26b21ca5d418ee49e103a5">Driver_USBD.c</a>
</li>
<li>ARM_USBD_SignalEndpointEvent()
: <a class="el" href="group__usbd__interface__gr.html#ga9aa5bc5cb45084194a77fae1457f6575">Driver_USBD.c</a>
</li>
<li>ARM_USBD_Uninitialize()
: <a class="el" href="group__usbd__interface__gr.html#gafaead6713f141be1734de0110eda966b">Driver_USBD.c</a>
</li>
<li>ARM_USBH_GetCapabilities()
: <a class="el" href="group__usbh__host__gr.html#gadb509db50fdccfc7198dfd7ac54530d7">Driver_USBH.c</a>
</li>
<li>ARM_USBH_GetFrameNumber()
: <a class="el" href="group__usbh__host__gr.html#ga9dc305fc234c9987b9efd679b5042cc9">Driver_USBH.c</a>
</li>
<li>ARM_USBH_GetVersion()
: <a class="el" href="group__usbh__host__gr.html#gab11e67e11e7a0edbc8a1afa86b971784">Driver_USBH.c</a>
</li>
<li>ARM_USBH_HCI_GetCapabilities()
: <a class="el" href="group__usbh__hci__gr.html#gae607c49ca97202500631473a901e8c2b">Driver_USBH.c</a>
</li>
<li>ARM_USBH_HCI_GetVersion()
: <a class="el" href="group__usbh__hci__gr.html#ga10109d0c2a9a128225b5e893d3f72d08">Driver_USBH.c</a>
</li>
<li>ARM_USBH_HCI_Initialize()
: <a class="el" href="group__usbh__hci__gr.html#gabc1392a544cb64491b5ea5ce6590d832">Driver_USBH.c</a>
</li>
<li>ARM_USBH_HCI_Interrupt()
: <a class="el" href="group__usbh__hci__gr.html#ga79d3c2509ed869c8d7d1485acad7b6c6">Driver_USBH.c</a>
</li>
<li>ARM_USBH_HCI_PortVbusOnOff()
: <a class="el" href="group__usbh__hci__gr.html#gade1e83403c6ea965fe3e6c4c21fbbded">Driver_USBH.c</a>
</li>
<li>ARM_USBH_HCI_PowerControl()
: <a class="el" href="group__usbh__hci__gr.html#ga27fa5ec8854cd9877bbef4abffe9a12b">Driver_USBH.c</a>
</li>
<li>ARM_USBH_HCI_Uninitialize()
: <a class="el" href="group__usbh__hci__gr.html#gaacb68fdf201cdb1846b31642a760f041">Driver_USBH.c</a>
</li>
<li>ARM_USBH_Initialize()
: <a class="el" href="group__usbh__host__gr.html#gad1e73f778c95dd46d4396e7741a97f0b">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PipeCreate()
: <a class="el" href="group__usbh__host__gr.html#ga30dcc05151a98c5a8f6fe17e83777fe0">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PipeDelete()
: <a class="el" href="group__usbh__host__gr.html#gab2135041e6d481f186015f36fa0d0521">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PipeModify()
: <a class="el" href="group__usbh__host__gr.html#ga2076a7ae55f603859c726e57b061ac73">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PipeReset()
: <a class="el" href="group__usbh__host__gr.html#ga7f5a605dbe98e450e6965d515fde65a7">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PipeTransfer()
: <a class="el" href="group__usbh__host__gr.html#ga817d503a24ad8927fa362c8f6394920d">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PipeTransferAbort()
: <a class="el" href="group__usbh__host__gr.html#ga1d4048a076aed71e585cea96a21f0afb">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PipeTransferGetResult()
: <a class="el" href="group__usbh__host__gr.html#ga85baa421345a5b92881ad190d72ca47f">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PortGetState()
: <a class="el" href="group__usbh__host__gr.html#gaea4ec5453c1d5fe37a2507d3cb4713bc">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PortReset()
: <a class="el" href="group__usbh__host__gr.html#gab99882e11ee03018da9ebe33797cc5ff">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PortResume()
: <a class="el" href="group__usbh__host__gr.html#gab438b55ada37e2987e77e105f061f2de">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PortSuspend()
: <a class="el" href="group__usbh__host__gr.html#ga620f8852a70a47a581001ed3050436d6">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PortVbusOnOff()
: <a class="el" href="group__usbh__host__gr.html#gaccca5ddd4a9d04388e7678a3aed3f6e4">Driver_USBH.c</a>
</li>
<li>ARM_USBH_PowerControl()
: <a class="el" href="group__usbh__host__gr.html#ga290a5e2e491da784e63be94699974d4a">Driver_USBH.c</a>
</li>
<li>ARM_USBH_SignalPipeEvent()
: <a class="el" href="group__usbh__host__gr.html#gae58d36afd83a0e32b07e89fb7145c9de">Driver_USBH.c</a>
</li>
<li>ARM_USBH_SignalPortEvent()
: <a class="el" href="group__usbh__host__gr.html#ga53619da2a3d56934629084b0d5c4700c">Driver_USBH.c</a>
</li>
<li>ARM_USBH_Uninitialize()
: <a class="el" href="group__usbh__host__gr.html#gafc2f18bc12bb0019f9cd1836dcca408d">Driver_USBH.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
