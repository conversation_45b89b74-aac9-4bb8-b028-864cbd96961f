<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__"><span>_</span></a></li>
      <li><a href="globals_0x61.html#index_a"><span>a</span></a></li>
      <li><a href="globals_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_0x6f.html#index_o"><span>o</span></a></li>
      <li class="current"><a href="globals_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="globals_0x76.html#index_v"><span>v</span></a></li>
      <li><a href="globals_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="globals_0x78.html#index_x"><span>x</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_0x70.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index_p"></a>- p -</h3><ul>
<li>arm_park_f32()
: <a class="el" href="group__park.html#ga08b3a683197de7e143fb00497787683c">arm_math.h</a>
</li>
<li>arm_park_q31()
: <a class="el" href="group__park.html#gaf4cc6370c0cfc14ea66774ed3c5bb10f">arm_math.h</a>
</li>
<li>arm_pid_f32()
: <a class="el" href="group___p_i_d.html#gac5c79ed46abf2d72b8cf41fa6c708bda">arm_math.h</a>
</li>
<li>arm_pid_init_f32()
: <a class="el" href="group___p_i_d.html#gae31536b19b82b93ed184fb1ab73cfcb3">arm_pid_init_f32.c</a>
, <a class="el" href="group___p_i_d.html#gae31536b19b82b93ed184fb1ab73cfcb3">arm_math.h</a>
</li>
<li>arm_pid_init_q15()
: <a class="el" href="group___p_i_d.html#ga2cb1e3d3ebb167348fdabec74653d5c3">arm_math.h</a>
, <a class="el" href="group___p_i_d.html#ga2cb1e3d3ebb167348fdabec74653d5c3">arm_pid_init_q15.c</a>
</li>
<li>arm_pid_init_q31()
: <a class="el" href="group___p_i_d.html#gad9d88485234fa9460b1ce9e64989ac86">arm_pid_init_q31.c</a>
, <a class="el" href="group___p_i_d.html#gad9d88485234fa9460b1ce9e64989ac86">arm_math.h</a>
</li>
<li>arm_pid_q15()
: <a class="el" href="group___p_i_d.html#ga084f646bbb20d55f225c3efafcf7fc1f">arm_math.h</a>
</li>
<li>arm_pid_q31()
: <a class="el" href="group___p_i_d.html#ga5f6f941e7ae981728dd3a662f8f4ecd7">arm_math.h</a>
</li>
<li>arm_pid_reset_f32()
: <a class="el" href="group___p_i_d.html#ga9ec860bcb6f8ca31205bf0f1b51ab723">arm_pid_reset_f32.c</a>
, <a class="el" href="group___p_i_d.html#ga9ec860bcb6f8ca31205bf0f1b51ab723">arm_math.h</a>
</li>
<li>arm_pid_reset_q15()
: <a class="el" href="group___p_i_d.html#ga408566dacb4fa6e0458b2c75672e525f">arm_math.h</a>
, <a class="el" href="group___p_i_d.html#ga408566dacb4fa6e0458b2c75672e525f">arm_pid_reset_q15.c</a>
</li>
<li>arm_pid_reset_q31()
: <a class="el" href="group___p_i_d.html#gaeecbacd3fb37c608ec25474d3a0dffa9">arm_pid_reset_q31.c</a>
, <a class="el" href="group___p_i_d.html#gaeecbacd3fb37c608ec25474d3a0dffa9">arm_math.h</a>
</li>
<li>arm_power_f32()
: <a class="el" href="group__power.html#ga993c00dd7f661d66bdb6e58426e893aa">arm_power_f32.c</a>
, <a class="el" href="group__power.html#ga993c00dd7f661d66bdb6e58426e893aa">arm_math.h</a>
</li>
<li>arm_power_q15()
: <a class="el" href="group__power.html#ga7050c04b7515e01a75c38f1abbaf71ba">arm_power_q15.c</a>
, <a class="el" href="group__power.html#ga7050c04b7515e01a75c38f1abbaf71ba">arm_math.h</a>
</li>
<li>arm_power_q31()
: <a class="el" href="group__power.html#ga0b93d31bb5b5ed214c2b94d8a7744cd2">arm_power_q31.c</a>
, <a class="el" href="group__power.html#ga0b93d31bb5b5ed214c2b94d8a7744cd2">arm_math.h</a>
</li>
<li>arm_power_q7()
: <a class="el" href="group__power.html#gaf969c85c5655e3d72d7b99ff188f92c9">arm_power_q7.c</a>
, <a class="el" href="group__power.html#gaf969c85c5655e3d72d7b99ff188f92c9">arm_math.h</a>
</li>
<li>arm_provide_guard_bits_q15()
: <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#ac8209be1b8081e833c3ec2e85ad2255b">arm_convolution_example/GCC/math_helper.h</a>
</li>
<li>arm_provide_guard_bits_q31()
: <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#aead320f821f927000386d9d7d5ad6d27">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#aead320f821f927000386d9d7d5ad6d27">arm_matrix_example/ARM/math_helper.h</a>
</li>
<li>arm_provide_guard_bits_q7()
: <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029">arm_convolution_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a392f7c2e7ab9bb58931c4efb56693029">arm_convolution_example/GCC/math_helper.c</a>
</li>
<li>PI
: <a class="el" href="arm__math_8h.html#a598a3330b3c21701223ee0ca14316eca">arm_math.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
