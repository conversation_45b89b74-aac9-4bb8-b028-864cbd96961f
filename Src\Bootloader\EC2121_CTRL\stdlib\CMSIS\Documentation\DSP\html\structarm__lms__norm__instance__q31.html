<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_lms_norm_instance_q31 Struct Reference</title>
<title>CMSIS-DSP: arm_lms_norm_instance_q31 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('structarm__lms__norm__instance__q31.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">arm_lms_norm_instance_q31 Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Instance structure for the Q31 normalized LMS filter.  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a28e4c085af69c9c3e2e95dacf8004c3e"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__lms__norm__instance__q31.html#a28e4c085af69c9c3e2e95dacf8004c3e">numTaps</a></td></tr>
<tr class="separator:a28e4c085af69c9c3e2e95dacf8004c3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b25c96cf048b77078d62f4252a01ec4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__lms__norm__instance__q31.html#a6b25c96cf048b77078d62f4252a01ec4">pState</a></td></tr>
<tr class="separator:a6b25c96cf048b77078d62f4252a01ec4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57a64c1ff102d033c1bd05043f1d9955"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__lms__norm__instance__q31.html#a57a64c1ff102d033c1bd05043f1d9955">pCoeffs</a></td></tr>
<tr class="separator:a57a64c1ff102d033c1bd05043f1d9955"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3dd2a2406e02fdaa7782ba6c3940a64"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__lms__norm__instance__q31.html#ad3dd2a2406e02fdaa7782ba6c3940a64">mu</a></td></tr>
<tr class="separator:ad3dd2a2406e02fdaa7782ba6c3940a64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28d7b9e437817f83397e081967e90f3c"><td class="memItemLeft" align="right" valign="top">uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__lms__norm__instance__q31.html#a28d7b9e437817f83397e081967e90f3c">postShift</a></td></tr>
<tr class="separator:a28d7b9e437817f83397e081967e90f3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85836d0907077b9ac660f7bbbaa9d694"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__lms__norm__instance__q31.html#a85836d0907077b9ac660f7bbbaa9d694">recipTable</a></td></tr>
<tr class="separator:a85836d0907077b9ac660f7bbbaa9d694"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c0ae42869afec8555dc8e3a7ef9b386"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__lms__norm__instance__q31.html#a3c0ae42869afec8555dc8e3a7ef9b386">energy</a></td></tr>
<tr class="separator:a3c0ae42869afec8555dc8e3a7ef9b386"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47c4466d644e0d8ba407995adfa9b917"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__lms__norm__instance__q31.html#a47c4466d644e0d8ba407995adfa9b917">x0</a></td></tr>
<tr class="separator:a47c4466d644e0d8ba407995adfa9b917"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="a3c0ae42869afec8555dc8e3a7ef9b386"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> arm_lms_norm_instance_q31::energy</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>saves previous frame energy. </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>, and <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ad3dd2a2406e02fdaa7782ba6c3940a64"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> arm_lms_norm_instance_q31::mu</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>step size that controls filter coefficient updates. </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>, and <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a28e4c085af69c9c3e2e95dacf8004c3e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t arm_lms_norm_instance_q31::numTaps</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>number of coefficients in the filter. </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>, and <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a57a64c1ff102d033c1bd05043f1d9955"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>* arm_lms_norm_instance_q31::pCoeffs</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the coefficient array. The array is of length numTaps. </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>, and <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a28d7b9e437817f83397e081967e90f3c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t arm_lms_norm_instance_q31::postShift</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>bit shift applied to coefficients. </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>, and <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a6b25c96cf048b77078d62f4252a01ec4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>* arm_lms_norm_instance_q31::pState</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the state variable array. The array is of length numTaps+blockSize-1. </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>, and <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a85836d0907077b9ac660f7bbbaa9d694"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>* arm_lms_norm_instance_q31::recipTable</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the reciprocal initial value table. </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>, and <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="a47c4466d644e0d8ba407995adfa9b917"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> arm_lms_norm_instance_q31::x0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>saves previous input sample. </p>

<p>Referenced by <a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31()</a>, and <a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structarm__lms__norm__instance__q31.html">arm_lms_norm_instance_q31</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
