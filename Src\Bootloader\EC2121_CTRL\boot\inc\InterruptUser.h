#ifndef __INTERRUPT_H
#define __INTERRUPT_H


//中断处理函数在启动文件里面定义了的，如果在其他文件中没有用到的话，可以不需要头文件，
/*
extern "C" void OTG_FS_IRQHandler(void);
extern "C" void TIM1_CC_IRQHandler(void);
extern "C" void TIM2_IRQHandler(void);
extern "C" void TIM3_IRQHandler(void);
extern "C" void TIM4_IRQHandler(void);
extern "C" void TIM6_IRQHandler(void);
extern "C" void TIM7_IRQHandler(void);
//extern "C" void CAN2_SCE_IRQHandler(void);
extern "C" void USART1_IRQHandler(void);
extern "C" void USART2_IRQHandler(void);
extern "C" void USART3_IRQHandler(void);
extern "C" void UART4_IRQ<PERSON><PERSON><PERSON>(void);
extern "C" void UART5_IR<PERSON><PERSON><PERSON><PERSON>(void);
*/
/*
typedef struct
{
    void*     BaseAddr;                 //设备寄存器基地址
    uint32_t  INTERRUPT_SOURCE;             //中断源
}INTERRUPT_SOURCE_MANAGER;
*/
enum
{
    INTERRUPT_SOURCE_TIM1,
    INTERRUPT_SOURCE_TIM2,
    INTERRUPT_SOURCE_TIM3,
    INTERRUPT_SOURCE_TIM4,
    INTERRUPT_SOURCE_TIM5,
    INTERRUPT_SOURCE_TIM6,
    INTERRUPT_SOURCE_TIM7,//tim7用作时间戳，不隔离
    INTERRUPT_SOURCE_TIM8,
    INTERRUPT_SOURCE_TIM9,
    INTERRUPT_SOURCE_TIM10,
    INTERRUPT_SOURCE_TIM11,
    INTERRUPT_SOURCE_TIM12,
    INTERRUPT_SOURCE_TIM13,
    INTERRUPT_SOURCE_TIM14,

    INTERRUPT_SOURCE_USART1_RX,//13
    INTERRUPT_SOURCE_USART1_TX,
    INTERRUPT_SOURCE_USART2_RX,
    INTERRUPT_SOURCE_USART2_TX,
    INTERRUPT_SOURCE_USART3_RX,
    INTERRUPT_SOURCE_USART3_TX,

    INTERRUPT_SOURCE_UART4_RX,
    INTERRUPT_SOURCE_UART4_TX,
    INTERRUPT_SOURCE_UART5_RX,
    INTERRUPT_SOURCE_UART5_TX,

    INTERRUPT_SOURCE_USART6_RX,
    INTERRUPT_SOURCE_USART6_TX,

    INTERRUPT_SOURCE_MCAN1_RX_CALLBACK,//芯片can
    INTERRUPT_SOURCE_MCAN1_TX_CALLBACK,
    INTERRUPT_SOURCE_MCAN2_RX_CALLBACK,//
    INTERRUPT_SOURCE_MCAN2_TX_CALLBACK


    
};

extern  void InterruptHandler(void *p);
extern  void SendIntSourceToHandle(uint32_t IntSource);


#endif

