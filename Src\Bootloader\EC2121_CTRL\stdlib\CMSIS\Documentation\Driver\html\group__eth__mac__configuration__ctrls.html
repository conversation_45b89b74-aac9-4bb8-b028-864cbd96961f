<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Ethernet MAC Configuration</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__eth__mac__configuration__ctrls.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">Ethernet MAC Configuration<div class="ingroups"><a class="el" href="group__eth__mac__control.html">Ethernet MAC Control Codes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Specifies speed mode, link mode, checksum, and frame filtering modes.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga8c5b40d018ecfad05fe2546ba717c1d4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga8c5b40d018ecfad05fe2546ba717c1d4">ARM_ETH_MAC_SPEED_10M</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a1f834c4c785d7f69b1eaca011ee298ec">ARM_ETH_SPEED_10M</a>   &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#ad7fd5c5f4d4f39a56466c2d34cb699ef">ARM_ETH_MAC_SPEED_Pos</a>)</td></tr>
<tr class="memdesc:ga8c5b40d018ecfad05fe2546ba717c1d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">10 Mbps link speed  <a href="#ga8c5b40d018ecfad05fe2546ba717c1d4">More...</a><br/></td></tr>
<tr class="separator:ga8c5b40d018ecfad05fe2546ba717c1d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga29160c83a7b0952c64053d86789c6490"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga29160c83a7b0952c64053d86789c6490">ARM_ETH_MAC_SPEED_100M</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a3bddfc4cf5645f8568d9cb6621fd606a">ARM_ETH_SPEED_100M</a>  &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#ad7fd5c5f4d4f39a56466c2d34cb699ef">ARM_ETH_MAC_SPEED_Pos</a>)</td></tr>
<tr class="memdesc:ga29160c83a7b0952c64053d86789c6490"><td class="mdescLeft">&#160;</td><td class="mdescRight">100 Mbps link speed  <a href="#ga29160c83a7b0952c64053d86789c6490">More...</a><br/></td></tr>
<tr class="separator:ga29160c83a7b0952c64053d86789c6490"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8acefed744d8397a1777b9fd0e6230d2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga8acefed744d8397a1777b9fd0e6230d2">ARM_ETH_MAC_SPEED_1G</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a218f470079b7c04de6776d467a53772a">ARM_ETH_SPEED_1G</a>    &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#ad7fd5c5f4d4f39a56466c2d34cb699ef">ARM_ETH_MAC_SPEED_Pos</a>)</td></tr>
<tr class="memdesc:ga8acefed744d8397a1777b9fd0e6230d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 Gpbs link speed  <a href="#ga8acefed744d8397a1777b9fd0e6230d2">More...</a><br/></td></tr>
<tr class="separator:ga8acefed744d8397a1777b9fd0e6230d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadb0fe2c5a1e21b0656d39c788ae22f36"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#gadb0fe2c5a1e21b0656d39c788ae22f36">ARM_ETH_MAC_DUPLEX_HALF</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#acb15afc2bfe61c56049b7279d6eae8fe">ARM_ETH_DUPLEX_HALF</a> &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#a245688f6265e8d017435dc6d2c233b87">ARM_ETH_MAC_DUPLEX_Pos</a>)</td></tr>
<tr class="memdesc:gadb0fe2c5a1e21b0656d39c788ae22f36"><td class="mdescLeft">&#160;</td><td class="mdescRight">Half duplex link.  <a href="#gadb0fe2c5a1e21b0656d39c788ae22f36">More...</a><br/></td></tr>
<tr class="separator:gadb0fe2c5a1e21b0656d39c788ae22f36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad5a7d4b5b8a31825eff1504e3828d8f6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#gad5a7d4b5b8a31825eff1504e3828d8f6">ARM_ETH_MAC_DUPLEX_FULL</a>&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a7848c83cd1fd6b2645c17919c2990354">ARM_ETH_DUPLEX_FULL</a> &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#a245688f6265e8d017435dc6d2c233b87">ARM_ETH_MAC_DUPLEX_Pos</a>)</td></tr>
<tr class="memdesc:gad5a7d4b5b8a31825eff1504e3828d8f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Full duplex link.  <a href="#gad5a7d4b5b8a31825eff1504e3828d8f6">More...</a><br/></td></tr>
<tr class="separator:gad5a7d4b5b8a31825eff1504e3828d8f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab32765f35c35b672ee476278fe24a24e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#gab32765f35c35b672ee476278fe24a24e">ARM_ETH_MAC_LOOPBACK</a>&#160;&#160;&#160;(1UL &lt;&lt; 4)</td></tr>
<tr class="memdesc:gab32765f35c35b672ee476278fe24a24e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Loop-back test mode.  <a href="#gab32765f35c35b672ee476278fe24a24e">More...</a><br/></td></tr>
<tr class="separator:gab32765f35c35b672ee476278fe24a24e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga281dfed993b5666ed999709b9f28578f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga281dfed993b5666ed999709b9f28578f">ARM_ETH_MAC_CHECKSUM_OFFLOAD_RX</a>&#160;&#160;&#160;(1UL &lt;&lt; 5)</td></tr>
<tr class="memdesc:ga281dfed993b5666ed999709b9f28578f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Receiver Checksum offload.  <a href="#ga281dfed993b5666ed999709b9f28578f">More...</a><br/></td></tr>
<tr class="separator:ga281dfed993b5666ed999709b9f28578f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7272d2c55aeeeadbb95c591cbf6c1a2e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga7272d2c55aeeeadbb95c591cbf6c1a2e">ARM_ETH_MAC_CHECKSUM_OFFLOAD_TX</a>&#160;&#160;&#160;(1UL &lt;&lt; 6)</td></tr>
<tr class="memdesc:ga7272d2c55aeeeadbb95c591cbf6c1a2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transmitter Checksum offload.  <a href="#ga7272d2c55aeeeadbb95c591cbf6c1a2e">More...</a><br/></td></tr>
<tr class="separator:ga7272d2c55aeeeadbb95c591cbf6c1a2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43792feab641c3c87eafb943351ab0f4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga43792feab641c3c87eafb943351ab0f4">ARM_ETH_MAC_ADDRESS_BROADCAST</a>&#160;&#160;&#160;(1UL &lt;&lt; 7)</td></tr>
<tr class="memdesc:ga43792feab641c3c87eafb943351ab0f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accept frames with Broadcast address.  <a href="#ga43792feab641c3c87eafb943351ab0f4">More...</a><br/></td></tr>
<tr class="separator:ga43792feab641c3c87eafb943351ab0f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1d3ff8c63362b385548fe91730f20588"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga1d3ff8c63362b385548fe91730f20588">ARM_ETH_MAC_ADDRESS_MULTICAST</a>&#160;&#160;&#160;(1UL &lt;&lt; 8)</td></tr>
<tr class="memdesc:ga1d3ff8c63362b385548fe91730f20588"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accept frames with any Multicast address.  <a href="#ga1d3ff8c63362b385548fe91730f20588">More...</a><br/></td></tr>
<tr class="separator:ga1d3ff8c63362b385548fe91730f20588"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab29ab9e295807f4c59ddd1c4642086d1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__configuration__ctrls.html#gab29ab9e295807f4c59ddd1c4642086d1">ARM_ETH_MAC_ADDRESS_ALL</a>&#160;&#160;&#160;(1UL &lt;&lt; 9)</td></tr>
<tr class="memdesc:gab29ab9e295807f4c59ddd1c4642086d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accept frames with any address (Promiscuous Mode)  <a href="#gab29ab9e295807f4c59ddd1c4642086d1">More...</a><br/></td></tr>
<tr class="separator:gab29ab9e295807f4c59ddd1c4642086d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Specifies speed mode, link mode, checksum, and frame filtering modes. </p>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a> with <em>control</em> = <a class="el" href="group__eth__mac__ctrls.html#ga7819c7a1aa7bbc13dc42d0fd7e75a23c">ARM_ETH_MAC_CONFIGURE</a> configures the Ethernet MAC interface as specified with <em>arg</em> listed bellow. </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="gab29ab9e295807f4c59ddd1c4642086d1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_ADDRESS_ALL&#160;&#160;&#160;(1UL &lt;&lt; 9)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Accept frames with any address (Promiscuous Mode) </p>

</div>
</div>
<a class="anchor" id="ga43792feab641c3c87eafb943351ab0f4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_ADDRESS_BROADCAST&#160;&#160;&#160;(1UL &lt;&lt; 7)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Accept frames with Broadcast address. </p>

</div>
</div>
<a class="anchor" id="ga1d3ff8c63362b385548fe91730f20588"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_ADDRESS_MULTICAST&#160;&#160;&#160;(1UL &lt;&lt; 8)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Accept frames with any Multicast address. </p>

</div>
</div>
<a class="anchor" id="ga281dfed993b5666ed999709b9f28578f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_CHECKSUM_OFFLOAD_RX&#160;&#160;&#160;(1UL &lt;&lt; 5)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Receiver Checksum offload. </p>

</div>
</div>
<a class="anchor" id="ga7272d2c55aeeeadbb95c591cbf6c1a2e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_CHECKSUM_OFFLOAD_TX&#160;&#160;&#160;(1UL &lt;&lt; 6)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transmitter Checksum offload. </p>

</div>
</div>
<a class="anchor" id="gad5a7d4b5b8a31825eff1504e3828d8f6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_DUPLEX_FULL&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a7848c83cd1fd6b2645c17919c2990354">ARM_ETH_DUPLEX_FULL</a> &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#a245688f6265e8d017435dc6d2c233b87">ARM_ETH_MAC_DUPLEX_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Full duplex link. </p>

</div>
</div>
<a class="anchor" id="gadb0fe2c5a1e21b0656d39c788ae22f36"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_DUPLEX_HALF&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#acb15afc2bfe61c56049b7279d6eae8fe">ARM_ETH_DUPLEX_HALF</a> &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#a245688f6265e8d017435dc6d2c233b87">ARM_ETH_MAC_DUPLEX_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Half duplex link. </p>

</div>
</div>
<a class="anchor" id="gab32765f35c35b672ee476278fe24a24e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_LOOPBACK&#160;&#160;&#160;(1UL &lt;&lt; 4)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Loop-back test mode. </p>

</div>
</div>
<a class="anchor" id="ga29160c83a7b0952c64053d86789c6490"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_SPEED_100M&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a3bddfc4cf5645f8568d9cb6621fd606a">ARM_ETH_SPEED_100M</a>  &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#ad7fd5c5f4d4f39a56466c2d34cb699ef">ARM_ETH_MAC_SPEED_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>100 Mbps link speed </p>

</div>
</div>
<a class="anchor" id="ga8c5b40d018ecfad05fe2546ba717c1d4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_SPEED_10M&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a1f834c4c785d7f69b1eaca011ee298ec">ARM_ETH_SPEED_10M</a>   &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#ad7fd5c5f4d4f39a56466c2d34cb699ef">ARM_ETH_MAC_SPEED_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>10 Mbps link speed </p>

</div>
</div>
<a class="anchor" id="ga8acefed744d8397a1777b9fd0e6230d2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_SPEED_1G&#160;&#160;&#160;(<a class="el" href="_driver___e_t_h_8h.html#a218f470079b7c04de6776d467a53772a">ARM_ETH_SPEED_1G</a>    &lt;&lt; <a class="el" href="_driver___e_t_h___m_a_c_8h.html#ad7fd5c5f4d4f39a56466c2d34cb699ef">ARM_ETH_MAC_SPEED_Pos</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>1 Gpbs link speed </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
