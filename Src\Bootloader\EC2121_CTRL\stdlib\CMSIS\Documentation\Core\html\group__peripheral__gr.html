<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Peripheral Access</title>
<title>CMSIS-CORE: Peripheral Access</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__peripheral__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Peripheral Access</div>  </div>
</div><!--header-->
<div class="contents">

<p>Describes naming conventions, requirements, and optional features for accessing peripherals.  
<a href="#details">More...</a></p>
<p>Each peripheral provides a data type definition with a name that is composed of a prefix <b>&lt;<em>device abbreviation&gt;</em>_</b> and the <b>&lt;<em>peripheral name</em>&gt;_</b>, for example <b>LPC_UART</b> for the device <b>LPC</b> and the peripheral <b>UART</b>. The intention is to avoid name collisions caused by short names. If more peripherals exist of the same type, identifiers have a postfix consisting of a digit or letter, for example <b>LPC_UART0</b>, <b>LPC_UART1</b>.</p>
<ul>
<li>The data type definition uses the standard C data types from the ANSI C header file &lt;stdint.h&gt;. IO Type Qualifiers are used to specify the access to peripheral variables. IO Type Qualifiers are indented to be used for automatic generation of debug information of peripheral registers and are defined as shown below:<br/>
 <div class="fragment"><div class="line"><span class="preprocessor">#define   __I     volatile const       </span></div>
<div class="line"><span class="preprocessor">#define   __O     volatile             </span></div>
<div class="line"><span class="preprocessor">#define   __IO    volatile             </span></div>
</div><!-- fragment --></li>
</ul>
<ul>
<li>The following typedef is an example for a UART. &lt;<em>device abbreviation</em>&gt;_UART_TypeDef: defines the generic register layout for all UART channels in a device. <br/>
 <div class="fragment"><div class="line"><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line">{</div>
<div class="line">  <span class="keyword">union </span>{</div>
<div class="line">  __I  uint8_t  RBR;                  <span class="comment">/* Offset: 0x000 (R/ )  Receiver Buffer Register            */</span></div>
<div class="line">  __O  uint8_t  THR;                  <span class="comment">/* Offset: 0x000 ( /W)  Transmit Holding Register           */</span></div>
<div class="line">  __IO uint8_t  DLL;                  <span class="comment">/* Offset: 0x000 (R/W)  Divisor Latch LSB                   */</span></div>
<div class="line">       uint32_t RESERVED0;</div>
<div class="line">  };</div>
<div class="line">  <span class="keyword">union </span>{</div>
<div class="line">  __IO uint8_t  DLM;                  <span class="comment">/* Offset: 0x004 (R/W)  Divisor Latch MSB                   */</span></div>
<div class="line">  __IO uint32_t IER;                  <span class="comment">/* Offset: 0x004 (R/W)  Interrupt Enable Register           */</span></div>
<div class="line">  };</div>
<div class="line">  <span class="keyword">union </span>{</div>
<div class="line">  __I  uint32_t IIR;                  <span class="comment">/* Offset: 0x008 (R/ )  Interrupt ID Register               */</span></div>
<div class="line">  __O  uint8_t  FCR;                  <span class="comment">/* Offset: 0x008 ( /W)  FIFO Control Register               */</span></div>
<div class="line">  };</div>
<div class="line">  __IO uint8_t  LCR;                  <span class="comment">/* Offset: 0x00C (R/W)  Line Control Register               */</span></div>
<div class="line">       uint8_t  RESERVED1[7];</div>
<div class="line">  __I  uint8_t  LSR;                  <span class="comment">/* Offset: 0x014 (R/ )  Line Status Register                */</span></div>
<div class="line">       uint8_t  RESERVED2[7];</div>
<div class="line">  __IO uint8_t  SCR;                  <span class="comment">/* Offset: 0x01C (R/W)  Scratch Pad Register                */</span></div>
<div class="line">       uint8_t  RESERVED3[3];</div>
<div class="line">  __IO uint32_t ACR;                  <span class="comment">/* Offset: 0x020 (R/W)  Autobaud Control Register           */</span></div>
<div class="line">  __IO uint8_t  ICR;                  <span class="comment">/* Offset: 0x024 (R/W)  IrDA Control Register               */</span></div>
<div class="line">       uint8_t  RESERVED4[3];</div>
<div class="line">  __IO uint8_t  FDR;                  <span class="comment">/* Offset: 0x028 (R/W)  Fractional Divider Register         */</span></div>
<div class="line">       uint8_t  RESERVED5[7];</div>
<div class="line">  __IO uint8_t  TER;                  <span class="comment">/* Offset: 0x030 (R/W)  Transmit Enable Register            */</span></div>
<div class="line">       uint8_t  RESERVED6[39];</div>
<div class="line">  __I  uint8_t  FIFOLVL;              <span class="comment">/* Offset: 0x058 (R/ )  FIFO Level Register                 */</span></div>
<div class="line">} LPC_UART_TypeDef;</div>
</div><!-- fragment --></li>
</ul>
<ul>
<li>To access the registers of the UART defined above, pointers to a register structure are defined. In this example &lt;<em>device abbreviation</em>&gt;_UART# are two pointers to UARTs defined with above register structure. <br/>
 <div class="fragment"><div class="line"><span class="preprocessor">#define LPC_UART2             ((LPC_UART_TypeDef      *) LPC_UART2_BASE    )</span></div>
<div class="line"><span class="preprocessor">#define LPC_UART3             ((LPC_UART_TypeDef      *) LPC_UART3_BASE    )</span></div>
</div><!-- fragment --></li>
</ul>
<ul>
<li>The registers in the various UARTs can now be referred in the user code as shown below:<br/>
 <div class="fragment"><div class="line">LPC_UART1-&gt;DR   <span class="comment">// is the data register of UART1.</span></div>
</div><!-- fragment --></li>
</ul>
<hr/>
<h1><a class="anchor" id="core_cmsis_pal_min_reqs"></a>
Minimal Requirements</h1>
<p>To access the peripheral registers and related function in a device, the files <b><em>device.h</em></b> and <b>core_cm<em>#</em>.h</b> define as a minimum: <br/>
<br/>
</p>
<ul>
<li>The <b>Register Layout Typedef</b> for each peripheral that defines all register names. RESERVED is used to introduce space into the structure for adjusting the addresses of the peripheral registers. <br/>
<br/>
 <b>Example:</b> <div class="fragment"><div class="line"><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line">{</div>
<div class="line">  __IO uint32_t CTRL;                 <span class="comment">/* Offset: 0x000 (R/W)  SysTick Control and Status Register */</span></div>
<div class="line">  __IO uint32_t LOAD;                 <span class="comment">/* Offset: 0x004 (R/W)  SysTick Reload Value Register       */</span></div>
<div class="line">  __IO uint32_t VAL;                  <span class="comment">/* Offset: 0x008 (R/W)  SysTick Current Value Register      */</span></div>
<div class="line">  __I  uint32_t CALIB;                <span class="comment">/* Offset: 0x00C (R/ )  SysTick Calibration Register        */</span></div>
<div class="line">} <a class="code" href="struct_sys_tick___type.html" title="Structure type to access the System Timer (SysTick).">SysTick_Type</a>;</div>
</div><!-- fragment --></li>
</ul>
<ul>
<li><b>Base Address</b> for each peripheral (in case of multiple peripherals that use the same <b>register layout typedef</b> multiple base addresses are defined). <br/>
<br/>
 <b>Example:</b> <div class="fragment"><div class="line"><span class="preprocessor">#define SysTick_BASE (SCS_BASE + 0x0010)            </span><span class="comment">/* SysTick Base Address     */</span><span class="preprocessor">    </span></div>
</div><!-- fragment --></li>
</ul>
<ul>
<li><b>Access Definitions</b> for each peripheral. In case of multiple peripherals that are using the same <b>register layout typdef</b>, multiple access definitions exist (LPC_UART0, LPC_UART2). <br/>
<br/>
 <b>Example:</b> <div class="fragment"><div class="line"><span class="preprocessor">#define SysTick ((SysTick_Type *) Systick_BASE)    </span><span class="comment">/* SysTick access definition */</span><span class="preprocessor"></span></div>
</div><!-- fragment --></li>
</ul>
<p>These definitions allow accessing peripheral registers with simple assignments.</p>
<p><b>Example:</b> <br/>
 </p>
<div class="fragment"><div class="line">SysTick-&gt;CTRL = 0;    </div>
</div><!-- fragment --><hr/>
<h1><a class="anchor" id="core_cmsis_pal_opts"></a>
Optional Features</h1>
<p>Optionally, the file <b><em>device</em>.h</b> may define:</p>
<ul>
<li>#define constants, which simplify access to peripheral registers. These constants define bit-positions or other specific patterns that are required for programming peripheral registers. The identifiers start with <b>&lt;<em>device abbreviation</em>&gt;_</b> and <b>&lt;<em>peripheral name</em>&gt;_</b>. It is recommended to use CAPITAL letters for such #define constants.</li>
</ul>
<ul>
<li>More complex functions (i.e. status query before a sending register is accessed). Again, these functions start with <b>&lt;<em>device abbreviation</em>&gt;_</b> and <b>&lt;<em>peripheral name</em>&gt;_</b>. </li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
