var group__group_filters =
[
    [ "High Precision Q31 Biquad Cascade Filter", "group___biquad_cascade_d_f1__32x64.html", "group___biquad_cascade_d_f1__32x64" ],
    [ "Biquad Cascade IIR Filters Using Direct Form I Structure", "group___biquad_cascade_d_f1.html", "group___biquad_cascade_d_f1" ],
    [ "Biquad Cascade IIR Filters Using a Direct Form II Transposed Structure", "group___biquad_cascade_d_f2_t.html", "group___biquad_cascade_d_f2_t" ],
    [ "Convolution", "group___conv.html", "group___conv" ],
    [ "Partial Convolution", "group___partial_conv.html", "group___partial_conv" ],
    [ "Correlation", "group___corr.html", "group___corr" ],
    [ "Finite Impulse Response (FIR) Decimator", "group___f_i_r__decimate.html", "group___f_i_r__decimate" ],
    [ "Finite Impulse Response (FIR) Filters", "group___f_i_r.html", "group___f_i_r" ],
    [ "Finite Impulse Response (FIR) Lattice Filters", "group___f_i_r___lattice.html", "group___f_i_r___lattice" ],
    [ "Finite Impulse Response (FIR) Sparse Filters", "group___f_i_r___sparse.html", "group___f_i_r___sparse" ],
    [ "Infinite Impulse Response (IIR) Lattice Filters", "group___i_i_r___lattice.html", "group___i_i_r___lattice" ],
    [ "Least Mean Square (LMS) Filters", "group___l_m_s.html", "group___l_m_s" ],
    [ "Normalized LMS Filters", "group___l_m_s___n_o_r_m.html", "group___l_m_s___n_o_r_m" ],
    [ "Finite Impulse Response (FIR) Interpolator", "group___f_i_r___interpolate.html", "group___f_i_r___interpolate" ]
];