/******************************************************************************

Copyright (c) 2020, AUTEL Ltd,.Co

All rights reserved.

2020.4.x A19173 guanqitai V1.0

*******************************************************************************/

#ifndef __FLASH_EXTERNAL_H
#define __FLASH_EXTERNAL_H

#include "stdio.h"
#include "stdarg.h"
#include "stdint.h"

#include "stm32f4xx_gpio.h"
#include "stm32f4xx_spi.h"

#define FLASH_SPIx                        SPI3
#define FLASH_SPI_APBxClock_FUN           RCC_APB1PeriphClockCmd
#define FLASH_SPI_CLK                     RCC_APB1Periph_SPI3

#define FLASH_SPI_SCK_APBxClock_FUN       RCC_AHB1PeriphClockCmd
#define FLASH_SPI_SCK_CLK                 RCC_AHB1Periph_GPIOB
#define FLASH_SPI_SCK_PORT                GPIOB
#define FLASH_SPI_SCK_PIN                 GPIO_Pin_3
#define FLASH_SPI_SCK_SOURCE              GPIO_PinSource3

#define FLASH_SPI_MISO_APBxClock_FUN      RCC_AHB1PeriphClockCmd
#define FLASH_SPI_MISO_CLK                RCC_AHB1Periph_GPIOB
#define FLASH_SPI_MISO_PORT               GPIOB
#define FLASH_SPI_MISO_PIN                GPIO_Pin_4
#define FLASH_SPI_MISO_SOURCE             GPIO_PinSource4

#define FLASH_SPI_MOSI_APBxClock_FUN      RCC_AHB1PeriphClockCmd
#define FLASH_SPI_MOSI_CLK                RCC_AHB1Periph_GPIOB
#define FLASH_SPI_MOSI_PORT               GPIOB
#define FLASH_SPI_MOSI_PIN                GPIO_Pin_5
#define FLASH_SPI_MOSI_SOURCE             GPIO_PinSource5

#define FLASH_SPI_CS_APBxClock_FUN        RCC_AHB1PeriphClockCmd
#define FLASH_SPI_CS_CLK                  RCC_AHB1Periph_GPIOB
#define FLASH_SPI_CS_PORT                 GPIOB
#define FLASH_SPI_CS_PIN                  GPIO_Pin_11

#define FLASH_SPI_CS_ENABLE()             GPIO_ResetBits(FLASH_SPI_CS_PORT, FLASH_SPI_CS_PIN)
#define FLASH_SPI_CS_DISABLE()            GPIO_SetBits(FLASH_SPI_CS_PORT, FLASH_SPI_CS_PIN)


#define FLASH_SPI_CMD_WREN           0x06  /*!< Write enable instruction */
#define FLASH_SPI_CMD_WRDIS          0x04  /*!< Write disenable instruction */
#define FLASH_SPI_CMD_RDSR           0x05  /*!< Read Status Register instruction  */
#define FLASH_SPI_CMD_WRSR           0x01  /*!< Write Status Register instruction */
#define FLASH_SPI_CMD_READ           0x03  /*!< Read from Memory instruction */
#define FLASH_SPI_CMD_FARDA          0x0B  /*!< Fast read data instruction */
#define FLASH_SPI_CMD_FARDO          0x3B  /*!< Fast read dual output */
#define FLASH_SPI_CMD_WRITE          0x02  /*!< Write to Memory instruction */
#define FLASH_SPI_CMD_BLOCK          0xD8  /*!< Block Erase instruction */
#define FLASH_SPI_CMD_SE             0x20  /*!< Sector Erase instruction */
#define FLASH_SPI_CMD_CHIPE          0xC7  /*!< Chip Erase instruction */
#define FLASH_SPI_CMD_PDOWN          0xB9  /*!< Power down instruction */
#define FLASH_SPI_CMD_RESP           0xAB  /*!< Release power instruction */
#define FLASH_SPI_CMD_RDID           0x90  /*!< Read identification */


#define FLASH_SPI_WIP_FLAG           0x01  /*!< Write In Progress (WIP) flag */

#define FLASH_SPI_DUMMY_BYTE         0xFF
#define FLASH_SPI_PAGESIZE           0x100
#define FLASH_SPI_4PAGESIZE          0x400
#define FLASH_SPI_SECTOR_SIZE        0x1000

#define FLASH_SPI_W25Q128_ID         0xEF17
#define FLASH_SPI_W25Q64_ID          0xEF16

#define   FLASH_OK         0x00
#define   FLASH_ERROR      0x01

#define FLASH_SPI_SECTOR_SIZE           0x1000

void BSP_SERIAL_FLASH_Init(void);
void BSP_SERIAL_FLASH_DeInit(void);
uint8_t BSP_SERIAL_FLASH_WritePage(uint32_t uwStartAddress, uint8_t *pData, uint32_t uwDataSize);
uint8_t BSP_SERIAL_FLASH_WriteNoCheck(uint32_t uwStartAddress, uint8_t *pData, uint32_t uwDataSize);
uint8_t BSP_SERIAL_FLASH_WriteData(uint32_t uwStartAddress, uint8_t *pData, uint16_t uwDataSize);
uint8_t BSP_SERIAL_FLASH_EraseChip(void);
uint8_t BSP_SERIAL_FLASH_EraseSector(uint32_t SectorAddr);
uint8_t BSP_SERIAL_FLASH_ReadData(uint32_t uwStartAddress, uint8_t *pData, uint32_t uwDataSize);
uint32_t BSP_SERIAL_FLASH_ReadID(void);

#endif
