<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Core Register Access</title>
<title>CMSIS-CORE: Core Register Access</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___core___register__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Core Register Access</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga963cf236b73219ce78e965deb01b81a7"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga963cf236b73219ce78e965deb01b81a7">__get_CONTROL</a> (void)</td></tr>
<tr class="memdesc:ga963cf236b73219ce78e965deb01b81a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the CONTROL register.  <a href="#ga963cf236b73219ce78e965deb01b81a7"></a><br/></td></tr>
<tr class="separator:ga963cf236b73219ce78e965deb01b81a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac64d37e7ff9de06437f9fb94bbab8b6c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c">__set_CONTROL</a> (uint32_t control)</td></tr>
<tr class="memdesc:gac64d37e7ff9de06437f9fb94bbab8b6c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the CONTROL Register.  <a href="#gac64d37e7ff9de06437f9fb94bbab8b6c"></a><br/></td></tr>
<tr class="separator:gac64d37e7ff9de06437f9fb94bbab8b6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8">__get_IPSR</a> (void)</td></tr>
<tr class="memdesc:ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the IPSR register.  <a href="#ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8"></a><br/></td></tr>
<tr class="separator:ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga811c0012221ee918a75111ca84c4d5e7"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga811c0012221ee918a75111ca84c4d5e7">__get_APSR</a> (void)</td></tr>
<tr class="memdesc:ga811c0012221ee918a75111ca84c4d5e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the APSR register.  <a href="#ga811c0012221ee918a75111ca84c4d5e7"></a><br/></td></tr>
<tr class="separator:ga811c0012221ee918a75111ca84c4d5e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga732e08184154f44a617963cc65ff95bd"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga732e08184154f44a617963cc65ff95bd">__get_xPSR</a> (void)</td></tr>
<tr class="memdesc:ga732e08184154f44a617963cc65ff95bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the xPSR register.  <a href="#ga732e08184154f44a617963cc65ff95bd"></a><br/></td></tr>
<tr class="separator:ga732e08184154f44a617963cc65ff95bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga914dfa8eff7ca53380dd54cf1d8bebd9"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga914dfa8eff7ca53380dd54cf1d8bebd9">__get_PSP</a> (void)</td></tr>
<tr class="memdesc:ga914dfa8eff7ca53380dd54cf1d8bebd9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the PSP register.  <a href="#ga914dfa8eff7ca53380dd54cf1d8bebd9"></a><br/></td></tr>
<tr class="separator:ga914dfa8eff7ca53380dd54cf1d8bebd9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga48e5853f417e17a8a65080f6a605b743"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga48e5853f417e17a8a65080f6a605b743">__set_PSP</a> (uint32_t topOfProcStack)</td></tr>
<tr class="memdesc:ga48e5853f417e17a8a65080f6a605b743"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the PSP register.  <a href="#ga48e5853f417e17a8a65080f6a605b743"></a><br/></td></tr>
<tr class="separator:ga48e5853f417e17a8a65080f6a605b743"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab898559392ba027814e5bbb5a98b38d2"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#gab898559392ba027814e5bbb5a98b38d2">__get_MSP</a> (void)</td></tr>
<tr class="memdesc:gab898559392ba027814e5bbb5a98b38d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the MSP register.  <a href="#gab898559392ba027814e5bbb5a98b38d2"></a><br/></td></tr>
<tr class="separator:gab898559392ba027814e5bbb5a98b38d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0bf9564ebc1613a8faba014275dac2a4"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga0bf9564ebc1613a8faba014275dac2a4">__set_MSP</a> (uint32_t topOfMainStack)</td></tr>
<tr class="memdesc:ga0bf9564ebc1613a8faba014275dac2a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the MSP register.  <a href="#ga0bf9564ebc1613a8faba014275dac2a4"></a><br/></td></tr>
<tr class="separator:ga0bf9564ebc1613a8faba014275dac2a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga799b5d9a2ae75e459264c8512c7c0e02"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga799b5d9a2ae75e459264c8512c7c0e02">__get_PRIMASK</a> (void)</td></tr>
<tr class="memdesc:ga799b5d9a2ae75e459264c8512c7c0e02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the PRIMASK register bit.  <a href="#ga799b5d9a2ae75e459264c8512c7c0e02"></a><br/></td></tr>
<tr class="separator:ga799b5d9a2ae75e459264c8512c7c0e02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70b4e1a6c1c86eb913fb9d6e8400156f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga70b4e1a6c1c86eb913fb9d6e8400156f">__set_PRIMASK</a> (uint32_t priMask)</td></tr>
<tr class="memdesc:ga70b4e1a6c1c86eb913fb9d6e8400156f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the Priority Mask bit.  <a href="#ga70b4e1a6c1c86eb913fb9d6e8400156f"></a><br/></td></tr>
<tr class="separator:ga70b4e1a6c1c86eb913fb9d6e8400156f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga32da759f46e52c95bcfbde5012260667"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga32da759f46e52c95bcfbde5012260667">__get_BASEPRI</a> (void)</td></tr>
<tr class="memdesc:ga32da759f46e52c95bcfbde5012260667"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga32da759f46e52c95bcfbde5012260667"></a><br/></td></tr>
<tr class="separator:ga32da759f46e52c95bcfbde5012260667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga360c73eb7ffb16088556f9278953b882"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882">__set_BASEPRI</a> (uint32_t basePri)</td></tr>
<tr class="memdesc:ga360c73eb7ffb16088556f9278953b882"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga360c73eb7ffb16088556f9278953b882"></a><br/></td></tr>
<tr class="separator:ga360c73eb7ffb16088556f9278953b882"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga62fa63d39cf22df348857d5f44ab64d9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga62fa63d39cf22df348857d5f44ab64d9">__set_BASEPRI_MAX</a> (uint32_t basePri)</td></tr>
<tr class="memdesc:ga62fa63d39cf22df348857d5f44ab64d9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Increase the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga62fa63d39cf22df348857d5f44ab64d9"></a><br/></td></tr>
<tr class="separator:ga62fa63d39cf22df348857d5f44ab64d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa78e4e6bf619a65e9f01b4af13fed3a8"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#gaa78e4e6bf619a65e9f01b4af13fed3a8">__get_FAULTMASK</a> (void)</td></tr>
<tr class="memdesc:gaa78e4e6bf619a65e9f01b4af13fed3a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gaa78e4e6bf619a65e9f01b4af13fed3a8"></a><br/></td></tr>
<tr class="separator:gaa78e4e6bf619a65e9f01b4af13fed3a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5587cc09031053a40a35c14ec36078a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#gaa5587cc09031053a40a35c14ec36078a">__set_FAULTMASK</a> (uint32_t faultMask)</td></tr>
<tr class="memdesc:gaa5587cc09031053a40a35c14ec36078a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gaa5587cc09031053a40a35c14ec36078a"></a><br/></td></tr>
<tr class="separator:gaa5587cc09031053a40a35c14ec36078a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6d7eca9ddd1d9072dd7b020cfe64905"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#gad6d7eca9ddd1d9072dd7b020cfe64905">__get_FPSCR</a> (void)</td></tr>
<tr class="memdesc:gad6d7eca9ddd1d9072dd7b020cfe64905"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the FPSCR register [only Cortex-M4 and Cortex-M7].  <a href="#gad6d7eca9ddd1d9072dd7b020cfe64905"></a><br/></td></tr>
<tr class="separator:gad6d7eca9ddd1d9072dd7b020cfe64905"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6f26bd75ca7e3247f27b272acc10536b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga6f26bd75ca7e3247f27b272acc10536b">__set_FPSCR</a> (uint32_t fpscr)</td></tr>
<tr class="memdesc:ga6f26bd75ca7e3247f27b272acc10536b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the FPSC register [only for Cortex-M4 and Cortex-M7].  <a href="#ga6f26bd75ca7e3247f27b272acc10536b"></a><br/></td></tr>
<tr class="separator:ga6f26bd75ca7e3247f27b272acc10536b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f98dfbd252b89d12564472dbeba9c27"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga0f98dfbd252b89d12564472dbeba9c27">__enable_irq</a> (void)</td></tr>
<tr class="memdesc:ga0f98dfbd252b89d12564472dbeba9c27"><td class="mdescLeft">&#160;</td><td class="mdescRight">Globally enables interrupts and configurable fault handlers.  <a href="#ga0f98dfbd252b89d12564472dbeba9c27"></a><br/></td></tr>
<tr class="separator:ga0f98dfbd252b89d12564472dbeba9c27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeb8e5f7564a8ea23678fe3c987b04013"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#gaeb8e5f7564a8ea23678fe3c987b04013">__disable_irq</a> (void)</td></tr>
<tr class="memdesc:gaeb8e5f7564a8ea23678fe3c987b04013"><td class="mdescLeft">&#160;</td><td class="mdescRight">Globally disables interrupts and configurable fault handlers.  <a href="#gaeb8e5f7564a8ea23678fe3c987b04013"></a><br/></td></tr>
<tr class="separator:gaeb8e5f7564a8ea23678fe3c987b04013"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6575d37863cec5d334864f93b5b783bf"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga6575d37863cec5d334864f93b5b783bf">__enable_fault_irq</a> (void)</td></tr>
<tr class="memdesc:ga6575d37863cec5d334864f93b5b783bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enables interrupts and all fault handlers [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga6575d37863cec5d334864f93b5b783bf"></a><br/></td></tr>
<tr class="separator:ga6575d37863cec5d334864f93b5b783bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d174f979b2f76fdb3228a9b338fd939"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___core___register__gr.html#ga9d174f979b2f76fdb3228a9b338fd939">__disable_fault_irq</a> (void)</td></tr>
<tr class="memdesc:ga9d174f979b2f76fdb3228a9b338fd939"><td class="mdescLeft">&#160;</td><td class="mdescRight">Disables interrupts and all fault handlers [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga9d174f979b2f76fdb3228a9b338fd939"></a><br/></td></tr>
<tr class="separator:ga9d174f979b2f76fdb3228a9b338fd939"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>The following functions provide access to Cortex-M core registers. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga9d174f979b2f76fdb3228a9b338fd939"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __disable_fault_irq </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function disables interrupts and all fault handlers by setting FAULTMASK. The function uses the instruction <b>CPSID f</b>.</p>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>Can be executed in privileged mode only.</li>
<li>An interrupt can enter pending state even if it is disabled. Disabling an interrupt only prevents the processor from taking that interrupt.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga6575d37863cec5d334864f93b5b783bf">__enable_fault_irq</a>; <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882" title="Set the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c" title="Set the CONTROL Register.">__set_CONTROL</a>; <a class="el" href="group___core___register__gr.html#gaa5587cc09031053a40a35c14ec36078a" title="Set the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_FAULTMASK</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gaeb8e5f7564a8ea23678fe3c987b04013"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __disable_irq </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function disables interrupts and all configurable fault handlers by setting PRIMASK. The function uses the instruction <b>CPSID i</b>.</p>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Can be executed in privileged mode only.</li>
<li>An interrupt can enter pending state even if it is disabled. Disabling an interrupt only prevents the processor from taking that interrupt.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga0f98dfbd252b89d12564472dbeba9c27">__enable_irq</a>; <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882" title="Set the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c" title="Set the CONTROL Register.">__set_CONTROL</a>; <a class="el" href="group___core___register__gr.html#ga70b4e1a6c1c86eb913fb9d6e8400156f" title="Set the Priority Mask bit.">__set_PRIMASK</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga6575d37863cec5d334864f93b5b783bf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __enable_fault_irq </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function enables interrupts and all fault handlers by clearing FAULTMASK. The function uses the instruction <b>CPSIE f</b>.</p>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>Can be executed in privileged mode only.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga9d174f979b2f76fdb3228a9b338fd939">__disable_fault_irq</a>; <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882" title="Set the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c" title="Set the CONTROL Register.">__set_CONTROL</a>; <a class="el" href="group___core___register__gr.html#gaa5587cc09031053a40a35c14ec36078a" title="Set the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_FAULTMASK</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga0f98dfbd252b89d12564472dbeba9c27"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __enable_irq </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function enables interrupts and all configurable fault handlers by clearing PRIMASK. The function uses the instruction <b>CPSIE i</b>.</p>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Can be executed in privileged mode only.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#gaeb8e5f7564a8ea23678fe3c987b04013">__disable_irq</a>; <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882" title="Set the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c" title="Set the CONTROL Register.">__set_CONTROL</a>; <a class="el" href="group___core___register__gr.html#ga70b4e1a6c1c86eb913fb9d6e8400156f" title="Set the Priority Mask bit.">__set_PRIMASK</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga811c0012221ee918a75111ca84c4d5e7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_APSR </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the Application Program Status Register (APSR) using the instruction <b>MRS</b>. <br/>
<br/>
 The APSR contains the current state of the condition flags from instructions executed previously. The APSR is essential for controlling conditional branches. The following flags are used:</p>
<ul>
<li><b>N</b> (APSR[31]) (Negative flag)<ul>
<li>=1 The instruction result has a negative value (when interpreted as signed integer).</li>
<li>=0 The instruction result has a positive value or equal zero. <br/>
<br/>
</li>
</ul>
</li>
<li><b>Z</b> (APSR[30]) (Zero flag)<ul>
<li>=1 The instruction result is zero. Or, after a compare instruction, when the two values are the same. <br/>
<br/>
</li>
</ul>
</li>
<li><b>C</b> (APSR[29]) (Carry or borrow flag)<ul>
<li>=1 For unsigned additions, if an unsigned overflow occurred.</li>
<li>=<em>inverse of borrow output status</em> For unsigned subtract operations. <br/>
<br/>
</li>
</ul>
</li>
<li><b>V</b> (APSR[28]) (Overflow flag)<ul>
<li>=1 A signed overflow occurred (for signed additions or subtractions). <br/>
<br/>
</li>
</ul>
</li>
<li><b>Q</b> (APSR[27]) (DSP overflow or saturation flag) [not Cortex-M0]<ul>
<li>This flag is a <em>sticky</em> flag. Saturating and certain mutliplying instructions can set the flag, but cannot clear it.</li>
<li>=1 When saturation or an overflow occurred. <br/>
<br/>
</li>
</ul>
</li>
<li><b>GE</b> (APSR[19:16]) (Greater than or Equal flags) [not Cortex-M0]<ul>
<li>Can be set by the parallel add and subtract instructions.</li>
<li>Are used by the <code>SEL</code> instruction to perform byte-based selection from two registers.</li>
</ul>
</li>
</ul>
<dl class="section return"><dt>Returns</dt><dd>APSR register value</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Some instructions update all flags; some instructions update a subset of the flags.</li>
<li>If a flag is not updated, the original value is preserved.</li>
<li>Conditional instructions that are not executed have no effect on the flags.</li>
<li>The CMSIS does not provide a function to update this register.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga732e08184154f44a617963cc65ff95bd">__get_xPSR</a>; <a class="el" href="union_a_p_s_r___type.html" title="Union type to access the Application Program Status Register (APSR).">APSR_Type</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga32da759f46e52c95bcfbde5012260667"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_BASEPRI </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function returns the Base Priority Mask register (BASEPRI) using the instruction <b>MRS</b>. <br/>
<br/>
 BASEPRI defines the minimum priority for exception processing. When BASEPRI is set to a non-zero value, it prevents the activation of all exceptions with the same or lower priority level as the BASEPRI value.</p>
<dl class="section return"><dt>Returns</dt><dd>BASEPRI register value</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Not for Cortex-M0, Cortex-M0+, or SC000.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882">__set_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#ga62fa63d39cf22df348857d5f44ab64d9" title="Increase the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI_MAX</a>; <a class="el" href="group___core___register__gr.html#gaa78e4e6bf619a65e9f01b4af13fed3a8" title="Read the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].">__get_FAULTMASK</a>; <a class="el" href="group___core___register__gr.html#ga799b5d9a2ae75e459264c8512c7c0e02" title="Read the PRIMASK register bit.">__get_PRIMASK</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga963cf236b73219ce78e965deb01b81a7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_CONTROL </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the CONTROL register value using the instruction <b>MRS</b>. <br/>
<br/>
 The CONTROL register controls the stack used and the privilege level for software execution when the processor is in thread mode and, if implemented, indicates whether the FPU state is active. This register uses the following bits: <br/>
</p>
<ul>
<li><b>CONTROL</b>[2] [only Cortex-M4 and Cortex-M7]<ul>
<li>=0 FPU not active</li>
<li>=1 FPU active <br/>
<br/>
</li>
</ul>
</li>
<li><b>CONTROL</b>[1]<ul>
<li>=0 In handler mode - MSP is selected. No alternate stack possible for handler mode.</li>
<li>=0 In thread mode - Default stack pointer MSP is used.</li>
<li>=1 In thread mode - Alternate stack pointer PSP is used. <br/>
<br/>
</li>
</ul>
</li>
<li><b>CONTROL</b>[0] [not Cortex-M0]<ul>
<li>=0 In thread mode and privileged state.</li>
<li>=1 In thread mode and user state.</li>
</ul>
</li>
</ul>
<dl class="section return"><dt>Returns</dt><dd>CONTROL register value</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The processor can be in user state or privileged state when running in thread mode.</li>
<li>Exception handlers always run in privileged state.</li>
<li>On reset, the processor is in thread mode with privileged access rights.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c">__set_CONTROL</a>; <a class="el" href="union_c_o_n_t_r_o_l___type.html" title="Union type to access the Control Registers (CONTROL).">CONTROL_Type</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gaa78e4e6bf619a65e9f01b4af13fed3a8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_FAULTMASK </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the Fault Mask register (FAULTMASK) value using the instruction <b>MRS</b>. <br/>
<br/>
 FAULTMASK prevents activation of all exceptions except for the Non-Maskable Interrupt (NMI).</p>
<dl class="section return"><dt>Returns</dt><dd>FAULTMASK register value</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>Is cleared automatically upon exiting the exception handler, except when returning from the NMI handler.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#gaa5587cc09031053a40a35c14ec36078a">__set_FAULTMASK</a>; <a class="el" href="group___core___register__gr.html#ga32da759f46e52c95bcfbde5012260667" title="Read the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__get_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#ga799b5d9a2ae75e459264c8512c7c0e02" title="Read the PRIMASK register bit.">__get_PRIMASK</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gad6d7eca9ddd1d9072dd7b020cfe64905"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_FPSCR </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the Floating-Point Status Control Register (FPSCR) value. <br/>
<br/>
 FPSCR provides all necessary User level controls of the floating-point system.</p>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>FPSCR register value, when __FPU_PRESENT=1</li>
<li>=0, when __FPU_PRESENT=0</li>
</ul>
</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Only for Cortex-M4 and Cortex-M7.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga6f26bd75ca7e3247f27b272acc10536b">__set_FPSCR</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_IPSR </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the Interrupt Program Status Register (IPSR) using the instruction <b>MRS</b>. <br/>
<br/>
 The ISPR contains the exception type number of the current Interrupt Service Routine (ISR). Each exception has an assocciated unique IRQn number. The following bits are used:</p>
<ul>
<li><b>ISR_NUMBER</b> (IPSR[8:0])<ul>
<li>=0 Thread mode</li>
<li>=1 Reserved</li>
<li>=2 NMI</li>
<li>=3 HardFault</li>
<li>=4 MemManage</li>
<li>=5 BusFault</li>
<li>=6 UsageFault</li>
<li>=7-10 Reserved</li>
<li>=11 SVCall</li>
<li>=12 Reserved for Debug</li>
<li>=13 Reserved</li>
<li>=14 PendSV</li>
<li>=15 SysTick</li>
<li>=16 IRQ0</li>
<li>...</li>
<li>=n+15 IRQ(n-1)</li>
</ul>
</li>
</ul>
<dl class="section return"><dt>Returns</dt><dd>ISPR register value</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>This register is read-only.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga732e08184154f44a617963cc65ff95bd">__get_xPSR</a>; <a class="el" href="union_i_p_s_r___type.html" title="Union type to access the Interrupt Program Status Register (IPSR).">IPSR_Type</a></li>
<li><a class="el" href="group___n_v_i_c__gr.html">Interrupts and Exceptions (NVIC)</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gab898559392ba027814e5bbb5a98b38d2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_MSP </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the Main Status Pointer (MSP) value using the instruction <b>MRS</b>. <br/>
<br/>
 Physically two different stack pointers (SP) exist:</p>
<ul>
<li>The Main Stack Pointer (MSP) is the default stack pointer after reset. It is also used when running exception handlers (handler mode).</li>
<li>The Process Stack Pointer (PSP), which can be used only in thread mode.</li>
</ul>
<p>Register R13 banks the SP. The SP selection is determined by the bit[1] of the CONTROL register:</p>
<ul>
<li>=0 MSP is the current stack pointer. This is also the default SP. The initial value is loaded from the first 32-bit word of the vector table from the program memory.</li>
<li>=1 PSP is the current stack pointer. The initial value is undefined.</li>
</ul>
<dl class="section return"><dt>Returns</dt><dd>MSP Register value</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Only one of the two SPs is visible at a time.</li>
<li>For many applications, the system can completely rely on the MSP.</li>
<li>The PSP is normally used in designs with an OS where the stack memory for OS Kernel must be separated from the application code.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga0bf9564ebc1613a8faba014275dac2a4">__set_MSP</a>; <a class="el" href="group___core___register__gr.html#ga914dfa8eff7ca53380dd54cf1d8bebd9" title="Read the PSP register.">__get_PSP</a>; <a class="el" href="group___core___register__gr.html#ga963cf236b73219ce78e965deb01b81a7" title="Read the CONTROL register.">__get_CONTROL</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga799b5d9a2ae75e459264c8512c7c0e02"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_PRIMASK </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the Priority Mask register (PRIMASK) value using the instruction <b>MRS</b>. <br/>
<br/>
 PRIMASK is a 1-bit-wide interrupt mask register. When set, it blocks all interrupts apart from the non-maskable interrupt (NMI) and the hard fault exception. The PRIMASK prevents activation of all exceptions with configurable priority.</p>
<dl class="section return"><dt>Returns</dt><dd>PRIMASK register value<ul>
<li>=0 no effect</li>
<li>=1 prevents the activation of all exceptions with configurable priority</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga70b4e1a6c1c86eb913fb9d6e8400156f">__set_PRIMASK</a>; <a class="el" href="group___core___register__gr.html#ga32da759f46e52c95bcfbde5012260667" title="Read the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__get_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#gaa78e4e6bf619a65e9f01b4af13fed3a8" title="Read the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].">__get_FAULTMASK</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga914dfa8eff7ca53380dd54cf1d8bebd9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_PSP </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the Program Status Pointer (PSP) value using the instruction <b>MRS</b>. <br/>
<br/>
 Physically two different stack pointers (SP) exist:</p>
<ul>
<li>The Main Stack Pointer (MSP) is the default stack pointer after reset. It is also used when running exception handlers (handler mode).</li>
<li>The Process Stack Pointer (PSP), which can be used only in thread mode.</li>
</ul>
<p>Register R13 banks the SP. The SP selection is determined by the bit[1] of the CONTROL register:</p>
<ul>
<li>=0 MSP is the current stack pointer. This is also the default SP. The initial value is loaded from the first 32-bit word of the vector table from the program memory.</li>
<li>=1 PSP is the current stack pointer. The initial value is undefined.</li>
</ul>
<dl class="section return"><dt>Returns</dt><dd>PSP register value</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Only one of the two SPs is visible at a time.</li>
<li>For many applications, the system can completely rely on the MSP.</li>
<li>The PSP is normally used in designs with an OS where the stack memory for OS Kernel must be separated from the application code.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga48e5853f417e17a8a65080f6a605b743">__set_PSP</a>; <a class="el" href="group___core___register__gr.html#gab898559392ba027814e5bbb5a98b38d2" title="Read the MSP register.">__get_MSP</a>; <a class="el" href="group___core___register__gr.html#ga963cf236b73219ce78e965deb01b81a7" title="Read the CONTROL register.">__get_CONTROL</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga732e08184154f44a617963cc65ff95bd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __get_xPSR </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function reads the combined Program Status Register (xPSR) using the instruction <b>MRS</b>. <br/>
<br/>
 xPSR provides information about program execution and the APSR flags. It consists of the following PSRs: </p>
<ul>
<li>Application Program Status Register (APSR) </li>
<li>Interrupt Program Status Register (IPSR) </li>
<li>Execution Program Status Register (EPSR)</li>
</ul>
<p>In addition to the flags described in <a class="el" href="group___core___register__gr.html#ga811c0012221ee918a75111ca84c4d5e7">__get_APSR</a> and <a class="el" href="group___core___register__gr.html#ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8">__get_IPSR</a>, the register provides the following flags:</p>
<ul>
<li><b>IT</b> (xPSR[26:25]) (If-Then condition instruction)<ul>
<li>Contains up to four instructions following an IT instruction.</li>
<li>Each instruction in the block is conditional.</li>
<li>The conditions for the instructions are either all the same, or some can be the inverse of others. <br/>
<br/>
</li>
</ul>
</li>
<li><b>T</b> (xPSR[24]) (Thumb bit)<ul>
<li>=1 Indicates that that the processor is in Thumb state.</li>
<li>=0 Attempting to execute instructions when the T bit is 0 results in a fault or lockup.</li>
<li>The conditions for the instructions are either all the same, or some can be the inverse of others.</li>
</ul>
</li>
</ul>
<dl class="section return"><dt>Returns</dt><dd>xPSR register value</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The CMSIS does not provide functions that access EPSR.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga811c0012221ee918a75111ca84c4d5e7">__get_APSR</a>; <a class="el" href="group___core___register__gr.html#ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8" title="Read the IPSR register.">__get_IPSR</a>; <a class="el" href="unionx_p_s_r___type.html" title="Union type to access the Special-Purpose Program Status Registers (xPSR).">xPSR_Type</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga360c73eb7ffb16088556f9278953b882"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __set_BASEPRI </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>basePri</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function sets the Base Priority Mask register (BASEPRI) value using the instruction <b>MSR</b>. <br/>
<br/>
 BASEPRI defines the minimum priority for exception processing. When BASEPRI is set to a non-zero value, it prevents the activation of all exceptions with the same or lower priority level as the BASEPRI value.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">basePri</td><td>BASEPRI value to set</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>Cannot be set in user state.</li>
<li>Useful for changing the masking level or disabling the masking.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga32da759f46e52c95bcfbde5012260667">__get_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#ga62fa63d39cf22df348857d5f44ab64d9" title="Increase the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI_MAX</a>; <a class="el" href="group___core___register__gr.html#gaa5587cc09031053a40a35c14ec36078a" title="Set the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_FAULTMASK</a>; <a class="el" href="group___core___register__gr.html#ga70b4e1a6c1c86eb913fb9d6e8400156f" title="Set the Priority Mask bit.">__set_PRIMASK</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga62fa63d39cf22df348857d5f44ab64d9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __set_BASEPRI_MAX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>basePri</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function only increases the Base Priority Mask register (BASEPRI) value using the instruction <b>MSR</b>. The value is set only if BASEPRI masking is disabled, or the new value increases the BASEPRI priority level. <br/>
<br/>
 BASEPRI defines the minimum priority for exception processing.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">basePri</td><td>BASEPRI value to set</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>Cannot be set in user state.</li>
<li>Useful for increasing the masking level.</li>
<li>Has no effect when <em>basePri</em> is lower than the current value of BASEPRI.</li>
<li>Use <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882">__set_BASEPRI</a> to lower the Base Priority Mask register.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882">__set_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#ga32da759f46e52c95bcfbde5012260667" title="Read the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__get_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#gaa5587cc09031053a40a35c14ec36078a" title="Set the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_FAULTMASK</a>; <a class="el" href="group___core___register__gr.html#ga70b4e1a6c1c86eb913fb9d6e8400156f" title="Set the Priority Mask bit.">__set_PRIMASK</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gac64d37e7ff9de06437f9fb94bbab8b6c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __set_CONTROL </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>control</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function sets the CONTROL register value using the instruction <b>MSR</b>. <br/>
<br/>
 The CONTROL register controls the stack used and the privilege level for software execution when the processor is in thread mode and, if implemented, indicates whether the FPU state is active. This register uses the following bits: <br/>
</p>
<ul>
<li><b>CONTROL</b>[2] [only Cortex-M4 and Cortex-M7]<ul>
<li>=0 FPU not active</li>
<li>=1 FPU active <br/>
<br/>
</li>
</ul>
</li>
<li><b>CONTROL</b>[1]<ul>
<li>Writeable only when the processor is in thread mode and privileged state (CONTROL[0]=0).</li>
<li>=0 In handler mode - MSP is selected. No alternate stack pointer possible for handler mode.</li>
<li>=0 In thread mode - Default stack pointer MSP is used.</li>
<li>=1 In thread mode - Alternate stack pointer PSP is used. <br/>
<br/>
</li>
</ul>
</li>
<li><b>CONTROL</b>[0] [not writeable for Cortex-M0]<ul>
<li>Writeable only when the processor is in privileged state.</li>
<li>Can be used to switch the processor to user state (thread mode).</li>
<li>Once in user state, trigger an interrupt and change the state to privileged in the exception handler (the only way).</li>
<li>=0 In thread mode and privileged state.</li>
<li>=1 In thread mode and user state.</li>
</ul>
</li>
</ul>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">control</td><td>CONTROL register value to set</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The processor can be in user state or privileged state when running in thread mode.</li>
<li>Exception handlers always run in privileged state.</li>
<li>On reset, the processor is in thread mode with privileged access rights.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga963cf236b73219ce78e965deb01b81a7">__get_CONTROL</a>; <a class="el" href="group___core___register__gr.html#ga48e5853f417e17a8a65080f6a605b743" title="Set the PSP register.">__set_PSP</a>; <a class="el" href="group___core___register__gr.html#ga0bf9564ebc1613a8faba014275dac2a4" title="Set the MSP register.">__set_MSP</a>; <a class="el" href="union_c_o_n_t_r_o_l___type.html" title="Union type to access the Control Registers (CONTROL).">CONTROL_Type</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gaa5587cc09031053a40a35c14ec36078a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __set_FAULTMASK </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>faultMask</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function sets the Fault Mask register (FAULTMASK) value using the instruction <b>MSR</b>. <br/>
<br/>
 FAULTMASK prevents activation of all exceptions except for Non-Maskable Interrupt (NMI). FAULTMASK can be used to escalate a configurable fault handler (BusFault, usage fault, or memory management fault) to hard fault level without invoking a hard fault. This allows the fault handler to pretend to be the hard fault handler, whith the ability to:</p>
<ol type="1">
<li><b>Mask BusFault</b> by setting the BFHFNMIGN in the Configuration Control register. It can be used to test the bus system without causing a lockup.</li>
<li><b>Bypass the MPU</b>, allowing accessing the MPU protected memory location without reprogramming the MPU to just carry out a few transfers for fixing faults.</li>
</ol>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">faultMask</td><td>FAULTMASK register value to set</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>Is cleared automatically upon exiting the exception handler, except when returning from the NMI handler.</li>
<li>When set, it changes the effective current priority level to -1, so that even the hard fault handler is blocked.</li>
<li>Can be used by fault handlers to change their priority to -1 to have access to some features for hard fault exceptions (see above).</li>
<li>When set, lockups can still be caused by incorrect or undefined instructions, or by using SVC in the wrong priority level.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#gaa78e4e6bf619a65e9f01b4af13fed3a8">__get_FAULTMASK</a>; <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882" title="Set the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#ga70b4e1a6c1c86eb913fb9d6e8400156f" title="Set the Priority Mask bit.">__set_PRIMASK</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga6f26bd75ca7e3247f27b272acc10536b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __set_FPSCR </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fpscr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function sets the Floating-Point Status Control Register (FPSCR) value. <br/>
<br/>
 FPSCR provides all necessary User level control of the floating-point system. <br/>
</p>
<ul>
<li><b>N</b> (FPSC[31]) (Negative flag)<ul>
<li>=1 The instruction result has a negative value (when interpreted as signed integer).</li>
<li>=0 The instruction result has a positive value or equal zero. <br/>
<br/>
</li>
</ul>
</li>
<li><b>Z</b> (FPSC[30]) (Zero flag)<ul>
<li>=1 The instruction result is zero. Or, after a compare instruction, when the two values are the same. <br/>
<br/>
</li>
</ul>
</li>
<li><b>C</b> (FPSC[29]) (Carry or borrow flag)<ul>
<li>=1 For unsigned additions, if an unsigned overflow occurred.</li>
<li>=<em>inverse of borrow output status</em> For unsigned subtract operations. <br/>
<br/>
</li>
</ul>
</li>
<li><b>V</b> (FPSC[28]) (Overflow flag)<ul>
<li>=1 A signed overflow occurred (for signed additions or subtractions). <br/>
<br/>
</li>
</ul>
</li>
<li><b>AHP</b> (FPSC[26]) (Alternative half-precision flag)<ul>
<li>=1 Alternative half-precision format selected.</li>
<li>=0 IEEE half-precision format selected. <br/>
<br/>
</li>
</ul>
</li>
<li><b>DN</b> (FPSC[25]) (Default NaN mode control flag)<ul>
<li>=1 Any operation involving one or more NaNs returns the Default NaN.</li>
<li>=0 NaN operands propagate through to the output of a floating-point operation. <br/>
<br/>
</li>
</ul>
</li>
<li><b>FZ</b> (FPSC[24]) (Flush-to-zero mode control flag)<ul>
<li>=1 Flush-to-zero mode enabled.</li>
<li>=0 Flush-to-zero mode disabled. Behavior of the floating-point system is fully compliant with the IEEE 754 standard. <br/>
<br/>
</li>
</ul>
</li>
<li><b>RMode</b> (FPSC[23:22]) (Rounding Mode control flags)<ul>
<li>=0b00 Round to Nearest (RN) mode.</li>
<li>=0b01 Round towards Plus Infinity (RP) mode.</li>
<li>=0b10 Round towards Minus Infinity (RM) mode.</li>
<li>=0b11 Round towards Zero (RZ) mode.</li>
<li>The specified rounding mode is used by almost all floating-point instructions. <br/>
<br/>
</li>
</ul>
</li>
<li><b>IDC</b> (FPSC[7]) (Input Denormal cumulative exception flags)<ul>
<li>See Cumulative exception bits (FPSC[4:0]). <br/>
<br/>
</li>
</ul>
</li>
<li><b>IXC</b> (FPSC[4]) (Inexact cumulative exception flag)<ul>
<li>=1 Exception occurred.</li>
<li>=0 Value has to be set explicitly.</li>
<li>Flag is not cleared automatically. <br/>
<br/>
</li>
</ul>
</li>
<li><b>UFC</b> (FPSC[3]) (Underflow cumulative exception flag)<ul>
<li>=1 Exception occurred.</li>
<li>=0 Value has to be set explicitly.</li>
<li>Flag is not cleared automatically. <br/>
<br/>
</li>
</ul>
</li>
<li><b>OFC</b> (FPSC[2]) (Overflow cumulative exception flag)<ul>
<li>=1 Exception occurred.</li>
<li>=0 Value has to be set explicitly.</li>
<li>Flag is not cleared automatically. <br/>
<br/>
</li>
</ul>
</li>
<li><b>DZC</b> (FPSC[1]) (Division by Zero cumulative exception flag)<ul>
<li>=1 Exception occurred.</li>
<li>=0 Value has to be set explicitly.</li>
<li>Flag is not cleared automatically. <br/>
<br/>
</li>
</ul>
</li>
<li><b>IOC</b> (FPSC[0]) (Invalid Operation cumulative exception flag)<ul>
<li>=1 Exception occurred.</li>
<li>=0 Value has to be set explicitly.</li>
<li>Flag is not cleared automatically.</li>
</ul>
</li>
</ul>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">fpscr</td><td>FPSCR value to set</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Only for Cortex-M4 and Cortex-M7.</li>
<li>The variable <b>__FPU_PRESENT</b> has to be set to 1.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#gad6d7eca9ddd1d9072dd7b020cfe64905">__get_FPSCR</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga0bf9564ebc1613a8faba014275dac2a4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __set_MSP </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>topOfMainStack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function sets the Main Status Pointer (MSP) value using the instruction <b>MSR</b>. <br/>
<br/>
 Physically two different stack pointers (SP) exist:</p>
<ul>
<li>The Main Stack Pointer (MSP) is the default stack pointer after reset. It is also used when running exception handlers (handler mode).</li>
<li>The Process Stack Pointer (PSP), which can be used only in thread mode.</li>
</ul>
<p>Register R13 banks the SP. The SP selection is determined by the bit[1] of the CONTROL register:</p>
<ul>
<li>=0 MSP is the current stack pointer. This is also the default SP. The initial value is loaded from the first 32-bit word of the vector table from the program memory.</li>
<li>=1 PSP is the current stack pointer. The initial value is undefined.</li>
</ul>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">topOfMainStack</td><td>MSP value to set</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Only one of the two SPs is visible at a time.</li>
<li>For many applications, the system can completely rely on the MSP.</li>
<li>The PSP is normally used in designs with an OS where the stack memory for OS Kernel must be separated from the application code.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#gab898559392ba027814e5bbb5a98b38d2">__get_MSP</a>; <a class="el" href="group___core___register__gr.html#ga48e5853f417e17a8a65080f6a605b743" title="Set the PSP register.">__set_PSP</a>; <a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c" title="Set the CONTROL Register.">__set_CONTROL</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga70b4e1a6c1c86eb913fb9d6e8400156f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __set_PRIMASK </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>priMask</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function sets the Priority Mask register (PRIMASK) value using the instruction <b>MSR</b>. <br/>
<br/>
 PRIMASK is a 1-bit-wide interrupt mask register. When set, it blocks all interrupts apart from the non-maskable interrupt (NMI) and the hard fault exception. The PRIMASK prevents activation of all exceptions with configurable priority.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">priMask</td><td>Priority Mask<ul>
<li>=0 no effect</li>
<li>=1 prevents the activation of all exceptions with configurable priority</li>
</ul>
</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>When set, PRIMASK effectively changes the current priority level to 0. This is the highest programmable level.</li>
<li>When set and a fault occurs, the hard fault handler will be executed.</li>
<li>Useful for temprorarily disabling all interrupts for timing critical tasks.</li>
<li>Does not have the ability to mask BusFault or bypass MPU.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga799b5d9a2ae75e459264c8512c7c0e02">__get_PRIMASK</a>; <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882" title="Set the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI</a>; <a class="el" href="group___core___register__gr.html#gaa5587cc09031053a40a35c14ec36078a" title="Set the FAULTMASK register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_FAULTMASK</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga48e5853f417e17a8a65080f6a605b743"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __set_PSP </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>topOfProcStack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function sets the Program Status Pointer (PSP) value using the instruction <b>MSR</b>. <br/>
<br/>
 Physically two different stack pointers (SP) exist:</p>
<ul>
<li>The Main Stack Pointer (MSP) is the default stack pointer after reset. It is also used when running exception handlers (handler mode).</li>
<li>The Process Stack Pointer (PSP), which can be used only in thread mode.</li>
</ul>
<p>Register R13 banks the SP. The SP selection is determined by the bit[1] of the CONTROL register:</p>
<ul>
<li>=0 MSP is the current stack pointer. This is also the default SP. The initial value is loaded from the first 32-bit word of the vector table from the program memory.</li>
<li>=1 PSP is the current stack pointer. The initial value is undefined.</li>
</ul>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">topOfProcStack</td><td>PSP value to set</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Only one of the two SPs is visible at a time.</li>
<li>For many applications, the system can completely rely on the MSP.</li>
<li>The PSP is normally used in designs with an OS where the stack memory for OS Kernel must be separated from the application code.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___core___register__gr.html#ga914dfa8eff7ca53380dd54cf1d8bebd9">__get_PSP</a>; <a class="el" href="group___core___register__gr.html#ga0bf9564ebc1613a8faba014275dac2a4" title="Set the MSP register.">__set_MSP</a>; <a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c" title="Set the CONTROL Register.">__set_CONTROL</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
