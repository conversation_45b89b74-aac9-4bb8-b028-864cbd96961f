<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Complex FFT Tables</title>
<title>CMSIS-DSP: Complex FFT Tables</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___c_f_f_t___c_i_f_f_t.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">Complex FFT Tables</div>  </div>
<div class="ingroups"><a class="el" href="group__group_transforms.html">Transform Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:gae247e83ad50d474107254e25b36ad42b"><td class="memItemLeft" align="right" valign="top">const uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae247e83ad50d474107254e25b36ad42b">armBitRevTable</a> [1024]</td></tr>
<tr class="separator:gae247e83ad50d474107254e25b36ad42b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae75e243ec61706427314270f222e0c8e"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae75e243ec61706427314270f222e0c8e">twiddleCoef_16</a> [32]</td></tr>
<tr class="separator:gae75e243ec61706427314270f222e0c8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78a72c85d88185de98050c930cfc76e3"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga78a72c85d88185de98050c930cfc76e3">twiddleCoef_32</a> [64]</td></tr>
<tr class="separator:ga78a72c85d88185de98050c930cfc76e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f3c6d98c7e66393b4ef3ac63746e43d"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga4f3c6d98c7e66393b4ef3ac63746e43d">twiddleCoef_64</a> [128]</td></tr>
<tr class="separator:ga4f3c6d98c7e66393b4ef3ac63746e43d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga948433536dafaac1381decfccf4e2d9c"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga948433536dafaac1381decfccf4e2d9c">twiddleCoef_128</a> [256]</td></tr>
<tr class="separator:ga948433536dafaac1381decfccf4e2d9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe813758a03a798e972359a092315be4"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafe813758a03a798e972359a092315be4">twiddleCoef_256</a> [512]</td></tr>
<tr class="separator:gafe813758a03a798e972359a092315be4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8830f0c068ab2cc19f2f87d220fa148"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gad8830f0c068ab2cc19f2f87d220fa148">twiddleCoef_512</a> [1024]</td></tr>
<tr class="separator:gad8830f0c068ab2cc19f2f87d220fa148"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27c056eb130a4333d1cc5dd43ec738b1"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga27c056eb130a4333d1cc5dd43ec738b1">twiddleCoef_1024</a> [2048]</td></tr>
<tr class="separator:ga27c056eb130a4333d1cc5dd43ec738b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga23e7f30421a7905b21c2015429779633"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga23e7f30421a7905b21c2015429779633">twiddleCoef_2048</a> [4096]</td></tr>
<tr class="separator:ga23e7f30421a7905b21c2015429779633"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae0182d1dd3b2f21aad4e38a815a0bd40"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae0182d1dd3b2f21aad4e38a815a0bd40">twiddleCoef_4096</a> [8192]</td></tr>
<tr class="separator:gae0182d1dd3b2f21aad4e38a815a0bd40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef4697e1ba348c4ac9358f2b9e279e93"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaef4697e1ba348c4ac9358f2b9e279e93">twiddleCoef_16_q31</a> [24]</td></tr>
<tr class="separator:gaef4697e1ba348c4ac9358f2b9e279e93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8ba78d5e6ef4bdc58e8f0044e0664a0a"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8ba78d5e6ef4bdc58e8f0044e0664a0a">twiddleCoef_32_q31</a> [48]</td></tr>
<tr class="separator:ga8ba78d5e6ef4bdc58e8f0044e0664a0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e0a7e941a25a0d74b2e6590307de47e"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6e0a7e941a25a0d74b2e6590307de47e">twiddleCoef_64_q31</a> [96]</td></tr>
<tr class="separator:ga6e0a7e941a25a0d74b2e6590307de47e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafecf9ed9873415d9f5f17f37b30c7250"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafecf9ed9873415d9f5f17f37b30c7250">twiddleCoef_128_q31</a> [192]</td></tr>
<tr class="separator:gafecf9ed9873415d9f5f17f37b30c7250"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef1ea005053b715b851cf5f908168ede"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaef1ea005053b715b851cf5f908168ede">twiddleCoef_256_q31</a> [384]</td></tr>
<tr class="separator:gaef1ea005053b715b851cf5f908168ede"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga416c61b2f08542a39111e06b0378bebe"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga416c61b2f08542a39111e06b0378bebe">twiddleCoef_512_q31</a> [768]</td></tr>
<tr class="separator:ga416c61b2f08542a39111e06b0378bebe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga514443c44b62b8b3d240afefebcda310"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga514443c44b62b8b3d240afefebcda310">twiddleCoef_1024_q31</a> [1536]</td></tr>
<tr class="separator:ga514443c44b62b8b3d240afefebcda310"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c5767de9f5a409fd0c2027e6ac67179"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9c5767de9f5a409fd0c2027e6ac67179">twiddleCoef_2048_q31</a> [3072]</td></tr>
<tr class="separator:ga9c5767de9f5a409fd0c2027e6ac67179"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga67c0890317deab3391e276f22c1fc400"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga67c0890317deab3391e276f22c1fc400">twiddleCoef_4096_q31</a> [6144]</td></tr>
<tr class="separator:ga67c0890317deab3391e276f22c1fc400"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e4e2e05f4a3112184c96cb3308d6c39"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8e4e2e05f4a3112184c96cb3308d6c39">twiddleCoef_16_q15</a> [24]</td></tr>
<tr class="separator:ga8e4e2e05f4a3112184c96cb3308d6c39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac194a4fe04a19051ae1811f69c6e5df2"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gac194a4fe04a19051ae1811f69c6e5df2">twiddleCoef_32_q15</a> [48]</td></tr>
<tr class="separator:gac194a4fe04a19051ae1811f69c6e5df2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0cc411e0b3c82078e85cfdf1b84290f"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gaa0cc411e0b3c82078e85cfdf1b84290f">twiddleCoef_64_q15</a> [96]</td></tr>
<tr class="separator:gaa0cc411e0b3c82078e85cfdf1b84290f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabfdd1c5cd2b3f96da5fe5f07c707a8e5"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gabfdd1c5cd2b3f96da5fe5f07c707a8e5">twiddleCoef_128_q15</a> [192]</td></tr>
<tr class="separator:gabfdd1c5cd2b3f96da5fe5f07c707a8e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6099ae5262a0a3a8d9ce1e6da02f0c2e"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6099ae5262a0a3a8d9ce1e6da02f0c2e">twiddleCoef_256_q15</a> [384]</td></tr>
<tr class="separator:ga6099ae5262a0a3a8d9ce1e6da02f0c2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6152621af210f847128c6f38958fa385"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga6152621af210f847128c6f38958fa385">twiddleCoef_512_q15</a> [768]</td></tr>
<tr class="separator:ga6152621af210f847128c6f38958fa385"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a0ec95d866fe96b740e77d6e1356b59"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga8a0ec95d866fe96b740e77d6e1356b59">twiddleCoef_1024_q15</a> [1536]</td></tr>
<tr class="separator:ga8a0ec95d866fe96b740e77d6e1356b59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd16ce08ffd1048c385e0534a3b19cbb"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gadd16ce08ffd1048c385e0534a3b19cbb">twiddleCoef_2048_q15</a> [3072]</td></tr>
<tr class="separator:gadd16ce08ffd1048c385e0534a3b19cbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9b409d6995eab17805b1d1881d4bc652"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga9b409d6995eab17805b1d1881d4bc652">twiddleCoef_4096_q15</a> [6144]</td></tr>
<tr class="separator:ga9b409d6995eab17805b1d1881d4bc652"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="gae247e83ad50d474107254e25b36ad42b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint16_t armBitRevTable[1024]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Pseudo code for Generation of Bit reversal Table is </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(l=1;l &lt;= N/4;l++)    
{    
  for(i=0;i&lt;logN2;i++)    
  {     
    a[i]=l&amp;(1&lt;&lt;i);    
  }    
  for(j=0; j&lt;logN2; j++)    
  {    
    if (a[j]!=0)    
    y[l]+=(1&lt;&lt;((logN2-1)-j));    
  }    
  y[l] = y[l] &gt;&gt; 1;    
 } </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 4096 logN2 = 12 </dd></dl>
<dl class="section user"><dt></dt><dd>N is the maximum FFT Size supported </dd></dl>

<p>Referenced by <a class="el" href="group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7">arm_cfft_radix2_init_f32()</a>, <a class="el" href="group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858">arm_cfft_radix2_init_q15()</a>, <a class="el" href="group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638">arm_cfft_radix2_init_q31()</a>, <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32()</a>, <a class="el" href="group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef">arm_cfft_radix4_init_q15()</a>, and <a class="el" href="group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c">arm_cfft_radix4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ga27c056eb130a4333d1cc5dd43ec738b1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_1024[2048]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 1024 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga8a0ec95d866fe96b740e77d6e1356b59"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_1024_q15[1536]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 1024 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga514443c44b62b8b3d240afefebcda310"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_1024_q31[1536]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 1024 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga948433536dafaac1381decfccf4e2d9c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_128[256]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 128 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gabfdd1c5cd2b3f96da5fe5f07c707a8e5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_128_q15[192]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 128 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

</div>
</div>
<a class="anchor" id="gafecf9ed9873415d9f5f17f37b30c7250"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_128_q31[192]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 128 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

</div>
</div>
<a class="anchor" id="gae75e243ec61706427314270f222e0c8e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_16[32]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 16 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga8e4e2e05f4a3112184c96cb3308d6c39"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_16_q15[24]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 16 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

</div>
</div>
<a class="anchor" id="gaef4697e1ba348c4ac9358f2b9e279e93"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_16_q31[24]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 16 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga23e7f30421a7905b21c2015429779633"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_2048[4096]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 2048 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gadd16ce08ffd1048c385e0534a3b19cbb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_2048_q15[3072]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 2048 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga9c5767de9f5a409fd0c2027e6ac67179"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_2048_q31[3072]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 2048 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

</div>
</div>
<a class="anchor" id="gafe813758a03a798e972359a092315be4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_256[512]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 256 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga6099ae5262a0a3a8d9ce1e6da02f0c2e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_256_q15[384]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 256 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

</div>
</div>
<a class="anchor" id="gaef1ea005053b715b851cf5f908168ede"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_256_q31[384]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 256 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga78a72c85d88185de98050c930cfc76e3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_32[64]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 32 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gac194a4fe04a19051ae1811f69c6e5df2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_32_q15[48]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 32 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga8ba78d5e6ef4bdc58e8f0044e0664a0a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_32_q31[48]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 32 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

</div>
</div>
<a class="anchor" id="gae0182d1dd3b2f21aad4e38a815a0bd40"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_4096[8192]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 4096 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

</div>
</div>
<a class="anchor" id="ga9b409d6995eab17805b1d1881d4bc652"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_4096_q15[6144]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 4096 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

<p>Referenced by <a class="el" href="group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858">arm_cfft_radix2_init_q15()</a>, and <a class="el" href="group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef">arm_cfft_radix4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ga67c0890317deab3391e276f22c1fc400"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_4096_q31[6144]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 4096 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

<p>Referenced by <a class="el" href="group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638">arm_cfft_radix2_init_q31()</a>, and <a class="el" href="group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c">arm_cfft_radix4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="gad8830f0c068ab2cc19f2f87d220fa148"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_512[1024]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 512 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga6152621af210f847128c6f38958fa385"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_512_q15[768]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 512 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga416c61b2f08542a39111e06b0378bebe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_512_q31[768]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 512 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga4f3c6d98c7e66393b4ef3ac63746e43d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> twiddleCoef_64[128]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Floating-point Twiddle factors Generation: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; N/; i++)    
{    
      twiddleCoef[2*i]= cos(i * 2*PI/(float)N);    
      twiddleCoef[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 64 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are in interleaved fashion </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gaa0cc411e0b3c82078e85cfdf1b84290f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> twiddleCoef_64_q15[96]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for q15 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefq15[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefq15[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 64 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to q15(Fixed point 1.15): round(twiddleCoefq15(i) * pow(2, 15)) </dd></dl>

</div>
</div>
<a class="anchor" id="ga6e0a7e941a25a0d74b2e6590307de47e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> twiddleCoef_64_q31[96]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Example code for Q31 Twiddle factors Generation:: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>for(i = 0; i&lt; 3N/4; i++)    
{    
   twiddleCoefQ31[2*i]= cos(i * 2*PI/(float)N);    
   twiddleCoefQ31[2*i+1]= sin(i * 2*PI/(float)N);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where N = 64 and PI = 3.14159265358979 </dd></dl>
<dl class="section user"><dt></dt><dd>Cos and Sin values are interleaved fashion </dd></dl>
<dl class="section user"><dt></dt><dd>Convert Floating point to Q31(Fixed point 1.31): round(twiddleCoefQ31(i) * pow(2, 31)) </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
