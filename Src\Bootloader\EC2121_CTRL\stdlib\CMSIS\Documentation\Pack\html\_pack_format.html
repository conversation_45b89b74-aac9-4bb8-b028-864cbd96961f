<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Pack: Pack Description (*.PDSC) Format</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Pack
   &#160;<span id="projectnumber">Version 1.3.1</span>
   </div>
   <div id="projectbrief">Delivery Mechanism for Software Packs</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li class="current"><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_pack_format.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Pack Description (*.PDSC) Format </div>  </div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#Filenames">Filename Conventions</a></li>
<li class="level1"><a href="#PackSchema">Pack Schema</a></li>
<li class="level1"><a href="#PDSC_Example">Example of a *.PDSC File</a></li>
<li class="level1"><a href="#PACK_Example">Example of a *.PACK File</a></li>
</ul>
</div>
<div class="textblock"><p>The <a class="el" href="_pack_format.html">Pack Description (*.PDSC) Format</a> uses standard XML annotations and is defined in the schema file <b>PACK.xsd</b>. Software components are distributed through <b>Software Packs</b>. A <a class="el" href="_c_p__s_w_components.html">Software Pack</a> is a zip file containing a single <b>Pack Description</b> file that describes dependencies to devices, processors, tool chains, or other software components.</p>
<p>A descriptive text of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>along the text information for devices, components, and examples is part of each a <b>Pack Description</b> (*.PDSC) file. In addition, information is provided which facilitates downloading, updating, and versioning of of a <a class="el" href="_c_p__s_w_components.html">Software Pack</a>. The *.PDSC file contains also the complete version history of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a> with a brief list of the most significant changes.</p>
<h1><a class="anchor" id="Filenames"></a>
Filename Conventions</h1>
<p>A <a class="el" href="_c_p__s_w_components.html">Software Pack</a> must have a unique filename which uses the following format: <b>&lt;vendor&gt;.&lt;name&gt;.&lt;version&gt;.pack</b>.</p>
<p><b>Where:</b> </p>
<ul>
<li><b>&lt;vendor&gt;</b>: name of the supplier or vendor of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><b>&lt;name&gt;</b>: name of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>. It is the vendor's responsibility to ensure unique package names.</li>
<li><b>&lt;version&gt;</b>: version number of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><b>.pack</b>: file extension identifying a <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
</ul>
<p>The <b>Pack Description</b> (*.PDSC) file belonging to such a <a class="el" href="_c_p__s_w_components.html">Software Pack</a> file (&lt;vendor&gt;.&lt;name&gt;.&lt;version&gt;.pack) is version independent and has the format: <b>&lt;vendor&gt;.&lt;name&gt;.pdsc</b>.</p>
<p><b>Where:</b> </p>
<ul>
<li><b>&lt;vendor&gt;</b>: name of the supplier or vendor of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><b>&lt;name&gt;</b>: name of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a> that is described by this *.PDSC file.</li>
<li><b>.pack</b>: file extension identifying a <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
</ul>
<p>Example filenames for software packs:</p>
<p>Software Pack for CMSIS Version 4.0 released by ARM.</p>
<ul>
<li><b>ARM.CMSIS.4.0.0.pack</b>: filename of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><b>ARM.CMSIS.pdsc</b>: filename of the <b>Pack Description</b> (*.PDSC) file.</li>
</ul>
<p>Device Family Pack for STM32F4 Version 1.0.0 released by ST Microelectronics.</p>
<ul>
<li><b>ST.STM32F4xx_DFP.1.0.0.pack</b>: filename of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><b>ST.STM32F4xx.pdsc</b>: filename of the <b>Pack Description</b> (*.PDSC) file.</li>
</ul>
<h1><a class="anchor" id="PackSchema"></a>
Pack Schema</h1>
<p>The XML schema file <b>PACK.xsd</b> defines the sections used in a <b>*.PDSC</b> file. The current <b>PACK.xsd</b> can be found under the ARM.CMSIS.*.Pack in the .\CMSIS\Utilities-directory.</p>
<p>The <a class="el" href="_pack_format.html">Pack Description (*.PDSC) Format</a> is structured using grouping elements and contains the following top level elements:</p>
<ul>
<li><a class="el" href="pdsc_package_pg.html">&lt;package&gt;</a>: describes package related information like vendor, package name, description, version. Is the root element.</li>
<li><a class="el" href="element_keywords.html">&lt;keywords&gt;</a>: lists keywords to search for packages. Can be used for search engines.</li>
<li><a class="el" href="element_releases.html">&lt;releases&gt;</a>: lists releas versions with descriptions.</li>
<li><a class="el" href="element_taxonomy.html">&lt;taxonomy&gt;</a>: lists description elements that define component classes and component group names.</li>
<li><a class="el" href="pdsc_apis_pg.html">&lt;apis&gt;</a>: describes Application Programming Interfaces (API) Specifications contained in the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><a class="el" href="pdsc_generators_pg.html">&lt;generators&gt;</a>: specifies the tools that have been used to generate the PDSC file.</li>
<li><a class="el" href="pdsc_devices_pg.html">&lt;devices&gt;</a>: lists the devices supported by this <a class="el" href="_c_p__s_w_components.html">Software Pack</a>. It specifies the device attributes and files like flash programming algorithms, CMSIS device header files, CMSIS System View Descriptions, etc.</li>
<li><a class="el" href="pdsc_boards_pg.html">&lt;boards&gt;</a>: lists the development boards supported by the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><a class="el" href="pdsc_conditions_pg.html">&lt;conditions&gt;</a>: defines dependencies to devices, processors, components, and tools that are used within the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>. It allows making components and files conditional.</li>
<li><a class="el" href="pdsc_components_pg.html">&lt;components&gt;</a>: lists the software components contained in the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><a class="el" href="pdsc_examples_pg.html">&lt;examples&gt;</a>: specifies example projects contained in the PACK.</li>
<li><a class="el" href="pdsc__sequence_name_enum_pg.html">&lt;sequence&gt;</a>: specifies sequences of debugger commands.</li>
</ul>
<h1><a class="anchor" id="PDSC_Example"></a>
Example of a *.PDSC File</h1>
<p>This example of a *.PDSC File explains the sections, particularly how dependencies are used to identify individual files of <a class="el" href="_c_p__s_w_components.html#CP_Components">Software Components</a>.</p>
<div class="image">
<img src="PDSC_Example_top.png" alt="PDSC_Example_top.png"/>
</div>
 <div class="image">
<img src="PDSC_Example_bottom.png" alt="PDSC_Example_bottom.png"/>
<div class="caption">
Example of a *.PDSC File</div></div>
 <h1><a class="anchor" id="PACK_Example"></a>
Example of a *.PACK File</h1>
<p>The ARM.CMSIS.*.Pack also contains an extracted example of a PACK file. It can be found in the .\CMSIS\Pack\Example-directory. In a real-life *.PACK file this would be the archive's top-level containing the *.PDSC file and the subdirectories with all the files that are part of the Software Pack. Here, an exemplary PDSC file is stored which is renamed to *.pdsc.txt. This PDSC file can be used to examine the contents of the PACK. It is also a starting point for writing your own Software Pack.</p>
<p>The folder structure of this exemplary Software Pack is as follows:</p>
<ul>
<li><b>ARM.STM32F4xx_DFP.pdsc.txt</b>: PDSC file example</li>
<li><b>CMSIS_Driver:</b> Examples of driver implementations adhering to <a href="../../Driver/html/index.html" class="el">CMSIS-Driver</a><ul>
<li><b>Config:</b> Driver configuration file</li>
</ul>
</li>
<li><b>Device:</b> used in a <a class="el" href="_create_pack__d_f_p.html">DFP</a><ul>
<li><b>Include:</b> <a href="../../Core/html/device_h_pg.html" class="el">device header files</a></li>
<li><b>Source:</b> <a href="../../Core/html/system_c_pg.html" class="el">system configuration files</a><ul>
<li><b>ARM:</b> <a href="../../Core/html/startup_s_pg.html" class="el">startup files</a></li>
</ul>
</li>
</ul>
</li>
<li><b>Documents:</b> All kinds of documentation (reference manuals, data sheets, board schematics)</li>
<li><b>Flash:</b> <a class="el" href="_flash_algorithm.html">Programming algorithms</a> for microcontroller devices<ul>
<li><b>STM32F4xx:</b> uVision project files for generating the Flash programming algorithms</li>
</ul>
</li>
<li><b>Images:</b> Pictures of developments boards used for display on web sites or other documentation</li>
<li><b>SVD:</b> <a href="../../SVD/html/index.html" class="el">System View Description</a> files for microcontroller devices </li>
</ul>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:18:10 for CMSIS-Pack by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
