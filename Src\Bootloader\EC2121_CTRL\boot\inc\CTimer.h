#ifndef CTIMER_H
#define CTIMER_H

#include "Globel.h"

extern "C"
{
#include "stm32f4xx.h"
}


#define MAX_COUNTER_VAL_32BIT                (0xFFFFFFFF)
#define MAX_COUNTER_VAL_16BIT                (0xFFFF)


#define TIMER_TICKS_PER_US_32BIT             168 /*168Mhz, 1us = 168 个tick*/

typedef void (*CALLBACKFNC_TIMER)(void *pTag);


extern "C" void msDelay(uint32_t ms);
extern "C" void usDeadDelay(uint32_t us);

class CTimer
{
public:
    typedef struct
    {
        TIM_TypeDef *       TIMx;//寄存器地址
        uint32_t            rccClock;//时钟
        uint8_t             RCC_Periph;//时钟类型
        IRQn_Type           timIRQn;
        uint8_t             AllocState;
        CTimer*             pTimerObj;//timer对象的指针
    } TIMER_MANAGER;
    
    typedef struct
    {
        uint8_t Priority;//保存当前timer的硬件优先级配置参数，在硬件timer初始化时使用，范围是0-15,数值越小优先级越高；
        uint8_t IsolationState;//是否中断隔离
        CALLBACKFNC_TIMER pTimerCallBack;//存放中断回调
        void * pTag;//回调函数的参数
    } TIMER_PARA;
    enum  tagTIMER
    {
        TIMER1 = 0,//这里对应ctimer.pp文件中的TIMER_NO_CONFIG CTimer::timNoCfg资源表格，不能随意改动
        TIMER2,
        TIMER3,
        TIMER4,
        TIMER5,
        TIMER6,
        TIMER7,
        TIMER8,
        TIMER9,
        TIMER10,
        TIMER11,
        TIMER12,
        TIMER13,
        TIMER14
    };

public:
    CTimer(void);

    //CTimer(uint32_t timer_no, uint32_t flag = false);

    CTimer(CALLBACKFNC_TIMER pHTimerCallBack, void *pTag, uint8_t &AllocRet, uint8_t PreemptionPriority = 6,uint8_t IsolationState = 1);

    ~CTimer(void);

    void Start();

    //void Init(uint32_t timer_no/*, uint32_t flag = 0*/);

    //void ReInit(uint32_t preSclerUs, uint32_t flag = 0);

    void SetPeriodMs(uint32_t ms);

    void SetPeriodUs(uint32_t us);

    void SetTimeOutUs(uint32_t us ,uint8_t isPeriod);

    void SetTimeOutMs(uint32_t ms ,uint8_t isPeriod);

    void Stop(void);

    bool IsOverFlow(void);

    bool IsPeriod(void);//定时器是周期还是单次

    //*****************************************************************************************************
    //功能描述：延时函数
    //参数说明：ms-延时时间，单位是毫秒
    //返　　回：
    //特殊说明：该MS级延时增加vTaskDelay，在延时时，可以切换到其他任务或者中断里
    //作　　者：瞿松松 20190113
    //*****************************************************************************************************
    void DelayMs(uint32_t ms);

    void DelayUs(uint32_t us);
/*
    void Enable(void);
    */

    void Disable(void);

    void SysTickConfiguration();

    static uint64_t GetTimeStampCnts_64(void);

    static uint32_t GetTimeStampUs(void);

    static uint32_t GetTimeStampMs(void);

    static uint8_t GetTimeStampS(void);//用于memdebug节省内存，或者时间精度要求不高的场合,范围0-255秒，赵命华20201214

    static uint32_t GetDeltaTimeUs(uint32_t startvalue_us);

    static uint32_t GetDeltaTimeMs(uint32_t startvalue_ms);

    static uint8_t GetDeltaTimeS(uint8_t startvalue_s);

    static void OnSysTick(void);

    //*****************************************************************************************************
    //功能描述：供timer硬件中断调用
    //参数说明：interrupt_source，如果中断是隔离，用于触发对应的隔离中断
    //返　　回：
    //特殊说明：
    //作　　者：赵命华20210326
    //*****************************************************************************************************
    void OnHardTimer(uint8_t interrupt_source);

    void DeadDelay(uint32_t ms);

private:
    void HWInit(/*uint32_t timer_no, uint32_t flag = 0*/);

    void TimeBaseInit(TIM_TypeDef *TIMx, TIM_TimeBaseInitTypeDef *TIM_TimeBaseInitStruct);

    uint32_t GetPassedVal();

private:
    volatile uint32_t m_startValue;/*开始计时时间戳*/

    volatile uint32_t m_period;/*设置延时时间*/

    volatile uint32_t m_curTimNo;/*当前定时器NUMBER*/

    static uint64_t m_TimerStampHalfMs;

    TIMER_MANAGER *m_pCurTimerCtl;

    uint8_t m_RCC_CLOCK_ABP;//abp总线频率，单位MHZ

    uint8_t m_IsPeriod;//记录当前定时是周期还是单次，周期：1，单次：0

public:
    TIMER_PARA m_curTimerPara;

    static TIMER_MANAGER m_timerManager[];

};

#endif

