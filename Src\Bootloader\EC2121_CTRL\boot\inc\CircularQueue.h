#ifndef CIRCULAR_QUEUE_H
#define CIRCULAR_QUEUE_H

#include <stdio.h>
#include <string.h>
#include "Globel.h"

#define SIZE_OF_HEAD    4//包长度占用空间

class CircularQueue
{
public:
    typedef struct element_t
    {
        volatile uint32_t len;//后面数据对应内存的长度，不包含len的4个字节
        uint8_t* data;//数据传入时data指向数据区域，数据取出时，&data表示数据区域的起点指针，
                      //使用时一定要注意！！！！！！！！！！！！！！！赵命华，20210420
    } element_t;

private:
    //static const uint32_t QUEUE_CNT         = 2;
/*
    typedef struct queue_t
    {
        volatile uint32_t tail;
        volatile uint32_t head;
        uint8_t buffer[1];
    } queue_t;
*/
    

public:
    CircularQueue(uint32_t size);

    ~CircularQueue();

    bool push(element_t *pElement);

    void *front();

    void pop();

    uint32_t size();

    bool empty();

    void CircularMemcpy(void *dest, element_t *pElement,uint32_t copylen);

private:

    void clear();

    uint32_t GetTailFreeSize();

    uint32_t GetHeadFreeSize();

    uint32_t m_BufferSize;// 20190320 瞿松松，用来记录队列的大小
    //queue_t *m_pQueue;

    volatile uint32_t m_tail;
    volatile uint32_t m_head;
    uint8_t*  m_pBuffer;

};

#endif

