<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>xPSR_Type Union Reference</title>
<title>CMSIS-CORE: xPSR_Type Union Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('unionx_p_s_r___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">xPSR_Type Union Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Union type to access the Special-Purpose Program Status Registers (xPSR).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a3b1063bb5cdad67e037cba993b693b70"><td class="memItemLeft" >struct {</td></tr>
<tr class="memitem:a14aa41f658bf70c2d44435d24761a760"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#a3e9120dcf1a829fc8d2302b4d0673970">ISR</a>:9</td></tr>
<tr class="memdesc:a14aa41f658bf70c2d44435d24761a760"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 0.. 8 Exception number  <a href="#a14aa41f658bf70c2d44435d24761a760"></a><br/></td></tr>
<tr class="separator:a14aa41f658bf70c2d44435d24761a760"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7af0067da9805e481890c297bf4ed70f"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#af438e0f407357e914a70b5bd4d6a97c5">_reserved0</a>:15</td></tr>
<tr class="memdesc:a7af0067da9805e481890c297bf4ed70f"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 9..23 Reserved  <a href="#a7af0067da9805e481890c297bf4ed70f"></a><br/></td></tr>
<tr class="separator:a7af0067da9805e481890c297bf4ed70f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5224815d0f90fb7d26c7007bfb8e38d5"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#a7eed9fe24ae8d354cd76ae1c1110a658">T</a>:1</td></tr>
<tr class="memdesc:a5224815d0f90fb7d26c7007bfb8e38d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 24 Thumb bit (read 0)  <a href="#a5224815d0f90fb7d26c7007bfb8e38d5"></a><br/></td></tr>
<tr class="separator:a5224815d0f90fb7d26c7007bfb8e38d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0c9d4cef85e4cc7d6dc701d7d3377af0"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#a3200966922a194d84425e2807a7f1328">IT</a>:2</td></tr>
<tr class="memdesc:a0c9d4cef85e4cc7d6dc701d7d3377af0"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 25..26 saved IT state (read 0)  <a href="#a0c9d4cef85e4cc7d6dc701d7d3377af0"></a><br/></td></tr>
<tr class="separator:a0c9d4cef85e4cc7d6dc701d7d3377af0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0713a6888c5b556e9050aa82d2c1b0e1"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#add7cbd2b0abd8954d62cd7831796ac7c">Q</a>:1</td></tr>
<tr class="memdesc:a0713a6888c5b556e9050aa82d2c1b0e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 27 Saturation condition flag  <a href="#a0713a6888c5b556e9050aa82d2c1b0e1"></a><br/></td></tr>
<tr class="separator:a0713a6888c5b556e9050aa82d2c1b0e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6dd30396c78f8bc53d30ca13b058cbb2"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#af14df16ea0690070c45b95f2116b7a0a">V</a>:1</td></tr>
<tr class="memdesc:a6dd30396c78f8bc53d30ca13b058cbb2"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 28 Overflow condition code flag  <a href="#a6dd30396c78f8bc53d30ca13b058cbb2"></a><br/></td></tr>
<tr class="separator:a6dd30396c78f8bc53d30ca13b058cbb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae33d83822b56cd849b9fa9affddd59b2"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#a40213a6b5620410cac83b0d89564609d">C</a>:1</td></tr>
<tr class="memdesc:ae33d83822b56cd849b9fa9affddd59b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 29 Carry condition code flag  <a href="#ae33d83822b56cd849b9fa9affddd59b2"></a><br/></td></tr>
<tr class="separator:ae33d83822b56cd849b9fa9affddd59b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1f7475b01a46aef06d9f53d3a2a69ef"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#a1e5d9801013d5146f2e02d9b7b3da562">Z</a>:1</td></tr>
<tr class="memdesc:ac1f7475b01a46aef06d9f53d3a2a69ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 30 Zero condition code flag  <a href="#ac1f7475b01a46aef06d9f53d3a2a69ef"></a><br/></td></tr>
<tr class="separator:ac1f7475b01a46aef06d9f53d3a2a69ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38ba57343e56c653939fd792c19af047"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="unionx_p_s_r___type.html#a2db9a52f6d42809627d1a7a607c5dbc5">N</a>:1</td></tr>
<tr class="memdesc:a38ba57343e56c653939fd792c19af047"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 31 Negative condition code flag  <a href="#a38ba57343e56c653939fd792c19af047"></a><br/></td></tr>
<tr class="separator:a38ba57343e56c653939fd792c19af047"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b1063bb5cdad67e037cba993b693b70"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionx_p_s_r___type.html#a3b1063bb5cdad67e037cba993b693b70">b</a></td></tr>
<tr class="memdesc:a3b1063bb5cdad67e037cba993b693b70"><td class="mdescLeft">&#160;</td><td class="mdescRight">Structure used for bit access.  <a href="#a3b1063bb5cdad67e037cba993b693b70"></a><br/></td></tr>
<tr class="separator:a3b1063bb5cdad67e037cba993b693b70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a47176768f45f79076c4f5b1b534bc2"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionx_p_s_r___type.html#a1a47176768f45f79076c4f5b1b534bc2">w</a></td></tr>
<tr class="memdesc:a1a47176768f45f79076c4f5b1b534bc2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type used for word access.  <a href="#a1a47176768f45f79076c4f5b1b534bc2"></a><br/></td></tr>
<tr class="separator:a1a47176768f45f79076c4f5b1b534bc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="af438e0f407357e914a70b5bd4d6a97c5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::_reserved0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3b1063bb5cdad67e037cba993b693b70"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   xPSR_Type::b</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a40213a6b5620410cac83b0d89564609d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::C</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3e9120dcf1a829fc8d2302b4d0673970"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::ISR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3200966922a194d84425e2807a7f1328"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::IT</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2db9a52f6d42809627d1a7a607c5dbc5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::N</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="add7cbd2b0abd8954d62cd7831796ac7c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::Q</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7eed9fe24ae8d354cd76ae1c1110a658"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::T</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af14df16ea0690070c45b95f2116b7a0a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::V</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1a47176768f45f79076c4f5b1b534bc2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::w</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1e5d9801013d5146f2e02d9b7b3da562"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t xPSR_Type::Z</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="unionx_p_s_r___type.html">xPSR_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
