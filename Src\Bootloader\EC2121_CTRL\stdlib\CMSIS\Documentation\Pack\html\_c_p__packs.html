<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Pack: Create Software Packs</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Pack
   &#160;<span id="projectnumber">Version 1.3.1</span>
   </div>
   <div id="projectbrief">Delivery Mechanism for Software Packs</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li class="current"><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_c_p__packs.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Create Software Packs </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>The <b>CMSIS-Pack</b> format is used to deliver a Software Pack and is aimed to be scalable for future requirements. It provides a management process and supports a tool independent distribution for:</p>
<ul>
<li><a class="el" href="_create_pack__d_f_p.html">Device Support</a> for tool chains and software applications to target a specific device:<ul>
<li>Information about the processor and it's features.</li>
<li>C and assembly files for the device startup and access to the memory mapped peripheral registers.</li>
<li>Parameters, technical information, and data sheets about the device family and the specific devices.</li>
<li>Device description and available peripherals.</li>
<li>Memory layout of internal and external RAM and ROM address ranges.</li>
<li><a class="el" href="_flash_algorithm.html">Flash algorithms</a> for programming the device.</li>
<li>Debug and trace configurations as well as System View Description files for device specific display of the memory mapped peripheral registers.</li>
</ul>
</li>
</ul>
<ul>
<li><a class="el" href="_create_pack_board.html">Board Support</a> for rapid development in the prototyping stage:<ul>
<li>Information about the development board and it's features.</li>
<li>Parameters, technical information, and data sheets about the board, the mounted microcontroller and peripheral devices.</li>
<li>Drivers for on-board peripheral devices</li>
</ul>
</li>
</ul>
<ul>
<li><a class="el" href="_c_p__s_w_components.html">Software Components</a> simplify the re-use of software and the management of 3rd party software. They contain:<ul>
<li>A collection of source modules, header and <a class="el" href="_config_wizard.html">configuration files</a> as well as libraries.</li>
<li>Documentation of the software, including features and APIs.</li>
</ul>
</li>
</ul>
<ul>
<li><a class="el" href="_c_p__s_w_components.html#CP_API">Application Programming Interfaces</a> specify one or more APIs for <a class="el" href="_c_p__s_w_components.html#CP_Components">software components</a> that are part of another <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
</ul>
<ul>
<li><a class="el" href="_c_p__s_w_components.html#CP_Examples">Example Projects</a> can be used as a reference and starting point for similar applications. They contain:<ul>
<li>Fully defined application projects that work out of the box in the specified build and debug environments.</li>
<li>Documentation about the purpose of the example, how to build, configure and execute the application.</li>
</ul>
</li>
</ul>
<ul>
<li><a class="el" href="_c_p__s_w_components.html#CP_CodeTemplates">User Code Templates</a> help to understand the basic concepts of a software component better and to give the user a good starting point for implementing his application.</li>
</ul>
<h1><a class="anchor" id="CP_PackSteps"></a>
Steps to Create a Pack</h1>
<p>The following image shows the basic stpes that are required to create a Software Pack:</p>
<div class="image">
<img src="PackCreateSteps.png" alt="PackCreateSteps.png"/>
</div>
<ol type="1">
<li><b>Produce input:</b> Create the files that will be delivered with the Pack.</li>
<li><b>Organize files:</b> Use directories to separate the content of the Pack.</li>
<li><b>Create PDSC file:</b> The XML based PDSC file can be created using any text editor. Editors that can validate XML code against a schema file help to find bugs early in the Pack development stage.</li>
<li><b>Generate PACK:</b> Any compression tool supporting the ZIP format can be used to create a PACK file. <a class="el" href="_pack_chk.html">PackChk.exe</a> is helpful for the validation of the contents of a Pack.</li>
</ol>
<p>The following pages will show how to create a Software Pack from scratch. The page <a class="el" href="_c_p__s_w_components.html">Pack with Software Components</a> will start with a simple Pack containing only the PDSC file and a single software component and will then explain further elements of a Pack in more detail. The page <a class="el" href="_create_pack__d_f_p.html">Pack with Device Support</a> adds information on the additional requirements that a DFP has, while <a class="el" href="_create_pack_board.html">Pack with Board Support</a> will elaborate on the content specific to a BSP.</p>
<h1><a class="anchor" id="PackFilenames"></a>
Pack Conventions</h1>
<p>All files that belong to a <a class="el" href="_c_p__s_w_components.html">Software Pack</a> are compressed into a *.PACK file using a standard <a href="http://en.wikipedia.org/wiki/Zip_%28file_format%29" target="_blank">ZIP file format</a>. The content of each <a class="el" href="_c_p__s_w_components.html">Software Pack</a> is described in the Pack Description (*.PDSC) file that is part of each <b>Pack</b>. Refer to <a class="el" href="_pack_format.html">Pack Description (*.PDSC) Format</a> for more information.</p>
<p>A <a class="el" href="_c_p__s_w_components.html">Software Pack</a> must have a unique filename following this naming convention: <b>&lt;vendor&gt;.&lt;name&gt;.&lt;version&gt;.pack</b>.</p>
<p><b>Where:</b> </p>
<ul>
<li><b>&lt;vendor&gt;</b> is name of the supplier or vendor of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><b>&lt;name&gt;</b> is name of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>. It is the vendor's responsibility to ensure unique package names.</li>
<li><b>&lt;version&gt;</b>specifies the version number of the <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
<li><b>.pack</b> is the file extension identifying a <a class="el" href="_c_p__s_w_components.html">Software Pack</a>.</li>
</ul>
<p>The XML elements <b>&lt;vendor&gt;</b>, <b>&lt;name&gt;</b>, and <b>&lt;version&gt;</b> are declared in the <a class="el" href="_pack_format.html#Filenames">PDSC</a> file.</p>
<p>The PDSC file needs to reside at the top-level of the Pack file, using it as the root directory for all file references.</p>
<h1><a class="anchor" id="PackTutorials"></a>
Pack Tutorial</h1>
<p>The <b>ARM::CMSIS</b> Pack contains tutorials that are explained on the following pages. In the <b>\CMSIS\Pack\Tutorials</b> directory the following three ZIP files are available: </p>
<table class="doxtable">
<tr>
<th>ZIP File </th><th>Documentation</th></tr>
<tr>
<td>Pack_with_Software_Components.zip </td><td><a class="el" href="_c_p__s_w_components.html">Pack with Software Components</a> </td></tr>
<tr>
<td>Pack_with_Device_Support.zip </td><td><a class="el" href="_create_pack__d_f_p.html">Pack with Device Support</a> </td></tr>
<tr>
<td>Pack_with_Board_Support.zip </td><td><a class="el" href="_create_pack_board.html">Pack with Board Support</a> </td></tr>
</table>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:18:10 for CMSIS-Pack by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
