#ifndef __UPDATE_APP_H_
#define __UPDATE_APP_H_

#include "Globel.h"

#define UPDATE_STARTOTA_FLAG            0x00864000      /* ota升级标志 */
#define UPDATE_START_ADDRESS            0x00865000      /* 存储ota升级app起始地址 */
#define UPDATE_END_ADDRESS              0x008E4FFF      /* 存储升级app结束地址，实际不到该地址，只使用224k+40字节*/

typedef void (*PFNCRESET)(void);

uint32_t GetFirmwareFileSize(void);

uint32_t GetFirmwareFileCRCValue(void);

bool IsFirmwareOK(void);

bool IsJumpToApp(void);

bool IsOtaUpdata();//判断外部flash的ota升级app有效标志

bool ReadUpdateDataHead(uint8_t select);

bool UpdateProcess(uint8_t select);

#endif

