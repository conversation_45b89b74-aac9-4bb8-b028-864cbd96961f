<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Pack: PackChk.exe</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Pack
   &#160;<span id="projectnumber">Version 1.3.1</span>
   </div>
   <div id="projectbrief">Delivery Mechanism for Software Packs</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li class="current"><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_pack_chk.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">PackChk.exe </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><b>PackChk.exe</b> is a utility for validation of a Software Pack. It operates on the unzipped content of the Software Pack and is located in the directory <b>.\CMSIS\Utilities</b> of the <b>ARM::CMSIS</b> Pack.</p>
<p><b>PackChk.exe</b> performs the following operations:</p>
<ol type="1">
<li>Reads the content of the specified *.PDSC file. The path to this *.PDSC file is considered as root directory of the Software Pack.</li>
<li>Verifies the existence of all files in the Software Pack that are referenced in the *.PDSC file.</li>
<li>Checks for presence and correctness of mandatory elements such as &lt;vendor&gt;, &lt;version&gt;, etc.</li>
<li>Optional, all System View Description files (*.SVD) that are referenced in the *.PDSC file are checked using SVDConv.exe. Refer to <a href="../../SVD/html/svd_validate_file_pg.html">SVD File Validation</a> for details.</li>
<li>Optional, the element &lt;url&gt; is checked.</li>
<li>Optional, creates the standardized name of the Software Pack file.</li>
<li>Sets the exit status reflect the result of the validation to:<ul>
<li>0 : no errors detected</li>
<li>1 : errors during validation detected</li>
</ul>
</li>
</ol>
<h2>Operation</h2>
<p><b>PackChk.exe</b> is invoked from the command line. The general command is: <br/>
 </p>
<div class="fragment"><div class="line">PackChk.exe &lt;pdsc_file&gt; &lt;options&gt;</div>
</div><!-- fragment --><p><br/>
 </p>
<table  class="cmtable" summary="PackChk Args">
<tr>
<th>&lt;options&gt; </th><th>Short Name </th><th>Description  </th></tr>
<tr>
<td>-n &lt;file&gt;  </td><td>Pack file name </td><td>Write a standardized name for the Software Pack to the specified ASCII file.   </td></tr>
<tr>
<td>-s  </td><td>Verify SVD files </td><td>Each SVD file referenced in the *.PDSC file is <a href="../../SVD/html/svd_validate_file_pg.html"><b>validated</b></a> with the <b>SVDConv.exe</b>. With this option set, <b>SVDConv.exe</b> is invoked from the same directory as <b>PackChk.exe</b>.   </td></tr>
<tr>
<td>-u &lt;url&gt;  </td><td>Pack&#160;Server&#160;URL </td><td>Verifies that the specified URL matches the &lt;url&gt; element in the *.PDSC file.   </td></tr>
<tr>
<td>-v  </td><td>Verbose  </td><td>Prints extra process information.   </td></tr>
</table>
<p><b>Examples</b></p>
<p>Run <b>PackChk.exe</b> in verbose mode on the package description file called <b>MyVendor.MyPack.pdsc</b>. <b>PackChk.exe</b> reads this file and verifies it against the Software Pack that is located in the same directory. </p>
<pre class="fragment">PackChk.exe .\MyVendor.MyPack.pdsc -v
</pre><p>Run <b>PackChk.exe</b> on the package description file called <b>MyVendor.MVCM3.pdsc</b>, verify SVD files, verify the URL to the Pack Server, and generate a ASCII text file with the standardized name of the Software Pack. </p>
<pre class="fragment">PackChk.exe ".\MyVendor.MVCM3.pdsc" -s -u "http://www.myvendor.com/pack" -n packname.txt
</pre><h1><a class="anchor" id="PackChkMessages"></a>
Error and Warning Messages</h1>
<p>The following table shows the errors and warnings issued by PackChk.</p>
<h2>Internal Errors</h2>
<p>For internal errors, please sent a problem report via email to <a href="#" onclick="location.href='mai'+'lto:'+'cms'+'is'+'@ar'+'m.'+'com'; return false;">cmsis<span style="display: none;">.nosp@m.</span>@arm<span style="display: none;">.nosp@m.</span>.com</a>.</p>
<table  class="cmtable" summary="PackChk Internal Msgs">
<tr>
<th>Message Number </th><th>Type </th><th>Description </th><th>Action  </th></tr>
<tr>
<td>M100 </td><td>ERROR </td><td>GetModuleHandle failed </td><td>Call support.  </td></tr>
<tr>
<td>M101 </td><td>ERROR </td><td>Unknown error! </td><td>Call support.   </td></tr>
<tr>
<td>M102 </td><td>ERROR </td><td>MFC initialization failed </td><td>Call support.  </td></tr>
<tr>
<td>M103 </td><td>ERROR </td><td>Cannot launch SVDConv.exe </td><td>Check your input and search paths. Copy <b>SVDConv.exe</b> into the directory of <b>PackChk.exe</b>. Default is <span class="XML-Token">CMSIS\Utilities</span>  </td></tr>
<tr>
<td>M104 </td><td>ERROR </td><td>Error calling SVDConv.exe </td><td>Check that <b>SVDConv.exe</b> exists in the same path as <b>PackChk.exe</b>.   </td></tr>
<tr>
<td>M105 </td><td>ERROR </td><td>Unknown SVD Check error </td><td>Call support and provide the files you are using together with the <b>SVDConv.exe</b> and <b>PackChk.exe</b>.  </td></tr>
<tr>
<td>M106 </td><td>ERROR </td><td>SVDConv received invalid parameter </td><td>No arguments for <a href="../../SVD/html/svd__s_v_d_conv_pg.html">SVDCONV.exe</a> are allowed when used together with <b>PackChk.exe</b>.  </td></tr>
<tr>
<td>M107 </td><td>ERROR </td><td>Lost xml file stream at line <em>'LINENUMBER'</em>  </td><td>Repeat the process.  </td></tr>
</table>
<h2>Invocation Errors</h2>
<table  class="cmtable" summary="PackChk Invocation Msgs">
<tr>
<th>Message Number </th><th>Type </th><th>Description </th><th>Action  </th></tr>
<tr>
<td>M200 </td><td>ERROR </td><td>Invalid arguments! </td><td>At least one argument is wrong. Correct the argument.   </td></tr>
<tr>
<td>M201 </td><td>ERROR </td><td>Too many arguments! </td><td>The list of arguments is too long. Check if you have used one argument twice.   </td></tr>
<tr>
<td>M202 </td><td>ERROR </td><td>No PDSC input file specified </td><td>Correct the command line. <b>PackChk.exe</b> expects a *.PDSC file name as input.   </td></tr>
<tr>
<td>M203 </td><td>ERROR </td><td>Error reading PDSC file <em>'PATH/FILENAME'!</em>  </td><td>Verify the PDSC file for consistency.   </td></tr>
<tr>
<td>M204 </td><td>ERROR </td><td>File not found: <em>'PATH'</em>  </td><td>The specified PDSC file could not be found in the <em>PATH</em> displayed in the message. Correct the path or the filename.   </td></tr>
<tr>
<td>M205 </td><td>ERROR </td><td>Cannot create Pack Name file <em>'PATH'</em>  </td><td>Check the disk space or your permissions. Correct the path name.   </td></tr>
<tr>
<td>M206 </td><td>ERROR </td><td>Multiple PDSC files found in package: <em>'FILES'</em>  </td><td>Only one PDSC file is allowed in a package. Remove unnecessary PDSC files. The message lists all *.PDSC files found.   </td></tr>
<tr>
<td>M207 </td><td>ERROR </td><td>PDSC file name mismatch! <br/>
 Expected: <em>'PDSC1.pdsc'</em> <br/>
 Actual : <em>'PDSC2.pdsc'</em>  </td><td>The PDSC file expected has not been found. Rename or exchange the PDSC file.   </td></tr>
<tr>
<td>M208 </td><td>ERROR </td><td>Cannot find SVDConv.exe: <em>'PATH'</em> </td><td>SVDConv.exe was not found. Copy the executable into the directory of PackChk.exe or enter the path where SVDConv.exe is located. Default is <span class="XML-Token">CMSIS\Utilities</span>.   </td></tr>
</table>
<h2>Validation Errors</h2>
<table  class="cmtable" summary="PackChk Validation Msgs">
<tr>
<th>Message Number </th><th>Type </th><th>Description </th><th>Action  </th></tr>
<tr>
<td>M300 </td><td>ERROR </td><td>The following files are listed in PDSC but not part of the package: <br/>
 <em>'PATH'</em>: <br/>
 <em>'FILES'</em>  </td><td>The files listed should be part of the package. However, these files could not be added to the package. Check whether the files exist or check the file permissions.   </td></tr>
<tr>
<td>M301 </td><td>ERROR </td><td>Checking Pack URL of PDSC file failed: <br/>
 Expected URL : <em>'URL1'</em> <br/>
 Package URL : <em>'URL2'</em>  </td><td>The URL entered in the package does not match the value entered for comparison. Change the URL in the package. Check possible misspellings of <em>URL1</em>.   </td></tr>
<tr>
<td>M302 </td><td>ERROR </td><td>No vendor tag found in the PDSC file! Add the &lt;vendor&gt; tag and provide the vendor name. For example: <code>&lt;vendor&gt;Keil&lt;/vendor&gt;</code>. </td><td>No vendor entered in the PDSC file. Enter the tag <span class="XML-Token">&lt;vendor&gt;</span> and add the vendor name. Refer to <a class="el" href="pdsc_package_pg.html#element_package">/package</a>.   </td></tr>
<tr>
<td>M303 </td><td>ERROR </td><td>No package name found in the PDSC file! Add the &lt;name&gt; tag and provide the package name. </td><td>No package name found in the PDSC file. Enter the tag <span class="XML-Token">&lt;name&gt;</span> and add the package name. For example <span class="XML-Token">&lt;name&gt;&lt;MCU-Name&gt;_DFP.&lt;/name&gt;</span>. The package name is mandatory to create the package. Refer to <a class="el" href="pdsc_package_pg.html#element_package">/package</a>.   </td></tr>
<tr>
<td>M304 </td><td>ERROR </td><td>No package URL (&lt;url&gt;-tag and/or value) found in PDSC file! </td><td>Add the tag <span class="XML-Token">&lt;url&gt;</span> and enter a URL in the PDSC file. The URL points to the web page from which users can download the package. Refer to <a class="el" href="pdsc_package_pg.html#element_package">/package</a>.   </td></tr>
<tr>
<td>M305 </td><td>ERROR </td><td>No package version ("version"-attribute in a &lt;release&gt;-tag) found in PDSC file! </td><td>No version or release information found in the PDSC file. Add the attribute <span class="XML-Token">version</span> to the tag <span class="XML-Token">&lt;release&gt;</span> and enter the version number. Refer to <a class="el" href="element_releases.html#element_release">/package/releases/release</a>.   </td></tr>
<tr>
<td>M306 </td><td>ERROR </td><td>No package description found in the PDSC file. Add the &lt;description&gt;-tag and provide a descriptive text. </td><td>No package description was found in the PDSC file. Enter the tag <span class="XML-Token">&lt;description&gt;</span> and briefly describe the package content. Refer to <a class="el" href="pdsc_package_pg.html#element_package">/package</a>.   </td></tr>
<tr>
<td>M307 </td><td>ERROR </td><td>Checking SVD file <em>'PATH'</em> failed! </td><td>Check the consistency of the *.SVD file. Refer to <a href="../../SVD/html/svd_validate_file_pg.html">SVD File Validation</a>.   </td></tr>
<tr>
<td>M308 </td><td>ERROR </td><td>SVD file <em>'PATH'</em> not found. Add the SVD file or correct the PDSC entry. </td><td>The *.SVD file was not found in the path specified in the message. Add the SVD file or correct the entry in the *.PDSC file.   </td></tr>
<tr>
<td>M309 </td><td>ERROR </td><td>Cannot read file information: <em>'PATH'</em>. </td><td>Access to the file specified failed. Check permissions and spelling.   </td></tr>
<tr>
<td>M310 </td><td>ERROR </td><td>Filename mismatch (case sensitive):<br/>
 PDSC name : <em>'PDSC_FILENAME'</em> <br/>
 Filename : <em>'SYSTEM'</em>  </td><td>Filenames are case sensitive. Correct spelling.   </td></tr>
<tr>
<td>M311 </td><td>WARNING </td><td>Preamble for <em>'UTF'</em> should not be used, specify via '&lt;?xml'  </td><td>Specify the character set in the tag mentioned.   </td></tr>
<tr>
<td>M312 </td><td>WARNING </td><td>Unsupported format or extra characters found before '&lt;?xml': <em>'STR'</em>  </td><td>Correct the XML file.   </td></tr>
<tr>
<td>M313 </td><td>WARNING </td><td>UTF Format not supported: <em>'UTF'</em>  </td><td>Select another UTF-format.   </td></tr>
<tr>
<td>M314 </td><td>ERROR </td><td>Cannot decode XML special character: <em>'SPECIALCHAR'</em>. Found END Tag!  </td><td>Correct the XML file.   </td></tr>
<tr>
<td>M315 </td><td>ERROR </td><td>Cannot decode XML special character: <em>'SPECIALCHAR'</em>. String too long!  </td><td>Correct the XML file.   </td></tr>
<tr>
<td>M316 </td><td>ERROR </td><td><em>'&lt;--'</em> found, should this be a comment '&lt;!--' ?  </td><td>Correct the XML file.   </td></tr>
<tr>
<td>M317 </td><td>ERROR </td><td>Begin Tag seems to end with a Single Tag. Is this a typo?  </td><td>Correct the XML file.   </td></tr>
<tr>
<td>M318 </td><td>ERROR </td><td>Inconsistent XML Structure  </td><td>Correct the XML file.   </td></tr>
<tr>
<td>M319 </td><td>ERROR </td><td>XML Stack deeper than 30 Items! Giving up... </td><td>Reduce the nested nodes.   </td></tr>
<tr>
<td>M320 </td><td>ERROR </td><td>Begin Tag follows Text. Missing End Tag? </td><td>Correct the XML file.   </td></tr>
<tr>
<td>M321 </td><td>ERROR </td><td>Missing '"' in Attributes: <em>'ATTRLINE'</em>  </td><td>Correct the XML file. Enclose values in " ".   </td></tr>
</table>
<p>&#160;</p>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="_create_pack_util.html">Utilities for Creating Packs</a></li>
    <li class="footer">Generated on Tue Sep 9 2014 08:18:10 for CMSIS-Pack by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
