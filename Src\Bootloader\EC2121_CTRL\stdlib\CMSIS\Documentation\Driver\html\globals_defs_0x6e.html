<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li class="current"><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals_defs.html#index__"><span>_</span></a></li>
      <li><a href="globals_defs_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_defs_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_defs_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_defs_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_defs_0x6d.html#index_m"><span>m</span></a></li>
      <li class="current"><a href="globals_defs_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_defs_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_defs_0x75.html#index_u"><span>u</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_defs_0x6e.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_n"></a>- n -</h3><ul>
<li>ARM_NAND_API_VERSION
: <a class="el" href="_driver___n_a_n_d_8h.html#a121ff96c31275cef4bb7e86007665e1c">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DATA_WIDTH
: <a class="el" href="group__nand__control__codes.html#ga2d3356f5b47871c465ae7136a2c533f4">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DATA_WIDTH_16
: <a class="el" href="group__nand__data__bus__width__codes.html#ga49e0e3a946a4d9f26dbd5b32ccc3b2f3">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DATA_WIDTH_8
: <a class="el" href="group__nand__data__bus__width__codes.html#ga578051cc193ae0b7125aec8007071d21">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR
: <a class="el" href="group__nand__bus__mode__codes.html#ga82b8261b3d0d85881535adada318a7df">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2
: <a class="el" href="group__nand__bus__mode__codes.html#ga13c102201d6021db184a2f068656c518">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_CMPD
: <a class="el" href="group__nand__bus__mode__codes.html#gad38354e4a34adbf881afc7f89ff06e89">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_CMPR
: <a class="el" href="group__nand__bus__mode__codes.html#ga8a2d599082b9fe56cee1c6454bb3c6a1">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DI_WCYC_0
: <a class="el" href="group__nand__bus__mode__codes.html#gaeee1853dea5e96cb19d2596cc0e70169">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DI_WCYC_1
: <a class="el" href="group__nand__bus__mode__codes.html#ga42560a1f046e20cc4956276156c4ce25">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DI_WCYC_2
: <a class="el" href="group__nand__bus__mode__codes.html#gaad2e7807292d84a5070143626f5c2756">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DI_WCYC_4
: <a class="el" href="group__nand__bus__mode__codes.html#ga3ebb54a1ae971cd34f3c8fc9ff3ab6d5">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DI_WCYC_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#ad9ab38101de68a1bc186f5687f63f7c3">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DI_WCYC_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#aa80b898cdf665aa14ff0e181e4ff31f1">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DO_WCYC_0
: <a class="el" href="group__nand__bus__mode__codes.html#ga77348df5f5c2c96bcaeec60b6da02c1b">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DO_WCYC_1
: <a class="el" href="group__nand__bus__mode__codes.html#ga5839be0b4b2eb930ec039a3403b5e89e">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DO_WCYC_2
: <a class="el" href="group__nand__bus__mode__codes.html#ga10a1ef3be69bfa7e6cc657bee751a077">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DO_WCYC_4
: <a class="el" href="group__nand__bus__mode__codes.html#ga7f9e8416c4a4e20c4a04323e39f2100d">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DO_WCYC_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#ad30dfdbdc50a7ff72a5bb173c5f549dc">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_DO_WCYC_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#a57b282c0818c87b79ea4f11d03cc4f3c">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_DDR2_VEN
: <a class="el" href="group__nand__bus__mode__codes.html#ga465ae06a6e097959620346304182e273">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_INTERFACE_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#aea213eb1ba9c67beb6216a630d81b91f">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_INTERFACE_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#a372fc9b9cc1315046ceaffd6fd99e12c">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_MODE
: <a class="el" href="group__nand__control__codes.html#ga9b063c3078e86b50d4aa892518b2e2d8">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_SDR
: <a class="el" href="group__nand__bus__mode__codes.html#gac7743aeb6411b97f9fc6a24b556f4963">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_0
: <a class="el" href="group__nand__bus__mode__codes.html#ga971e574ac412bbba445055e9afc384ba">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_1
: <a class="el" href="group__nand__bus__mode__codes.html#ga475a339e929eca46e11bc8a7b330aa45">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_2
: <a class="el" href="group__nand__bus__mode__codes.html#gaed6154fb03b5516faf0bfd11d7a46309">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_3
: <a class="el" href="group__nand__bus__mode__codes.html#gacbc4e07e1af6ef0e4c656428e81464a9">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_4
: <a class="el" href="group__nand__bus__mode__codes.html#ga709d51a5215cd23ce2d85aec57141456">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_5
: <a class="el" href="group__nand__bus__mode__codes.html#gaee3cad14ce2b8b9af69149bf74597791">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_6
: <a class="el" href="group__nand__bus__mode__codes.html#ga4a3524e0eba994b3a66e06cde877f0f6">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_7
: <a class="el" href="group__nand__bus__mode__codes.html#gaa63d75f5f2b48a7345a066d58de1bd23">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#a57f6c319265b00878661656103abe660">Driver_NAND.h</a>
</li>
<li>ARM_NAND_BUS_TIMING_MODE_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#acc98e42d23656734c7f9a8a5421842d6">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_COL1_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#a0951de69f3836c1ab229ec60b3996fcc">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_COL1_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#ab8b06772e2b6c5930319b17bbb806133">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_COL2_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#a6126261e7c53713cee04aeae839d330e">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_COL2_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#a1c4b9e7f44f77ebf665af8860a3c7528">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_ROW1_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#ac24600be47e725ab1ad4193fd84daf80">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_ROW1_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#a8b75efa00810fcf23fb0f12e7f62d338">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_ROW2_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#ae17a3f9b9fd70a88f9f9f38dd2c17951">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_ROW2_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#a326e135c57b38c78ae88cea121722a30">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_ROW3_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#acf1ecacc2b225877c9cfe4f15dafc03c">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_ADDR_ROW3_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#a6873f7aedfe81efa8ca21dc85cbb384c">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_CMD1_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#ac65db62329bb943592afdb523e4aadca">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_CMD1_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#ae34722cf52938f50bf117780a742b6f1">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_CMD2_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#a0f963016c81be2ddf7a09d983de226a9">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_CMD2_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#aeebe274650e7d0c02b478318759972e5">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_CMD3_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#a16d474e55d0f6ea6efc3cc5436493b22">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_CMD3_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#aa0b87b819cf3c94f32e3ef18dcfd1c6c">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_INC_ADDR_ROW
: <a class="el" href="group__nand__driver__seq__exec__codes.html#ga959522c98183036da32984dd5e07979b">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_READ_DATA
: <a class="el" href="group__nand__driver__seq__exec__codes.html#gab524d840ab57c720ce8560144651dc9d">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_READ_STATUS
: <a class="el" href="group__nand__driver__seq__exec__codes.html#ga2250f6a532d2c0834bfdc618761ddc86">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_SEND_ADDR_COL1
: <a class="el" href="group__nand__driver__seq__exec__codes.html#ga891bcba60ebb1195ec80c00c9bec748a">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_SEND_ADDR_COL2
: <a class="el" href="group__nand__driver__seq__exec__codes.html#ga62a3f6ddcfb9ee317655bbec9e09bc10">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_SEND_ADDR_ROW1
: <a class="el" href="group__nand__driver__seq__exec__codes.html#gadc001e69d1e81dc28a542237c6fe11ff">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_SEND_ADDR_ROW2
: <a class="el" href="group__nand__driver__seq__exec__codes.html#ga5e55628cb59f5d7d35c529f04ebfcd10">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_SEND_ADDR_ROW3
: <a class="el" href="group__nand__driver__seq__exec__codes.html#gaeb5d1be9c13b7ad2ad246d5db10cd419">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_SEND_CMD1
: <a class="el" href="group__nand__driver__seq__exec__codes.html#gaef90c96cd4f2309044d7d438c6b0930a">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_SEND_CMD2
: <a class="el" href="group__nand__driver__seq__exec__codes.html#gacffafbbbca74f7ffa4cd3bb6b067c4ef">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_SEND_CMD3
: <a class="el" href="group__nand__driver__seq__exec__codes.html#ga20f96743ab77bda14ba391dc0c3cdba5">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_WAIT_BUSY
: <a class="el" href="group__nand__driver__seq__exec__codes.html#ga0f4a8b1e97656e09f1c383852f290a37">Driver_NAND.h</a>
</li>
<li>ARM_NAND_CODE_WRITE_DATA
: <a class="el" href="group__nand__driver__seq__exec__codes.html#ga1b40fc5fbf22dc4fa8130f5836e30d12">Driver_NAND.h</a>
</li>
<li>ARM_NAND_DEVICE_READY_EVENT
: <a class="el" href="group__nand__control__codes.html#ga1bffc9f341e704ee0e845d86a2989921">Driver_NAND.h</a>
</li>
<li>ARM_NAND_DRIVER_DONE_EVENT
: <a class="el" href="group__nand__driver__flag__codes.html#gaf40631ba62411e0ac06c3a945d608581">Driver_NAND.h</a>
</li>
<li>ARM_NAND_DRIVER_READY_EVENT
: <a class="el" href="group__nand__control__codes.html#gaab6dea1b565aeb53e360876a4e50783c">Driver_NAND.h</a>
</li>
<li>ARM_NAND_DRIVER_STRENGTH
: <a class="el" href="group__nand__control__codes.html#ga5d1d46198404fe115b013bdae7af2a2f">Driver_NAND.h</a>
</li>
<li>ARM_NAND_DRIVER_STRENGTH_18
: <a class="el" href="group__nand__driver__strength__codes.html#ga942e20df12022f3bbd0e9a558ec1c7a0">Driver_NAND.h</a>
</li>
<li>ARM_NAND_DRIVER_STRENGTH_25
: <a class="el" href="group__nand__driver__strength__codes.html#ga17188e039f5f87c581033327399a057d">Driver_NAND.h</a>
</li>
<li>ARM_NAND_DRIVER_STRENGTH_35
: <a class="el" href="group__nand__driver__strength__codes.html#ga33562a66a5bf328eea82b2f1893a7874">Driver_NAND.h</a>
</li>
<li>ARM_NAND_DRIVER_STRENGTH_50
: <a class="el" href="group__nand__driver__strength__codes.html#gaa502e2c995447037d266f939faa43223">Driver_NAND.h</a>
</li>
<li>ARM_NAND_ECC
: <a class="el" href="group__nand__driver__ecc__codes.html#gac2eb4475f12a443209165d29fe200030">Driver_NAND.h</a>
</li>
<li>ARM_NAND_ECC0
: <a class="el" href="group__nand__driver__ecc__codes.html#ga15c79a12200c16f953936635f930df1d">Driver_NAND.h</a>
</li>
<li>ARM_NAND_ECC1
: <a class="el" href="group__nand__driver__ecc__codes.html#gaee653288a88318ee33d1db81baa69bbc">Driver_NAND.h</a>
</li>
<li>ARM_NAND_ECC_INDEX_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#a656537439264ab495c86e4c36051a3c1">Driver_NAND.h</a>
</li>
<li>ARM_NAND_ECC_INDEX_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#a7944be4f63c439d5d64053ad9476407b">Driver_NAND.h</a>
</li>
<li>ARM_NAND_ERROR_ECC
: <a class="el" href="group__nand__execution__status.html#gafebec6ac091750a47b1d59bc843c15b0">Driver_NAND.h</a>
</li>
<li>ARM_NAND_EVENT_DEVICE_READY
: <a class="el" href="group___n_a_n_d__events.html#gae0be7e1b41188def905de0a1568d442d">Driver_NAND.h</a>
</li>
<li>ARM_NAND_EVENT_DRIVER_DONE
: <a class="el" href="group___n_a_n_d__events.html#gac774a334871789d24107b843d1ebd00c">Driver_NAND.h</a>
</li>
<li>ARM_NAND_EVENT_DRIVER_READY
: <a class="el" href="group___n_a_n_d__events.html#ga7b390a906db42c5ea4db38e0e85bb9e9">Driver_NAND.h</a>
</li>
<li>ARM_NAND_EVENT_ECC_ERROR
: <a class="el" href="group___n_a_n_d__events.html#ga7bee0c32528ab991c0c064f895f80664">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCC_1V8
: <a class="el" href="_driver___n_a_n_d_8h.html#aa7b9d5a71125b745caba5c1d7aff6385">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCC_3V3
: <a class="el" href="_driver___n_a_n_d_8h.html#ad15355d67bc239ff49cceac69c2024b3">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCC_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#ad898ef5cd4ffe3b6b09d69e224aa0912">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCC_OFF
: <a class="el" href="_driver___n_a_n_d_8h.html#a323c320a6195b78c2c79f5c6e85f02e1">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCC_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#a848a27ec9ebf0a13a82a1d9760f39d90">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCCQ_1V8
: <a class="el" href="_driver___n_a_n_d_8h.html#a653d9b4d7bee173beb49d8fec0469476">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCCQ_3V3
: <a class="el" href="_driver___n_a_n_d_8h.html#a6d5a8a33a0fdaaff2e57e1ac53c984c2">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCCQ_Msk
: <a class="el" href="_driver___n_a_n_d_8h.html#a7a453227301d7c08d09b22dc8afafbe7">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCCQ_OFF
: <a class="el" href="_driver___n_a_n_d_8h.html#aca7679e8269ee986559f4218816937c3">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VCCQ_Pos
: <a class="el" href="_driver___n_a_n_d_8h.html#ac38023b94cd8a68295d48a1019a386e0">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VPP_OFF
: <a class="el" href="_driver___n_a_n_d_8h.html#ae2d278901881ffc73d3e0b48717b22f0">Driver_NAND.h</a>
</li>
<li>ARM_NAND_POWER_VPP_ON
: <a class="el" href="_driver___n_a_n_d_8h.html#aeb0d50e30bbcd8ab59c3b78db634aad5">Driver_NAND.h</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
