<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_fir_example_f32.c</title>
<title>CMSIS-DSP: arm_fir_example_f32.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_fir_example_f32_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_fir_example_f32.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment">* $Date:         17. January 2013</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.0</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment"> * Title:        arm_fir_example_f32.c</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * Description:  Example code demonstrating how an FIR filter can be used</span></div>
<div class="line"><span class="comment"> *               as a low pass filter.</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment"> * -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** Include Files</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;math_helper.h&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** Macro Defines</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#define TEST_LENGTH_SAMPLES  320</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define SNR_THRESHOLD_F32    140.0f</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define BLOCK_SIZE            32</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define NUM_TAPS              29</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* -------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * The input signal and reference output (computed with MATLAB)</span></div>
<div class="line"><span class="comment"> * are defined externally in arm_fir_lpf_data.c.</span></div>
<div class="line"><span class="comment"> * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="arm__fir__data_8c.html#a143154a165358f0016714cb7f1c83970">testInput_f32_1kHz_15kHz</a>[<a name="a1"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>];</div>
<div class="line"><span class="keyword">extern</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a2"></a><a class="code" href="arm__fir__data_8c.html#aa7570f8c2e7a3c929d9d32a14a51389f">refOutput</a>[<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* -------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Declare Test output buffer</span></div>
<div class="line"><span class="comment"> * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a3"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>[<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* -------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Declare State buffer of size (numTaps + blockSize - 1)</span></div>
<div class="line"><span class="comment"> * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a4"></a><a class="code" href="arm__signal__converge__example__f32_8c.html#a358ec4e79689e6d3787b89fe78bdb772">firStateF32</a>[<a name="a5"></a><a class="code" href="arm__fir__example__f32_8c.html#ad51ded0bbd705f02f73fc60c0b721ced">BLOCK_SIZE</a> + <a name="a6"></a><a class="code" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a> - 1];</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** FIR Coefficients buffer generated using fir1() MATLAB function.</span></div>
<div class="line"><span class="comment">** fir1(28, 6/24)</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a7"></a><a class="code" href="arm__fir__example__f32_8c.html#ae070afd14f437ad1ae0a947e4403dd0e">firCoeffs32</a>[<a class="code" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a>] = {</div>
<div class="line">  -0.0018225230f, -0.0015879294f, +0.0000000000f, +0.0036977508f, +0.0080754303f, +0.0085302217f, -0.0000000000f, -0.0173976984f,</div>
<div class="line">  -0.0341458607f, -0.0333591565f, +0.0000000000f, +0.0676308395f, +0.1522061835f, +0.2229246956f, +0.2504960933f, +0.2229246956f,</div>
<div class="line">  +0.1522061835f, +0.0676308395f, +0.0000000000f, -0.0333591565f, -0.0341458607f, -0.0173976984f, -0.0000000000f, +0.0085302217f,</div>
<div class="line">  +0.0080754303f, +0.0036977508f, +0.0000000000f, -0.0015879294f, -0.0018225230f</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Global variables for FIR LPF Example</span></div>
<div class="line"><span class="comment"> * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">uint32_t <a name="a8"></a><a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a> = <a class="code" href="arm__fir__example__f32_8c.html#ad51ded0bbd705f02f73fc60c0b721ced">BLOCK_SIZE</a>;</div>
<div class="line">uint32_t <a name="a9"></a><a class="code" href="arm__fir__example__f32_8c.html#af7d5613bda9a19b2ccae5d6cb79a22bc">numBlocks</a> = <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>/<a class="code" href="arm__fir__example__f32_8c.html#ad51ded0bbd705f02f73fc60c0b721ced">BLOCK_SIZE</a>;</div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  <a name="a10"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a>;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * FIR LPF Example</span></div>
<div class="line"><span class="comment"> * ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">int32_t <a name="a11"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">  uint32_t i;</div>
<div class="line">  <a name="_a12"></a><a class="code" href="structarm__fir__instance__f32.html" title="Instance structure for the floating-point FIR filter.">arm_fir_instance_f32</a> S;</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a13"></a><a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>;</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  *inputF32, *outputF32;</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Initialize input and output buffer pointers */</span></div>
<div class="line">  inputF32 = &amp;<a class="code" href="arm__fir__data_8c.html#a143154a165358f0016714cb7f1c83970">testInput_f32_1kHz_15kHz</a>[0];</div>
<div class="line">  outputF32 = &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>[0];</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Call FIR init function to initialize the instance structure. */</span></div>
<div class="line">  <a name="a14"></a><a class="code" href="group___f_i_r.html#ga98d13def6427e29522829f945d0967db" title="Initialization function for the floating-point FIR filter.">arm_fir_init_f32</a>(&amp;S, <a class="code" href="arm__fir__example__f32_8c.html#a7579d94e0a80fb9d376ea6c7897f73b0">NUM_TAPS</a>, (<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)&amp;<a class="code" href="arm__fir__example__f32_8c.html#ae070afd14f437ad1ae0a947e4403dd0e">firCoeffs32</a>[0], &amp;<a class="code" href="arm__signal__converge__example__f32_8c.html#a358ec4e79689e6d3787b89fe78bdb772">firStateF32</a>[0], <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Call the FIR process function for every blockSize samples</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">for</span>(i=0; i &lt; <a class="code" href="arm__fir__example__f32_8c.html#af7d5613bda9a19b2ccae5d6cb79a22bc">numBlocks</a>; i++)</div>
<div class="line">  {</div>
<div class="line">    <a name="a15"></a><a class="code" href="group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a" title="Processing function for the floating-point FIR filter.">arm_fir_f32</a>(&amp;S, inputF32 + (i * <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>), outputF32 + (i * blockSize), blockSize);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Compare the generated output against the reference output computed</span></div>
<div class="line"><span class="comment">  ** in MATLAB.</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">  <a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a> = <a name="a16"></a><a class="code" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#aeea2952e70a1040a6efa555564bbeeab" title="Caluclation of SNR.">arm_snr_f32</a>(&amp;<a class="code" href="arm__fir__data_8c.html#aa7570f8c2e7a3c929d9d32a14a51389f">refOutput</a>[0], &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>[0], <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>);</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span> (<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af06013f588a7003278de222913c9d819">snr</a> &lt; <a name="a17"></a><a class="code" href="arm__fir__example__f32_8c.html#af7d1dd4deffa8e7ed6429e5dd0fe1812">SNR_THRESHOLD_F32</a>)</div>
<div class="line">  {</div>
<div class="line">    status = <a name="a18"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>;</div>
<div class="line">  }</div>
<div class="line">  <span class="keywordflow">else</span></div>
<div class="line">  {</div>
<div class="line">    status = <a name="a19"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>;</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Loop here if the signal does not match the reference output.</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span>( status != <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>)</div>
<div class="line">  {</div>
<div class="line">    <span class="keywordflow">while</span>(1);</div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
<div class="line"></div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
