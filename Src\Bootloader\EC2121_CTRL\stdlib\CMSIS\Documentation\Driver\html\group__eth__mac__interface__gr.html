<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Ethernet MAC Interface</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__eth__mac__interface__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Content</a> &#124;
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Ethernet MAC Interface<div class="ingroups"><a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Driver API for Ethernet MAC Peripheral (Driver_ETH_MAC.h)  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Content</h2></td></tr>
<tr class="memitem:group___e_t_h___m_a_c__events"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___e_t_h___m_a_c__events.html">Ethernet MAC Events</a></td></tr>
<tr class="memdesc:group___e_t_h___m_a_c__events"><td class="mdescLeft">&#160;</td><td class="mdescRight">The Ethernet MAC driver generates call back events that are notified via the function <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a>. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__eth__mac__control"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__control.html">Ethernet MAC Control Codes</a></td></tr>
<tr class="memdesc:group__eth__mac__control"><td class="mdescLeft">&#160;</td><td class="mdescRight">Configure and control the Ethernet MAC using the <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a>. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__eth__mac__time__control"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__time__control.html">Ethernet MAC Timer Control Codes</a></td></tr>
<tr class="memdesc:group__eth__mac__time__control"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control codes for <a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">ARM_ETH_MAC_ControlTimer</a> function. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__eth__mac__frame__transmit__ctrls"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__frame__transmit__ctrls.html">Ethernet MAC Frame Transmit Flags</a></td></tr>
<tr class="memdesc:group__eth__mac__frame__transmit__ctrls"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specify frame transmit flags. <br/></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_ETH_MAC_CAPABILITIES</a></td></tr>
<tr class="memdesc:struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ethernet MAC Capabilities.  <a href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c">ARM_DRIVER_ETH_MAC</a></td></tr>
<tr class="memdesc:struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access structure of the Ethernet MAC Driver.  <a href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:struct_a_r_m___e_t_h___m_a_c___t_i_m_e"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a></td></tr>
<tr class="memdesc:struct_a_r_m___e_t_h___m_a_c___t_i_m_e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ethernet MAC Time.  <a href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">More...</a><br/></td></tr>
<tr class="separator:struct_a_r_m___e_t_h___m_a_c___t_i_m_e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gadfc95cb09c541a29a72da86963668726"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#gadfc95cb09c541a29a72da86963668726">ARM_ETH_MAC_SignalEvent_t</a> )(uint32_t event)</td></tr>
<tr class="memdesc:gadfc95cb09c541a29a72da86963668726"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> : Signal Ethernet Event.  <a href="#gadfc95cb09c541a29a72da86963668726">More...</a><br/></td></tr>
<tr class="separator:gadfc95cb09c541a29a72da86963668726"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga86b15062c297384ad5842dd57b9d6b1d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga86b15062c297384ad5842dd57b9d6b1d">ARM_ETH_MAC_GetVersion</a> (void)</td></tr>
<tr class="memdesc:ga86b15062c297384ad5842dd57b9d6b1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver version.  <a href="#ga86b15062c297384ad5842dd57b9d6b1d">More...</a><br/></td></tr>
<tr class="separator:ga86b15062c297384ad5842dd57b9d6b1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2b13b230502736d8c7679b359dff20d0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_ETH_MAC_CAPABILITIES</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga2b13b230502736d8c7679b359dff20d0">ARM_ETH_MAC_GetCapabilities</a> (void)</td></tr>
<tr class="memdesc:ga2b13b230502736d8c7679b359dff20d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get driver capabilities.  <a href="#ga2b13b230502736d8c7679b359dff20d0">More...</a><br/></td></tr>
<tr class="separator:ga2b13b230502736d8c7679b359dff20d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacf42d11b171cd032f0ec1de6db2b6832"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#gacf42d11b171cd032f0ec1de6db2b6832">ARM_ETH_MAC_Initialize</a> (<a class="el" href="group__eth__mac__interface__gr.html#gadfc95cb09c541a29a72da86963668726">ARM_ETH_MAC_SignalEvent_t</a> cb_event)</td></tr>
<tr class="memdesc:gacf42d11b171cd032f0ec1de6db2b6832"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize Ethernet MAC Device.  <a href="#gacf42d11b171cd032f0ec1de6db2b6832">More...</a><br/></td></tr>
<tr class="separator:gacf42d11b171cd032f0ec1de6db2b6832"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacb2c2ae06f32328775bffbdeaaabfb5d"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#gacb2c2ae06f32328775bffbdeaaabfb5d">ARM_ETH_MAC_Uninitialize</a> (void)</td></tr>
<tr class="memdesc:gacb2c2ae06f32328775bffbdeaaabfb5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">De-initialize Ethernet MAC Device.  <a href="#gacb2c2ae06f32328775bffbdeaaabfb5d">More...</a><br/></td></tr>
<tr class="separator:gacb2c2ae06f32328775bffbdeaaabfb5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga346fef040a0e9bac5762a04a306b1be7"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga346fef040a0e9bac5762a04a306b1be7">ARM_ETH_MAC_PowerControl</a> (<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:ga346fef040a0e9bac5762a04a306b1be7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control Ethernet MAC Device Power.  <a href="#ga346fef040a0e9bac5762a04a306b1be7">More...</a><br/></td></tr>
<tr class="separator:ga346fef040a0e9bac5762a04a306b1be7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga66308c1e791952047e974bd653037fae"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga66308c1e791952047e974bd653037fae">ARM_ETH_MAC_GetMacAddress</a> (<a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr)</td></tr>
<tr class="memdesc:ga66308c1e791952047e974bd653037fae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get Ethernet MAC Address.  <a href="#ga66308c1e791952047e974bd653037fae">More...</a><br/></td></tr>
<tr class="separator:ga66308c1e791952047e974bd653037fae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7cc3d17c7312c5032202dfd9a915f24a"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga7cc3d17c7312c5032202dfd9a915f24a">ARM_ETH_MAC_SetMacAddress</a> (const <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr)</td></tr>
<tr class="memdesc:ga7cc3d17c7312c5032202dfd9a915f24a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set Ethernet MAC Address.  <a href="#ga7cc3d17c7312c5032202dfd9a915f24a">More...</a><br/></td></tr>
<tr class="separator:ga7cc3d17c7312c5032202dfd9a915f24a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga150fe30290275a4b32756f94208124e8"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga150fe30290275a4b32756f94208124e8">ARM_ETH_MAC_SetAddressFilter</a> (const <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr, uint32_t num_addr)</td></tr>
<tr class="memdesc:ga150fe30290275a4b32756f94208124e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Configure Address Filter.  <a href="#ga150fe30290275a4b32756f94208124e8">More...</a><br/></td></tr>
<tr class="separator:ga150fe30290275a4b32756f94208124e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5bf58defdb239ed7dc948f1da147a1c3"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3">ARM_ETH_MAC_SendFrame</a> (const uint8_t *frame, uint32_t len, uint32_t flags)</td></tr>
<tr class="memdesc:ga5bf58defdb239ed7dc948f1da147a1c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send Ethernet frame.  <a href="#ga5bf58defdb239ed7dc948f1da147a1c3">More...</a><br/></td></tr>
<tr class="separator:ga5bf58defdb239ed7dc948f1da147a1c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b79f57d8624bb4410ee12c73a483993"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a> (uint8_t *frame, uint32_t len)</td></tr>
<tr class="memdesc:ga4b79f57d8624bb4410ee12c73a483993"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read data of received Ethernet frame.  <a href="#ga4b79f57d8624bb4410ee12c73a483993">More...</a><br/></td></tr>
<tr class="separator:ga4b79f57d8624bb4410ee12c73a483993"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ee86d6b0efab5329b9bc191c23a466d"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga5ee86d6b0efab5329b9bc191c23a466d">ARM_ETH_MAC_GetRxFrameSize</a> (void)</td></tr>
<tr class="memdesc:ga5ee86d6b0efab5329b9bc191c23a466d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get size of received Ethernet frame.  <a href="#ga5ee86d6b0efab5329b9bc191c23a466d">More...</a><br/></td></tr>
<tr class="separator:ga5ee86d6b0efab5329b9bc191c23a466d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7c6865fb09754be869778142466c5e4"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#gaa7c6865fb09754be869778142466c5e4">ARM_ETH_MAC_GetRxFrameTime</a> (<a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td></tr>
<tr class="memdesc:gaa7c6865fb09754be869778142466c5e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get time of received Ethernet frame.  <a href="#gaa7c6865fb09754be869778142466c5e4">More...</a><br/></td></tr>
<tr class="separator:gaa7c6865fb09754be869778142466c5e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga115b5c7e149aec2b181de760f5d83f60"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga115b5c7e149aec2b181de760f5d83f60">ARM_ETH_MAC_GetTxFrameTime</a> (<a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td></tr>
<tr class="memdesc:ga115b5c7e149aec2b181de760f5d83f60"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get time of transmitted Ethernet frame.  <a href="#ga115b5c7e149aec2b181de760f5d83f60">More...</a><br/></td></tr>
<tr class="separator:ga115b5c7e149aec2b181de760f5d83f60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3e90c66058d20077f04ac8e8b8d0536"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a> (uint32_t control, uint32_t arg)</td></tr>
<tr class="memdesc:gac3e90c66058d20077f04ac8e8b8d0536"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control Ethernet Interface.  <a href="#gac3e90c66058d20077f04ac8e8b8d0536">More...</a><br/></td></tr>
<tr class="separator:gac3e90c66058d20077f04ac8e8b8d0536"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga85d9dc865af3702b71a514b18a588643"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">ARM_ETH_MAC_ControlTimer</a> (uint32_t control, <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td></tr>
<tr class="memdesc:ga85d9dc865af3702b71a514b18a588643"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control Precision Timer.  <a href="#ga85d9dc865af3702b71a514b18a588643">More...</a><br/></td></tr>
<tr class="separator:ga85d9dc865af3702b71a514b18a588643"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaded29ad58366e9222487db9944373c29"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">ARM_ETH_MAC_PHY_Read</a> (uint8_t phy_addr, uint8_t reg_addr, uint16_t *data)</td></tr>
<tr class="memdesc:gaded29ad58366e9222487db9944373c29"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read Ethernet PHY Register through Management Interface.  <a href="#gaded29ad58366e9222487db9944373c29">More...</a><br/></td></tr>
<tr class="separator:gaded29ad58366e9222487db9944373c29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga79dd38672749aeebd28f39d9b4f813ce"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">ARM_ETH_MAC_PHY_Write</a> (uint8_t phy_addr, uint8_t reg_addr, uint16_t data)</td></tr>
<tr class="memdesc:ga79dd38672749aeebd28f39d9b4f813ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Write Ethernet PHY Register through Management Interface.  <a href="#ga79dd38672749aeebd28f39d9b4f813ce">More...</a><br/></td></tr>
<tr class="separator:ga79dd38672749aeebd28f39d9b4f813ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae0697be4c4229601f3bfc17e2978ada6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> (uint32_t event)</td></tr>
<tr class="memdesc:gae0697be4c4229601f3bfc17e2978ada6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Callback function that signals a Ethernet Event.  <a href="#gae0697be4c4229601f3bfc17e2978ada6">More...</a><br/></td></tr>
<tr class="separator:gae0697be4c4229601f3bfc17e2978ada6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Driver API for Ethernet MAC Peripheral (Driver_ETH_MAC.h) </p>
<p>The following section describes the Ethernet MAC Interface as defined in the Driver_ETH_MAC.h header file. </p>
<hr/><h2 class="groupheader">Data Structure Documentation</h2>
<a name="struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s" id="struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_ETH_MAC_CAPABILITIES</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Ethernet MAC Capabilities. </p>
<p>An Ethernet MAC driver can be implemented with different capabilities. The bitfield members of this struct encode the capabilities implemented by this driver.</p>
<p><b>Returned by:</b></p>
<ul>
<li><a class="el" href="group__eth__mac__interface__gr.html#ga2b13b230502736d8c7679b359dff20d0">ARM_ETH_MAC_GetCapabilities</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a142179445bfdbaaaf0d451f277fb0e96"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_rx_icmp: 1</td>
<td class="fielddoc">
1 = ICMP payload checksum verified on receive </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a0051111be2e389c3161da1c444746216"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_rx_ip4: 1</td>
<td class="fielddoc">
1 = IPv4 header checksum verified on receive </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a674b2306c64901e924b3cb7bb882f32f"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_rx_ip6: 1</td>
<td class="fielddoc">
1 = IPv6 checksum verification supported on receive </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a730d6be6a7b868e0690d9548e77b7aae"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_rx_tcp: 1</td>
<td class="fielddoc">
1 = TCP payload checksum verified on receive </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a5a447f05a5fbfd35896aad9cd769511c"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_rx_udp: 1</td>
<td class="fielddoc">
1 = UDP payload checksum verified on receive </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a7b701bac9d66886b5c6964b20c6ca55a"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_tx_icmp: 1</td>
<td class="fielddoc">
1 = ICMP payload checksum generated on transmit </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ac787d70407ce70e28724932fb32ef0ba"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_tx_ip4: 1</td>
<td class="fielddoc">
1 = IPv4 header checksum generated on transmit </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a8f7a154565e652d976b9e65bf3516504"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_tx_ip6: 1</td>
<td class="fielddoc">
1 = IPv6 checksum generation supported on transmit </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a6c2b80bbfe520f3e7808cf3d4aaedb45"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_tx_tcp: 1</td>
<td class="fielddoc">
1 = TCP payload checksum generated on transmit </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="ab3f9560668a087606c40cd81b935396b"></a>uint32_t</td>
<td class="fieldname">
checksum_offload_tx_udp: 1</td>
<td class="fielddoc">
1 = UDP payload checksum generated on transmit </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a8c8f1ac2bf053a9bac98c476646a6018"></a>uint32_t</td>
<td class="fieldname">
event_rx_frame: 1</td>
<td class="fielddoc">
1 = callback event <a class="el" href="group___e_t_h___m_a_c__events.html#ga76943471a4a3e9e8c1ff9fe83e43bd47">ARM_ETH_MAC_EVENT_RX_FRAME</a> generated </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a1b4af3590d59ea4f8e845b4239a4e445"></a>uint32_t</td>
<td class="fieldname">
event_tx_frame: 1</td>
<td class="fielddoc">
1 = callback event <a class="el" href="group___e_t_h___m_a_c__events.html#ga0c0328ff7cf886d5fdb53bb84ec03c1b">ARM_ETH_MAC_EVENT_TX_FRAME</a> generated </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a7536d9b9818b20b6974a712e0449439b"></a>uint32_t</td>
<td class="fieldname">
event_wakeup: 1</td>
<td class="fielddoc">
1 = wakeup event <a class="el" href="group___e_t_h___m_a_c__events.html#ga1f3bdb219afa8f2a121b58cc84f5761c">ARM_ETH_MAC_EVENT_WAKEUP</a> generated </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a7fdea04bacd9c0e12792751055ef6238"></a>uint32_t</td>
<td class="fieldname">
mac_address: 1</td>
<td class="fielddoc">
1 = driver provides serialized MAC address </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a3c5cb74e086417a01d0079f847a3fc8d"></a>uint32_t</td>
<td class="fieldname">
media_interface: 2</td>
<td class="fielddoc">
Ethernet Media Interface type. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a881a863974d32f95d7829f768ac47aa2"></a>uint32_t</td>
<td class="fieldname">
precision_timer: 1</td>
<td class="fielddoc">
1 = Precision Timer supported </td></tr>
</table>

</div>
</div>
<a name="struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c" id="struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_DRIVER_ETH_MAC</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Access structure of the Ethernet MAC Driver. </p>
<p>The functions of the Ethernet MAC are accessed by function pointers. Refer to <a class="el" href="_theory_operation.html#DriverFunctions">Driver Functions</a> for overview information.</p>
<p>Each instance of an Ethernet MAC provides such an access struct. The instance is indicated by a postfix in the symbol name of the access struct, for example:</p>
<ul>
<li><b>Driver_ETH_MAC0</b> is the name of the access struct of the first instance (no. 0).</li>
<li><b>Driver_ETH_MAC1</b> is the name of the access struct of the second instance (no. 1).</li>
</ul>
<p>A configuration setting in the middleware allows connecting the middleware to a specific driver instance <b>Driver_ETH_MAC<em>n</em></b>. The default is <span class="XML-Token">0</span>, which connects a middleware to the first instance of a driver. </p>
</div><table class="memberdecls">
<tr><td colspan="2"><h3>Data Fields</h3></td></tr>
<tr class="memitem:a8834b281da48583845c044a81566c1b3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a8834b281da48583845c044a81566c1b3">GetVersion</a> )(void)</td></tr>
<tr class="memdesc:a8834b281da48583845c044a81566c1b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga86b15062c297384ad5842dd57b9d6b1d">ARM_ETH_MAC_GetVersion</a> : Get driver version.  <a href="#a8834b281da48583845c044a81566c1b3">More...</a><br/></td></tr>
<tr class="separator:a8834b281da48583845c044a81566c1b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9fd725bb058c584a9ced9c579561cdf1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_ETH_MAC_CAPABILITIES</a>(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a9fd725bb058c584a9ced9c579561cdf1">GetCapabilities</a> )(void)</td></tr>
<tr class="memdesc:a9fd725bb058c584a9ced9c579561cdf1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga2b13b230502736d8c7679b359dff20d0">ARM_ETH_MAC_GetCapabilities</a> : Get driver capabilities.  <a href="#a9fd725bb058c584a9ced9c579561cdf1">More...</a><br/></td></tr>
<tr class="separator:a9fd725bb058c584a9ced9c579561cdf1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa34417c70cb8b43567c59aa530866cc7"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#aa34417c70cb8b43567c59aa530866cc7">Initialize</a> )(<a class="el" href="group__eth__mac__interface__gr.html#gadfc95cb09c541a29a72da86963668726">ARM_ETH_MAC_SignalEvent_t</a> cb_event)</td></tr>
<tr class="memdesc:aa34417c70cb8b43567c59aa530866cc7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gacf42d11b171cd032f0ec1de6db2b6832">ARM_ETH_MAC_Initialize</a> : Initialize Ethernet MAC Device.  <a href="#aa34417c70cb8b43567c59aa530866cc7">More...</a><br/></td></tr>
<tr class="separator:aa34417c70cb8b43567c59aa530866cc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcf20681a1402869ecb5c6447fada17b"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#adcf20681a1402869ecb5c6447fada17b">Uninitialize</a> )(void)</td></tr>
<tr class="memdesc:adcf20681a1402869ecb5c6447fada17b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gacb2c2ae06f32328775bffbdeaaabfb5d">ARM_ETH_MAC_Uninitialize</a> : De-initialize Ethernet MAC Device.  <a href="#adcf20681a1402869ecb5c6447fada17b">More...</a><br/></td></tr>
<tr class="separator:adcf20681a1402869ecb5c6447fada17b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba8f1c8019af95ffe19c32403e3240ef"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#aba8f1c8019af95ffe19c32403e3240ef">PowerControl</a> )(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td></tr>
<tr class="memdesc:aba8f1c8019af95ffe19c32403e3240ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga346fef040a0e9bac5762a04a306b1be7">ARM_ETH_MAC_PowerControl</a> : Control Ethernet MAC Device Power.  <a href="#aba8f1c8019af95ffe19c32403e3240ef">More...</a><br/></td></tr>
<tr class="separator:aba8f1c8019af95ffe19c32403e3240ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02837059933cd04b04bf795a7138f218"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a02837059933cd04b04bf795a7138f218">GetMacAddress</a> )(<a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr)</td></tr>
<tr class="memdesc:a02837059933cd04b04bf795a7138f218"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga66308c1e791952047e974bd653037fae">ARM_ETH_MAC_GetMacAddress</a> : Get Ethernet MAC Address.  <a href="#a02837059933cd04b04bf795a7138f218">More...</a><br/></td></tr>
<tr class="separator:a02837059933cd04b04bf795a7138f218"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac640f929dc4d5bde3e4282c75b25c00d"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ac640f929dc4d5bde3e4282c75b25c00d">SetMacAddress</a> )(const <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr)</td></tr>
<tr class="memdesc:ac640f929dc4d5bde3e4282c75b25c00d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga7cc3d17c7312c5032202dfd9a915f24a">ARM_ETH_MAC_SetMacAddress</a> : Set Ethernet MAC Address.  <a href="#ac640f929dc4d5bde3e4282c75b25c00d">More...</a><br/></td></tr>
<tr class="separator:ac640f929dc4d5bde3e4282c75b25c00d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45b879a6df608f582d1866daff715798"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a45b879a6df608f582d1866daff715798">SetAddressFilter</a> )(const <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr, uint32_t num_addr)</td></tr>
<tr class="memdesc:a45b879a6df608f582d1866daff715798"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga150fe30290275a4b32756f94208124e8">ARM_ETH_MAC_SetAddressFilter</a> : Configure Address Filter.  <a href="#a45b879a6df608f582d1866daff715798">More...</a><br/></td></tr>
<tr class="separator:a45b879a6df608f582d1866daff715798"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac095aea379f23e30a0e51b1f3518ad37"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ac095aea379f23e30a0e51b1f3518ad37">SendFrame</a> )(const uint8_t *frame, uint32_t len, uint32_t flags)</td></tr>
<tr class="memdesc:ac095aea379f23e30a0e51b1f3518ad37"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3">ARM_ETH_MAC_SendFrame</a> : Send Ethernet frame.  <a href="#ac095aea379f23e30a0e51b1f3518ad37">More...</a><br/></td></tr>
<tr class="separator:ac095aea379f23e30a0e51b1f3518ad37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a466b724be2167ea7d9a14569062a8fa8"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a466b724be2167ea7d9a14569062a8fa8">ReadFrame</a> )(uint8_t *frame, uint32_t len)</td></tr>
<tr class="memdesc:a466b724be2167ea7d9a14569062a8fa8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a> : Read data of received Ethernet frame.  <a href="#a466b724be2167ea7d9a14569062a8fa8">More...</a><br/></td></tr>
<tr class="separator:a466b724be2167ea7d9a14569062a8fa8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3286cc9c7624168b162aa3ce3cbe135e"><td class="memItemLeft" align="right" valign="top">uint32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a3286cc9c7624168b162aa3ce3cbe135e">GetRxFrameSize</a> )(void)</td></tr>
<tr class="memdesc:a3286cc9c7624168b162aa3ce3cbe135e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga5ee86d6b0efab5329b9bc191c23a466d">ARM_ETH_MAC_GetRxFrameSize</a> : Get size of received Ethernet frame.  <a href="#a3286cc9c7624168b162aa3ce3cbe135e">More...</a><br/></td></tr>
<tr class="separator:a3286cc9c7624168b162aa3ce3cbe135e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ae5a588bf4055bba3de73cfba78f7e8"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a8ae5a588bf4055bba3de73cfba78f7e8">GetRxFrameTime</a> )(<a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td></tr>
<tr class="memdesc:a8ae5a588bf4055bba3de73cfba78f7e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gaa7c6865fb09754be869778142466c5e4">ARM_ETH_MAC_GetRxFrameTime</a> : Get time of received Ethernet frame.  <a href="#a8ae5a588bf4055bba3de73cfba78f7e8">More...</a><br/></td></tr>
<tr class="separator:a8ae5a588bf4055bba3de73cfba78f7e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf081f5020f4ef1435bcff7333a70b93"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#acf081f5020f4ef1435bcff7333a70b93">GetTxFrameTime</a> )(<a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td></tr>
<tr class="memdesc:acf081f5020f4ef1435bcff7333a70b93"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga115b5c7e149aec2b181de760f5d83f60">ARM_ETH_MAC_GetTxFrameTime</a> : Get time of transmitted Ethernet frame.  <a href="#acf081f5020f4ef1435bcff7333a70b93">More...</a><br/></td></tr>
<tr class="separator:acf081f5020f4ef1435bcff7333a70b93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6bdbdc7fdfcc52e027201738b88b431"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ab6bdbdc7fdfcc52e027201738b88b431">ControlTimer</a> )(uint32_t control, <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td></tr>
<tr class="memdesc:ab6bdbdc7fdfcc52e027201738b88b431"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">ARM_ETH_MAC_ControlTimer</a> : Control Precision Timer.  <a href="#ab6bdbdc7fdfcc52e027201738b88b431">More...</a><br/></td></tr>
<tr class="separator:ab6bdbdc7fdfcc52e027201738b88b431"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e0f47a92f626a971c5197fca6545505"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a6e0f47a92f626a971c5197fca6545505">Control</a> )(uint32_t control, uint32_t arg)</td></tr>
<tr class="memdesc:a6e0f47a92f626a971c5197fca6545505"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a> : Control Ethernet Interface.  <a href="#a6e0f47a92f626a971c5197fca6545505">More...</a><br/></td></tr>
<tr class="separator:a6e0f47a92f626a971c5197fca6545505"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0f2ddb734e4242077275761400b26e35"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#a0f2ddb734e4242077275761400b26e35">PHY_Read</a> )(uint8_t phy_addr, uint8_t reg_addr, uint16_t *data)</td></tr>
<tr class="memdesc:a0f2ddb734e4242077275761400b26e35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">ARM_ETH_MAC_PHY_Read</a> : Read Ethernet PHY Register through Management Interface.  <a href="#a0f2ddb734e4242077275761400b26e35">More...</a><br/></td></tr>
<tr class="separator:a0f2ddb734e4242077275761400b26e35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3efe9bdc31c3b1d7fd8eb82bbfb4c13"><td class="memItemLeft" align="right" valign="top">int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__interface__gr.html#ac3efe9bdc31c3b1d7fd8eb82bbfb4c13">PHY_Write</a> )(uint8_t phy_addr, uint8_t reg_addr, uint16_t data)</td></tr>
<tr class="memdesc:ac3efe9bdc31c3b1d7fd8eb82bbfb4c13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">ARM_ETH_MAC_PHY_Write</a> : Write Ethernet PHY Register through Management Interface.  <a href="#ac3efe9bdc31c3b1d7fd8eb82bbfb4c13">More...</a><br/></td></tr>
<tr class="separator:ac3efe9bdc31c3b1d7fd8eb82bbfb4c13"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h4 class="groupheader">Field Documentation</h4>
<a class="anchor" id="a6e0f47a92f626a971c5197fca6545505"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Control)(uint32_t control, uint32_t arg)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a> : Control Ethernet Interface. </p>

</div>
</div>
<a class="anchor" id="ab6bdbdc7fdfcc52e027201738b88b431"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* ControlTimer)(uint32_t control, <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">ARM_ETH_MAC_ControlTimer</a> : Control Precision Timer. </p>

</div>
</div>
<a class="anchor" id="a9fd725bb058c584a9ced9c579561cdf1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_ETH_MAC_CAPABILITIES</a>(* GetCapabilities)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga2b13b230502736d8c7679b359dff20d0">ARM_ETH_MAC_GetCapabilities</a> : Get driver capabilities. </p>

</div>
</div>
<a class="anchor" id="a02837059933cd04b04bf795a7138f218"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* GetMacAddress)(<a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga66308c1e791952047e974bd653037fae">ARM_ETH_MAC_GetMacAddress</a> : Get Ethernet MAC Address. </p>

</div>
</div>
<a class="anchor" id="a3286cc9c7624168b162aa3ce3cbe135e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t(* GetRxFrameSize)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga5ee86d6b0efab5329b9bc191c23a466d">ARM_ETH_MAC_GetRxFrameSize</a> : Get size of received Ethernet frame. </p>

</div>
</div>
<a class="anchor" id="a8ae5a588bf4055bba3de73cfba78f7e8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* GetRxFrameTime)(<a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gaa7c6865fb09754be869778142466c5e4">ARM_ETH_MAC_GetRxFrameTime</a> : Get time of received Ethernet frame. </p>

</div>
</div>
<a class="anchor" id="acf081f5020f4ef1435bcff7333a70b93"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* GetTxFrameTime)(<a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga115b5c7e149aec2b181de760f5d83f60">ARM_ETH_MAC_GetTxFrameTime</a> : Get time of transmitted Ethernet frame. </p>

</div>
</div>
<a class="anchor" id="a8834b281da48583845c044a81566c1b3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a>(* GetVersion)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga86b15062c297384ad5842dd57b9d6b1d">ARM_ETH_MAC_GetVersion</a> : Get driver version. </p>

</div>
</div>
<a class="anchor" id="aa34417c70cb8b43567c59aa530866cc7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Initialize)(<a class="el" href="group__eth__mac__interface__gr.html#gadfc95cb09c541a29a72da86963668726">ARM_ETH_MAC_SignalEvent_t</a> cb_event)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gacf42d11b171cd032f0ec1de6db2b6832">ARM_ETH_MAC_Initialize</a> : Initialize Ethernet MAC Device. </p>

</div>
</div>
<a class="anchor" id="a0f2ddb734e4242077275761400b26e35"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* PHY_Read)(uint8_t phy_addr, uint8_t reg_addr, uint16_t *data)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gaded29ad58366e9222487db9944373c29">ARM_ETH_MAC_PHY_Read</a> : Read Ethernet PHY Register through Management Interface. </p>

</div>
</div>
<a class="anchor" id="ac3efe9bdc31c3b1d7fd8eb82bbfb4c13"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* PHY_Write)(uint8_t phy_addr, uint8_t reg_addr, uint16_t data)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga79dd38672749aeebd28f39d9b4f813ce">ARM_ETH_MAC_PHY_Write</a> : Write Ethernet PHY Register through Management Interface. </p>

</div>
</div>
<a class="anchor" id="aba8f1c8019af95ffe19c32403e3240ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* PowerControl)(<a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a> state)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga346fef040a0e9bac5762a04a306b1be7">ARM_ETH_MAC_PowerControl</a> : Control Ethernet MAC Device Power. </p>

</div>
</div>
<a class="anchor" id="a466b724be2167ea7d9a14569062a8fa8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* ReadFrame)(uint8_t *frame, uint32_t len)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a> : Read data of received Ethernet frame. </p>

</div>
</div>
<a class="anchor" id="ac095aea379f23e30a0e51b1f3518ad37"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* SendFrame)(const uint8_t *frame, uint32_t len, uint32_t flags)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3">ARM_ETH_MAC_SendFrame</a> : Send Ethernet frame. </p>

</div>
</div>
<a class="anchor" id="a45b879a6df608f582d1866daff715798"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* SetAddressFilter)(const <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr, uint32_t num_addr)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga150fe30290275a4b32756f94208124e8">ARM_ETH_MAC_SetAddressFilter</a> : Configure Address Filter. </p>

</div>
</div>
<a class="anchor" id="ac640f929dc4d5bde3e4282c75b25c00d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* SetMacAddress)(const <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *ptr_addr)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#ga7cc3d17c7312c5032202dfd9a915f24a">ARM_ETH_MAC_SetMacAddress</a> : Set Ethernet MAC Address. </p>

</div>
</div>
<a class="anchor" id="adcf20681a1402869ecb5c6447fada17b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t(* Uninitialize)(void)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gacb2c2ae06f32328775bffbdeaaabfb5d">ARM_ETH_MAC_Uninitialize</a> : De-initialize Ethernet MAC Device. </p>

</div>
</div>

</div>
</div>
<a name="struct_a_r_m___e_t_h___m_a_c___t_i_m_e" id="struct_a_r_m___e_t_h___m_a_c___t_i_m_e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct ARM_ETH_MAC_TIME</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Ethernet MAC Time. </p>
<p>The two members of this struct provide fields to encode time values in the order <span class="XML-Token">Nano seconds</span> and <span class="XML-Token">seconds</span>.</p>
<p>The member <em>ns</em> is also used as a correction factor for <a class="el" href="group__eth__mac__time__control.html#ga85cb862eba0934e958a8552022588db7">ARM_ETH_MAC_TIMER_ADJUST_CLOCK</a>.</p>
<p><b>Used in:</b></p>
<ul>
<li><a class="el" href="group__eth__mac__interface__gr.html#gaa7c6865fb09754be869778142466c5e4">ARM_ETH_MAC_GetRxFrameTime</a></li>
<li><a class="el" href="group__eth__mac__interface__gr.html#ga115b5c7e149aec2b181de760f5d83f60">ARM_ETH_MAC_GetTxFrameTime</a></li>
<li><a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">ARM_ETH_MAC_ControlTimer</a> </li>
</ul>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a class="anchor" id="a048317f84621fb38ed0bf8c8255e26f0"></a>uint32_t</td>
<td class="fieldname">
ns</td>
<td class="fielddoc">
Nano seconds. </td></tr>
<tr><td class="fieldtype">
<a class="anchor" id="aaf5f5a3fa5d596a9136b4331f2b54bfc"></a>uint32_t</td>
<td class="fieldname">
sec</td>
<td class="fielddoc">
Seconds. </td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="gadfc95cb09c541a29a72da86963668726"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ARM_ETH_MAC_SignalEvent_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> : Signal Ethernet Event. </p>
<p>Provides the typedef for the callback function <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a>.</p>
<p><b>Parameter for:</b></p>
<ul>
<li><a class="el" href="group__eth__mac__interface__gr.html#gacf42d11b171cd032f0ec1de6db2b6832">ARM_ETH_MAC_Initialize</a> </li>
</ul>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gac3e90c66058d20077f04ac8e8b8d0536"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_Control </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>control</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>arg</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control Ethernet Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">control</td><td>Operation </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">arg</td><td>Argument of operation (optional) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Controls the Ethernet MAC interface settings and executes various operations.</p>
<p>The parameter <em>control</em> specifies various operations (see tables below). Depending on the control bits, the parameter <em>arg</em> provides additional information or sets values.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions</li>
</ul>
<p><b>ARM_ETH_MAC_xxx</b> controls configure the Ethernet MAC interface or execute various operations:</p>
<table class="doxtable">
<tr>
<th align="left">Controls </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__ctrls.html#ga7819c7a1aa7bbc13dc42d0fd7e75a23c">ARM_ETH_MAC_CONFIGURE</a> </td><td align="left">Configure the Ethernet MAC interface; <em>arg</em> sets the configuration </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__ctrls.html#ga3a98c8a7ee5ed4b1ffd250eecaeefe5c">ARM_ETH_MAC_CONTROL_TX</a> </td><td align="left">Enable or disable the transmitter; <em>arg</em> : <span class="XML-Token">0=disabled; 1=enabled</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__ctrls.html#gae0964364b81b38b6e1fbf7196f3be869">ARM_ETH_MAC_CONTROL_RX</a> </td><td align="left">Enable or disable the receiver; <em>arg</em> : <span class="XML-Token">0=disabled; 1=enabled</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__ctrls.html#ga530812ef349a2e297f23de72e660fe27">ARM_ETH_MAC_FLUSH</a> </td><td align="left">Flush a buffer; <em>arg</em> = takes values as described in <a class="el" href="group__eth__mac__flush__flag__ctrls.html">Ethernet MAC Flush Flags</a> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__ctrls.html#ga4afe66589216f566f529af52f9075fdf">ARM_ETH_MAC_SLEEP</a> </td><td align="left">Enter/Exit Sleep mode; <em>arg</em> : <span class="XML-Token">1=enter and wait for Magic packet; 0=exit</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__ctrls.html#gab332b58ba320e73864830dc42ad74181">ARM_ETH_MAC_VLAN_FILTER</a> </td><td align="left">Configure VLAN Filter for received frames; <em>arg15</em>..0 : VLAN Tag; <em>arg16</em> : optional <a class="el" href="group__eth__mac__vlan__filter__ctrls.html#ga2511c9e4c22a2b351ce2e454be1c9427">ARM_ETH_MAC_VLAN_FILTER_ID_ONLY</a> </td></tr>
</table>
<p><b>Ethernet MAC Configuration</b> specifies speed mode, link mode, checksum, and frame filtering modes:</p>
<table class="doxtable">
<tr>
<th align="left">Ethernet MAC Configuration </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga8c5b40d018ecfad05fe2546ba717c1d4">ARM_ETH_MAC_SPEED_10M</a> </td><td align="left">Set the link speed to <span class="XML-Token">10 [Mbps]</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga29160c83a7b0952c64053d86789c6490">ARM_ETH_MAC_SPEED_100M</a> </td><td align="left">Set the link speed to <span class="XML-Token">100 [Mbps]</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga8acefed744d8397a1777b9fd0e6230d2">ARM_ETH_MAC_SPEED_1G</a> </td><td align="left">Set the link speed to <span class="XML-Token">1 [Gbps]</span> </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#gadb0fe2c5a1e21b0656d39c788ae22f36">ARM_ETH_MAC_DUPLEX_HALF</a> </td><td align="left">Set the link mode to half duplex </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#gad5a7d4b5b8a31825eff1504e3828d8f6">ARM_ETH_MAC_DUPLEX_FULL</a> </td><td align="left">Set the link mode to full duplex </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#gab32765f35c35b672ee476278fe24a24e">ARM_ETH_MAC_LOOPBACK</a> </td><td align="left">Set the interface into a Loop-back test mode </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga281dfed993b5666ed999709b9f28578f">ARM_ETH_MAC_CHECKSUM_OFFLOAD_RX</a> </td><td align="left">Enable Receiver Checksum offload </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga7272d2c55aeeeadbb95c591cbf6c1a2e">ARM_ETH_MAC_CHECKSUM_OFFLOAD_TX</a> </td><td align="left">Enable Transmitter Checksum offload </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga43792feab641c3c87eafb943351ab0f4">ARM_ETH_MAC_ADDRESS_BROADCAST</a> </td><td align="left">Accept frames with Broadcast address </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#ga1d3ff8c63362b385548fe91730f20588">ARM_ETH_MAC_ADDRESS_MULTICAST</a> </td><td align="left">Accept frames with any Multicast address </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__configuration__ctrls.html#gab29ab9e295807f4c59ddd1c4642086d1">ARM_ETH_MAC_ADDRESS_ALL</a> </td><td align="left">Accept frames with any address (Promiscuous Mode) </td></tr>
</table>
<p><b>ARM_ETH_MAC_FLUSH_xx</b> specifies which buffer to flush:</p>
<table class="doxtable">
<tr>
<th align="left">Ethernet MAC Flush Flags </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__flush__flag__ctrls.html#gac18950811038319960756f063e1ef6d4">ARM_ETH_MAC_FLUSH_RX</a> </td><td align="left">Flush the Receive buffer </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__flush__flag__ctrls.html#ga2d10ff33f4f4927820c6a17a2262b120">ARM_ETH_MAC_FLUSH_TX</a> </td><td align="left">Flush the Transmit buffer </td></tr>
</table>

</div>
</div>
<a class="anchor" id="ga85d9dc865af3702b71a514b18a588643"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_ControlTimer </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>control</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *&#160;</td>
          <td class="paramname"><em>time</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control Precision Timer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">control</td><td>Operation </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">time</td><td>Pointer to time structure </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Controls the timer required for PTP (Precision Time Protocol).</p>
<p>The parameter <em>control</em> receives <b>ARM_ETH_MAC_TIMER_xxx</b> codes to manage the timer for a PTP enabled Ethernet MAC interface.</p>
<p>The parameter <em>time</em> is pointer to a struct that holds time information.</p>
<table class="doxtable">
<tr>
<th align="left">Mode Parameters: Timer Controls </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__time__control.html#gad9a439b9727c032a7d851df2a7a622c2">ARM_ETH_MAC_TIMER_GET_TIME</a> </td><td align="left">Retrieve the current time and update the content <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time. </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__time__control.html#ga5e867a003c06046d7944bcb5723e6049">ARM_ETH_MAC_TIMER_SET_TIME</a> </td><td align="left">Set the new time using the values provided with <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time. </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__time__control.html#ga3c57b3150717fb1a8cbbbac6a9b7ff69">ARM_ETH_MAC_TIMER_INC_TIME</a> </td><td align="left">Increment the current time by using the values provided with <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time. </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__time__control.html#gaca9f1c4259d0342e9717a362de1ccf41">ARM_ETH_MAC_TIMER_DEC_TIME</a> </td><td align="left">Decrement the current time by using the values provided with <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time. </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__time__control.html#ga04c2469ba027b020bc6b5baf3b51cf74">ARM_ETH_MAC_TIMER_SET_ALARM</a> </td><td align="left">Set the alarm time to the values provided with <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *time. </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__time__control.html#ga85cb862eba0934e958a8552022588db7">ARM_ETH_MAC_TIMER_ADJUST_CLOCK</a> </td><td align="left">Set the clock frequency; the value in time-&gt;ns is the <b>correction factor</b> in fractional format q31. </td></tr>
</table>

</div>
</div>
<a class="anchor" id="ga2b13b230502736d8c7679b359dff20d0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_ETH_MAC_CAPABILITIES</a> ARM_ETH_MAC_GetCapabilities </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver capabilities. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_ETH_MAC_CAPABILITIES</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga2b13b230502736d8c7679b359dff20d0">ARM_ETH_MAC_GetCapabilities</a> retrieves information about capabilities in this driver implementation. The bitfield members of the struct <a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s">ARM_ETH_MAC_CAPABILITIES</a> encode various capabilities, for example if a hardware is capable to create checksums in hardware or signal events using the <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> callback function.</p>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c" title="Access structure of the Ethernet MAC Driver.">ARM_DRIVER_ETH_MAC</a> Driver_ETH_MAC0;</div>
<div class="line"><a class="code" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c" title="Access structure of the Ethernet MAC Driver.">ARM_DRIVER_ETH_MAC</a> *mac;</div>
<div class="line">  </div>
<div class="line"><span class="keywordtype">void</span> read_capabilities (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___c_a_p_a_b_i_l_i_t_i_e_s" title="Ethernet MAC Capabilities.">ARM_ETH_MAC_CAPABILITIES</a> mac_capabilities;</div>
<div class="line"> </div>
<div class="line">  mac = &amp;Driver_ETH_MAC0;  </div>
<div class="line">  mac_capabilities = mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a9fd725bb058c584a9ced9c579561cdf1" title="Pointer to ARM_ETH_MAC_GetCapabilities : Get driver capabilities.">GetCapabilities</a> ();</div>
<div class="line">  <span class="comment">// interrogate capabilities</span></div>
<div class="line"> </div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="ga66308c1e791952047e974bd653037fae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_GetMacAddress </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *&#160;</td>
          <td class="paramname"><em>ptr_addr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get Ethernet MAC Address. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr_addr</td><td>Pointer to address </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga66308c1e791952047e974bd653037fae">ARM_ETH_MAC_GetMacAddress</a> retrieves the Ethernet MAC own address from the driver. </p>

</div>
</div>
<a class="anchor" id="ga5ee86d6b0efab5329b9bc191c23a466d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t ARM_ETH_MAC_GetRxFrameSize </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get size of received Ethernet frame. </p>
<dl class="section return"><dt>Returns</dt><dd>number of bytes in received frame</dd></dl>
<p>Retrieve size of a received <a href="http://en.wikipedia.org/wiki/Ethernet_address" target="_blank"><b>Ethernet frame</b></a>. This function is called before <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a> and supplies the value <em>len</em>.</p>
<p>The frame size includes MAC destination and ends with the last Payload data byte.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a> </li>
</ul>

</div>
</div>
<a class="anchor" id="gaa7c6865fb09754be869778142466c5e4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_GetRxFrameTime </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *&#160;</td>
          <td class="paramname"><em>time</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get time of received Ethernet frame. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">time</td><td>Pointer to time structure for data to read into </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Retrieve time stamp of a received <a href="http://en.wikipedia.org/wiki/Ethernet_address" target="_blank"><b>Ethernet frame</b></a>. This function must be called before the frame is read using <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a>. </p>

</div>
</div>
<a class="anchor" id="ga115b5c7e149aec2b181de760f5d83f60"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_GetTxFrameTime </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__eth__mac__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___t_i_m_e">ARM_ETH_MAC_TIME</a> *&#160;</td>
          <td class="paramname"><em>time</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get time of transmitted Ethernet frame. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">time</td><td>Pointer to time structure for data to read into </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Retrieve time stamp of a transmitted <a href="http://en.wikipedia.org/wiki/Ethernet_address" target="_blank"><b>Ethernet frame</b></a>. </p>

</div>
</div>
<a class="anchor" id="ga86b15062c297384ad5842dd57b9d6b1d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a> ARM_ETH_MAC_GetVersion </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get driver version. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga86b15062c297384ad5842dd57b9d6b1d">ARM_ETH_MAC_GetVersion</a> returns version information of the driver implementation in <a class="el" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n">ARM_DRIVER_VERSION</a></p>
<ul>
<li>API version is the version of the CMSIS-Driver specification used to implement this driver.</li>
<li>Driver version is source code version of the actual driver implementation.</li>
</ul>
<p>Example: </p>
<div class="fragment"><div class="line"><span class="keyword">extern</span> <a class="code" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c" title="Access structure of the Ethernet MAC Driver.">ARM_DRIVER_ETH_MAC</a> Driver_ETH_MAC0;</div>
<div class="line"><a class="code" href="group__eth__mac__interface__gr.html#struct_a_r_m___d_r_i_v_e_r___e_t_h___m_a_c" title="Access structure of the Ethernet MAC Driver.">ARM_DRIVER_ETH_MAC</a> *mac;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> setup_ethernet (<span class="keywordtype">void</span>)  {</div>
<div class="line">  <a class="code" href="group__common__drv__gr.html#struct_a_r_m___d_r_i_v_e_r___v_e_r_s_i_o_n" title="Driver Version.">ARM_DRIVER_VERSION</a>  version;</div>
<div class="line"> </div>
<div class="line">  mac = &amp;Driver_ETH_MAC0;  </div>
<div class="line">  version = mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a8834b281da48583845c044a81566c1b3" title="Pointer to ARM_ETH_MAC_GetVersion : Get driver version.">GetVersion</a> ();</div>
<div class="line">  <span class="keywordflow">if</span> (version.<a class="code" href="group__common__drv__gr.html#ad180da20fbde1d3dafc074af87c19540" title="API version.">api</a> &lt; 0x10A)   {      <span class="comment">// requires at minimum API version 1.10 or higher</span></div>
<div class="line">    <span class="comment">// error handling</span></div>
<div class="line">    <span class="keywordflow">return</span>;</div>
<div class="line">  }</div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="gacf42d11b171cd032f0ec1de6db2b6832"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_Initialize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__eth__mac__interface__gr.html#gadfc95cb09c541a29a72da86963668726">ARM_ETH_MAC_SignalEvent_t</a>&#160;</td>
          <td class="paramname"><em>cb_event</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize Ethernet MAC Device. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">cb_event</td><td>Pointer to <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#gacf42d11b171cd032f0ec1de6db2b6832">ARM_ETH_MAC_Initialize</a> initializes the Ethernet MAC interface. It is called when the middleware component starts operation.</p>
<p>The <a class="el" href="group__eth__mac__interface__gr.html#gacf42d11b171cd032f0ec1de6db2b6832">ARM_ETH_MAC_Initialize</a> function performs the following operations:</p>
<ul>
<li>Initializes the resources needed for the Ethernet MAC peripheral.</li>
<li>Registers the <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> callback function.</li>
</ul>
<p>The parameter <em>cb_event</em> is a pointer to the <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> callback function; use a NULL pointer when no callback signals are required.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="gaded29ad58366e9222487db9944373c29"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_PHY_Read </td>
          <td>(</td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>phy_addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>reg_addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t *&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Read Ethernet PHY Register through Management Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">phy_addr</td><td>5-bit device address </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">reg_addr</td><td>5-bit register address </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">data</td><td>Pointer where the result is written to </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Read Ethernet PHY Register through the Management Interface. The function is passed to <a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a>. The Ethernet PHY driver uses this function to read the value of PHY registers.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="ga79dd38672749aeebd28f39d9b4f813ce"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_PHY_Write </td>
          <td>(</td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>phy_addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>reg_addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Write Ethernet PHY Register through Management Interface. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">phy_addr</td><td>5-bit device address </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">reg_addr</td><td>5-bit register address </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">data</td><td>16-bit data to write </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>Write Ethernet PHY Register through the Management Interface. The function is passed to <a class="el" href="group__eth__phy__interface__gr.html#gacf2332a7fa2d84694b8e5f0838135589">ARM_ETH_PHY_Initialize</a>. The Ethernet PHY driver uses this function to write data to PHY registers.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="ga346fef040a0e9bac5762a04a306b1be7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_PowerControl </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__common__drv__gr.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5">ARM_POWER_STATE</a>&#160;</td>
          <td class="paramname"><em>state</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Control Ethernet MAC Device Power. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">state</td><td>Power state </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga346fef040a0e9bac5762a04a306b1be7">ARM_ETH_MAC_PowerControl</a> allows you to configure the power modes of the Ethernet MAC interface.</p>
<p>The argument <em>state</em> can be:</p>
<ul>
<li>ARM_POWER_OFF: Ethernet MAC peripheral is turned off.</li>
<li>ARM_POWER_FULL: Ethernet MAC peripheral is turned on and fully operational.</li>
</ul>
<p>If power <em>state</em> specifies an unsupported mode, the function returns <a class="el" href="group__execution__status.html#ga2efa59e480d82697795439220e6884e4">ARM_DRIVER_ERROR_UNSUPPORTED</a> as status information and the previous power state of the peripheral is unchanged. Multiple calls with the same <em>state</em> generate no error.</p>
<p><b>Example:</b> </p>
<ul>
<li>see <a class="el" href="group__eth__interface__gr.html">Ethernet Interface</a> - Driver Functions </li>
</ul>

</div>
</div>
<a class="anchor" id="ga4b79f57d8624bb4410ee12c73a483993"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_ReadFrame </td>
          <td>(</td>
          <td class="paramtype">uint8_t *&#160;</td>
          <td class="paramname"><em>frame</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>len</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Read data of received Ethernet frame. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">frame</td><td>Pointer to frame buffer for data to read into </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">len</td><td>Frame buffer length in bytes </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>number of data bytes read or execution status<ul>
<li>value &gt;= 0: number of data bytes read</li>
<li>value &lt; 0: error occurred, value is execution status as defined with <a class="el" href="group__execution__status.html">Status Error Codes</a></li>
</ul>
</dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a> reads an <a href="http://en.wikipedia.org/wiki/Ethernet_address" target="_blank"><b>Ethernet frame</b></a> from the Ethernet MAC receive buffer.</p>
<p>The Ethernet MAC receive engine must be enabled using the function <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a> (ARM_ETH_MAC_CONTROL_RX , 1) before a call to this function. The <em>len</em> of the Ethernet frame can be checked using the function <a class="el" href="group__eth__mac__interface__gr.html#ga5ee86d6b0efab5329b9bc191c23a466d">ARM_ETH_MAC_GetRxFrameSize</a>.</p>
<p>The frame data addressed by <em>buf</em> starts with MAC destination and ends with the last Payload data byte. The frame data is read from the receive buffer of the Ethernet MAC interface and the number of bytes written into the memory addressed by <em>buf</em> is returned. A negative return value indicates an error whereby the status code is defined with driver common return codes.</p>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a> may be called with <em>buf</em> = NULL and <em>len</em> = 0 to discard or release an frame. This is useful when an incorrect frame has been received or no memory is available to hold the Ethernet frame.</p>
<p><b>Example:</b> </p>
<div class="fragment"><div class="line">size = mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a3286cc9c7624168b162aa3ce3cbe135e" title="Pointer to ARM_ETH_MAC_GetRxFrameSize : Get size of received Ethernet frame.">GetRxFrameSize</a> ();</div>
<div class="line"><span class="keywordflow">if</span> ((size &lt; 14) || (size &gt; 1514)) {    <span class="comment">// frame excludes CRC</span></div>
<div class="line">  mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a466b724be2167ea7d9a14569062a8fa8" title="Pointer to ARM_ETH_MAC_ReadFrame : Read data of received Ethernet frame.">ReadFrame</a> (NULL, 0);            <span class="comment">// Frame error, release it</span></div>
<div class="line">}</div>
<div class="line">len = mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#a466b724be2167ea7d9a14569062a8fa8" title="Pointer to ARM_ETH_MAC_ReadFrame : Read data of received Ethernet frame.">ReadFrame</a> (&amp;frame-&gt;data[0], size);</div>
<div class="line"><span class="keywordflow">if</span> (len &lt; 0)  {</div>
<div class="line">  <span class="comment">// error handling</span></div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="ga5bf58defdb239ed7dc948f1da147a1c3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_SendFrame </td>
          <td>(</td>
          <td class="paramtype">const uint8_t *&#160;</td>
          <td class="paramname"><em>frame</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>len</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>flags</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send Ethernet frame. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">frame</td><td>Pointer to frame buffer with data to send </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">len</td><td>Frame buffer length in bytes </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">flags</td><td>Frame transmit flags (see ARM_ETH_MAC_TX_FRAME_...) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3">ARM_ETH_MAC_SendFrame</a> writes an <a href="http://en.wikipedia.org/wiki/Ethernet_frame" target="_blank"><b>Ethernet frame</b></a> to the Ethernet MAC transmit buffer.</p>
<p>The Ethernet MAC transmit engine must be enabled by using the function <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a> (ARM_ETH_MAC_CONTROL_TX, 1) before a call to this function.</p>
<p>The frame data addressed by <em>buf</em> starts with MAC destination and ends with the last Payload data byte. The frame data is copied into the transmit buffer of the Ethernet MAC interface. The function does not wait until the transmission over the Ethernet is complete, however the memory addressed by <em>buf</em> is available for the next Ethernet frame after return.</p>
<p>The maximum value for <em>len</em> is implied by the size restrictions of the Ethernet frame but is not verified. Using an invalid value for <em>len</em> may generate unpredicted results.</p>
<p>The parameter <em>flags</em> specifies additional attributes for the function as shown in the following table. Multiple flags can be combined, for example: ARM_ETH_MAC_TX_FRAME_EVENT | ARM_ETH_MAC_TX_FRAME_TIMESTAMP.</p>
<table class="doxtable">
<tr>
<th align="left">Flag bit </th><th align="left">Description</th></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__frame__transmit__ctrls.html#gab7bd6dea5bb57240291db71e95c99d9c">ARM_ETH_MAC_TX_FRAME_FRAGMENT</a> </td><td align="left">Indicates that it is a fragment of the frame. allows you to collect multiple fragments before the frame is sent. </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__frame__transmit__ctrls.html#ga91a923680ea0dad758b8950a3fbd237e">ARM_ETH_MAC_TX_FRAME_EVENT</a> </td><td align="left"><a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> with <em>event</em> bit <a class="el" href="group___e_t_h___m_a_c__events.html#ga0c0328ff7cf886d5fdb53bb84ec03c1b">ARM_ETH_MAC_EVENT_TX_FRAME</a> set will be called when frame send is complete. </td></tr>
<tr>
<td align="left"><a class="el" href="group__eth__mac__frame__transmit__ctrls.html#gade137f65dd345ae40e93c77d495f9b54">ARM_ETH_MAC_TX_FRAME_TIMESTAMP</a> </td><td align="left">Capture the time stamp of the frame. The time stamp can be obtained using the function <a class="el" href="group__eth__mac__interface__gr.html#ga115b5c7e149aec2b181de760f5d83f60">ARM_ETH_MAC_GetTxFrameTime</a>. </td></tr>
</table>
<p><b>Example:</b> </p>
<div class="fragment"><div class="line">status = mac-&gt;<a class="code" href="group__eth__mac__interface__gr.html#ac095aea379f23e30a0e51b1f3518ad37" title="Pointer to ARM_ETH_MAC_SendFrame : Send Ethernet frame.">SendFrame</a> (&amp;frame-&gt;data[0], frame-&gt;length, 0);</div>
<div class="line"><span class="keywordflow">if</span> (status != <a class="code" href="group__execution__status.html#ga85752c5de59e8adeb001e35ff5be6be7" title="Operation succeeded.">ARM_DRIVER_OK</a>)  {</div>
<div class="line">  <span class="comment">// error handling</span></div>
<div class="line">}</div>
</div><!-- fragment --> 
</div>
</div>
<a class="anchor" id="ga150fe30290275a4b32756f94208124e8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_SetAddressFilter </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *&#160;</td>
          <td class="paramname"><em>ptr_addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>num_addr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Configure Address Filter. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr_addr</td><td>Pointer to addresses </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">num_addr</td><td>Number of addresses to configure </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga150fe30290275a4b32756f94208124e8">ARM_ETH_MAC_SetAddressFilter</a> configures Ethernet MAC receiver address filtering. The Ethernet MAC accepts packets <a href="http://en.wikipedia.org/wiki/Ethernet_frame" target="_blank"><b>Ethernet frames</b></a> which contains a MAC destination address of the list supplied with <em>ptr_addr</em>. The parameter <em>ptr_addr</em> provides and array of Ethernet MAC addresses. The number of addresses is supplied by <em>num_addr</em>.</p>
<p>The Ethernet MAC receiver will accept packets addressed to its own address and packets with addresses configured by this function.</p>
<p>MAC receiver can be configured to accept also packets with broadcast address, any multicast address or even all packets regardless of address (Promiscuity Mode). This is configured by function <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a> with <a class="el" href="group__eth__mac__ctrls.html#ga7819c7a1aa7bbc13dc42d0fd7e75a23c">ARM_ETH_MAC_CONFIGURE</a> as control parameter. </p>

</div>
</div>
<a class="anchor" id="ga7cc3d17c7312c5032202dfd9a915f24a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_SetMacAddress </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="group__eth__interface__gr.html#struct_a_r_m___e_t_h___m_a_c___a_d_d_r">ARM_ETH_MAC_ADDR</a> *&#160;</td>
          <td class="paramname"><em>ptr_addr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set Ethernet MAC Address. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr_addr</td><td>Pointer to address </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#ga7cc3d17c7312c5032202dfd9a915f24a">ARM_ETH_MAC_SetMacAddress</a> configures Ethernet MAC own address. The Ethernet MAC accepts packets <a href="http://en.wikipedia.org/wiki/Ethernet_frame" target="_blank"><b>Ethernet frames</b></a> which contains a MAC destination address that matches the address specified with <em>ptr_addr</em>.</p>
<p>The Ethernet MAC receiver will accept also packets with addresses configured by <a class="el" href="group__eth__mac__interface__gr.html#ga150fe30290275a4b32756f94208124e8">ARM_ETH_MAC_SetAddressFilter</a> function.</p>
<p>MAC receiver can be configured to accept also packets with broadcast address, any multicast address or even all packets regardless of address (Promiscuity Mode). This is configured by function <a class="el" href="group__eth__mac__interface__gr.html#gac3e90c66058d20077f04ac8e8b8d0536">ARM_ETH_MAC_Control</a> with <a class="el" href="group__eth__mac__ctrls.html#ga7819c7a1aa7bbc13dc42d0fd7e75a23c">ARM_ETH_MAC_CONFIGURE</a> as control parameter. </p>

</div>
</div>
<a class="anchor" id="gae0697be4c4229601f3bfc17e2978ada6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void ARM_ETH_MAC_SignalEvent </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>event</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Callback function that signals a Ethernet Event. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">event</td><td>event notification mask </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#gae0697be4c4229601f3bfc17e2978ada6">ARM_ETH_MAC_SignalEvent</a> notifies the various <a class="el" href="group___e_t_h___m_a_c__events.html">Ethernet MAC Events</a> and it is registered by the function <a class="el" href="group__eth__mac__interface__gr.html#gacf42d11b171cd032f0ec1de6db2b6832">ARM_ETH_MAC_Initialize</a>. The function <a class="el" href="group__eth__mac__interface__gr.html#ga2b13b230502736d8c7679b359dff20d0">ARM_ETH_MAC_GetCapabilities</a> returns information about the implemented optional events in a driver. This function is typically called from interrupt service routines (ISR) to indicate that a frame is processed or a special event occurred.</p>
<p>The argument <em>event</em> represents the notification mask of the events. One or more events may be notified with the same callback.</p>
<p>Each event is coded in a bit and therefore it is possible to signal multiple events in the event call back function. The following call back notifications are generated:</p>
<table class="doxtable">
<tr>
<th>Bit</th><th align="left">Event </th><th align="left">Description</th></tr>
<tr>
<td>0 </td><td align="left"><a class="el" href="group___e_t_h___m_a_c__events.html#ga76943471a4a3e9e8c1ff9fe83e43bd47">ARM_ETH_MAC_EVENT_RX_FRAME</a> </td><td align="left">Occurs after a frame is received. Frame can be read by calling <a class="el" href="group__eth__mac__interface__gr.html#ga4b79f57d8624bb4410ee12c73a483993">ARM_ETH_MAC_ReadFrame</a>. </td></tr>
<tr>
<td>1 </td><td align="left"><a class="el" href="group___e_t_h___m_a_c__events.html#ga0c0328ff7cf886d5fdb53bb84ec03c1b">ARM_ETH_MAC_EVENT_TX_FRAME</a> </td><td align="left">Occurs after call to <a class="el" href="group__eth__mac__interface__gr.html#ga5bf58defdb239ed7dc948f1da147a1c3">ARM_ETH_MAC_SendFrame</a> to indicate that the frame is transmitted. </td></tr>
<tr>
<td>2 </td><td align="left"><a class="el" href="group___e_t_h___m_a_c__events.html#ga1f3bdb219afa8f2a121b58cc84f5761c">ARM_ETH_MAC_EVENT_WAKEUP</a> </td><td align="left">Indicates that a Magic Packet is received while the driver is set to <a class="el" href="_driver___common_8h.html#ga47d6d7c31f88f3b8ae4aaf9d8444afa5a9ef9e57cbcc948d0e22314e73dc8c434">ARM_POWER_LOW</a> mode using <a class="el" href="group__eth__mac__interface__gr.html#ga346fef040a0e9bac5762a04a306b1be7">ARM_ETH_MAC_PowerControl</a>. </td></tr>
<tr>
<td>3 </td><td align="left"><a class="el" href="group___e_t_h___m_a_c__events.html#ga4afc71ecac964f195e27be4acdbe7c61">ARM_ETH_MAC_EVENT_TIMER_ALARM</a> </td><td align="left">Indicates that a Timer Alarm occurred that was set with <a class="el" href="group__eth__mac__time__control.html#ga04c2469ba027b020bc6b5baf3b51cf74">ARM_ETH_MAC_TIMER_SET_ALARM</a> using ARM_ETH_MAC_ControlTimer. </td></tr>
</table>

</div>
</div>
<a class="anchor" id="gacb2c2ae06f32328775bffbdeaaabfb5d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ARM_ETH_MAC_Uninitialize </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>De-initialize Ethernet MAC Device. </p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="group__execution__status.html">Status Error Codes</a></dd></dl>
<p>The function <a class="el" href="group__eth__mac__interface__gr.html#gacb2c2ae06f32328775bffbdeaaabfb5d">ARM_ETH_MAC_Uninitialize</a> de-initializes the resources of Ethernet MAC interface.</p>
<p>It is called when the middleware component stops operation and releases the software resources used by the interface. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
