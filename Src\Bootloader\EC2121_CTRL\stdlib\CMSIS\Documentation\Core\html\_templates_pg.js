var _templates_pg =
[
    [ "Template Files", "_templates_pg.html#template_files_sec", null ],
    [ "Adaption of Template Files to Devices", "_templates_pg.html#adapt_template_files_sec", null ],
    [ "Startup File startup_<device>.s", "startup_s_pg.html", [
      [ "startup_Device.s Template File", "startup_s_pg.html#startup_s_sec", null ]
    ] ],
    [ "System Configuration Files system_<device>.c and system_<device>.h", "system_c_pg.html", [
      [ "system_Device.c Template File", "system_c_pg.html#system_Device_sec", null ],
      [ "system_Device.h Template File", "system_c_pg.html#system_Device_h_sec", null ]
    ] ],
    [ "Device Header File <device.h>", "device_h_pg.html", [
      [ "Interrupt Number Definition", "device_h_pg.html#interrupt_number_sec", null ],
      [ "Configuration of the Processor and Core Peripherals", "device_h_pg.html#core_config_sect", null ],
      [ "CMSIS Version and Processor Information", "device_h_pg.html#core_version_sect", null ],
      [ "Device Peripheral Access Layer", "device_h_pg.html#device_access", null ],
      [ "Device.h Template File", "device_h_pg.html#device_h_sec", null ]
    ] ]
];