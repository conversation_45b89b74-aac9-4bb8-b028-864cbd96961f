<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>APSR_Type Union Reference</title>
<title>CMSIS-CORE: APSR_Type Union Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('union_a_p_s_r___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">APSR_Type Union Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Union type to access the Application Program Status Register (APSR).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a7dbc79a057ded4b11ca5323fc2d5ab14"><td class="memItemLeft" >struct {</td></tr>
<tr class="memitem:a40a16164602a889d31a6bd92e9ccde92"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_a_p_s_r___type.html#afbce95646fd514c10aa85ec0a33db728">_reserved0</a>:27</td></tr>
<tr class="memdesc:a40a16164602a889d31a6bd92e9ccde92"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 0..26 Reserved  <a href="#a40a16164602a889d31a6bd92e9ccde92"></a><br/></td></tr>
<tr class="separator:a40a16164602a889d31a6bd92e9ccde92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebf336ed17f711353ef40d16b9fcc305"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_a_p_s_r___type.html#a22d10913489d24ab08bd83457daa88de">Q</a>:1</td></tr>
<tr class="memdesc:aebf336ed17f711353ef40d16b9fcc305"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 27 Saturation condition flag  <a href="#aebf336ed17f711353ef40d16b9fcc305"></a><br/></td></tr>
<tr class="separator:aebf336ed17f711353ef40d16b9fcc305"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8003e190933fcfbff0b0878f48aa32b6"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_a_p_s_r___type.html#a8004d224aacb78ca37774c35f9156e7e">V</a>:1</td></tr>
<tr class="memdesc:a8003e190933fcfbff0b0878f48aa32b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 28 Overflow condition code flag  <a href="#a8003e190933fcfbff0b0878f48aa32b6"></a><br/></td></tr>
<tr class="separator:a8003e190933fcfbff0b0878f48aa32b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c6e27604bd227c0c7685ae13ee33dc4"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_a_p_s_r___type.html#a86e2c5b891ecef1ab55b1edac0da79a6">C</a>:1</td></tr>
<tr class="memdesc:a7c6e27604bd227c0c7685ae13ee33dc4"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 29 Carry condition code flag  <a href="#a7c6e27604bd227c0c7685ae13ee33dc4"></a><br/></td></tr>
<tr class="separator:a7c6e27604bd227c0c7685ae13ee33dc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8030e626bbdfa4d8f50cf01ea2d1c0ea"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_a_p_s_r___type.html#a3b04d58738b66a28ff13f23d8b0ba7e5">Z</a>:1</td></tr>
<tr class="memdesc:a8030e626bbdfa4d8f50cf01ea2d1c0ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 30 Zero condition code flag  <a href="#a8030e626bbdfa4d8f50cf01ea2d1c0ea"></a><br/></td></tr>
<tr class="separator:a8030e626bbdfa4d8f50cf01ea2d1c0ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77dede9507ca1f554043f49035299f2e"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_a_p_s_r___type.html#a7e7bbba9b00b0bb3283dc07f1abe37e0">N</a>:1</td></tr>
<tr class="memdesc:a77dede9507ca1f554043f49035299f2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 31 Negative condition code flag  <a href="#a77dede9507ca1f554043f49035299f2e"></a><br/></td></tr>
<tr class="separator:a77dede9507ca1f554043f49035299f2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dbc79a057ded4b11ca5323fc2d5ab14"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="union_a_p_s_r___type.html#a7dbc79a057ded4b11ca5323fc2d5ab14">b</a></td></tr>
<tr class="memdesc:a7dbc79a057ded4b11ca5323fc2d5ab14"><td class="mdescLeft">&#160;</td><td class="mdescRight">Structure used for bit access.  <a href="#a7dbc79a057ded4b11ca5323fc2d5ab14"></a><br/></td></tr>
<tr class="separator:a7dbc79a057ded4b11ca5323fc2d5ab14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4c2ef8c9430d7b7bef5cbfbbaed3a94"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="union_a_p_s_r___type.html#ae4c2ef8c9430d7b7bef5cbfbbaed3a94">w</a></td></tr>
<tr class="memdesc:ae4c2ef8c9430d7b7bef5cbfbbaed3a94"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type used for word access.  <a href="#ae4c2ef8c9430d7b7bef5cbfbbaed3a94"></a><br/></td></tr>
<tr class="separator:ae4c2ef8c9430d7b7bef5cbfbbaed3a94"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="afbce95646fd514c10aa85ec0a33db728"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t APSR_Type::_reserved0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7dbc79a057ded4b11ca5323fc2d5ab14"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   APSR_Type::b</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a86e2c5b891ecef1ab55b1edac0da79a6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t APSR_Type::C</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7e7bbba9b00b0bb3283dc07f1abe37e0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t APSR_Type::N</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a22d10913489d24ab08bd83457daa88de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t APSR_Type::Q</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8004d224aacb78ca37774c35f9156e7e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t APSR_Type::V</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae4c2ef8c9430d7b7bef5cbfbbaed3a94"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t APSR_Type::w</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3b04d58738b66a28ff13f23d8b0ba7e5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t APSR_Type::Z</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="union_a_p_s_r___type.html">APSR_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
