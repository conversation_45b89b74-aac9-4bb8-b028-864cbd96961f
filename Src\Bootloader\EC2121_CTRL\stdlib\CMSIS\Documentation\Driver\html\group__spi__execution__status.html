<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Status Error Codes</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__spi__execution__status.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">Status Error Codes<div class="ingroups"><a class="el" href="group__spi__interface__gr.html">SPI Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Negative values indicate errors (SPI has specific codes in addition to common <a class="el" href="group__execution__status.html">Status Error Codes</a>).  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga273a55c5d19491c565e5f05d03d66f3f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__execution__status.html#ga273a55c5d19491c565e5f05d03d66f3f">ARM_SPI_ERROR_MODE</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 1)</td></tr>
<tr class="memdesc:ga273a55c5d19491c565e5f05d03d66f3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Mode not supported.  <a href="#ga273a55c5d19491c565e5f05d03d66f3f">More...</a><br/></td></tr>
<tr class="separator:ga273a55c5d19491c565e5f05d03d66f3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac47584fe5691889c056611bc589b25aa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__execution__status.html#gac47584fe5691889c056611bc589b25aa">ARM_SPI_ERROR_FRAME_FORMAT</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 2)</td></tr>
<tr class="memdesc:gac47584fe5691889c056611bc589b25aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Frame Format not supported.  <a href="#gac47584fe5691889c056611bc589b25aa">More...</a><br/></td></tr>
<tr class="separator:gac47584fe5691889c056611bc589b25aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76f895d3380ca474124f83acbebc5651"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__execution__status.html#ga76f895d3380ca474124f83acbebc5651">ARM_SPI_ERROR_DATA_BITS</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 3)</td></tr>
<tr class="memdesc:ga76f895d3380ca474124f83acbebc5651"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified number of Data bits not supported.  <a href="#ga76f895d3380ca474124f83acbebc5651">More...</a><br/></td></tr>
<tr class="separator:ga76f895d3380ca474124f83acbebc5651"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b8ac31930ea6ca3a9635f2ac935466d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__execution__status.html#ga6b8ac31930ea6ca3a9635f2ac935466d">ARM_SPI_ERROR_BIT_ORDER</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 4)</td></tr>
<tr class="memdesc:ga6b8ac31930ea6ca3a9635f2ac935466d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Bit order not supported.  <a href="#ga6b8ac31930ea6ca3a9635f2ac935466d">More...</a><br/></td></tr>
<tr class="separator:ga6b8ac31930ea6ca3a9635f2ac935466d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaae7b1a1feb46faa1830c92b73bd775ad"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__spi__execution__status.html#gaae7b1a1feb46faa1830c92b73bd775ad">ARM_SPI_ERROR_SS_MODE</a>&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 5)</td></tr>
<tr class="memdesc:gaae7b1a1feb46faa1830c92b73bd775ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specified Slave Select Mode not supported.  <a href="#gaae7b1a1feb46faa1830c92b73bd775ad">More...</a><br/></td></tr>
<tr class="separator:gaae7b1a1feb46faa1830c92b73bd775ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Negative values indicate errors (SPI has specific codes in addition to common <a class="el" href="group__execution__status.html">Status Error Codes</a>). </p>
<p>The SPI driver has additional status error codes that are listed below. Note that the SPI driver also returns the comon <a class="el" href="group__execution__status.html">Status Error Codes</a>. </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="ga6b8ac31930ea6ca3a9635f2ac935466d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_ERROR_BIT_ORDER&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 4)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Bit order not supported. </p>
<p>The <b>bit order</b> requested with the function <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="ga76f895d3380ca474124f83acbebc5651"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_ERROR_DATA_BITS&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 3)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified number of Data bits not supported. </p>
<p>The number of <b>data bits</b> requested with the function <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="gac47584fe5691889c056611bc589b25aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_ERROR_FRAME_FORMAT&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 2)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Frame Format not supported. </p>
<p>The <b>frame format</b> requested with the function <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="ga273a55c5d19491c565e5f05d03d66f3f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_ERROR_MODE&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 1)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Mode not supported. </p>
<p>The <b>mode</b> requested with the function <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> is not supported by this driver. </p>

</div>
</div>
<a class="anchor" id="gaae7b1a1feb46faa1830c92b73bd775ad"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_SPI_ERROR_SS_MODE&#160;&#160;&#160;(<a class="el" href="group__execution__status.html#ga5a2b5d68f6649598d099b88c0eaee3e5">ARM_DRIVER_ERROR_SPECIFIC</a> - 5)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Specified Slave Select Mode not supported. </p>
<p>The <b>slave select mode</b> requested with the function <a class="el" href="group__spi__interface__gr.html#gad18d229992598d6677bec250015e5d1a">ARM_SPI_Control</a> is not supported by this driver. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
