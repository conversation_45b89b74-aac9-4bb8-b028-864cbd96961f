<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__"><span>_</span></a></li>
      <li><a href="globals_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="globals_0x66.html#index_f"><span>f</span></a></li>
      <li class="current"><a href="globals_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_0x75.html#index_u"><span>u</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_0x69.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index_i"></a>- i -</h3><ul>
<li>ARM_I2C_ABORT_TRANSFER
: <a class="el" href="group__i2c__control__codes.html#ga661e91aaa642d10ba80e3cc72f263040">Driver_I2C.h</a>
</li>
<li>ARM_I2C_ADDRESS_10BIT
: <a class="el" href="group__i2c__address__flags.html#ga16be1861b90774bf062feab2dbb829a4">Driver_I2C.h</a>
</li>
<li>ARM_I2C_ADDRESS_GC
: <a class="el" href="group__i2c__address__flags.html#ga337f4f1aa082e9b593b2dcd43c50134e">Driver_I2C.h</a>
</li>
<li>ARM_I2C_API_VERSION
: <a class="el" href="_driver___i2_c_8h.html#a1d02c71e603ea569af1755251b1d179f">Driver_I2C.h</a>
</li>
<li>ARM_I2C_BUS_CLEAR
: <a class="el" href="group__i2c__control__codes.html#gadacf04578770faca4b3eaae34b2c5f03">Driver_I2C.h</a>
</li>
<li>ARM_I2C_BUS_SPEED
: <a class="el" href="group__i2c__control__codes.html#ga35733133237d65146abd9449f5353a7f">Driver_I2C.h</a>
</li>
<li>ARM_I2C_BUS_SPEED_FAST
: <a class="el" href="group__i2c__bus__speed__ctrls.html#ga39f49ef4cd1100a8d9dc9003329e5ecd">Driver_I2C.h</a>
</li>
<li>ARM_I2C_BUS_SPEED_FAST_PLUS
: <a class="el" href="group__i2c__bus__speed__ctrls.html#ga2615262062e0327ab478ec85675ca649">Driver_I2C.h</a>
</li>
<li>ARM_I2C_BUS_SPEED_HIGH
: <a class="el" href="group__i2c__bus__speed__ctrls.html#ga10aae5a8c7fcc90e514c5fb7393056ec">Driver_I2C.h</a>
</li>
<li>ARM_I2C_BUS_SPEED_STANDARD
: <a class="el" href="group__i2c__bus__speed__ctrls.html#ga0aaa6398280fdd7ad651d7d6d44c863f">Driver_I2C.h</a>
</li>
<li>ARM_I2C_Control()
: <a class="el" href="group__i2c__interface__gr.html#ga828f5fa289d065675ef78a9a73d129dc">Driver_I2C.c</a>
</li>
<li>ARM_I2C_EVENT_ADDRESS_NACK
: <a class="el" href="group___i2_c__events.html#ga98b815769634d9578526b43589caa017">Driver_I2C.h</a>
</li>
<li>ARM_I2C_EVENT_ARBITRATION_LOST
: <a class="el" href="group___i2_c__events.html#gac9000f44a578e2117d64dbc2093cec6d">Driver_I2C.h</a>
</li>
<li>ARM_I2C_EVENT_BUS_CLEAR
: <a class="el" href="group___i2_c__events.html#ga81ca21fad73dac1ffaff58921f848ea9">Driver_I2C.h</a>
</li>
<li>ARM_I2C_EVENT_BUS_ERROR
: <a class="el" href="group___i2_c__events.html#gaeef542840355131c18b53fd9ed1904a8">Driver_I2C.h</a>
</li>
<li>ARM_I2C_EVENT_GENERAL_CALL
: <a class="el" href="group___i2_c__events.html#ga3ab54410b6410ed3a58762ff0c0d68b9">Driver_I2C.h</a>
</li>
<li>ARM_I2C_EVENT_SLAVE_RECEIVE
: <a class="el" href="group___i2_c__events.html#gabd875b57ce39dadd849c53b885ad6661">Driver_I2C.h</a>
</li>
<li>ARM_I2C_EVENT_SLAVE_TRANSMIT
: <a class="el" href="group___i2_c__events.html#gacfbbec9af083d35e8ea87ad16e9c6ec2">Driver_I2C.h</a>
</li>
<li>ARM_I2C_EVENT_TRANSFER_DONE
: <a class="el" href="group___i2_c__events.html#ga5992dc0f6e839c4d066cfa83d535f30d">Driver_I2C.h</a>
</li>
<li>ARM_I2C_EVENT_TRANSFER_INCOMPLETE
: <a class="el" href="group___i2_c__events.html#gafac3989c7b57727e1bed4ee9f2496ac9">Driver_I2C.h</a>
</li>
<li>ARM_I2C_GetCapabilities()
: <a class="el" href="group__i2c__interface__gr.html#gad20e6731f627aa7b9d6e99a50806122e">Driver_I2C.c</a>
</li>
<li>ARM_I2C_GetDataCount()
: <a class="el" href="group__i2c__interface__gr.html#ga19db20ad8d7fde84d07f6db4d75f4b7c">Driver_I2C.c</a>
</li>
<li>ARM_I2C_GetStatus()
: <a class="el" href="group__i2c__interface__gr.html#gaba4e0f3eb4018e7dafd51b675c465f3e">Driver_I2C.c</a>
</li>
<li>ARM_I2C_GetVersion()
: <a class="el" href="group__i2c__interface__gr.html#ga956bd87590c7fb6e23609a0abfb5412c">Driver_I2C.c</a>
</li>
<li>ARM_I2C_Initialize()
: <a class="el" href="group__i2c__interface__gr.html#ga79d2f7d01b3a681d1cf0d70ac6692696">Driver_I2C.c</a>
</li>
<li>ARM_I2C_MasterReceive()
: <a class="el" href="group__i2c__interface__gr.html#gafa22504bcf88a85584dfe6e0dd270ad5">Driver_I2C.c</a>
</li>
<li>ARM_I2C_MasterTransmit()
: <a class="el" href="group__i2c__interface__gr.html#ga8bf4214580149d5a5d2360f71f0feb94">Driver_I2C.c</a>
</li>
<li>ARM_I2C_OWN_ADDRESS
: <a class="el" href="group__i2c__control__codes.html#ga69d130b9f8aa34d6df5334ab67c74307">Driver_I2C.h</a>
</li>
<li>ARM_I2C_PowerControl()
: <a class="el" href="group__i2c__interface__gr.html#ga734a69200e063fdbfb5110062afe9329">Driver_I2C.c</a>
</li>
<li>ARM_I2C_SignalEvent()
: <a class="el" href="group__i2c__interface__gr.html#gad4f93d2895794b416dc8d8e9de91c05e">Driver_I2C.c</a>
</li>
<li>ARM_I2C_SignalEvent_t
: <a class="el" href="group__i2c__interface__gr.html#ga24277c48248a09b0dd7f12bbe22ce13c">Driver_I2C.h</a>
</li>
<li>ARM_I2C_SlaveReceive()
: <a class="el" href="group__i2c__interface__gr.html#gae3c9abccd1d377385d3d4cfe29035164">Driver_I2C.c</a>
</li>
<li>ARM_I2C_SlaveTransmit()
: <a class="el" href="group__i2c__interface__gr.html#gafe164f30eba78f066272373b98a62cd4">Driver_I2C.c</a>
</li>
<li>ARM_I2C_Uninitialize()
: <a class="el" href="group__i2c__interface__gr.html#ga30d8bf600b6b3182a1f867407b3d6e75">Driver_I2C.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:52 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
