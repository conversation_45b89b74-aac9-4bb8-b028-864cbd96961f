<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Driver: Ethernet MAC Timer Control Codes</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Driver
   &#160;<span id="projectnumber">Version 2.02</span>
   </div>
   <div id="projectbrief">Peripheral Interface for Middleware and Application Code</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li class="current"><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__eth__mac__time__control.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">Ethernet MAC Timer Control Codes<div class="ingroups"><a class="el" href="group__eth__mac__interface__gr.html">Ethernet MAC Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Control codes for <a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">ARM_ETH_MAC_ControlTimer</a> function.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gad9a439b9727c032a7d851df2a7a622c2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__time__control.html#gad9a439b9727c032a7d851df2a7a622c2">ARM_ETH_MAC_TIMER_GET_TIME</a>&#160;&#160;&#160;(0x01)</td></tr>
<tr class="memdesc:gad9a439b9727c032a7d851df2a7a622c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get current time.  <a href="#gad9a439b9727c032a7d851df2a7a622c2">More...</a><br/></td></tr>
<tr class="separator:gad9a439b9727c032a7d851df2a7a622c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5e867a003c06046d7944bcb5723e6049"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__time__control.html#ga5e867a003c06046d7944bcb5723e6049">ARM_ETH_MAC_TIMER_SET_TIME</a>&#160;&#160;&#160;(0x02)</td></tr>
<tr class="memdesc:ga5e867a003c06046d7944bcb5723e6049"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set new time.  <a href="#ga5e867a003c06046d7944bcb5723e6049">More...</a><br/></td></tr>
<tr class="separator:ga5e867a003c06046d7944bcb5723e6049"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3c57b3150717fb1a8cbbbac6a9b7ff69"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__time__control.html#ga3c57b3150717fb1a8cbbbac6a9b7ff69">ARM_ETH_MAC_TIMER_INC_TIME</a>&#160;&#160;&#160;(0x03)</td></tr>
<tr class="memdesc:ga3c57b3150717fb1a8cbbbac6a9b7ff69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Increment current time.  <a href="#ga3c57b3150717fb1a8cbbbac6a9b7ff69">More...</a><br/></td></tr>
<tr class="separator:ga3c57b3150717fb1a8cbbbac6a9b7ff69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaca9f1c4259d0342e9717a362de1ccf41"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__time__control.html#gaca9f1c4259d0342e9717a362de1ccf41">ARM_ETH_MAC_TIMER_DEC_TIME</a>&#160;&#160;&#160;(0x04)</td></tr>
<tr class="memdesc:gaca9f1c4259d0342e9717a362de1ccf41"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decrement current time.  <a href="#gaca9f1c4259d0342e9717a362de1ccf41">More...</a><br/></td></tr>
<tr class="separator:gaca9f1c4259d0342e9717a362de1ccf41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04c2469ba027b020bc6b5baf3b51cf74"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__time__control.html#ga04c2469ba027b020bc6b5baf3b51cf74">ARM_ETH_MAC_TIMER_SET_ALARM</a>&#160;&#160;&#160;(0x05)</td></tr>
<tr class="memdesc:ga04c2469ba027b020bc6b5baf3b51cf74"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set alarm time.  <a href="#ga04c2469ba027b020bc6b5baf3b51cf74">More...</a><br/></td></tr>
<tr class="separator:ga04c2469ba027b020bc6b5baf3b51cf74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga85cb862eba0934e958a8552022588db7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__eth__mac__time__control.html#ga85cb862eba0934e958a8552022588db7">ARM_ETH_MAC_TIMER_ADJUST_CLOCK</a>&#160;&#160;&#160;(0x06)</td></tr>
<tr class="memdesc:ga85cb862eba0934e958a8552022588db7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adjust clock frequency; time-&gt;ns: correction factor * 2^31.  <a href="#ga85cb862eba0934e958a8552022588db7">More...</a><br/></td></tr>
<tr class="separator:ga85cb862eba0934e958a8552022588db7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Control codes for <a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">ARM_ETH_MAC_ControlTimer</a> function. </p>
<p>The following timer controls are used as parameter <em>control</em> for the <a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643">ARM_ETH_MAC_ControlTimer</a> function: </p>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="ga85cb862eba0934e958a8552022588db7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TIMER_ADJUST_CLOCK&#160;&#160;&#160;(0x06)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adjust clock frequency; time-&gt;ns: correction factor * 2^31. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643" title="Control Precision Timer.">ARM_ETH_MAC_ControlTimer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaca9f1c4259d0342e9717a362de1ccf41"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TIMER_DEC_TIME&#160;&#160;&#160;(0x04)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Decrement current time. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643" title="Control Precision Timer.">ARM_ETH_MAC_ControlTimer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad9a439b9727c032a7d851df2a7a622c2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TIMER_GET_TIME&#160;&#160;&#160;(0x01)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get current time. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643" title="Control Precision Timer.">ARM_ETH_MAC_ControlTimer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3c57b3150717fb1a8cbbbac6a9b7ff69"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TIMER_INC_TIME&#160;&#160;&#160;(0x03)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Increment current time. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643" title="Control Precision Timer.">ARM_ETH_MAC_ControlTimer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga04c2469ba027b020bc6b5baf3b51cf74"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TIMER_SET_ALARM&#160;&#160;&#160;(0x05)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set alarm time. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643" title="Control Precision Timer.">ARM_ETH_MAC_ControlTimer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5e867a003c06046d7944bcb5723e6049"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ARM_ETH_MAC_TIMER_SET_TIME&#160;&#160;&#160;(0x02)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set new time. </p>
<dl class="section see"><dt>See Also</dt><dd><a class="el" href="group__eth__mac__interface__gr.html#ga85d9dc865af3702b71a514b18a588643" title="Control Precision Timer.">ARM_ETH_MAC_ControlTimer</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:17:50 for CMSIS-Driver by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
