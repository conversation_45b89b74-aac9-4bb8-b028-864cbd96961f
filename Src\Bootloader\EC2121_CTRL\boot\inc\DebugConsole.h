#ifndef __DEBUG_CONSOLE_H__
#define __DEBUG_CONSOLE_H__
#include "stdarg.h"
#include "stm32f4xx_usart.h"
#include "stm32f4xx_gpio.h"
#include "stdio.h"
#define KPRINTF_DEBUG        1

#define CONSOLE_DEBUG_ON    1

#if  (KPRINTF_DEBUG || KPRINTF_LOG)
void DebugUartConfig();
void kprintf(const char *fmt, ...);
void kprintfFromISR(const char *fmt, ...);
void kprintfOnlyChar(const char *fmt);
void Refresh(void);
#endif

#if CONSOLE_DEBUG_ON
#define Kprintf kprintf
#else
#define Kprintf(fmt, ...)
#endif

#endif

