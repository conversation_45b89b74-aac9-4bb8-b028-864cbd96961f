<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-DSP: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li class="current"><a href="globals_func.html#index_a"><span>a</span></a></li>
      <li><a href="globals_func_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="globals_func_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="globals_func_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="globals_func_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="globals_func_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="globals_func_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="globals_func_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="globals_func_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="globals_func_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="globals_func_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="globals_func_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="globals_func_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="globals_func_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="globals_func_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="globals_func_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="globals_func_0x76.html#index_v"><span>v</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals_func.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_a"></a>- a -</h3><ul>
<li>arm_abs_f32()
: <a class="el" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02">arm_abs_f32.c</a>
, <a class="el" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02">arm_math.h</a>
</li>
<li>arm_abs_q15()
: <a class="el" href="group___basic_abs.html#ga39f92964c9b649ba252e26ebe7b95594">arm_math.h</a>
, <a class="el" href="group___basic_abs.html#ga39f92964c9b649ba252e26ebe7b95594">arm_abs_q15.c</a>
</li>
<li>arm_abs_q31()
: <a class="el" href="group___basic_abs.html#ga59eafcdcdb52da60d37f20aec6ff4577">arm_abs_q31.c</a>
, <a class="el" href="group___basic_abs.html#ga59eafcdcdb52da60d37f20aec6ff4577">arm_math.h</a>
</li>
<li>arm_abs_q7()
: <a class="el" href="group___basic_abs.html#gadc30985e33fbf96802a5a7954dece3b1">arm_math.h</a>
, <a class="el" href="group___basic_abs.html#gadc30985e33fbf96802a5a7954dece3b1">arm_abs_q7.c</a>
</li>
<li>arm_add_f32()
: <a class="el" href="group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd">arm_add_f32.c</a>
, <a class="el" href="group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd">arm_math.h</a>
</li>
<li>arm_add_q15()
: <a class="el" href="group___basic_add.html#gabb51285a41f511670bbff62fc0e1bf62">arm_add_q15.c</a>
, <a class="el" href="group___basic_add.html#gabb51285a41f511670bbff62fc0e1bf62">arm_math.h</a>
</li>
<li>arm_add_q31()
: <a class="el" href="group___basic_add.html#ga24d6c3f7f8b9fae4847c0c3f26a39a3b">arm_add_q31.c</a>
, <a class="el" href="group___basic_add.html#ga24d6c3f7f8b9fae4847c0c3f26a39a3b">arm_math.h</a>
</li>
<li>arm_add_q7()
: <a class="el" href="group___basic_add.html#gaed633f415a7840a66861debca2dfb96b">arm_math.h</a>
, <a class="el" href="group___basic_add.html#gaed633f415a7840a66861debca2dfb96b">arm_add_q7.c</a>
</li>
<li>arm_apply_guard_bits()
: <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_convolution_example/GCC/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_linear_interp_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_graphic_equalizer_example/ARM/math_helper.h</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_matrix_example/ARM/math_helper.h</a>
, <a class="el" href="arm__graphic__equalizer__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_graphic_equalizer_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_signal_converge_example/ARM/math_helper.c</a>
, <a class="el" href="arm__matrix__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_matrix_example/ARM/math_helper.c</a>
, <a class="el" href="arm__linear__interp__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_linear_interp_example/ARM/math_helper.c</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_convolution_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_g_c_c_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_convolution_example/GCC/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_fir_example/ARM/math_helper.h</a>
, <a class="el" href="arm__fir__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_fir_example/ARM/math_helper.c</a>
, <a class="el" href="arm__signal__converge__example_2_a_r_m_2math__helper_8h.html#a13580a6ff7a8a68146de727bdf8fba88">arm_signal_converge_example/ARM/math_helper.h</a>
, <a class="el" href="arm__convolution__example_2_a_r_m_2math__helper_8c.html#a13580a6ff7a8a68146de727bdf8fba88">arm_convolution_example/ARM/math_helper.c</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
