<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS-Pack: Pack with Software Components</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-Pack
   &#160;<span id="projectnumber">Version 1.3.1</span>
   </div>
   <div id="projectbrief">Delivery Mechanism for Software Packs</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <li><a href="../../General/html/index.html"><span>CMSIS</span></a></li>
      <li><a href="../../Core/html/index.html"><span>CORE</span></a></li>
      <li><a href="../../Driver/html/index.html"><span>Driver</span></a></li>
      <li><a href="../../DSP/html/index.html"><span>DSP</span></a></li>
      <li><a href="../../RTOS/html/index.html"><span>RTOS API</span></a></li>
      <li class="current"><a href="../../Pack/html/index.html"><span>Pack</span></a></li>
      <li><a href="../../SVD/html/index.html"><span>SVD</span></a></li>
    </ul>
</div>
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_c_p__s_w_components.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Pack with Software Components </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>This section is a tutorial that explains how to create a Software Pack. Initially, a Software Pack with a simple PDSC file is created that contains only one software component. This Software Pack is then extended to show the various features for describing software components.</p>
<p><b>Preparations</b></p>
<ol type="1">
<li>Create a working directory on you PC, for example <b>C:\temp\working</b>.</li>
<li>Go to the directory <b>\CMSIS\Pack\Tutorials</b> available in the <b>ARM::CMSIS</b> Pack installation. Please consult your development tool's documentation for more information on the Pack installation directory structure. In µVision, you will find it below <b>C:\Keil\ARM\Pack\ARM\CMSIS\<em>version</em></b>.</li>
<li>Open the file <b>Pack_with_Software_Components.zip</b>.</li>
<li>Copy the <em>content</em> of the ZIP file's <b>01_Create_PDSC</b> directory into your working directory.</li>
<li>Make sure that files/directories are not write protected (remove read-only flag).</li>
<li>Copy from the directory <b>\CMSIS\Utilities</b> available in the <b>ARM::CMSIS</b> Pack installation the following files into your working directory:<ul>
<li>PackChk.exe</li>
<li>PACK.xsd</li>
</ul>
</li>
</ol>
<h1><a class="anchor" id="CP_CreatePDSC"></a>
Create PDSC File</h1>
<ol>
<li>
<p class="startli">Open the file <b>vendor.pack_name.pdsc</b> in an editor and change it like this: </p>
<div class="fragment"><div class="line">&lt;?xml version=<span class="stringliteral">&quot;1.0&quot;</span> encoding=<span class="stringliteral">&quot;UTF-8&quot;</span>?&gt;</div>
<div class="line">&lt;<span class="keyword">package </span>schemaVersion=&quot;1.2&quot; xmlns:xs=&quot;http:<span class="comment">//www.w3.org/2001/XMLSchema-instance&quot; xs:noNamespaceSchemaLocation=&quot;PACK.xsd&quot;&gt;</span></div>
<div class="line">  &lt;name&gt;MyPack&lt;/name&gt;</div>
<div class="line">  &lt;description&gt;Internal Software Pack&lt;/description&gt;</div>
<div class="line">  &lt;vendor&gt;MyVendor&lt;/vendor&gt;</div>
<div class="line">  &lt;url&gt;&lt;/url&gt;</div>
<div class="line">  &lt;supportContact&gt;&lt;/supportContact&gt;</div>
<div class="line">  &lt;license&gt;Docs\license.txt&lt;/license&gt;</div>
</div><!-- fragment --><dl class="section note"><dt>Note</dt><dd>All code examples in this and the following sections can be found in the <code>snippets.xml</code> file in the <b>01_Create_PDSC</b> directory.</dd></dl>
<p>A PDSC file starts with information on the XML version and the encoding. The <a class="el" href="pdsc_package_pg.html">&lt;package&gt;</a> element declares the version of the schema file and the XSD file that is used to validate the PDSC file (hence the copy of the PACK.xsd file into the working directory):</p>
<ul>
<li><a class="el" href="pdsc_package_pg.html#element_package">&lt;name&gt;</a> specifies the name of the Pack</li>
<li><a class="el" href="pdsc_package_pg.html#element_package">&lt;description&gt;</a> describes the Pack in a few words</li>
<li><a class="el" href="pdsc_package_pg.html#element_package">&lt;vendor&gt;</a> states the name of the Pack's vendor</li>
<li><a class="el" href="pdsc_package_pg.html#element_package">&lt;url&gt;</a> defines the download URL of the Pack (may be left empty)</li>
<li><a class="el" href="pdsc_package_pg.html#element_package">&lt;supportContact&gt;</a> may provide an email address for Pack specific questions/problems</li>
<li><a class="el" href="pdsc_package_pg.html#element_package">&lt;license&gt;</a> links to an optional license file that will be shown during the installation of the Pack. The installation in development tools will abort if the license agreement is not accepted.  </li>
</ul>
</li>
<li>
<p class="startli">Add release information and keywords: </p>
<div class="fragment"><div class="line">&lt;releases&gt;</div>
<div class="line">  &lt;release version=<span class="stringliteral">&quot;1.0.0&quot;</span>&gt;</div>
<div class="line">    2014/04/23, Initial version</div>
<div class="line">  &lt;/release&gt;</div>
<div class="line">&lt;/releases&gt;</div>
<div class="line">    </div>
<div class="line">&lt;keywords&gt;</div>
<div class="line">  &lt;keyword&gt;MyVendor&lt;/keyword&gt;</div>
<div class="line">  &lt;keyword&gt;My Software Component&lt;/keyword&gt;</div>
<div class="line">&lt;/keywords&gt;</div>
</div><!-- fragment --><p> The information from the <a class="el" href="element_releases.html">&lt;releases&gt;</a> section will be used for:</p>
<ul>
<li>Determining the Pack version (required for the file name of the Pack file)</li>
<li>Display of release information</li>
</ul>
<p class="endli"><a class="el" href="element_keywords.html">&lt;keywords&gt;</a> may be used for better visibility for search engines.  </p>
</li>
<li>
Uncomment the <a class="el" href="pdsc_components_pg.html">&lt;components&gt;</a> section and add the following: <div class="fragment"><div class="line">&lt;components&gt;</div>
<div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;MyClass&quot;</span> Cgroup=<span class="stringliteral">&quot;MyGroup&quot;</span> Csub=<span class="stringliteral">&quot;MySubGroup&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.0&quot;</span>&gt;</div>
<div class="line">      &lt;description&gt;MySWComp&lt;/description&gt;</div>
<div class="line">      &lt;files&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;doc&quot;</span>    name=<span class="stringliteral">&quot;Docs\MySWComp.htm&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;MySWComp\header_mylib.h&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;MySWComp\config_mylib.h&quot;</span> attr=<span class="stringliteral">&quot;config&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\mylib_one.c&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\mylib_two.c&quot;</span>/&gt;</div>
<div class="line">      &lt;/files&gt;</div>
<div class="line">    &lt;/component&gt;</div>
<div class="line">&lt;/components&gt;</div>
</div><!-- fragment --> More information on <b>&lt;components&gt;</b> will be given in section <a class="el" href="_c_p__s_w_components.html#CP_Components">Software Components</a>. Save the file and close your editor. In the next section, the generation of a Pack will be explained in detail.  </li>
</ol>
<h1><a class="anchor" id="CP_GeneratePack"></a>
Generate a Pack</h1>
<ol>
<li>
Rename the file <b>vendor.pack_name.pdsc</b> to <b>MyVendor.MyPack.pdsc</b>.  </li>
<li>
Open the batch file <b>gen_pack.bat</b> from your working directory in a text editor. Check if the following line of code reflects your setup: <div class="fragment"><div class="line"><span class="stringliteral">&quot;C:\Program Files\7-Zip\7z.exe&quot;</span> a %PackName% Files -tzip</div>
</div><!-- fragment --> If you do not have <a href="http://www.7-zip.org/" target="_blank">7-Zip</a> installed on your PC, you can specify any other ZIP tool that supports command line calls. In that case you have to adapt the command line arguments accordingly.  </li>
<li>
Having saved <b>gen_pack.bat</b>, you can execute it either by double-clicking or by using the command line (which is recommended to see the output of PackChk). The batch file will:<ul>
<li>Check the availability of the PDSC file</li>
<li>Copy the PDSC file to the <b>Files</b> directory</li>
<li>Run PackChk.exe on the <b>Files</b> directory<br/>
</li>
<li>Create a Pack file in the working directory. It will be called <b>MyVendor.MyPack.1.0.0.pack</b>.  </li>
</ul>
</li>
<li>
<p class="startli">Install the Pack by double-clicking it. Depending on the development software that is used, the display of the Pack contents may differ:</p>
<div class="image">
<img src="simplepack_rte.png" alt="simplepack_rte.png"/>
<div class="caption">
MyClass:MyGroup:MySubGroup shown in development tool</div></div>
 </li>
</ol>
<h1><a class="anchor" id="CP_Components"></a>
Software Components</h1>
<p>A component lists the files that belong to it and that are relevant for a project. The component itself or each individual file may refer to a <a class="el" href="pdsc_conditions_pg.html#element_condition">condition</a> that must resolve to true; if it is false the component or file is not applicable in the given context.</p>
<p>Each software components must have the following attributes that are used to identify the component:</p>
<ul>
<li>Component Class (<code>Cclass</code>): examples are <b>CMSIS</b>, <b>Device</b>, <b>File</b> <b>System</b> </li>
<li>Component Group (<code>Cgroup</code>): examples are <b>CMSIS:RTOS</b>, <b>Device:Startup</b>, <b>File</b> <b>System:CORE</b> </li>
<li>Component Version (<code>Cversion</code>): the version number of the software component.</li>
</ul>
<p>Optionally, a software component may have additional attributes:</p>
<ul>
<li>Component Sub-Group (<code>Csub</code>): examples are <b>CMSIS:RTOS:MyRTOS</b>, <b>Device:Driver</b> <b>USBD:Full-speed</b> </li>
<li>Component Variant (<code>Cvariant</code>): a variant of the software component.</li>
<li>Component Vendor (<code>Cvendor</code>): the supplier of the software component.</li>
</ul>
<p>The <code>Cclass</code>, <code>Cgroup</code>, <code>Csub</code>, <code>Cvariant</code> and <code>Cversion</code> attributes are used together with the vendor specified by the PACK to identify a component. A component vendor must ensure that the combination <code>Cclass</code>, <code>Cgroup</code>, <code>Csub</code> and <code>Cversion</code> is unique and not used by multiple components.</p>
<p>The following <code>Cclass</code> names have a special meaning:</p>
<ul>
<li><b>Board support</b> usually contains board specific drivers for external peripheral devices.</li>
<li><b>CMSIS</b> contains general CMSIS components like the CORE, DSP extensions, RTOSes (e.g. Keil RTX)</li>
<li><b>Device</b> contains startup files for the target device</li>
<li><b>CMSIS Driver</b> contain CMSIS-Driver conform peripheral drivers for the target device. These will be listed below the respective Cgroup API entries (for example <b>Ethernet (API)</b>)</li>
<li><b>File System</b>, <b>Graphics</b>, <b>Network</b>, and <b>USB</b> contain middleware to support these components</li>
</ul>
<p>Other <code>Cclass</code> names can be freely assigned to software components.</p>
<p>In case multiple interdependent components (belonging to the same <code>Cclass</code>) form part of a solution, these can be grouped in a so called <a class="el" href="_c_p__s_w_components.html#CP_Bundles">bundle</a>.</p>
<p>Use the following syntax to reference to software components and APIs:<br/>
 <b>&lt;Vendor&gt;::&lt;Component Class&gt;:&lt;Component Group&gt;:&lt;Sub-Group&gt;</b></p>
<p><b>Examples:</b> </p>
<ul>
<li><b>::CMSIS:RTOS (API)</b> - the CMSIS-RTOS API.</li>
<li><b>ARM::CMSIS:DSP</b> - the CMSIS-DSP Library.</li>
<li><b>::File System:Drive:NOR</b> - the NOR Flash Drive of a file system.</li>
</ul>
<p>Here is an example for the display of software components in a development tool:</p>
<div class="image">
<img src="ComponentDisplay.png" alt="ComponentDisplay.png"/>
<div class="caption">
Display of a software component in development tools</div></div>
 <h1><a class="anchor" id="CP_Conditions"></a>
Conditions</h1>
<p>A condition describes dependencies on:</p>
<ul>
<li>a specific device</li>
<li>a certain processor</li>
<li>tool attributes</li>
<li>the presence of other components</li>
</ul>
<p>Conditions are used to define <b>AND/OR</b> rules that make components conditional and therefore only available under certain circumstances, for example for specific devices or processors. Conditions are also used to express dependencies between software components.</p>
<p>Each condition has an <b>id</b> that is unique within the scope of a the PDSC file. An id can be referenced in the <code>condition</code> attribute of components, APIs, examples, files and other conditions. All attributes set in a <b>accept</b>, <b>require</b>, or <b>deny</b> element must resolve to true for the element to become true. A condition becomes true when:</p>
<ul>
<li>At least one <b>accept</b> element is true, <em>AND</em> </li>
<li>all <b>require</b> elements are true, <em>AND</em> </li>
<li>no <b>deny</b> element is true.</li>
</ul>
<p>If a condition resolves to false during processing, the respective element will be ignored.</p>
<p>Let's modify the example from the <a class="el" href="_c_p__s_w_components.html#CP_CreatePDSC">Create PDSC File</a> section by adding the requirement for a CMSIS-RTOS and libraries that are specific to a certain Cortex-M class core:</p>
<ul>
<li><code>mylib_cm0.lib</code> for use with a Cortex-M0 and Cortex-M0+ processor</li>
<li><code>mylib_cm3.lib</code> for use with a Cortex-M3 processor</li>
<li><code>mylib_cm4.lib</code> for use with a Cortex-M4 processor</li>
</ul>
<p>Copy the <em>content</em> of the <b>02_Conditions</b> directory of the <b>Pack_with_Software_Components.zip</b> file to the <b>Files</b> directory in your working environment: </p>
<ol>
<li>
Uncomment the <a class="el" href="pdsc_conditions_pg.html">&lt;conditions&gt;</a> section and add the following: <div class="fragment"><div class="line">&lt;conditions&gt; </div>
<div class="line">  &lt;condition <span class="keywordtype">id</span>=<span class="stringliteral">&quot;CM0&quot;</span>&gt;</div>
<div class="line">    &lt;description&gt;Cortex-M0 based device&lt;/description&gt;</div>
<div class="line">    &lt;accept Dcore=<span class="stringliteral">&quot;Cortex-M0&quot;</span>/&gt;</div>
<div class="line">    &lt;accept Dcore=<span class="stringliteral">&quot;Cortex-M0+&quot;</span>/&gt;</div>
<div class="line">  &lt;/condition&gt;</div>
<div class="line">  &lt;condition <span class="keywordtype">id</span>=<span class="stringliteral">&quot;CM3&quot;</span>&gt;</div>
<div class="line">    &lt;description&gt;Cortex-M3 based device&lt;/description&gt;</div>
<div class="line">    &lt;accept Dcore=<span class="stringliteral">&quot;Cortex-M3&quot;</span>/&gt;</div>
<div class="line">  &lt;/condition&gt;</div>
<div class="line">  &lt;condition <span class="keywordtype">id</span>=<span class="stringliteral">&quot;CM4&quot;</span>&gt;</div>
<div class="line">    &lt;description&gt;Cortex-M4 based device&lt;/description&gt;</div>
<div class="line">    &lt;accept Dcore=<span class="stringliteral">&quot;Cortex-M4&quot;</span>/&gt;</div>
<div class="line">  &lt;/condition&gt;</div>
<div class="line">  &lt;condition <span class="keywordtype">id</span>=<span class="stringliteral">&quot;CMSIS Core with RTOS&quot;</span>&gt;</div>
<div class="line">    &lt;description&gt;CMSIS Core with RTOS <span class="keywordflow">for</span> Cortex-M processor&lt;/description&gt;</div>
<div class="line">    &lt;accept condition=<span class="stringliteral">&quot;CM0&quot;</span>/&gt;</div>
<div class="line">    &lt;accept condition=<span class="stringliteral">&quot;CM3&quot;</span>/&gt;</div>
<div class="line">    &lt;accept condition=<span class="stringliteral">&quot;CM4&quot;</span>/&gt;</div>
<div class="line">    &lt;require Cclass=<span class="stringliteral">&quot;CMSIS&quot;</span> Cgroup=<span class="stringliteral">&quot;CORE&quot;</span>/&gt;</div>
<div class="line">    &lt;require Cclass=<span class="stringliteral">&quot;CMSIS&quot;</span> Cgroup=<span class="stringliteral">&quot;RTOS&quot;</span>/&gt;</div>
<div class="line">  &lt;/condition&gt;</div>
<div class="line">&lt;/conditions&gt;</div>
</div><!-- fragment -->  </li>
<li>
Change the first line of the already existing <b>component</b> by adding the condition and increasing the version number of the component: <div class="fragment"><div class="line">&lt;component Cclass=<span class="stringliteral">&quot;MyClass&quot;</span> Cgroup=<span class="stringliteral">&quot;MyGroup&quot;</span> Csub=<span class="stringliteral">&quot;MySubGroup&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.1&quot;</span> condition=<span class="stringliteral">&quot;CMSIS Core with RTOS&quot;</span>&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add the following code to the already existing <b>component</b>: <div class="fragment"><div class="line">&lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM0&quot;</span> name=<span class="stringliteral">&quot;MySWComp\mylib_cm0.lib&quot;</span>/&gt;</div>
<div class="line">&lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM3&quot;</span> name=<span class="stringliteral">&quot;MySWComp\mylib_cm3.lib&quot;</span>/&gt;</div>
<div class="line">&lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM4&quot;</span> name=<span class="stringliteral">&quot;MySWComp\mylib_cm4.lib&quot;</span>/&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new version number to the header of the PDSC file so that a Pack with a new version number will be created: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.1&quot;</span>&gt;</div>
<div class="line">  Conditions added</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
<p class="startli">Finally, save the PDSC file and regenerate the Pack file using the <b>gen_pack.bat</b> script. See <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">Generate a Pack</a> for further details. Afterwards, install the Pack in your development tool and observe the differences to version 1.0.0.</p>
<div class="image">
<img src="ConditionsDisplay.png" alt="ConditionsDisplay.png"/>
<div class="caption">
Display of conditionally added files for a Cortex-M3 device in development tools</div></div>
 </li>
</ol>
<h1><a class="anchor" id="CP_Variants"></a>
Variants</h1>
<p>Software components may have <b>variants</b>, for example:</p>
<ul>
<li>Debug version with diagnostic output/Release version without diagnostic</li>
<li>Long/short file names</li>
<li>Fast/slow modes</li>
</ul>
<p>Variants are mutually exclusive (only one variant can be chosen at a time). <code>Cvariant</code> is an optional part of the component ID. The variant specifier is a brief string (e.g. <code>release</code>, <code>debug</code>).</p>
<dl class="section note"><dt>Note</dt><dd>Version management relies on variants to <b>remain unchanged between versions</b>.</dd></dl>
<p>The following example introduces a new component to the Pack in two variants: <code>debug</code> and <code>release</code>. Copy the <em>content</em> of the <b>03_Variants</b> directory of the <b>Pack_with_Software_Components.zip</b> file to the <b>Files</b> directory in your working environment. </p>
<ol>
<li>
Add the following lines to the <b>components</b> section in your PDSC file: <div class="fragment"><div class="line">&lt;component Cclass=<span class="stringliteral">&quot;MyVariant&quot;</span> Cgroup=<span class="stringliteral">&quot;MyGroup&quot;</span> Cvariant=<span class="stringliteral">&quot;Release&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.2&quot;</span> condition=<span class="stringliteral">&quot;CMSIS Core with RTOS&quot;</span>&gt;</div>
<div class="line">  &lt;description&gt;Release version of MyVariant&lt;/description&gt;</div>
<div class="line">  &lt;RTE_Components_h&gt;</div>
<div class="line">    &lt;!-- the following content goes into file <span class="stringliteral">&#39;RTE_Components.h&#39;</span> --&gt;</div>
<div class="line">    #define RTE_MyVariant_Release               <span class="comment">/* MyVariant Release Version */</span></div>
<div class="line">  &lt;/RTE_Components_h&gt;</div>
<div class="line">  &lt;files&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;doc&quot;</span> name=<span class="stringliteral">&quot;Docs\MySWComp.htm&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;MySWComp\header_mylib.h&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;MySWComp\config_mylib.h&quot;</span> attr=<span class="stringliteral">&quot;config&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\mylib_one.c&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\mylib_two.c&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM0&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Lib\mylib_cm0.lib&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM3&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Lib\mylib_cm3.lib&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM4&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Lib\mylib_cm4.lib&quot;</span>/&gt;</div>
<div class="line">  &lt;/files&gt;</div>
<div class="line">&lt;/component&gt;</div>
<div class="line">      </div>
<div class="line">&lt;component Cclass=<span class="stringliteral">&quot;MyVariant&quot;</span> Cgroup=<span class="stringliteral">&quot;MyGroup&quot;</span> Cvariant=<span class="stringliteral">&quot;Debug&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.2&quot;</span> condition=<span class="stringliteral">&quot;CMSIS Core with RTOS&quot;</span>&gt;</div>
<div class="line">  &lt;description&gt;Debug version of MyVariant&lt;/description&gt;</div>
<div class="line">  &lt;RTE_Components_h&gt;</div>
<div class="line">    &lt;!-- the following content goes into file <span class="stringliteral">&#39;RTE_Components.h&#39;</span> --&gt;</div>
<div class="line">#define RTE_MyVariant_Debug</div>
<div class="line">  &lt;/RTE_Components_h&gt;</div>
<div class="line">  &lt;files&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;doc&quot;</span> name=<span class="stringliteral">&quot;Docs\MySWComp.htm&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;MySWComp\header_mylib.h&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;MySWComp\debug_config_mylib.h&quot;</span> attr=<span class="stringliteral">&quot;config&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\debug_mylib_one.c&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\debug_mylib_two.c&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM0&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Lib\debug_mylib_cm0.lib&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM3&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Lib\debug_mylib_cm3.lib&quot;</span>/&gt;</div>
<div class="line">    &lt;file category=<span class="stringliteral">&quot;library&quot;</span> condition=<span class="stringliteral">&quot;CM4&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Lib\debug_mylib_cm4.lib&quot;</span>/&gt;</div>
<div class="line">  &lt;/files&gt;</div>
<div class="line">&lt;/component&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new revision to reflect the changes in a newly generated Pack: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.2&quot;</span>&gt;</div>
<div class="line">  Variants introduced</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
<p class="startli">Finally, save the PDSC file and regenerate the Pack file using the <b>gen_pack.bat</b> script. See <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">Generate a Pack</a> for further details. Afterwards, install the Pack in your development tool and observe the differences to version 1.0.1.</p>
<div class="image">
<img src="VariantDisplay.png" alt="VariantDisplay.png"/>
<div class="caption">
Display of MyVariant in development tools</div></div>
 </li>
</ol>
<h2><a class="anchor" id="CP_RTEComponents_h"></a>
RTE_Components.h</h2>
<p>Sometimes, software components need to know of other components. Depending on the availability or configuration of another component, certain settings may be required. A distinct header file is available to make components aware of each other: <b>RTE_Components.h</b>. To add a line of C code to this header file, simply add this to your component:</p>
<pre class="fragment">&lt;RTE_Components_h&gt;
#define RTE_MyVariant_Debug
&lt;/RTE_Components_h&gt;
</pre><p> The <code>#define</code> can be checked by other components in a project.</p>
<dl class="section note"><dt>Note</dt><dd>The RTE_components.h file needs to be included somewhere in your source files.</dd></dl>
<h1><a class="anchor" id="CP_Bundles"></a>
Bundles</h1>
<p>A bundle is basically a variant on the <code>Cclass</code> level. It specifies the attributes <code>Cclass</code>, <code>Cversion</code> and optionally <code>Cgroup</code> and <code>Cvendor</code> for a collection of interdependent components. Components within a bundle inherit the attributes set by the bundle and must not set these attributes again. Bundles ensure consistency of attributes across multiple interworking components and restrict the mix and match of components within a <code>Cclass</code> from different solutions. In addition to components, a bundle has the mandatory elements <code>description</code> and <code>doc</code> (for documentation).</p>
<p>An example of a <b>bundle</b> is shown in the <a class="el" href="_create_pack_board.html#CP_BundleExample">Create a BSP Bundle</a> section where the bundle is used to deliver board support files for a certain development platform.</p>
<h1><a class="anchor" id="CP_Instances"></a>
Instances</h1>
<p>Some <a class="el" href="pdsc_components_pg.html">software components</a> allow several instances. This is useful if more than one peripheral can be connected.</p>
<ol>
<li>
Adding the information about a maximum number of instances is easy. Change the first component (::MyClass:MyGroup:MySub) as follows: <div class="fragment"><div class="line">&lt;component Cclass=<span class="stringliteral">&quot;MyClass&quot;</span> Cgroup=<span class="stringliteral">&quot;MyGroup&quot;</span> Csub=<span class="stringliteral">&quot;MySubGroup&quot;</span> Cversion=<span class="stringliteral">&quot;1.0.3&quot;</span> condition=<span class="stringliteral">&quot;CMSIS Core with RTOS&quot;</span> maxInstances=<span class="stringliteral">&quot;3&quot;</span>&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new version number: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.3&quot;</span>&gt;</div>
<div class="line">  Maximum number of instances specified</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
<p class="startli">Finally, save the PDSC file and regenerate the Pack file using the <b>gen_pack.bat</b> script. See <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">Generate a Pack</a> for further details. Afterwards, install the Pack in your development tool and observe the differences to version 1.0.2. When selecting a certain number of instances of the component, causes the development tool to copy the required configuration files multiple times into the project. This ensures that each component instance can be configured separately.</p>
<div class="image">
<img src="maxInstancesDisplay.png" alt="maxInstancesDisplay.png"/>
<div class="caption">
Display of component instances in development tools</div></div>
 </li>
</ol>
<h1><a class="anchor" id="CP_API"></a>
API Interface</h1>
<p>An <a class="el" href="pdsc_apis_pg.html">API</a> is a special form of a software component that only defines a C/C++ Application Programming Interface (API). An API does not contain the actual implementation (usually provided by source code or library files) and cannot be selected in a development tool. One example is the CMSIS-RTOS API, which is specified as part of CMSIS, however, the actual RTOS implementation is provided by different vendors. An API consists of a name, a brief description as well as one or more header files, and a document containing a detailed specification of the API.</p>
<p>Copy the <em>content</em> of the <b>05_APIs</b> directory of the <b>Pack_with_Software_Components.zip</b> file to the <b>Files</b> directory in your working environment: </p>
<ol>
<li>
Uncomment the <a class="el" href="pdsc_apis_pg.html">&lt;apis&gt;</a> section in your PDSC and add the following: <div class="fragment"><div class="line">&lt;api Cclass=<span class="stringliteral">&quot;Device&quot;</span> Cgroup=<span class="stringliteral">&quot;MyAPI&quot;</span> exclusive=<span class="stringliteral">&quot;0&quot;</span>&gt;</div>
<div class="line">      &lt;description&gt;API <span class="keywordflow">for</span> MyAPI&lt;/description&gt;</div>
<div class="line">      &lt;files&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;doc&quot;</span>    name=<span class="stringliteral">&quot;Docs\API\MyAPI.htm&quot;</span>/&gt;</div>
<div class="line">        &lt;file category=<span class="stringliteral">&quot;header&quot;</span> name=<span class="stringliteral">&quot;API\Include\MyAPI.h&quot;</span>/&gt;</div>
<div class="line">      &lt;/files&gt;</div>
<div class="line">    &lt;/api&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new version number: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.4&quot;</span>&gt;</div>
<div class="line">  MyAPI added</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
Finally, save the PDSC file and regenerate the Pack file using the <b>gen_pack.bat</b> script. See <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">Generate a Pack</a> for further details. Afterwards, install the Pack in your development tool and observe the differences to version 1.0.3. Most likely, you will see none as APIs are not selectable and are thus not displayed in the tools.  </li>
</ol>
<h1><a class="anchor" id="CP_CodeTemplates"></a>
User Code Templates</h1>
<p>User code templates provide a quick start for implementing an application. To add user code templates, add source files with the attribute <code>attr=“template”</code> in a <code>&lt;component&gt;</code>. The <code>select</code> attribute is used to identify template files. Template files with the same <code>select</code> attribute will be added to a project simultaneously.</p>
<p>Copy the <em>content</em> of the <b>06_User_Code_Templates</b> directory of the <b>Pack_with_Software_Components.zip</b> file to the <b>Files</b> directory in your working environment: </p>
<ol>
<li>
Add the following code to both variants (<code>debug</code> and <code>release</code>) of the ::MyVariant:MyGroup component: <div class="fragment"><div class="line">&lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Templates\mylib_template1.c&quot;</span> attr=<span class="stringliteral">&quot;template&quot;</span> select=<span class="stringliteral">&quot;Easy Template&quot;</span>/&gt;</div>
<div class="line">&lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Templates\mylib_template2.c&quot;</span> attr=<span class="stringliteral">&quot;template&quot;</span> select=<span class="stringliteral">&quot;Complex Template&quot;</span>/&gt;</div>
<div class="line">&lt;file category=<span class="stringliteral">&quot;source&quot;</span> name=<span class="stringliteral">&quot;MySWComp\Templates\mylib_template3.c&quot;</span> attr=<span class="stringliteral">&quot;template&quot;</span> select=<span class="stringliteral">&quot;Complex Template&quot;</span>/&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new version number: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.5&quot;</span>&gt;</div>
<div class="line">  User Code Templates added</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
<p class="startli">Finally, save the PDSC file and regenerate the Pack file using the <b>gen_pack.bat</b> script. See <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">Generate a Pack</a> for further details. Afterwards, install the Pack in your development tool and observe the differences to version 1.0.4.</p>
<div class="image">
<img src="UserCodeTemplatesDisplay.png" alt="UserCodeTemplatesDisplay.png"/>
<div class="caption">
Display of user code templates in development tools</div></div>
 </li>
</ol>
<h1><a class="anchor" id="CP_Examples"></a>
Example Projects</h1>
<p>Example projects help to understand a certain MCU or development board better. An example and each individual file of it may refer to a condition that must resolve to true; if it is false the example or file will be ignored. The board element is used to reference to one or more board descriptions using the board vendor and name an example is targeted for. Each example can specify attributes listing related components using <code>Cclass</code>, <code>Cgroup</code>, <code>Csub</code> and <code>Cversion</code>. The <b>&lt;project&gt;</b> element contains the names of the supported development tools and the project files to be loaded.</p>
<p>Copy the <em>content</em> of the <b>07_Example_Projects</b> directory of the <b>Pack_with_Software_Components.zip</b> file to the <b>Files</b> directory in your working environment: </p>
<ol>
<li>
Add the following code to both variants (<code>debug</code> and <code>release</code>) of the ::MyVariant:MyGroup component: <div class="fragment"><div class="line">&lt;example name=<span class="stringliteral">&quot;MyDevBoard Example&quot;</span> doc=<span class="stringliteral">&quot;Abstract.txt&quot;</span> folder=<span class="stringliteral">&quot;MyPackExample&quot;</span>&gt;</div>
<div class="line">  &lt;description&gt;CMSIS-RTOS based example&lt;/description&gt;</div>
<div class="line">  &lt;board name=<span class="stringliteral">&quot;MyDevBoard&quot;</span> vendor=<span class="stringliteral">&quot;MyVendor&quot;</span>/&gt;</div>
<div class="line">  &lt;project&gt;</div>
<div class="line">    &lt;environment name=<span class="stringliteral">&quot;uv&quot;</span> load=<span class="stringliteral">&quot;MyPackExample.uvprojx&quot;</span>/&gt;</div>
<div class="line">  &lt;/project&gt;</div>
<div class="line">  &lt;attributes&gt;</div>
<div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;CMSIS&quot;</span>     Cgroup=<span class="stringliteral">&quot;CORE&quot;</span>/&gt;</div>
<div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;Device&quot;</span>    Cgroup=<span class="stringliteral">&quot;Startup&quot;</span>/&gt;</div>
<div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;CMSIS&quot;</span>     Cgroup=<span class="stringliteral">&quot;RTOS&quot;</span>/&gt;</div>
<div class="line">    &lt;component Cclass=<span class="stringliteral">&quot;MyVariant&quot;</span> Cgroup=<span class="stringliteral">&quot;MyGroup&quot;</span>/&gt;</div>
<div class="line">  &lt;/attributes&gt;</div>
<div class="line">&lt;/example&gt;</div>
</div><!-- fragment -->  </li>
<li>
Add a new version number: <div class="fragment"><div class="line">&lt;release version=<span class="stringliteral">&quot;1.0.6&quot;</span>&gt;</div>
<div class="line">  Example project added</div>
<div class="line">&lt;/release&gt;</div>
</div><!-- fragment -->  </li>
<li>
<p class="startli">Finally, save the PDSC file and regenerate the Pack file using the <b>gen_pack.bat</b> script. See <a class="el" href="_c_p__s_w_components.html#CP_GeneratePack">Generate a Pack</a> for further details. Afterwards, install the Pack in your development tool and observe the differences to version 1.0.5.</p>
<div class="image">
<img src="ExampleDisplay.png" alt="ExampleDisplay.png"/>
<div class="caption">
Display of example projects in development tools</div></div>
 </li>
</ol>
<p>Example projects rely on a development board to be present (as the code needs to run on real hardware). In the code above you will notice that a development board called "MyDevBoard" was specified. <a class="el" href="_create_pack_board.html">Pack with Board Support</a> explains how to integrate boards into a Pack. </p>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Tue Sep 9 2014 08:18:10 for CMSIS-Pack by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> ******* 
	-->
	</li>
  </ul>
</div>
</body>
</html>
