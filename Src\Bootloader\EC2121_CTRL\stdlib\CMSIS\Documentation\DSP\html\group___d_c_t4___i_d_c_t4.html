<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>DCT Type IV Functions</title>
<title>CMSIS-DSP: DCT Type IV Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___d_c_t4___i_d_c_t4.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">DCT Type IV Functions</div>  </div>
<div class="ingroups"><a class="el" href="group__group_transforms.html">Transform Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gafd538d68886848bc090ec2b0d364cc81"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gafd538d68886848bc090ec2b0d364cc81">arm_dct4_f32</a> (const <a class="el" href="structarm__dct4__instance__f32.html">arm_dct4_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pState, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pInlineBuffer)</td></tr>
<tr class="memdesc:gafd538d68886848bc090ec2b0d364cc81"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point DCT4/IDCT4.  <a href="#gafd538d68886848bc090ec2b0d364cc81"></a><br/></td></tr>
<tr class="separator:gafd538d68886848bc090ec2b0d364cc81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab094ad3bc6fa1b84e8b12a24e1850a06"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32</a> (<a class="el" href="structarm__dct4__instance__f32.html">arm_dct4_instance_f32</a> *S, <a class="el" href="structarm__rfft__instance__f32.html">arm_rfft_instance_f32</a> *S_RFFT, <a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *S_CFFT, uint16_t N, uint16_t Nby2, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> normalize)</td></tr>
<tr class="memdesc:gab094ad3bc6fa1b84e8b12a24e1850a06"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point DCT4/IDCT4.  <a href="#gab094ad3bc6fa1b84e8b12a24e1850a06"></a><br/></td></tr>
<tr class="separator:gab094ad3bc6fa1b84e8b12a24e1850a06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga966fd1b66a80873964533703ab5dc054"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15</a> (<a class="el" href="structarm__dct4__instance__q15.html">arm_dct4_instance_q15</a> *S, <a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a> *S_RFFT, <a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a> *S_CFFT, uint16_t N, uint16_t Nby2, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> normalize)</td></tr>
<tr class="memdesc:ga966fd1b66a80873964533703ab5dc054"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 DCT4/IDCT4.  <a href="#ga966fd1b66a80873964533703ab5dc054"></a><br/></td></tr>
<tr class="separator:ga966fd1b66a80873964533703ab5dc054"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga631bb59c7c97c814ff7147ecba6a716a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31</a> (<a class="el" href="structarm__dct4__instance__q31.html">arm_dct4_instance_q31</a> *S, <a class="el" href="structarm__rfft__instance__q31.html">arm_rfft_instance_q31</a> *S_RFFT, <a class="el" href="structarm__cfft__radix4__instance__q31.html">arm_cfft_radix4_instance_q31</a> *S_CFFT, uint16_t N, uint16_t Nby2, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> normalize)</td></tr>
<tr class="memdesc:ga631bb59c7c97c814ff7147ecba6a716a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 DCT4/IDCT4.  <a href="#ga631bb59c7c97c814ff7147ecba6a716a"></a><br/></td></tr>
<tr class="separator:ga631bb59c7c97c814ff7147ecba6a716a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga114cb9635059f678df291fcc887aaf2b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga114cb9635059f678df291fcc887aaf2b">arm_dct4_q15</a> (const <a class="el" href="structarm__dct4__instance__q15.html">arm_dct4_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pInlineBuffer)</td></tr>
<tr class="memdesc:ga114cb9635059f678df291fcc887aaf2b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q15 DCT4/IDCT4.  <a href="#ga114cb9635059f678df291fcc887aaf2b"></a><br/></td></tr>
<tr class="separator:ga114cb9635059f678df291fcc887aaf2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad04d0baab6ed081d8e8afe02538eb80b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gad04d0baab6ed081d8e8afe02538eb80b">arm_dct4_q31</a> (const <a class="el" href="structarm__dct4__instance__q31.html">arm_dct4_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pState, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pInlineBuffer)</td></tr>
<tr class="memdesc:gad04d0baab6ed081d8e8afe02538eb80b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q31 DCT4/IDCT4.  <a href="#gad04d0baab6ed081d8e8afe02538eb80b"></a><br/></td></tr>
<tr class="separator:gad04d0baab6ed081d8e8afe02538eb80b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:gad00f29d896d64d6da7afbbb9d3e182a4"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gad00f29d896d64d6da7afbbb9d3e182a4">Weights_128</a> [256]</td></tr>
<tr class="separator:gad00f29d896d64d6da7afbbb9d3e182a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeb67b0be5b3c2139d660e02cedeed908"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaeb67b0be5b3c2139d660e02cedeed908">Weights_512</a> [1024]</td></tr>
<tr class="separator:gaeb67b0be5b3c2139d660e02cedeed908"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3a2a00b3106dfcb5e0a582f50c65692"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gac3a2a00b3106dfcb5e0a582f50c65692">Weights_2048</a> [4096]</td></tr>
<tr class="separator:gac3a2a00b3106dfcb5e0a582f50c65692"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga45a8ec91e5da91790566105bc7e6f0c2"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga45a8ec91e5da91790566105bc7e6f0c2">Weights_8192</a> [16384]</td></tr>
<tr class="separator:ga45a8ec91e5da91790566105bc7e6f0c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16248ed86161ef97538011b49f13e8b7"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga16248ed86161ef97538011b49f13e8b7">cos_factors_128</a> [128]</td></tr>
<tr class="separator:ga16248ed86161ef97538011b49f13e8b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49fd288352ca5bb43f5cec52273b0d80"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga49fd288352ca5bb43f5cec52273b0d80">cos_factors_512</a> [512]</td></tr>
<tr class="separator:ga49fd288352ca5bb43f5cec52273b0d80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ba5306e0bc44730b40ab34cced45fd6"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga1ba5306e0bc44730b40ab34cced45fd6">cos_factors_2048</a> [2048]</td></tr>
<tr class="separator:ga1ba5306e0bc44730b40ab34cced45fd6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac12484542bc6aaecc754c855457411de"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gac12484542bc6aaecc754c855457411de">cos_factors_8192</a> [8192]</td></tr>
<tr class="separator:gac12484542bc6aaecc754c855457411de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4ff5e6f062efb1d1ec8c6c2207c3727"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaa4ff5e6f062efb1d1ec8c6c2207c3727">WeightsQ15_128</a> [256]</td></tr>
<tr class="separator:gaa4ff5e6f062efb1d1ec8c6c2207c3727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc8ee250fc217d6cb5c84dd7c1eb6d31"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gadc8ee250fc217d6cb5c84dd7c1eb6d31">WeightsQ15_512</a> [1024]</td></tr>
<tr class="separator:gadc8ee250fc217d6cb5c84dd7c1eb6d31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2235ec700d0d6925d9733f48541d46f5"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga2235ec700d0d6925d9733f48541d46f5">WeightsQ15_2048</a> [4096]</td></tr>
<tr class="separator:ga2235ec700d0d6925d9733f48541d46f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4fdc60621eb306984a82ce8b2d645bb7"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga4fdc60621eb306984a82ce8b2d645bb7">WeightsQ15_8192</a> [16384]</td></tr>
<tr class="separator:ga4fdc60621eb306984a82ce8b2d645bb7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1477edd21c7b08b0b59a564f6c24d6c5"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga1477edd21c7b08b0b59a564f6c24d6c5">cos_factorsQ15_128</a> [128]</td></tr>
<tr class="separator:ga1477edd21c7b08b0b59a564f6c24d6c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac056c3d026058eab3ba650828ff5642f"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gac056c3d026058eab3ba650828ff5642f">cos_factorsQ15_512</a> [512]</td></tr>
<tr class="separator:gac056c3d026058eab3ba650828ff5642f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeee5df7c1be2374441868ecbbc6c7e5d"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaeee5df7c1be2374441868ecbbc6c7e5d">cos_factorsQ15_2048</a> [2048]</td></tr>
<tr class="separator:gaeee5df7c1be2374441868ecbbc6c7e5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga988ff0563cc9df7848c9348871ac6c07"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga988ff0563cc9df7848c9348871ac6c07">cos_factorsQ15_8192</a> [8192]</td></tr>
<tr class="separator:ga988ff0563cc9df7848c9348871ac6c07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga02d7024538a87214296b01d83ba36b02"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga02d7024538a87214296b01d83ba36b02">WeightsQ31_128</a> [256]</td></tr>
<tr class="separator:ga02d7024538a87214296b01d83ba36b02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31a8217a96f7d3171921e98398f31596"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga31a8217a96f7d3171921e98398f31596">WeightsQ31_512</a> [1024]</td></tr>
<tr class="separator:ga31a8217a96f7d3171921e98398f31596"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga725b65c25a02b3cad329e18bb832f65e"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga725b65c25a02b3cad329e18bb832f65e">WeightsQ31_2048</a> [4096]</td></tr>
<tr class="separator:ga725b65c25a02b3cad329e18bb832f65e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16bf6bbe5c4c9b35f88253cf7bdcc435"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga16bf6bbe5c4c9b35f88253cf7bdcc435">WeightsQ31_8192</a> [16384]</td></tr>
<tr class="separator:ga16bf6bbe5c4c9b35f88253cf7bdcc435"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabb8ee2004a3520fd08388db637d43875"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gabb8ee2004a3520fd08388db637d43875">cos_factorsQ31_128</a> [128]</td></tr>
<tr class="separator:gabb8ee2004a3520fd08388db637d43875"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3559569e603cb918911074be88523d0e"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#ga3559569e603cb918911074be88523d0e">cos_factorsQ31_512</a> [512]</td></tr>
<tr class="separator:ga3559569e603cb918911074be88523d0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa15fc3fb058482defda371113cd12e74"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaa15fc3fb058482defda371113cd12e74">cos_factorsQ31_2048</a> [2048]</td></tr>
<tr class="separator:gaa15fc3fb058482defda371113cd12e74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf687c4bbdbc700a3ad5d807d28de63e4"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___d_c_t4___i_d_c_t4.html#gaf687c4bbdbc700a3ad5d807d28de63e4">cos_factorsQ31_8192</a> [8192]</td></tr>
<tr class="separator:gaf687c4bbdbc700a3ad5d807d28de63e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Representation of signals by minimum number of values is important for storage and transmission. The possibility of large discontinuity between the beginning and end of a period of a signal in DFT can be avoided by extending the signal so that it is even-symmetric. Discrete Cosine Transform (DCT) is constructed such that its energy is heavily concentrated in the lower part of the spectrum and is very widely used in signal and image coding applications. The family of DCTs (DCT type- 1,2,3,4) is the outcome of different combinations of homogeneous boundary conditions. DCT has an excellent energy-packing capability, hence has many applications and in data compression in particular.</p>
<p>DCT is essentially the Discrete Fourier Transform(DFT) of an even-extended real signal. Reordering of the input data makes the computation of DCT just a problem of computing the DFT of a real signal with a few additional operations. This approach provides regular, simple, and very efficient DCT algorithms for practical hardware and software implementations.</p>
<p>DCT type-II can be implemented using Fast fourier transform (FFT) internally, as the transform is applied on real values, Real FFT can be used. DCT4 is implemented using DCT2 as their implementations are similar except with some added pre-processing and post-processing. DCT2 implementation can be described in the following steps:</p>
<ul>
<li>Re-ordering input</li>
<li>Calculating Real FFT</li>
<li>Multiplication of weights and Real FFT output and getting real part from the product.</li>
</ul>
<p>This process is explained by the block diagram below: </p>
<div class="image">
<img src="DCT4.gif" alt="DCT4.gif"/>
<div class="caption">
Discrete Cosine Transform - type-IV</div></div>
<dl class="section user"><dt>Algorithm: </dt><dd>The N-point type-IV DCT is defined as a real, linear transformation by the formula: <div class="image">
<img src="DCT4Equation.gif" alt="DCT4Equation.gif"/>
</div>
 where <code>k = 0,1,2,.....N-1</code> </dd></dl>
<dl class="section user"><dt></dt><dd>Its inverse is defined as follows: <div class="image">
<img src="IDCT4Equation.gif" alt="IDCT4Equation.gif"/>
</div>
 where <code>n = 0,1,2,.....N-1</code> </dd></dl>
<dl class="section user"><dt></dt><dd>The DCT4 matrices become involutory (i.e. they are self-inverse) by multiplying with an overall scale factor of sqrt(2/N). The symmetry of the transform matrix indicates that the fast algorithms for the forward and inverse transform computation are identical. Note that the implementation of Inverse DCT4 and DCT4 is same, hence same process function can be used for both.</dd></dl>
<dl class="section user"><dt>Lengths supported by the transform: </dt><dd>As DCT4 internally uses Real FFT, it supports all the lengths supported by <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc" title="Processing function for the floating-point RFFT/RIFFT.">arm_rfft_f32()</a>. The library provides separate functions for Q15, Q31, and floating-point data types. </dd></dl>
<dl class="section user"><dt>Instance Structure </dt><dd>The instances for Real FFT and FFT, cosine values table and twiddle factor table are stored in an instance data structure. A separate instance structure must be defined for each transform. There are separate instance structure declarations for each of the 3 supported data types.</dd></dl>
<dl class="section user"><dt>Initialization Functions </dt><dd>There is also an associated initialization function for each data type. The initialization function performs the following operations:<ul>
<li>Sets the values of the internal structure fields.</li>
<li>Initializes Real FFT as its process function is used internally in DCT4, by calling <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068" title="Initialization function for the floating-point RFFT/RIFFT.">arm_rfft_init_f32()</a>. </li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Use of the initialization function is optional. However, if the initialization function is used, then the instance structure cannot be placed into a const data section. To place an instance structure into a const data section, the instance structure must be manually initialized. Manually initialize the instance structure as follows: <pre>    
*arm_dct4_instance_f32 S = {N, Nby2, normalize, pTwiddle, pCosFactor, pRfft, pCfft};    
*arm_dct4_instance_q31 S = {N, Nby2, normalize, pTwiddle, pCosFactor, pRfft, pCfft};   
*arm_dct4_instance_q15 S = {N, Nby2, normalize, pTwiddle, pCosFactor, pRfft, pCfft};   
  </pre> where <code>N</code> is the length of the DCT4; <code>Nby2</code> is half of the length of the DCT4; <code>normalize</code> is normalizing factor used and is equal to <code>sqrt(2/N)</code>; <code>pTwiddle</code> points to the twiddle factor table; <code>pCosFactor</code> points to the cosFactor table; <code>pRfft</code> points to the real FFT instance; <code>pCfft</code> points to the complex FFT instance; The CFFT and RFFT structures also needs to be initialized, refer to <a class="el" href="group___complex_f_f_t.html#ga521f670cd9c571bc61aff9bec89f4c26" title="Processing function for the floating-point Radix-4 CFFT/CIFFT.">arm_cfft_radix4_f32()</a> and <a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc" title="Processing function for the floating-point RFFT/RIFFT.">arm_rfft_f32()</a> respectively for details regarding static initialization.</dd></dl>
<dl class="section user"><dt>Fixed-Point Behavior </dt><dd>Care must be taken when using the fixed-point versions of the DCT4 transform functions. In particular, the overflow and saturation behavior of the accumulator used in each function must be considered. Refer to the function specific documentation below for usage guidelines. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gafd538d68886848bc090ec2b0d364cc81"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_dct4_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__dct4__instance__f32.html">arm_dct4_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pInlineBuffer</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point DCT4/IDCT4 structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to state buffer. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pInlineBuffer</td><td>points to the in-place input and output buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32">arm_mult_f32()</a>, <a class="el" href="group__scale.html#ga3487af88b112f682ee90589cd419e123">arm_scale_f32()</a>, <a class="el" href="structarm__dct4__instance__f32.html#a262b29a51c371b46efc89120e31ccf37">arm_dct4_instance_f32::N</a>, <a class="el" href="structarm__dct4__instance__f32.html#adb1ef2739ddbe62e5cdadc47455a4147">arm_dct4_instance_f32::Nby2</a>, <a class="el" href="structarm__dct4__instance__f32.html#a6da1187e070801e011ce5e0582efa861">arm_dct4_instance_f32::pCosFactor</a>, and <a class="el" href="structarm__dct4__instance__f32.html#ad13544aafad268588c62e3eb35ae662c">arm_dct4_instance_f32::pTwiddle</a>.</p>

</div>
</div>
<a class="anchor" id="gab094ad3bc6fa1b84e8b12a24e1850a06"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_dct4_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__dct4__instance__f32.html">arm_dct4_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__rfft__instance__f32.html">arm_rfft_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S_RFFT</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S_CFFT</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>N</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>Nby2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td>
          <td class="paramname"><em>normalize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of floating-point DCT4/IDCT4 structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*S_RFFT</td><td>points to an instance of floating-point RFFT/RIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*S_CFFT</td><td>points to an instance of floating-point CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">N</td><td>length of the DCT4. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Nby2</td><td>half of the length of the DCT4. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">normalize</td><td>normalizing factor. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>arm_status function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLenReal</code> is not a supported transform length. </dd></dl>
<dl class="section user"><dt>Normalizing factor: </dt><dd>The normalizing factor is <code>sqrt(2/N)</code>, which depends on the size of transform <code>N</code>. Floating-point normalizing factors are mentioned in the table below for different DCT sizes: <div class="image">
<img src="dct4NormalizingF32Table.gif" alt="dct4NormalizingF32Table.gif"/>
</div>
 </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga16248ed86161ef97538011b49f13e8b7">cos_factors_128</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga1ba5306e0bc44730b40ab34cced45fd6">cos_factors_2048</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga49fd288352ca5bb43f5cec52273b0d80">cos_factors_512</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gac12484542bc6aaecc754c855457411de">cos_factors_8192</a>, <a class="el" href="structarm__dct4__instance__f32.html#a262b29a51c371b46efc89120e31ccf37">arm_dct4_instance_f32::N</a>, <a class="el" href="structarm__dct4__instance__f32.html#adb1ef2739ddbe62e5cdadc47455a4147">arm_dct4_instance_f32::Nby2</a>, <a class="el" href="structarm__dct4__instance__f32.html#a61ce8c967b2e998a9c0041cca73cdef8">arm_dct4_instance_f32::normalize</a>, <a class="el" href="structarm__dct4__instance__f32.html#a018f7860b6e070af533fb7d76c7cdc32">arm_dct4_instance_f32::pCfft</a>, <a class="el" href="structarm__dct4__instance__f32.html#a6da1187e070801e011ce5e0582efa861">arm_dct4_instance_f32::pCosFactor</a>, <a class="el" href="structarm__dct4__instance__f32.html#a978f37fc19add31af243ab5c63ae502f">arm_dct4_instance_f32::pRfft</a>, <a class="el" href="structarm__dct4__instance__f32.html#ad13544aafad268588c62e3eb35ae662c">arm_dct4_instance_f32::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gad00f29d896d64d6da7afbbb9d3e182a4">Weights_128</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gac3a2a00b3106dfcb5e0a582f50c65692">Weights_2048</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaeb67b0be5b3c2139d660e02cedeed908">Weights_512</a>, and <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga45a8ec91e5da91790566105bc7e6f0c2">Weights_8192</a>.</p>

</div>
</div>
<a class="anchor" id="ga966fd1b66a80873964533703ab5dc054"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_dct4_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__dct4__instance__q15.html">arm_dct4_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S_RFFT</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S_CFFT</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>N</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>Nby2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td>
          <td class="paramname"><em>normalize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of Q15 DCT4/IDCT4 structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*S_RFFT</td><td>points to an instance of Q15 RFFT/RIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*S_CFFT</td><td>points to an instance of Q15 CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">N</td><td>length of the DCT4. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Nby2</td><td>half of the length of the DCT4. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">normalize</td><td>normalizing factor. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>arm_status function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>N</code> is not a supported transform length. </dd></dl>
<dl class="section user"><dt>Normalizing factor: </dt><dd>The normalizing factor is <code>sqrt(2/N)</code>, which depends on the size of transform <code>N</code>. Normalizing factors in 1.15 format are mentioned in the table below for different DCT sizes: <div class="image">
<img src="dct4NormalizingQ15Table.gif" alt="dct4NormalizingQ15Table.gif"/>
</div>
 </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga1477edd21c7b08b0b59a564f6c24d6c5">cos_factorsQ15_128</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaeee5df7c1be2374441868ecbbc6c7e5d">cos_factorsQ15_2048</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gac056c3d026058eab3ba650828ff5642f">cos_factorsQ15_512</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga988ff0563cc9df7848c9348871ac6c07">cos_factorsQ15_8192</a>, <a class="el" href="structarm__dct4__instance__q15.html#a53d24009bb9b2e93d0aa07db7f1a6c25">arm_dct4_instance_q15::N</a>, <a class="el" href="structarm__dct4__instance__q15.html#af43dcbbc2fc661ffbc525afe3dcbd7da">arm_dct4_instance_q15::Nby2</a>, <a class="el" href="structarm__dct4__instance__q15.html#a197098140d68e89a08f7a249003a0b86">arm_dct4_instance_q15::normalize</a>, <a class="el" href="structarm__dct4__instance__q15.html#a7284932ee8c36107c33815eb62eadffc">arm_dct4_instance_q15::pCfft</a>, <a class="el" href="structarm__dct4__instance__q15.html#ac76df681b1bd502fb4874c06f055dded">arm_dct4_instance_q15::pCosFactor</a>, <a class="el" href="structarm__dct4__instance__q15.html#a11cf95c1cd9dd2dd5e4b81b8f88dc208">arm_dct4_instance_q15::pRfft</a>, <a class="el" href="structarm__dct4__instance__q15.html#abc6c847e9f906781e1d5da40e9aafa76">arm_dct4_instance_q15::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaa4ff5e6f062efb1d1ec8c6c2207c3727">WeightsQ15_128</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga2235ec700d0d6925d9733f48541d46f5">WeightsQ15_2048</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gadc8ee250fc217d6cb5c84dd7c1eb6d31">WeightsQ15_512</a>, and <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga4fdc60621eb306984a82ce8b2d645bb7">WeightsQ15_8192</a>.</p>

</div>
</div>
<a class="anchor" id="ga631bb59c7c97c814ff7147ecba6a716a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_dct4_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__dct4__instance__q31.html">arm_dct4_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__rfft__instance__q31.html">arm_rfft_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S_RFFT</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix4__instance__q31.html">arm_cfft_radix4_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S_CFFT</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>N</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>Nby2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>normalize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of Q31 DCT4/IDCT4 structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*S_RFFT</td><td>points to an instance of Q31 RFFT/RIFFT structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*S_CFFT</td><td>points to an instance of Q31 CFFT/CIFFT structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">N</td><td>length of the DCT4. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Nby2</td><td>half of the length of the DCT4. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">normalize</td><td>normalizing factor. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>arm_status function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>N</code> is not a supported transform length. </dd></dl>
<dl class="section user"><dt>Normalizing factor: </dt><dd>The normalizing factor is <code>sqrt(2/N)</code>, which depends on the size of transform <code>N</code>. Normalizing factors in 1.31 format are mentioned in the table below for different DCT sizes: <div class="image">
<img src="dct4NormalizingQ31Table.gif" alt="dct4NormalizingQ31Table.gif"/>
</div>
 </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gabb8ee2004a3520fd08388db637d43875">cos_factorsQ31_128</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaa15fc3fb058482defda371113cd12e74">cos_factorsQ31_2048</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga3559569e603cb918911074be88523d0e">cos_factorsQ31_512</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gaf687c4bbdbc700a3ad5d807d28de63e4">cos_factorsQ31_8192</a>, <a class="el" href="structarm__dct4__instance__q31.html#a46a9f136457350676e2bfd3768ff9d6d">arm_dct4_instance_q31::N</a>, <a class="el" href="structarm__dct4__instance__q31.html#a32d3268ba4629908dba056599f0a904d">arm_dct4_instance_q31::Nby2</a>, <a class="el" href="structarm__dct4__instance__q31.html#ac80ff7b28fca36aeef74dea12e8312dd">arm_dct4_instance_q31::normalize</a>, <a class="el" href="structarm__dct4__instance__q31.html#ac96579cfb28d08bb11dd2fe4c6303833">arm_dct4_instance_q31::pCfft</a>, <a class="el" href="structarm__dct4__instance__q31.html#af97204d1838925621fc82021a0c2d6c1">arm_dct4_instance_q31::pCosFactor</a>, <a class="el" href="structarm__dct4__instance__q31.html#af1487dab5e7963b85dc0fdc6bf492542">arm_dct4_instance_q31::pRfft</a>, <a class="el" href="structarm__dct4__instance__q31.html#a7db236e22673146bb1d2c962f0713f08">arm_dct4_instance_q31::pTwiddle</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga02d7024538a87214296b01d83ba36b02">WeightsQ31_128</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga725b65c25a02b3cad329e18bb832f65e">WeightsQ31_2048</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga31a8217a96f7d3171921e98398f31596">WeightsQ31_512</a>, and <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga16bf6bbe5c4c9b35f88253cf7bdcc435">WeightsQ31_8192</a>.</p>

</div>
</div>
<a class="anchor" id="ga114cb9635059f678df291fcc887aaf2b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_dct4_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__dct4__instance__q15.html">arm_dct4_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pInlineBuffer</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 DCT4 structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to state buffer. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pInlineBuffer</td><td>points to the in-place input and output buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Input an output formats: </dt><dd>Internally inputs are downscaled in the RFFT process function to avoid overflows. Number of bits downscaled, depends on the size of the transform. The input and output formats for different DCT sizes and number of bits to upscale are mentioned in the table below:</dd></dl>
<div class="image">
<img src="dct4FormatsQ15Table.gif" alt="dct4FormatsQ15Table.gif"/>
</div>
 
<p>References <a class="el" href="group___basic_mult.html#gafb0778d27ed98a2a6f2ecb7d48cc8c75">arm_mult_q15()</a>, <a class="el" href="group__shift.html#gaa1757e53279780107acc92cf100adb61">arm_shift_q15()</a>, <a class="el" href="structarm__dct4__instance__q15.html#a53d24009bb9b2e93d0aa07db7f1a6c25">arm_dct4_instance_q15::N</a>, <a class="el" href="structarm__dct4__instance__q15.html#af43dcbbc2fc661ffbc525afe3dcbd7da">arm_dct4_instance_q15::Nby2</a>, <a class="el" href="structarm__dct4__instance__q15.html#ac76df681b1bd502fb4874c06f055dded">arm_dct4_instance_q15::pCosFactor</a>, and <a class="el" href="structarm__dct4__instance__q15.html#abc6c847e9f906781e1d5da40e9aafa76">arm_dct4_instance_q15::pTwiddle</a>.</p>

</div>
</div>
<a class="anchor" id="gad04d0baab6ed081d8e8afe02538eb80b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_dct4_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__dct4__instance__q31.html">arm_dct4_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pInlineBuffer</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 DCT4 structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to state buffer. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*pInlineBuffer</td><td>points to the in-place input and output buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl class="section user"><dt>Input an output formats: </dt><dd>Input samples need to be downscaled by 1 bit to avoid saturations in the Q31 DCT process, as the conversion from DCT2 to DCT4 involves one subtraction. Internally inputs are downscaled in the RFFT process function to avoid overflows. Number of bits downscaled, depends on the size of the transform. The input and output formats for different DCT sizes and number of bits to upscale are mentioned in the table below:</dd></dl>
<div class="image">
<img src="dct4FormatsQ31Table.gif" alt="dct4FormatsQ31Table.gif"/>
</div>
 
<p>References <a class="el" href="group___cmplx_by_cmplx_mult.html#ga1829e50993a90742de225a0ce4213838">arm_cmplx_mult_cmplx_q31()</a>, <a class="el" href="group___basic_mult.html#ga3528c0f54a0607acc603f0490d3ca6c6">arm_mult_q31()</a>, <a class="el" href="group___real_f_f_t.html#gabaeab5646aeea9844e6d42ca8c73fe3a">arm_rfft_q31()</a>, <a class="el" href="group__shift.html#ga387dd8b7b87377378280978f16cdb13d">arm_shift_q31()</a>, <a class="el" href="structarm__dct4__instance__q31.html#a46a9f136457350676e2bfd3768ff9d6d">arm_dct4_instance_q31::N</a>, <a class="el" href="structarm__dct4__instance__q31.html#a32d3268ba4629908dba056599f0a904d">arm_dct4_instance_q31::Nby2</a>, <a class="el" href="structarm__dct4__instance__q31.html#ac80ff7b28fca36aeef74dea12e8312dd">arm_dct4_instance_q31::normalize</a>, <a class="el" href="structarm__dct4__instance__q31.html#af97204d1838925621fc82021a0c2d6c1">arm_dct4_instance_q31::pCosFactor</a>, <a class="el" href="structarm__dct4__instance__q31.html#af1487dab5e7963b85dc0fdc6bf492542">arm_dct4_instance_q31::pRfft</a>, and <a class="el" href="structarm__dct4__instance__q31.html#a7db236e22673146bb1d2c962f0713f08">arm_dct4_instance_q31::pTwiddle</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="ga16248ed86161ef97538011b49f13e8b7"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> cos_factors_128[128]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>cosFactor tables are generated using the formula : <pre>cos_factors[n] = 2 * cos((2n+1)*pi/(4*N))</pre> </dd></dl>
<dl class="section user"><dt></dt><dd>C command to generate the table </dd></dl>
<dl class="section user"><dt></dt><dd><pre> for(i = 0; i&lt; N; i++)    
{    
   cos_factors[i]= 2 * cos((2*i+1)*c/2);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where <code>N</code> is the number of factors to generate and <code>c</code> is <code>pi/(2*N)</code> </dd></dl>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga1ba5306e0bc44730b40ab34cced45fd6"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> cos_factors_2048[2048]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga49fd288352ca5bb43f5cec52273b0d80"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> cos_factors_512[512]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gac12484542bc6aaecc754c855457411de"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> cos_factors_8192[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga1477edd21c7b08b0b59a564f6c24d6c5"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> cos_factorsQ15_128[128]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>cosFactor tables are generated using the formula : <pre> cos_factors[n] = 2 * cos((2n+1)*pi/(4*N)) </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>C command to generate the table <pre>    
for(i = 0; i&lt; N; i++)    
{    
  cos_factors[i]= 2 * cos((2*i+1)*c/2);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where <code>N</code> is the number of factors to generate and <code>c</code> is <code>pi/(2*N)</code> </dd></dl>
<dl class="section user"><dt></dt><dd>Then converted to q15 format by multiplying with 2^31 and saturated if required. </dd></dl>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="gaeee5df7c1be2374441868ecbbc6c7e5d"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> cos_factorsQ15_2048[2048]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="gac056c3d026058eab3ba650828ff5642f"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> cos_factorsQ15_512[512]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ga988ff0563cc9df7848c9348871ac6c07"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> cos_factorsQ15_8192[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="gabb8ee2004a3520fd08388db637d43875"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> cos_factorsQ31_128[128]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>cosFactor tables are generated using the formula : <pre>cos_factors[n] = 2 * cos((2n+1)*pi/(4*N))</pre> </dd></dl>
<dl class="section user"><dt></dt><dd>C command to generate the table <pre>    
for(i = 0; i&lt; N; i++)    
{    
  cos_factors[i]= 2 * cos((2*i+1)*c/2);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where <code>N</code> is the number of factors to generate and <code>c</code> is <code>pi/(2*N)</code> </dd></dl>
<dl class="section user"><dt></dt><dd>Then converted to q31 format by multiplying with 2^31 and saturated if required. </dd></dl>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="gaa15fc3fb058482defda371113cd12e74"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> cos_factorsQ31_2048[2048]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ga3559569e603cb918911074be88523d0e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> cos_factorsQ31_512[512]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="gaf687c4bbdbc700a3ad5d807d28de63e4"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> cos_factorsQ31_8192[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="gad00f29d896d64d6da7afbbb9d3e182a4"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> Weights_128[256]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Weights tables are generated using the formula : <pre>weights[n] = e^(-j*n*pi/(2*N))</pre> </dd></dl>
<dl class="section user"><dt></dt><dd>C command to generate the table <pre>    
for(i = 0; i&lt; N; i++)    
{    
   weights[2*i]= cos(i*c);    
   weights[(2*i)+1]= -sin(i * c);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>Where <code>N</code> is the Number of weights to be calculated and <code>c</code> is <code>pi/(2*N)</code> </dd></dl>
<dl class="section user"><dt></dt><dd>In the tables below the real and imaginary values are placed alternatively, hence the array length is <code>2*N</code>. </dd></dl>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gac3a2a00b3106dfcb5e0a582f50c65692"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> Weights_2048[4096]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gaeb67b0be5b3c2139d660e02cedeed908"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> Weights_512[1024]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga45a8ec91e5da91790566105bc7e6f0c2"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> Weights_8192[16384]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gaa4ff5e6f062efb1d1ec8c6c2207c3727"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> WeightsQ15_128[256]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Weights tables are generated using the formula : <pre>weights[n] = e^(-j*n*pi/(2*N))</pre> </dd></dl>
<dl class="section user"><dt></dt><dd>C command to generate the table <pre>    
for(i = 0; i&lt; N; i++)    
{    
  weights[2*i]= cos(i*c);    
  weights[(2*i)+1]= -sin(i * c);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where <code>N</code> is the Number of weights to be calculated and <code>c</code> is <code>pi/(2*N)</code> </dd></dl>
<dl class="section user"><dt></dt><dd>Converted the output to q15 format by multiplying with 2^31 and saturated if required. </dd></dl>
<dl class="section user"><dt></dt><dd>In the tables below the real and imaginary values are placed alternatively, hence the array length is <code>2*N</code>. </dd></dl>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ga2235ec700d0d6925d9733f48541d46f5"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> WeightsQ15_2048[4096]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="gadc8ee250fc217d6cb5c84dd7c1eb6d31"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> WeightsQ15_512[1024]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ga4fdc60621eb306984a82ce8b2d645bb7"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> WeightsQ15_8192[16384]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ga02d7024538a87214296b01d83ba36b02"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> WeightsQ31_128[256]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Weights tables are generated using the formula : <pre>weights[n] = e^(-j*n*pi/(2*N))</pre> </dd></dl>
<dl class="section user"><dt></dt><dd>C command to generate the table <pre>    
for(i = 0; i&lt; N; i++)    
{    
  weights[2*i]= cos(i*c);    
  weights[(2*i)+1]= -sin(i * c);    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where <code>N</code> is the Number of weights to be calculated and <code>c</code> is <code>pi/(2*N)</code> </dd></dl>
<dl class="section user"><dt></dt><dd>Convert the output to q31 format by multiplying with 2^31 and saturated if required. </dd></dl>
<dl class="section user"><dt></dt><dd>In the tables below the real and imaginary values are placed alternatively, hence the array length is <code>2*N</code>. </dd></dl>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ga725b65c25a02b3cad329e18bb832f65e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> WeightsQ31_2048[4096]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ga31a8217a96f7d3171921e98398f31596"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> WeightsQ31_512[1024]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ga16bf6bbe5c4c9b35f88253cf7bdcc435"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> WeightsQ31_8192[16384]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
