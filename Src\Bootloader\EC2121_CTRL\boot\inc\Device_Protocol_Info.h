#ifndef DEVICE_PROTOCOL_H
#define DEVICE_PROTOCOL_H


#define DEVICE_INFO_SIZE    0x004c
#define PROTOCOL_INFO_SIZE    0x001c
// PP QQ RR SS
static const unsigned int g_DeviceInfo[DEVICE_INFO_SIZE] =
{
    0xffffffff,                // RSVD
    0x7361656A,                // SERIAL_NUMBER
    0x00010101,                // J1850PWM_SUPPORTED
    0x00010101,                // J1850VPW_SUPPORTED
    0x00010D01,                // ISO9141_SUPPORTED
    0x00010D01,                // ISO14230_SUPPORTED
    0x00010D01,                // CAN_SUPPORTED
    0x00010D01,                // ISO15765_SUPPORTED
    0x00000001,                // SCI_A_ENGINE_SUPPORTED
    0x00000001,                // SCI_A_TRANS_SUPPORTED
    0x00000001,                // SCI_B_ENGINE_SUPPORTED
    0x00000001,                // SCI_B_TRANS_SUPPORTED
    0x00010D00,                // SW_ISO15765_SUPPORTED
    0x00010D00,                // SW_CAN_SUPPORTED
    0x00010D00,                // GM_UART_SUPPORTED
    0x00010D00,                // UART_ECHO_BYTE_SUPPORTED
    0x00010D00,                // HONDA_DIAGH_SUPPORTED
    0x00010D00,                // J1939_SUPPORTED
    0x00010D00,                // J1708_SUPPORTED
    0x00010D00,                // TP2_0_SUPPORTED
    0x00010D00,                // J2610_SUPPORTED
    0x00010D00,                // ANALOG_IN_SUPPORTED
    0x00000100,                // MAX_NON_VOLATILE_STORAGE
    0x00004100,                // SHORT_TO_GND_J1962 9 and 15
    0x7FE70000,                // PGM_VOLTAGE_J1962
    0x00020200,                // J1850PWM_PS_J1962
    0x00020000,                // J1850VPW_PS_J1962
    0x7FE70000,                // ISO9141_PS_K_LINE_J1962
    0x00007FE7,                // ISO9141_PS_L_LINE_J1962
    0x7FE70000,                // ISO14230_PS_K_LINE_J1962
    0x00007FE7,                // ISO14230_PS_L_LINE_J1962
    0x7FE77FE7,                // CAN_PS_J1962
    0x7FE77FE7,                // ISO15765_PS_J1962
    0x7FE70000,                // SW_CAN_PS_J1962
    0x7FE70000,                // SW_ISO15765_PS_J1962
    0x7FE70000,                // GM_UART_PS_J1962
    0x7FE70000,                // UART_ECHO_BYTE_PS_J1962
    0x7FE70000,                // HONDA_DIAGH_PS_J1962
    0x7FE77FE7,                // J1939_PS_J1962
    0x7FE77FE7,                // J1708_PS_J1962
    0x7FE77FE7,                // TP2_0_PS_J1962
    0x7FE77FE7,                // J2610_PS_J1962
    0x00080010,                // J1939_PS_J1939
    0x00200040,                // J1708_PS_J1939
    0x00200040,                // ISO9141_PS_K_LINE_J1939
    0x00200040,                // ISO9141_PS_L_LINE_J1939
    0x00200040,                // ISO14230_PS_K_LINE_J1939
    0x00200040,                // ISO14230_PS_L_LINE_J1939
    0x00010002,                // J1708_PS_J1708
    0x00010200,                // FT_CAN_SUPPORTED
    0x00010200,                // FT_ISO15765_SUPPORTED
    0x05000005,                // FT_CAN_PS_J1962
    0x05000005,                // FT_ISO15765_PS_J1962
    0x00010101,                // J1850PWM_SIMULTANEOUS
    0x00010101,                // J1850VPW_SIMULTANEOUS
    0x00010101,                // ISO9141_SIMULTANEOUS
    0x00010101,                // ISO14230_SIMULTANEOUS
    0x00010101,                // CAN_SIMULTANEOUS
    0x00010101,                // ISO15765_SIMULTANEOUS
    0x00010101,                // SCI_A_ENGINE_SIMULTANEOUS
    0x00010101,                // SCI_A_TRANS_SIMULTANEOUS
    0x00010101,                // SCI_B_ENGINE_SIMULTANEOUS
    0x00010101,                // SCI_B_TRANS_SIMULTANEOUS
    0x00010101,                // SW_ISO15765_SIMULTANEOUS
    0x00010101,                // SW_CAN_SIMULTANEOUS
    0x00010101,                // GM_UART_SIMULTANEOUS
    0x00010101,                // UART_ECHO_BYTE_SIMULTANEOUS
    0x00010101,                // HONDA_DIAGH_SIMULTANEOUS
    0x00010101,                // J1939_SIMULTANEOUS
    0x00010101,                // J1708_SIMULTANEOUS
    0x00010101,                // TP2_0_SIMULTANEOUS
    0x00010101,                // J2610_SIMULTANEOUS
    0x00010101,                // ANALOG_IN_SIMULTANEOUS
    0x25340808,                // PART_NUMBER
    0x00010101,                // FT_CAN_SIMULTANEOUS
    0x00010101,                // FT_ISO15765_SIMULTANEOUS
};

static const uint32_t g_ProtocolInfo[PROTOCOL_INFO_SIZE] =
{
    0xffffffff,       // RSVD
    0x00001020,                // MAX_RX_BUFFER_SIZE=1
    0x0000000a,                // MAX_PASS_FILTER
    0x0000000a,                // MAX_BLOCK_FILTER
    0x0000000c,                // MAX_FILTER_MSG_LENGTH
    0x0000000a,                // MAX_PERIODIC_MSGS
    0x0000000c,                // MAX_PERIODIC_MSG_LENGTH
    0x00000000,                // DESIRED_DATA_RATE
    0x0000000a,                // MAX_REPEAT_MESSAGING
    0x0000000c,                // MAX_REPEAT_MESSAGING_LENGTH
    0x00000003,                // NETWORK_LINE_SUPPORTED
    0x00000020,                // MAX_FUNCT_MSG_LOOKUP
    0x00000007,                // PARITY_SUPPORTED
    0x00000001,                // DATA_BITS_SUPPORTED
    0x0000000f,                // FIVE_BAUD_MOD_SUPPORTED
    0x00000000,                // L_LINE_SUPPORTED
    0x00000000,                // CAN_11_29_IDS_SUPPORTED
    0x00000000,                // CAN_MIXED_FORMAT_SUPPORTED
    0x0000000a,                // MAX_FLOW_CONTROL_FILTER
    0x000000ff,                // MAX_ISO15765_WFT_MAX
    0x0000000e,                // MAX_AD_ACTIVE_CHANNELS
    0x000000c8,                // MAX_AD_SAMPLE_RATE
    0x00000400,                // MAX_AD_SAMPLES_PER_READING
    0x0000000a,                // AD_SAMPLE_RESOLUTION
    0x00000000,                // AD_INPUT_RANGE_LOW
    0x000061a8,                // AD_INPUT_RANGE_HIGH 25000mv
    0x00000000,                // RESOURCE_GROUP
    0x000003e8,                // TIMESTAMP_RESOLUTION
};

#endif //DEVICE_PROTOCOL_H

