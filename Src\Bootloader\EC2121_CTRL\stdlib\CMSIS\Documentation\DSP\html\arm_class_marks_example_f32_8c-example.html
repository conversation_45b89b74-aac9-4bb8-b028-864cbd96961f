<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_class_marks_example_f32.c</title>
<title>CMSIS-DSP: arm_class_marks_example_f32.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_class_marks_example_f32_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_class_marks_example_f32.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* $Date:         17. January 2013</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.0</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment">* Title:         arm_class_marks_example_f32.c</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Description:   Example code to calculate Minimum, Maximum</span></div>
<div class="line"><span class="comment">*                Mean, std and variance of marks obtained in a class</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment">* -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#define USE_STATIC_INIT</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"> <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** Global defines</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#define TEST_LENGTH_SAMPLES   (20*4)</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** List of Marks scored by 20 students for 4 subjects</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a0153222efa82b7f1a0ea3835921bf921">testMarks_f32</a>[<a name="a1"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>] =</div>
<div class="line">{</div>
<div class="line">  42.000000,  37.000000,  81.000000,  28.000000,</div>
<div class="line">  83.000000,  72.000000,  36.000000,  38.000000,</div>
<div class="line">  32.000000,  51.000000,  63.000000,  64.000000,</div>
<div class="line">  97.000000,  82.000000,  95.000000,  90.000000,</div>
<div class="line">  66.000000,  51.000000,  54.000000,  42.000000,</div>
<div class="line">  67.000000,  56.000000,  45.000000,  57.000000,</div>
<div class="line">  67.000000,  69.000000,  35.000000,  52.000000,</div>
<div class="line">  29.000000,  81.000000,  58.000000,  47.000000,</div>
<div class="line">  38.000000,  76.000000, 100.000000,  29.000000,</div>
<div class="line">  33.000000,  47.000000,  29.000000,  50.000000,</div>
<div class="line">  34.000000,  41.000000,  61.000000,  46.000000,</div>
<div class="line">  52.000000,  50.000000,  48.000000,  36.000000,</div>
<div class="line">  47.000000,  55.000000,  44.000000,  40.000000,</div>
<div class="line"> 100.000000,  94.000000,  84.000000,  37.000000,</div>
<div class="line">  32.000000,  71.000000,  47.000000,  77.000000,</div>
<div class="line">  31.000000,  50.000000,  49.000000,  35.000000,</div>
<div class="line">  63.000000,  67.000000,  40.000000,  31.000000,</div>
<div class="line">  29.000000,  68.000000,  61.000000,  38.000000,</div>
<div class="line">  31.000000,  28.000000,  28.000000,  76.000000,</div>
<div class="line">  55.000000,  33.000000,  29.000000,  39.000000</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Number of subjects X 1</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a2"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a993b9b2a1faf43b319c1c6d58b26e7a1">testUnity_f32</a>[4] =</div>
<div class="line">{</div>
<div class="line">  1.000,  1.000,   1.000,  1.000</div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">** f32 Output buffer</span></div>
<div class="line"><span class="comment">** ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="keyword">static</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a3"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>[<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>];</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Global defines</span></div>
<div class="line"><span class="comment">*------------------------------------------------------------------- */</span></div>
<div class="line"><span class="preprocessor">#define   NUMSTUDENTS  20</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define     NUMSUBJECTS  4</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"><span class="comment">/* ------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Global variables</span></div>
<div class="line"><span class="comment">*------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"> uint32_t    <a name="a4"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a> = 20;</div>
<div class="line"> uint32_t    <a name="a5"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a3d01884f63bcb694226ca7c24980757a">numSubjects</a> = 4;</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>    <a name="a6"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#aad32888fa966b3d9db9c31bcbba9d9ef">max_marks</a>, <a name="a7"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abb7687fa07ec54d8e792cfcbfe2ca809">min_marks</a>, <a name="a8"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681">mean</a>, <a name="a9"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a150b0cf729b51893379f5b5548d4f989">std</a>, <a name="a10"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a3bd39c4335d84be071cc1eaa9b0a8642">var</a>;</div>
<div class="line"> uint32_t    <a name="a11"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a798cf43a3725d7df2fcaf3f328969f53">student_num</a>;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Main f32 test function.  It returns maximum marks secured and student number</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line">int32_t <a name="a12"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>()</div>
<div class="line">{</div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#ifndef  USE_STATIC_INIT</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line">  <a name="_a13"></a><a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> srcA;</div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> srcB;</div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> dstC;</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* Input and output matrices initializations */</span></div>
<div class="line">  <a name="a14"></a><a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;srcA, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a>, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a3d01884f63bcb694226ca7c24980757a">numSubjects</a>, (<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a0153222efa82b7f1a0ea3835921bf921">testMarks_f32</a>);</div>
<div class="line">  <a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;srcB, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a3d01884f63bcb694226ca7c24980757a">numSubjects</a>, 1, (<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a993b9b2a1faf43b319c1c6d58b26e7a1">testUnity_f32</a>);</div>
<div class="line">  <a class="code" href="group___matrix_init.html#ga11e3dc41592a6401c13182fef9416a27" title="Floating-point matrix initialization.">arm_mat_init_f32</a>(&amp;dstC, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a>, 1, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>);</div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#else</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line">  <span class="comment">/* Static Initializations of Input and output matrix sizes and array */</span></div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> srcA = {<a name="a15"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a9d89ac0707e7c9363544986d47a70bd3">NUMSTUDENTS</a>, <a name="a16"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a7b02f9b34bf2cd4d12633f5bf30771ec">NUMSUBJECTS</a>, (<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a0153222efa82b7f1a0ea3835921bf921">testMarks_f32</a>};</div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> srcB = {<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a7b02f9b34bf2cd4d12633f5bf30771ec">NUMSUBJECTS</a>, 1, (<a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> *)<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a993b9b2a1faf43b319c1c6d58b26e7a1">testUnity_f32</a>};</div>
<div class="line">  <a class="code" href="structarm__matrix__instance__f32.html" title="Instance structure for the floating-point matrix structure.">arm_matrix_instance_f32</a> dstC = {<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a9d89ac0707e7c9363544986d47a70bd3">NUMSTUDENTS</a>, 1, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>};</div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#endif</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  *Call the Matrix multiplication process function</span></div>
<div class="line"><span class="comment">  * ------------------------------------------------------------------- */</span></div>
<div class="line">  <a name="a17"></a><a class="code" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0" title="Floating-point matrix multiplication.">arm_mat_mult_f32</a>(&amp;srcA, &amp;srcB, &amp;dstC);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Call the Max function to calculate max marks among numStudents</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line">  <a name="a18"></a><a class="code" href="group___max.html#ga5b89d1b04575aeec494f678695fb87d8" title="Maximum value of a floating-point vector.">arm_max_f32</a>(<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a>, &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#aad32888fa966b3d9db9c31bcbba9d9ef">max_marks</a>, &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a798cf43a3725d7df2fcaf3f328969f53">student_num</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Call the Min function to calculate min marks among numStudents</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line">  <a name="a19"></a><a class="code" href="group___min.html#gaf62b1673740fc516ea64daf777b7d74a" title="Minimum value of a floating-point vector.">arm_min_f32</a>(<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a>, &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#abb7687fa07ec54d8e792cfcbfe2ca809">min_marks</a>, &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a798cf43a3725d7df2fcaf3f328969f53">student_num</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Call the Mean function to calculate mean</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line">  <a name="a20"></a><a class="code" href="group__mean.html#ga74ce08c49ab61e57bd50c3a0ca1fdb2b" title="Mean value of a floating-point vector.">arm_mean_f32</a>(<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a>, &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#acc9290716b3c97381ce52d14b4b01681">mean</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Call the std function to calculate standard deviation</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line">  <a name="a21"></a><a class="code" href="group___s_t_d.html#ga4969b5b5f3d001377bc401a3ee99dfc2" title="Standard deviation of the elements of a floating-point vector.">arm_std_f32</a>(<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a>, &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a150b0cf729b51893379f5b5548d4f989">std</a>);</div>
<div class="line"></div>
<div class="line">  <span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">  ** Call the var function to calculate variance</span></div>
<div class="line"><span class="comment">  ** ------------------------------------------------------------------- */</span></div>
<div class="line">  <a name="a22"></a><a class="code" href="group__variance.html#ga393f26c5a3bfa05624fb8d32232a6d96" title="Variance of the elements of a floating-point vector.">arm_var_f32</a>(<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#ab3b66d06b4af1af4ea2740d0ccf4e7bd">numStudents</a>, &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a3bd39c4335d84be071cc1eaa9b0a8642">var</a>);</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
