<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_sin_cos_example_f32.c</title>
<title>CMSIS-DSP: arm_sin_cos_example_f32.c</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('arm_sin_cos_example_f32_8c-example.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">arm_sin_cos_example_f32.c</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Copyright (C) 2010-2012 ARM Limited. All rights reserved.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* $Date:         12. March 2014</span></div>
<div class="line"><span class="comment">* $Revision:     V1.4.3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Project:       CMSIS DSP Library</span></div>
<div class="line"><span class="comment">* Title:         arm_sin_cos_example_f32.c</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Description:   Example code demonstrating sin and cos calculation of input signal.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Target Processor: Cortex-M4/Cortex-M3</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* Redistribution and use in source and binary forms, with or without</span></div>
<div class="line"><span class="comment">* modification, are permitted provided that the following conditions</span></div>
<div class="line"><span class="comment">* are met:</span></div>
<div class="line"><span class="comment">*   - Redistributions of source code must retain the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer.</span></div>
<div class="line"><span class="comment">*   - Redistributions in binary form must reproduce the above copyright</span></div>
<div class="line"><span class="comment">*     notice, this list of conditions and the following disclaimer in</span></div>
<div class="line"><span class="comment">*     the documentation and/or other materials provided with the</span></div>
<div class="line"><span class="comment">*     distribution.</span></div>
<div class="line"><span class="comment">*   - Neither the name of ARM LIMITED nor the names of its contributors</span></div>
<div class="line"><span class="comment">*     may be used to endorse or promote products derived from this</span></div>
<div class="line"><span class="comment">*     software without specific prior written permission.</span></div>
<div class="line"><span class="comment">*</span></div>
<div class="line"><span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS</span></div>
<div class="line"><span class="comment">* &quot;AS IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT</span></div>
<div class="line"><span class="comment">* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS</span></div>
<div class="line"><span class="comment">* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE</span></div>
<div class="line"><span class="comment">* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,</span></div>
<div class="line"><span class="comment">* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div>
<div class="line"><span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div>
<div class="line"><span class="comment">* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span></div>
<div class="line"><span class="comment">* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span></div>
<div class="line"><span class="comment">* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN</span></div>
<div class="line"><span class="comment">* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE</span></div>
<div class="line"><span class="comment">* POSSIBILITY OF SUCH DAMAGE.</span></div>
<div class="line"><span class="comment">* -------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="preprocessor">#include &lt;math.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="arm__math_8h.html">arm_math.h</a>&quot;</span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Defines each of the tests performed</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"><span class="preprocessor">#define MAX_BLOCKSIZE   32</span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define DELTA           (0.0001f)</span></div>
<div class="line"><span class="preprocessor"></span></div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Test input data for Floating point sin_cos example for 32-blockSize</span></div>
<div class="line"><span class="comment">* Generated by the MATLAB randn() function</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a0"></a><a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>[<a name="a1"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>] =</div>
<div class="line">{</div>
<div class="line">  -1.244916875853235400,  -4.793533929171324800,   0.360705030233248850,   0.827929644170887320,  -3.299532218312426900,   3.427441903227623800,   3.422401784294607700,  -0.108308165334010680,</div>
<div class="line">   0.941943896490312180,   0.502609575000365850,  -0.537345278736373500,   2.088817392965764500,  -1.693168684143455700,   6.283185307179590700,  -0.392545884746175080,   0.327893095115825040,</div>
<div class="line">   3.070147440456292300,   0.170611405884662230,  -0.275275082396073010,  -2.395492805446796300,   0.847311163536506600,  -3.845517018083148800,   2.055818378415868300,   4.672594161978930800,</div>
<div class="line">  -1.990923030266425800,   2.469305197656249500,   3.609002606064021000,  -4.586736582331667500,  -4.147080139136136300,   1.643756718868359500,  -1.150866392366494800,   1.985805026477433800</div>
<div class="line"></div>
<div class="line"></div>
<div class="line">};</div>
<div class="line"></div>
<div class="line"><span class="keyword">const</span> <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> <a name="a2"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">testRefOutput_f32</a> = 1.000000000;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Declare Global variables</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line">uint32_t <a name="a3"></a><a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a> = 32;</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  <a name="a4"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>;</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  <a name="a5"></a><a class="code" href="arm__sin__cos__example__f32_8c.html#a85b1050fcef4347d69f35a9aee798f8a">cosOutput</a>;</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  <a name="a6"></a><a class="code" href="arm__sin__cos__example__f32_8c.html#a1e232694019f6b61710fbff5ee27126c">sinOutput</a>;</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  <a name="a7"></a><a class="code" href="arm__sin__cos__example__f32_8c.html#a2cb185794dcb587d158f346ab049cc4e">cosSquareOutput</a>;</div>
<div class="line"><a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a>  <a name="a8"></a><a class="code" href="arm__sin__cos__example__f32_8c.html#aa5a66e866ebb91eb971f2805209e9d36">sinSquareOutput</a>;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* ----------------------------------------------------------------------</span></div>
<div class="line"><span class="comment">* Max magnitude FFT Bin test</span></div>
<div class="line"><span class="comment">* ------------------------------------------------------------------- */</span></div>
<div class="line"></div>
<div class="line"><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6" title="Error status returned by some functions in the library.">arm_status</a> <a name="a9"></a><a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>;</div>
<div class="line"></div>
<div class="line">int32_t <a name="a10"></a><a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#a196718f834091385d38586a0ce4009dc">main</a>(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">  <a class="code" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715" title="32-bit floating-point type definition.">float32_t</a> diff;</div>
<div class="line">  uint32_t i;</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">for</span>(i=0; i&lt; <a class="code" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>; i++)</div>
<div class="line">  {</div>
<div class="line">    <a class="code" href="arm__sin__cos__example__f32_8c.html#a85b1050fcef4347d69f35a9aee798f8a">cosOutput</a> = <a name="a11"></a><a class="code" href="group__cos.html#gace15287f9c64b9b4084d1c797d4c49d8" title="Fast approximation to the trigonometric cosine function for floating-point data.">arm_cos_f32</a>(<a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>[i]);</div>
<div class="line">    <a class="code" href="arm__sin__cos__example__f32_8c.html#a1e232694019f6b61710fbff5ee27126c">sinOutput</a> = <a name="a12"></a><a class="code" href="group__sin.html#gae164899c4a3fc0e946dc5d55555fe541" title="Fast approximation to the trigonometric sine function for floating-point data.">arm_sin_f32</a>(<a class="code" href="arm__graphic__equalizer__data_8c.html#a987ef9f3767fa5e083bcf2dd1efed05c">testInput_f32</a>[i]);</div>
<div class="line"></div>
<div class="line">    <a name="a13"></a><a class="code" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32" title="Floating-point vector multiplication.">arm_mult_f32</a>(&amp;<a class="code" href="arm__sin__cos__example__f32_8c.html#a85b1050fcef4347d69f35a9aee798f8a">cosOutput</a>, &amp;<a class="code" href="arm__sin__cos__example__f32_8c.html#a85b1050fcef4347d69f35a9aee798f8a">cosOutput</a>, &amp;<a class="code" href="arm__sin__cos__example__f32_8c.html#a2cb185794dcb587d158f346ab049cc4e">cosSquareOutput</a>, 1);</div>
<div class="line">    <a class="code" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32" title="Floating-point vector multiplication.">arm_mult_f32</a>(&amp;<a class="code" href="arm__sin__cos__example__f32_8c.html#a1e232694019f6b61710fbff5ee27126c">sinOutput</a>, &amp;<a class="code" href="arm__sin__cos__example__f32_8c.html#a1e232694019f6b61710fbff5ee27126c">sinOutput</a>, &amp;<a class="code" href="arm__sin__cos__example__f32_8c.html#aa5a66e866ebb91eb971f2805209e9d36">sinSquareOutput</a>, 1);</div>
<div class="line"></div>
<div class="line">    <a name="a14"></a><a class="code" href="group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd" title="Floating-point vector addition.">arm_add_f32</a>(&amp;<a class="code" href="arm__sin__cos__example__f32_8c.html#a2cb185794dcb587d158f346ab049cc4e">cosSquareOutput</a>, &amp;<a class="code" href="arm__sin__cos__example__f32_8c.html#aa5a66e866ebb91eb971f2805209e9d36">sinSquareOutput</a>, &amp;<a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>, 1);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* absolute value of difference between ref and test */</span></div>
<div class="line">    diff = fabsf(<a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a7ede41b07b8766013744c8fdbb80af75">testRefOutput_f32</a> - <a class="code" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>);</div>
<div class="line"></div>
<div class="line">    <span class="comment">/* Comparison of sin_cos value with reference */</span></div>
<div class="line">    <span class="keywordflow">if</span>(diff &gt; <a name="a15"></a><a class="code" href="_a_r_m_2arm__convolution__example__f32_8c.html#a3fd2b1bcd7ddcf506237987ad780f495">DELTA</a>)</div>
<div class="line">    {</div>
<div class="line">       <a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a> = <a name="a16"></a><a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>;</div>
<div class="line">    }</div>
<div class="line"></div>
<div class="line">    <span class="keywordflow">if</span>( <a class="code" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a> == <a class="code" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>)</div>
<div class="line">    {</div>
<div class="line">       <span class="keywordflow">while</span>(1);</div>
<div class="line">    }</div>
<div class="line"></div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);                             <span class="comment">/* main function does not return */</span></div>
<div class="line">}</div>
<div class="line"></div>
</div><!-- fragment --> </div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:47 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
